{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createOrUpdatePatternFromDecal } from '../util/decal.js';\nexport default function decalVisual(ecModel, api) {\n  ecModel.eachRawSeries(function (seriesModel) {\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var data = seriesModel.getData();\n    if (data.hasItemVisual()) {\n      data.each(function (idx) {\n        var decal = data.getItemVisual(idx, 'decal');\n        if (decal) {\n          var itemStyle = data.ensureUniqueItemVisual(idx, 'style');\n          itemStyle.decal = createOrUpdatePatternFromDecal(decal, api);\n        }\n      });\n    }\n    var decal = data.getVisual('decal');\n    if (decal) {\n      var style = data.getVisual('style');\n      style.decal = createOrUpdatePatternFromDecal(decal, api);\n    }\n  });\n}", "map": {"version": 3, "names": ["createOrUpdatePatternFromDecal", "decalVisual", "ecModel", "api", "eachRawSeries", "seriesModel", "isSeriesFiltered", "data", "getData", "hasItemVisual", "each", "idx", "decal", "getItemVisual", "itemStyle", "ensureUniqueItemVisual", "getVisual", "style"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/visual/decal.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createOrUpdatePatternFromDecal } from '../util/decal.js';\nexport default function decalVisual(ecModel, api) {\n  ecModel.eachRawSeries(function (seriesModel) {\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var data = seriesModel.getData();\n    if (data.hasItemVisual()) {\n      data.each(function (idx) {\n        var decal = data.getItemVisual(idx, 'decal');\n        if (decal) {\n          var itemStyle = data.ensureUniqueItemVisual(idx, 'style');\n          itemStyle.decal = createOrUpdatePatternFromDecal(decal, api);\n        }\n      });\n    }\n    var decal = data.getVisual('decal');\n    if (decal) {\n      var style = data.getVisual('style');\n      style.decal = createOrUpdatePatternFromDecal(decal, api);\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,8BAA8B,QAAQ,kBAAkB;AACjE,eAAe,SAASC,WAAWA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAChDD,OAAO,CAACE,aAAa,CAAC,UAAUC,WAAW,EAAE;IAC3C,IAAIH,OAAO,CAACI,gBAAgB,CAACD,WAAW,CAAC,EAAE;MACzC;IACF;IACA,IAAIE,IAAI,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;IAChC,IAAID,IAAI,CAACE,aAAa,CAAC,CAAC,EAAE;MACxBF,IAAI,CAACG,IAAI,CAAC,UAAUC,GAAG,EAAE;QACvB,IAAIC,KAAK,GAAGL,IAAI,CAACM,aAAa,CAACF,GAAG,EAAE,OAAO,CAAC;QAC5C,IAAIC,KAAK,EAAE;UACT,IAAIE,SAAS,GAAGP,IAAI,CAACQ,sBAAsB,CAACJ,GAAG,EAAE,OAAO,CAAC;UACzDG,SAAS,CAACF,KAAK,GAAGZ,8BAA8B,CAACY,KAAK,EAAET,GAAG,CAAC;QAC9D;MACF,CAAC,CAAC;IACJ;IACA,IAAIS,KAAK,GAAGL,IAAI,CAACS,SAAS,CAAC,OAAO,CAAC;IACnC,IAAIJ,KAAK,EAAE;MACT,IAAIK,KAAK,GAAGV,IAAI,CAACS,SAAS,CAAC,OAAO,CAAC;MACnCC,KAAK,CAACL,KAAK,GAAGZ,8BAA8B,CAACY,KAAK,EAAET,GAAG,CAAC;IAC1D;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}