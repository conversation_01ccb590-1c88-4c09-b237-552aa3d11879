{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isFunction, isObject, retrieve2 } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../util/model.js';\n// Stored properties for further transition.\nexport var transitionStore = makeInner();\n/**\r\n * Return null if animation is disabled.\r\n */\nexport function getAnimationConfig(animationType, animatableModel, dataIndex,\n// Extra opts can override the option in animatable model.\nextraOpts,\n// TODO It's only for pictorial bar now.\nextraDelayParams) {\n  var animationPayload;\n  // Check if there is global animation configuration from dataZoom/resize can override the config in option.\n  // If animation is enabled. Will use this animation config in payload.\n  // If animation is disabled. Just ignore it.\n  if (animatableModel && animatableModel.ecModel) {\n    var updatePayload = animatableModel.ecModel.getUpdatePayload();\n    animationPayload = updatePayload && updatePayload.animation;\n  }\n  var animationEnabled = animatableModel && animatableModel.isAnimationEnabled();\n  var isUpdate = animationType === 'update';\n  if (animationEnabled) {\n    var duration = void 0;\n    var easing = void 0;\n    var delay = void 0;\n    if (extraOpts) {\n      duration = retrieve2(extraOpts.duration, 200);\n      easing = retrieve2(extraOpts.easing, 'cubicOut');\n      delay = 0;\n    } else {\n      duration = animatableModel.getShallow(isUpdate ? 'animationDurationUpdate' : 'animationDuration');\n      easing = animatableModel.getShallow(isUpdate ? 'animationEasingUpdate' : 'animationEasing');\n      delay = animatableModel.getShallow(isUpdate ? 'animationDelayUpdate' : 'animationDelay');\n    }\n    // animation from payload has highest priority.\n    if (animationPayload) {\n      animationPayload.duration != null && (duration = animationPayload.duration);\n      animationPayload.easing != null && (easing = animationPayload.easing);\n      animationPayload.delay != null && (delay = animationPayload.delay);\n    }\n    if (isFunction(delay)) {\n      delay = delay(dataIndex, extraDelayParams);\n    }\n    if (isFunction(duration)) {\n      duration = duration(dataIndex);\n    }\n    var config = {\n      duration: duration || 0,\n      delay: delay,\n      easing: easing\n    };\n    return config;\n  } else {\n    return null;\n  }\n}\nfunction animateOrSetProps(animationType, el, props, animatableModel, dataIndex, cb, during) {\n  var isFrom = false;\n  var removeOpt;\n  if (isFunction(dataIndex)) {\n    during = cb;\n    cb = dataIndex;\n    dataIndex = null;\n  } else if (isObject(dataIndex)) {\n    cb = dataIndex.cb;\n    during = dataIndex.during;\n    isFrom = dataIndex.isFrom;\n    removeOpt = dataIndex.removeOpt;\n    dataIndex = dataIndex.dataIndex;\n  }\n  var isRemove = animationType === 'leave';\n  if (!isRemove) {\n    // Must stop the remove animation.\n    el.stopAnimation('leave');\n  }\n  var animationConfig = getAnimationConfig(animationType, animatableModel, dataIndex, isRemove ? removeOpt || {} : null, animatableModel && animatableModel.getAnimationDelayParams ? animatableModel.getAnimationDelayParams(el, dataIndex) : null);\n  if (animationConfig && animationConfig.duration > 0) {\n    var duration = animationConfig.duration;\n    var animationDelay = animationConfig.delay;\n    var animationEasing = animationConfig.easing;\n    var animateConfig = {\n      duration: duration,\n      delay: animationDelay || 0,\n      easing: animationEasing,\n      done: cb,\n      force: !!cb || !!during,\n      // Set to final state in update/init animation.\n      // So the post processing based on the path shape can be done correctly.\n      setToFinal: !isRemove,\n      scope: animationType,\n      during: during\n    };\n    isFrom ? el.animateFrom(props, animateConfig) : el.animateTo(props, animateConfig);\n  } else {\n    el.stopAnimation();\n    // If `isFrom`, the props is the \"from\" props.\n    !isFrom && el.attr(props);\n    // Call during at least once.\n    during && during(1);\n    cb && cb();\n  }\n}\n/**\r\n * Update graphic element properties with or without animation according to the\r\n * configuration in series.\r\n *\r\n * Caution: this method will stop previous animation.\r\n * So do not use this method to one element twice before\r\n * animation starts, unless you know what you are doing.\r\n * @example\r\n *     graphic.updateProps(el, {\r\n *         position: [100, 100]\r\n *     }, seriesModel, dataIndex, function () { console.log('Animation done!'); });\r\n *     // Or\r\n *     graphic.updateProps(el, {\r\n *         position: [100, 100]\r\n *     }, seriesModel, function () { console.log('Animation done!'); });\r\n */\nfunction updateProps(el, props,\n// TODO: TYPE AnimatableModel\nanimatableModel, dataIndex, cb, during) {\n  animateOrSetProps('update', el, props, animatableModel, dataIndex, cb, during);\n}\nexport { updateProps };\n/**\r\n * Init graphic element properties with or without animation according to the\r\n * configuration in series.\r\n *\r\n * Caution: this method will stop previous animation.\r\n * So do not use this method to one element twice before\r\n * animation starts, unless you know what you are doing.\r\n */\nexport function initProps(el, props, animatableModel, dataIndex, cb, during) {\n  animateOrSetProps('enter', el, props, animatableModel, dataIndex, cb, during);\n}\n/**\r\n * If element is removed.\r\n * It can determine if element is having remove animation.\r\n */\nexport function isElementRemoved(el) {\n  if (!el.__zr) {\n    return true;\n  }\n  for (var i = 0; i < el.animators.length; i++) {\n    var animator = el.animators[i];\n    if (animator.scope === 'leave') {\n      return true;\n    }\n  }\n  return false;\n}\n/**\r\n * Remove graphic element\r\n */\nexport function removeElement(el, props, animatableModel, dataIndex, cb, during) {\n  // Don't do remove animation twice.\n  if (isElementRemoved(el)) {\n    return;\n  }\n  animateOrSetProps('leave', el, props, animatableModel, dataIndex, cb, during);\n}\nfunction fadeOutDisplayable(el, animatableModel, dataIndex, done) {\n  el.removeTextContent();\n  el.removeTextGuideLine();\n  removeElement(el, {\n    style: {\n      opacity: 0\n    }\n  }, animatableModel, dataIndex, done);\n}\nexport function removeElementWithFadeOut(el, animatableModel, dataIndex) {\n  function doRemove() {\n    el.parent && el.parent.remove(el);\n  }\n  // Hide label and labelLine first\n  // TODO Also use fade out animation?\n  if (!el.isGroup) {\n    fadeOutDisplayable(el, animatableModel, dataIndex, doRemove);\n  } else {\n    el.traverse(function (disp) {\n      if (!disp.isGroup) {\n        // Can invoke doRemove multiple times.\n        fadeOutDisplayable(disp, animatableModel, dataIndex, doRemove);\n      }\n    });\n  }\n}\n/**\r\n * Save old style for style transition in universalTransition module.\r\n * It's used when element will be reused in each render.\r\n * For chart like map, heatmap, which will always create new element.\r\n * We don't need to save this because universalTransition can get old style from the old element\r\n */\nexport function saveOldStyle(el) {\n  transitionStore(el).oldStyle = el.style;\n}\nexport function getOldStyle(el) {\n  return transitionStore(el).oldStyle;\n}", "map": {"version": 3, "names": ["isFunction", "isObject", "retrieve2", "makeInner", "transitionStore", "getAnimationConfig", "animationType", "animatableModel", "dataIndex", "extraOpts", "extraDelayParams", "animationPayload", "ecModel", "updatePayload", "getUpdatePayload", "animation", "animationEnabled", "isAnimationEnabled", "isUpdate", "duration", "easing", "delay", "getShallow", "config", "animateOrSetProps", "el", "props", "cb", "during", "isFrom", "removeOpt", "isRemove", "stopAnimation", "animationConfig", "getAnimationDelayParams", "animationDelay", "animationEasing", "animateConfig", "done", "force", "setToFinal", "scope", "animateFrom", "animateTo", "attr", "updateProps", "initProps", "isElementRemoved", "__zr", "i", "animators", "length", "animator", "removeElement", "fadeOutDisplayable", "removeTextContent", "removeTextGuideLine", "style", "opacity", "removeElementWithFadeOut", "doRemove", "parent", "remove", "isGroup", "traverse", "disp", "saveOldStyle", "oldStyle", "getOldStyle"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/animation/basicTransition.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isFunction, isObject, retrieve2 } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../util/model.js';\n// Stored properties for further transition.\nexport var transitionStore = makeInner();\n/**\r\n * Return null if animation is disabled.\r\n */\nexport function getAnimationConfig(animationType, animatableModel, dataIndex,\n// Extra opts can override the option in animatable model.\nextraOpts,\n// TODO It's only for pictorial bar now.\nextraDelayParams) {\n  var animationPayload;\n  // Check if there is global animation configuration from dataZoom/resize can override the config in option.\n  // If animation is enabled. Will use this animation config in payload.\n  // If animation is disabled. Just ignore it.\n  if (animatableModel && animatableModel.ecModel) {\n    var updatePayload = animatableModel.ecModel.getUpdatePayload();\n    animationPayload = updatePayload && updatePayload.animation;\n  }\n  var animationEnabled = animatableModel && animatableModel.isAnimationEnabled();\n  var isUpdate = animationType === 'update';\n  if (animationEnabled) {\n    var duration = void 0;\n    var easing = void 0;\n    var delay = void 0;\n    if (extraOpts) {\n      duration = retrieve2(extraOpts.duration, 200);\n      easing = retrieve2(extraOpts.easing, 'cubicOut');\n      delay = 0;\n    } else {\n      duration = animatableModel.getShallow(isUpdate ? 'animationDurationUpdate' : 'animationDuration');\n      easing = animatableModel.getShallow(isUpdate ? 'animationEasingUpdate' : 'animationEasing');\n      delay = animatableModel.getShallow(isUpdate ? 'animationDelayUpdate' : 'animationDelay');\n    }\n    // animation from payload has highest priority.\n    if (animationPayload) {\n      animationPayload.duration != null && (duration = animationPayload.duration);\n      animationPayload.easing != null && (easing = animationPayload.easing);\n      animationPayload.delay != null && (delay = animationPayload.delay);\n    }\n    if (isFunction(delay)) {\n      delay = delay(dataIndex, extraDelayParams);\n    }\n    if (isFunction(duration)) {\n      duration = duration(dataIndex);\n    }\n    var config = {\n      duration: duration || 0,\n      delay: delay,\n      easing: easing\n    };\n    return config;\n  } else {\n    return null;\n  }\n}\nfunction animateOrSetProps(animationType, el, props, animatableModel, dataIndex, cb, during) {\n  var isFrom = false;\n  var removeOpt;\n  if (isFunction(dataIndex)) {\n    during = cb;\n    cb = dataIndex;\n    dataIndex = null;\n  } else if (isObject(dataIndex)) {\n    cb = dataIndex.cb;\n    during = dataIndex.during;\n    isFrom = dataIndex.isFrom;\n    removeOpt = dataIndex.removeOpt;\n    dataIndex = dataIndex.dataIndex;\n  }\n  var isRemove = animationType === 'leave';\n  if (!isRemove) {\n    // Must stop the remove animation.\n    el.stopAnimation('leave');\n  }\n  var animationConfig = getAnimationConfig(animationType, animatableModel, dataIndex, isRemove ? removeOpt || {} : null, animatableModel && animatableModel.getAnimationDelayParams ? animatableModel.getAnimationDelayParams(el, dataIndex) : null);\n  if (animationConfig && animationConfig.duration > 0) {\n    var duration = animationConfig.duration;\n    var animationDelay = animationConfig.delay;\n    var animationEasing = animationConfig.easing;\n    var animateConfig = {\n      duration: duration,\n      delay: animationDelay || 0,\n      easing: animationEasing,\n      done: cb,\n      force: !!cb || !!during,\n      // Set to final state in update/init animation.\n      // So the post processing based on the path shape can be done correctly.\n      setToFinal: !isRemove,\n      scope: animationType,\n      during: during\n    };\n    isFrom ? el.animateFrom(props, animateConfig) : el.animateTo(props, animateConfig);\n  } else {\n    el.stopAnimation();\n    // If `isFrom`, the props is the \"from\" props.\n    !isFrom && el.attr(props);\n    // Call during at least once.\n    during && during(1);\n    cb && cb();\n  }\n}\n/**\r\n * Update graphic element properties with or without animation according to the\r\n * configuration in series.\r\n *\r\n * Caution: this method will stop previous animation.\r\n * So do not use this method to one element twice before\r\n * animation starts, unless you know what you are doing.\r\n * @example\r\n *     graphic.updateProps(el, {\r\n *         position: [100, 100]\r\n *     }, seriesModel, dataIndex, function () { console.log('Animation done!'); });\r\n *     // Or\r\n *     graphic.updateProps(el, {\r\n *         position: [100, 100]\r\n *     }, seriesModel, function () { console.log('Animation done!'); });\r\n */\nfunction updateProps(el, props,\n// TODO: TYPE AnimatableModel\nanimatableModel, dataIndex, cb, during) {\n  animateOrSetProps('update', el, props, animatableModel, dataIndex, cb, during);\n}\nexport { updateProps };\n/**\r\n * Init graphic element properties with or without animation according to the\r\n * configuration in series.\r\n *\r\n * Caution: this method will stop previous animation.\r\n * So do not use this method to one element twice before\r\n * animation starts, unless you know what you are doing.\r\n */\nexport function initProps(el, props, animatableModel, dataIndex, cb, during) {\n  animateOrSetProps('enter', el, props, animatableModel, dataIndex, cb, during);\n}\n/**\r\n * If element is removed.\r\n * It can determine if element is having remove animation.\r\n */\nexport function isElementRemoved(el) {\n  if (!el.__zr) {\n    return true;\n  }\n  for (var i = 0; i < el.animators.length; i++) {\n    var animator = el.animators[i];\n    if (animator.scope === 'leave') {\n      return true;\n    }\n  }\n  return false;\n}\n/**\r\n * Remove graphic element\r\n */\nexport function removeElement(el, props, animatableModel, dataIndex, cb, during) {\n  // Don't do remove animation twice.\n  if (isElementRemoved(el)) {\n    return;\n  }\n  animateOrSetProps('leave', el, props, animatableModel, dataIndex, cb, during);\n}\nfunction fadeOutDisplayable(el, animatableModel, dataIndex, done) {\n  el.removeTextContent();\n  el.removeTextGuideLine();\n  removeElement(el, {\n    style: {\n      opacity: 0\n    }\n  }, animatableModel, dataIndex, done);\n}\nexport function removeElementWithFadeOut(el, animatableModel, dataIndex) {\n  function doRemove() {\n    el.parent && el.parent.remove(el);\n  }\n  // Hide label and labelLine first\n  // TODO Also use fade out animation?\n  if (!el.isGroup) {\n    fadeOutDisplayable(el, animatableModel, dataIndex, doRemove);\n  } else {\n    el.traverse(function (disp) {\n      if (!disp.isGroup) {\n        // Can invoke doRemove multiple times.\n        fadeOutDisplayable(disp, animatableModel, dataIndex, doRemove);\n      }\n    });\n  }\n}\n/**\r\n * Save old style for style transition in universalTransition module.\r\n * It's used when element will be reused in each render.\r\n * For chart like map, heatmap, which will always create new element.\r\n * We don't need to save this because universalTransition can get old style from the old element\r\n */\nexport function saveOldStyle(el) {\n  transitionStore(el).oldStyle = el.style;\n}\nexport function getOldStyle(el) {\n  return transitionStore(el).oldStyle;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,0BAA0B;AAC1E,SAASC,SAAS,QAAQ,kBAAkB;AAC5C;AACA,OAAO,IAAIC,eAAe,GAAGD,SAAS,CAAC,CAAC;AACxC;AACA;AACA;AACA,OAAO,SAASE,kBAAkBA,CAACC,aAAa,EAAEC,eAAe,EAAEC,SAAS;AAC5E;AACAC,SAAS;AACT;AACAC,gBAAgB,EAAE;EAChB,IAAIC,gBAAgB;EACpB;EACA;EACA;EACA,IAAIJ,eAAe,IAAIA,eAAe,CAACK,OAAO,EAAE;IAC9C,IAAIC,aAAa,GAAGN,eAAe,CAACK,OAAO,CAACE,gBAAgB,CAAC,CAAC;IAC9DH,gBAAgB,GAAGE,aAAa,IAAIA,aAAa,CAACE,SAAS;EAC7D;EACA,IAAIC,gBAAgB,GAAGT,eAAe,IAAIA,eAAe,CAACU,kBAAkB,CAAC,CAAC;EAC9E,IAAIC,QAAQ,GAAGZ,aAAa,KAAK,QAAQ;EACzC,IAAIU,gBAAgB,EAAE;IACpB,IAAIG,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAIC,MAAM,GAAG,KAAK,CAAC;IACnB,IAAIC,KAAK,GAAG,KAAK,CAAC;IAClB,IAAIZ,SAAS,EAAE;MACbU,QAAQ,GAAGjB,SAAS,CAACO,SAAS,CAACU,QAAQ,EAAE,GAAG,CAAC;MAC7CC,MAAM,GAAGlB,SAAS,CAACO,SAAS,CAACW,MAAM,EAAE,UAAU,CAAC;MAChDC,KAAK,GAAG,CAAC;IACX,CAAC,MAAM;MACLF,QAAQ,GAAGZ,eAAe,CAACe,UAAU,CAACJ,QAAQ,GAAG,yBAAyB,GAAG,mBAAmB,CAAC;MACjGE,MAAM,GAAGb,eAAe,CAACe,UAAU,CAACJ,QAAQ,GAAG,uBAAuB,GAAG,iBAAiB,CAAC;MAC3FG,KAAK,GAAGd,eAAe,CAACe,UAAU,CAACJ,QAAQ,GAAG,sBAAsB,GAAG,gBAAgB,CAAC;IAC1F;IACA;IACA,IAAIP,gBAAgB,EAAE;MACpBA,gBAAgB,CAACQ,QAAQ,IAAI,IAAI,KAAKA,QAAQ,GAAGR,gBAAgB,CAACQ,QAAQ,CAAC;MAC3ER,gBAAgB,CAACS,MAAM,IAAI,IAAI,KAAKA,MAAM,GAAGT,gBAAgB,CAACS,MAAM,CAAC;MACrET,gBAAgB,CAACU,KAAK,IAAI,IAAI,KAAKA,KAAK,GAAGV,gBAAgB,CAACU,KAAK,CAAC;IACpE;IACA,IAAIrB,UAAU,CAACqB,KAAK,CAAC,EAAE;MACrBA,KAAK,GAAGA,KAAK,CAACb,SAAS,EAAEE,gBAAgB,CAAC;IAC5C;IACA,IAAIV,UAAU,CAACmB,QAAQ,CAAC,EAAE;MACxBA,QAAQ,GAAGA,QAAQ,CAACX,SAAS,CAAC;IAChC;IACA,IAAIe,MAAM,GAAG;MACXJ,QAAQ,EAAEA,QAAQ,IAAI,CAAC;MACvBE,KAAK,EAAEA,KAAK;MACZD,MAAM,EAAEA;IACV,CAAC;IACD,OAAOG,MAAM;EACf,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;AACA,SAASC,iBAAiBA,CAAClB,aAAa,EAAEmB,EAAE,EAAEC,KAAK,EAAEnB,eAAe,EAAEC,SAAS,EAAEmB,EAAE,EAAEC,MAAM,EAAE;EAC3F,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,SAAS;EACb,IAAI9B,UAAU,CAACQ,SAAS,CAAC,EAAE;IACzBoB,MAAM,GAAGD,EAAE;IACXA,EAAE,GAAGnB,SAAS;IACdA,SAAS,GAAG,IAAI;EAClB,CAAC,MAAM,IAAIP,QAAQ,CAACO,SAAS,CAAC,EAAE;IAC9BmB,EAAE,GAAGnB,SAAS,CAACmB,EAAE;IACjBC,MAAM,GAAGpB,SAAS,CAACoB,MAAM;IACzBC,MAAM,GAAGrB,SAAS,CAACqB,MAAM;IACzBC,SAAS,GAAGtB,SAAS,CAACsB,SAAS;IAC/BtB,SAAS,GAAGA,SAAS,CAACA,SAAS;EACjC;EACA,IAAIuB,QAAQ,GAAGzB,aAAa,KAAK,OAAO;EACxC,IAAI,CAACyB,QAAQ,EAAE;IACb;IACAN,EAAE,CAACO,aAAa,CAAC,OAAO,CAAC;EAC3B;EACA,IAAIC,eAAe,GAAG5B,kBAAkB,CAACC,aAAa,EAAEC,eAAe,EAAEC,SAAS,EAAEuB,QAAQ,GAAGD,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,EAAEvB,eAAe,IAAIA,eAAe,CAAC2B,uBAAuB,GAAG3B,eAAe,CAAC2B,uBAAuB,CAACT,EAAE,EAAEjB,SAAS,CAAC,GAAG,IAAI,CAAC;EAClP,IAAIyB,eAAe,IAAIA,eAAe,CAACd,QAAQ,GAAG,CAAC,EAAE;IACnD,IAAIA,QAAQ,GAAGc,eAAe,CAACd,QAAQ;IACvC,IAAIgB,cAAc,GAAGF,eAAe,CAACZ,KAAK;IAC1C,IAAIe,eAAe,GAAGH,eAAe,CAACb,MAAM;IAC5C,IAAIiB,aAAa,GAAG;MAClBlB,QAAQ,EAAEA,QAAQ;MAClBE,KAAK,EAAEc,cAAc,IAAI,CAAC;MAC1Bf,MAAM,EAAEgB,eAAe;MACvBE,IAAI,EAAEX,EAAE;MACRY,KAAK,EAAE,CAAC,CAACZ,EAAE,IAAI,CAAC,CAACC,MAAM;MACvB;MACA;MACAY,UAAU,EAAE,CAACT,QAAQ;MACrBU,KAAK,EAAEnC,aAAa;MACpBsB,MAAM,EAAEA;IACV,CAAC;IACDC,MAAM,GAAGJ,EAAE,CAACiB,WAAW,CAAChB,KAAK,EAAEW,aAAa,CAAC,GAAGZ,EAAE,CAACkB,SAAS,CAACjB,KAAK,EAAEW,aAAa,CAAC;EACpF,CAAC,MAAM;IACLZ,EAAE,CAACO,aAAa,CAAC,CAAC;IAClB;IACA,CAACH,MAAM,IAAIJ,EAAE,CAACmB,IAAI,CAAClB,KAAK,CAAC;IACzB;IACAE,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC;IACnBD,EAAE,IAAIA,EAAE,CAAC,CAAC;EACZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,WAAWA,CAACpB,EAAE,EAAEC,KAAK;AAC9B;AACAnB,eAAe,EAAEC,SAAS,EAAEmB,EAAE,EAAEC,MAAM,EAAE;EACtCJ,iBAAiB,CAAC,QAAQ,EAAEC,EAAE,EAAEC,KAAK,EAAEnB,eAAe,EAAEC,SAAS,EAAEmB,EAAE,EAAEC,MAAM,CAAC;AAChF;AACA,SAASiB,WAAW;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACrB,EAAE,EAAEC,KAAK,EAAEnB,eAAe,EAAEC,SAAS,EAAEmB,EAAE,EAAEC,MAAM,EAAE;EAC3EJ,iBAAiB,CAAC,OAAO,EAAEC,EAAE,EAAEC,KAAK,EAAEnB,eAAe,EAAEC,SAAS,EAAEmB,EAAE,EAAEC,MAAM,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmB,gBAAgBA,CAACtB,EAAE,EAAE;EACnC,IAAI,CAACA,EAAE,CAACuB,IAAI,EAAE;IACZ,OAAO,IAAI;EACb;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,EAAE,CAACyB,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAC5C,IAAIG,QAAQ,GAAG3B,EAAE,CAACyB,SAAS,CAACD,CAAC,CAAC;IAC9B,IAAIG,QAAQ,CAACX,KAAK,KAAK,OAAO,EAAE;MAC9B,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AACA;AACA;AACA;AACA,OAAO,SAASY,aAAaA,CAAC5B,EAAE,EAAEC,KAAK,EAAEnB,eAAe,EAAEC,SAAS,EAAEmB,EAAE,EAAEC,MAAM,EAAE;EAC/E;EACA,IAAImB,gBAAgB,CAACtB,EAAE,CAAC,EAAE;IACxB;EACF;EACAD,iBAAiB,CAAC,OAAO,EAAEC,EAAE,EAAEC,KAAK,EAAEnB,eAAe,EAAEC,SAAS,EAAEmB,EAAE,EAAEC,MAAM,CAAC;AAC/E;AACA,SAAS0B,kBAAkBA,CAAC7B,EAAE,EAAElB,eAAe,EAAEC,SAAS,EAAE8B,IAAI,EAAE;EAChEb,EAAE,CAAC8B,iBAAiB,CAAC,CAAC;EACtB9B,EAAE,CAAC+B,mBAAmB,CAAC,CAAC;EACxBH,aAAa,CAAC5B,EAAE,EAAE;IAChBgC,KAAK,EAAE;MACLC,OAAO,EAAE;IACX;EACF,CAAC,EAAEnD,eAAe,EAAEC,SAAS,EAAE8B,IAAI,CAAC;AACtC;AACA,OAAO,SAASqB,wBAAwBA,CAAClC,EAAE,EAAElB,eAAe,EAAEC,SAAS,EAAE;EACvE,SAASoD,QAAQA,CAAA,EAAG;IAClBnC,EAAE,CAACoC,MAAM,IAAIpC,EAAE,CAACoC,MAAM,CAACC,MAAM,CAACrC,EAAE,CAAC;EACnC;EACA;EACA;EACA,IAAI,CAACA,EAAE,CAACsC,OAAO,EAAE;IACfT,kBAAkB,CAAC7B,EAAE,EAAElB,eAAe,EAAEC,SAAS,EAAEoD,QAAQ,CAAC;EAC9D,CAAC,MAAM;IACLnC,EAAE,CAACuC,QAAQ,CAAC,UAAUC,IAAI,EAAE;MAC1B,IAAI,CAACA,IAAI,CAACF,OAAO,EAAE;QACjB;QACAT,kBAAkB,CAACW,IAAI,EAAE1D,eAAe,EAAEC,SAAS,EAAEoD,QAAQ,CAAC;MAChE;IACF,CAAC,CAAC;EACJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,YAAYA,CAACzC,EAAE,EAAE;EAC/BrB,eAAe,CAACqB,EAAE,CAAC,CAAC0C,QAAQ,GAAG1C,EAAE,CAACgC,KAAK;AACzC;AACA,OAAO,SAASW,WAAWA,CAAC3C,EAAE,EAAE;EAC9B,OAAOrB,eAAe,CAACqB,EAAE,CAAC,CAAC0C,QAAQ;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}