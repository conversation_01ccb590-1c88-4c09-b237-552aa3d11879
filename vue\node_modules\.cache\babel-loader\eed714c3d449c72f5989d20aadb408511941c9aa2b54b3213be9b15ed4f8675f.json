{"ast": null, "code": "import Point from './Point.js';\nvar extent = [0, 0];\nvar extent2 = [0, 0];\nvar minTv = new Point();\nvar maxTv = new Point();\nvar OrientedBoundingRect = function () {\n  function OrientedBoundingRect(rect, transform) {\n    this._corners = [];\n    this._axes = [];\n    this._origin = [0, 0];\n    for (var i = 0; i < 4; i++) {\n      this._corners[i] = new Point();\n    }\n    for (var i = 0; i < 2; i++) {\n      this._axes[i] = new Point();\n    }\n    if (rect) {\n      this.fromBoundingRect(rect, transform);\n    }\n  }\n  OrientedBoundingRect.prototype.fromBoundingRect = function (rect, transform) {\n    var corners = this._corners;\n    var axes = this._axes;\n    var x = rect.x;\n    var y = rect.y;\n    var x2 = x + rect.width;\n    var y2 = y + rect.height;\n    corners[0].set(x, y);\n    corners[1].set(x2, y);\n    corners[2].set(x2, y2);\n    corners[3].set(x, y2);\n    if (transform) {\n      for (var i = 0; i < 4; i++) {\n        corners[i].transform(transform);\n      }\n    }\n    Point.sub(axes[0], corners[1], corners[0]);\n    Point.sub(axes[1], corners[3], corners[0]);\n    axes[0].normalize();\n    axes[1].normalize();\n    for (var i = 0; i < 2; i++) {\n      this._origin[i] = axes[i].dot(corners[0]);\n    }\n  };\n  OrientedBoundingRect.prototype.intersect = function (other, mtv) {\n    var overlapped = true;\n    var noMtv = !mtv;\n    minTv.set(Infinity, Infinity);\n    maxTv.set(0, 0);\n    if (!this._intersectCheckOneSide(this, other, minTv, maxTv, noMtv, 1)) {\n      overlapped = false;\n      if (noMtv) {\n        return overlapped;\n      }\n    }\n    if (!this._intersectCheckOneSide(other, this, minTv, maxTv, noMtv, -1)) {\n      overlapped = false;\n      if (noMtv) {\n        return overlapped;\n      }\n    }\n    if (!noMtv) {\n      Point.copy(mtv, overlapped ? minTv : maxTv);\n    }\n    return overlapped;\n  };\n  OrientedBoundingRect.prototype._intersectCheckOneSide = function (self, other, minTv, maxTv, noMtv, inverse) {\n    var overlapped = true;\n    for (var i = 0; i < 2; i++) {\n      var axis = this._axes[i];\n      this._getProjMinMaxOnAxis(i, self._corners, extent);\n      this._getProjMinMaxOnAxis(i, other._corners, extent2);\n      if (extent[1] < extent2[0] || extent[0] > extent2[1]) {\n        overlapped = false;\n        if (noMtv) {\n          return overlapped;\n        }\n        var dist0 = Math.abs(extent2[0] - extent[1]);\n        var dist1 = Math.abs(extent[0] - extent2[1]);\n        if (Math.min(dist0, dist1) > maxTv.len()) {\n          if (dist0 < dist1) {\n            Point.scale(maxTv, axis, -dist0 * inverse);\n          } else {\n            Point.scale(maxTv, axis, dist1 * inverse);\n          }\n        }\n      } else if (minTv) {\n        var dist0 = Math.abs(extent2[0] - extent[1]);\n        var dist1 = Math.abs(extent[0] - extent2[1]);\n        if (Math.min(dist0, dist1) < minTv.len()) {\n          if (dist0 < dist1) {\n            Point.scale(minTv, axis, dist0 * inverse);\n          } else {\n            Point.scale(minTv, axis, -dist1 * inverse);\n          }\n        }\n      }\n    }\n    return overlapped;\n  };\n  OrientedBoundingRect.prototype._getProjMinMaxOnAxis = function (dim, corners, out) {\n    var axis = this._axes[dim];\n    var origin = this._origin;\n    var proj = corners[0].dot(axis) + origin[dim];\n    var min = proj;\n    var max = proj;\n    for (var i = 1; i < corners.length; i++) {\n      var proj_1 = corners[i].dot(axis) + origin[dim];\n      min = Math.min(proj_1, min);\n      max = Math.max(proj_1, max);\n    }\n    out[0] = min;\n    out[1] = max;\n  };\n  return OrientedBoundingRect;\n}();\nexport default OrientedBoundingRect;", "map": {"version": 3, "names": ["Point", "extent", "extent2", "minTv", "maxTv", "OrientedBoundingRect", "rect", "transform", "_corners", "_axes", "_origin", "i", "fromBoundingRect", "prototype", "corners", "axes", "x", "y", "x2", "width", "y2", "height", "set", "sub", "normalize", "dot", "intersect", "other", "mtv", "overlapped", "noMtv", "Infinity", "_intersectCheckOneSide", "copy", "self", "inverse", "axis", "_getProjMinMaxOnAxis", "dist0", "Math", "abs", "dist1", "min", "len", "scale", "dim", "out", "origin", "proj", "max", "length", "proj_1"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/zrender/lib/core/OrientedBoundingRect.js"], "sourcesContent": ["import Point from './Point.js';\nvar extent = [0, 0];\nvar extent2 = [0, 0];\nvar minTv = new Point();\nvar maxTv = new Point();\nvar OrientedBoundingRect = (function () {\n    function OrientedBoundingRect(rect, transform) {\n        this._corners = [];\n        this._axes = [];\n        this._origin = [0, 0];\n        for (var i = 0; i < 4; i++) {\n            this._corners[i] = new Point();\n        }\n        for (var i = 0; i < 2; i++) {\n            this._axes[i] = new Point();\n        }\n        if (rect) {\n            this.fromBoundingRect(rect, transform);\n        }\n    }\n    OrientedBoundingRect.prototype.fromBoundingRect = function (rect, transform) {\n        var corners = this._corners;\n        var axes = this._axes;\n        var x = rect.x;\n        var y = rect.y;\n        var x2 = x + rect.width;\n        var y2 = y + rect.height;\n        corners[0].set(x, y);\n        corners[1].set(x2, y);\n        corners[2].set(x2, y2);\n        corners[3].set(x, y2);\n        if (transform) {\n            for (var i = 0; i < 4; i++) {\n                corners[i].transform(transform);\n            }\n        }\n        Point.sub(axes[0], corners[1], corners[0]);\n        Point.sub(axes[1], corners[3], corners[0]);\n        axes[0].normalize();\n        axes[1].normalize();\n        for (var i = 0; i < 2; i++) {\n            this._origin[i] = axes[i].dot(corners[0]);\n        }\n    };\n    OrientedBoundingRect.prototype.intersect = function (other, mtv) {\n        var overlapped = true;\n        var noMtv = !mtv;\n        minTv.set(Infinity, Infinity);\n        maxTv.set(0, 0);\n        if (!this._intersectCheckOneSide(this, other, minTv, maxTv, noMtv, 1)) {\n            overlapped = false;\n            if (noMtv) {\n                return overlapped;\n            }\n        }\n        if (!this._intersectCheckOneSide(other, this, minTv, maxTv, noMtv, -1)) {\n            overlapped = false;\n            if (noMtv) {\n                return overlapped;\n            }\n        }\n        if (!noMtv) {\n            Point.copy(mtv, overlapped ? minTv : maxTv);\n        }\n        return overlapped;\n    };\n    OrientedBoundingRect.prototype._intersectCheckOneSide = function (self, other, minTv, maxTv, noMtv, inverse) {\n        var overlapped = true;\n        for (var i = 0; i < 2; i++) {\n            var axis = this._axes[i];\n            this._getProjMinMaxOnAxis(i, self._corners, extent);\n            this._getProjMinMaxOnAxis(i, other._corners, extent2);\n            if (extent[1] < extent2[0] || extent[0] > extent2[1]) {\n                overlapped = false;\n                if (noMtv) {\n                    return overlapped;\n                }\n                var dist0 = Math.abs(extent2[0] - extent[1]);\n                var dist1 = Math.abs(extent[0] - extent2[1]);\n                if (Math.min(dist0, dist1) > maxTv.len()) {\n                    if (dist0 < dist1) {\n                        Point.scale(maxTv, axis, -dist0 * inverse);\n                    }\n                    else {\n                        Point.scale(maxTv, axis, dist1 * inverse);\n                    }\n                }\n            }\n            else if (minTv) {\n                var dist0 = Math.abs(extent2[0] - extent[1]);\n                var dist1 = Math.abs(extent[0] - extent2[1]);\n                if (Math.min(dist0, dist1) < minTv.len()) {\n                    if (dist0 < dist1) {\n                        Point.scale(minTv, axis, dist0 * inverse);\n                    }\n                    else {\n                        Point.scale(minTv, axis, -dist1 * inverse);\n                    }\n                }\n            }\n        }\n        return overlapped;\n    };\n    OrientedBoundingRect.prototype._getProjMinMaxOnAxis = function (dim, corners, out) {\n        var axis = this._axes[dim];\n        var origin = this._origin;\n        var proj = corners[0].dot(axis) + origin[dim];\n        var min = proj;\n        var max = proj;\n        for (var i = 1; i < corners.length; i++) {\n            var proj_1 = corners[i].dot(axis) + origin[dim];\n            min = Math.min(proj_1, min);\n            max = Math.max(proj_1, max);\n        }\n        out[0] = min;\n        out[1] = max;\n    };\n    return OrientedBoundingRect;\n}());\nexport default OrientedBoundingRect;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAC9B,IAAIC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACnB,IAAIC,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACpB,IAAIC,KAAK,GAAG,IAAIH,KAAK,CAAC,CAAC;AACvB,IAAII,KAAK,GAAG,IAAIJ,KAAK,CAAC,CAAC;AACvB,IAAIK,oBAAoB,GAAI,YAAY;EACpC,SAASA,oBAAoBA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAC3C,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAI,CAACH,QAAQ,CAACG,CAAC,CAAC,GAAG,IAAIX,KAAK,CAAC,CAAC;IAClC;IACA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAI,CAACF,KAAK,CAACE,CAAC,CAAC,GAAG,IAAIX,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIM,IAAI,EAAE;MACN,IAAI,CAACM,gBAAgB,CAACN,IAAI,EAAEC,SAAS,CAAC;IAC1C;EACJ;EACAF,oBAAoB,CAACQ,SAAS,CAACD,gBAAgB,GAAG,UAAUN,IAAI,EAAEC,SAAS,EAAE;IACzE,IAAIO,OAAO,GAAG,IAAI,CAACN,QAAQ;IAC3B,IAAIO,IAAI,GAAG,IAAI,CAACN,KAAK;IACrB,IAAIO,CAAC,GAAGV,IAAI,CAACU,CAAC;IACd,IAAIC,CAAC,GAAGX,IAAI,CAACW,CAAC;IACd,IAAIC,EAAE,GAAGF,CAAC,GAAGV,IAAI,CAACa,KAAK;IACvB,IAAIC,EAAE,GAAGH,CAAC,GAAGX,IAAI,CAACe,MAAM;IACxBP,OAAO,CAAC,CAAC,CAAC,CAACQ,GAAG,CAACN,CAAC,EAAEC,CAAC,CAAC;IACpBH,OAAO,CAAC,CAAC,CAAC,CAACQ,GAAG,CAACJ,EAAE,EAAED,CAAC,CAAC;IACrBH,OAAO,CAAC,CAAC,CAAC,CAACQ,GAAG,CAACJ,EAAE,EAAEE,EAAE,CAAC;IACtBN,OAAO,CAAC,CAAC,CAAC,CAACQ,GAAG,CAACN,CAAC,EAAEI,EAAE,CAAC;IACrB,IAAIb,SAAS,EAAE;MACX,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxBG,OAAO,CAACH,CAAC,CAAC,CAACJ,SAAS,CAACA,SAAS,CAAC;MACnC;IACJ;IACAP,KAAK,CAACuB,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;IAC1Cd,KAAK,CAACuB,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;IAC1CC,IAAI,CAAC,CAAC,CAAC,CAACS,SAAS,CAAC,CAAC;IACnBT,IAAI,CAAC,CAAC,CAAC,CAACS,SAAS,CAAC,CAAC;IACnB,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAI,CAACD,OAAO,CAACC,CAAC,CAAC,GAAGI,IAAI,CAACJ,CAAC,CAAC,CAACc,GAAG,CAACX,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7C;EACJ,CAAC;EACDT,oBAAoB,CAACQ,SAAS,CAACa,SAAS,GAAG,UAAUC,KAAK,EAAEC,GAAG,EAAE;IAC7D,IAAIC,UAAU,GAAG,IAAI;IACrB,IAAIC,KAAK,GAAG,CAACF,GAAG;IAChBzB,KAAK,CAACmB,GAAG,CAACS,QAAQ,EAAEA,QAAQ,CAAC;IAC7B3B,KAAK,CAACkB,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,IAAI,CAAC,IAAI,CAACU,sBAAsB,CAAC,IAAI,EAAEL,KAAK,EAAExB,KAAK,EAAEC,KAAK,EAAE0B,KAAK,EAAE,CAAC,CAAC,EAAE;MACnED,UAAU,GAAG,KAAK;MAClB,IAAIC,KAAK,EAAE;QACP,OAAOD,UAAU;MACrB;IACJ;IACA,IAAI,CAAC,IAAI,CAACG,sBAAsB,CAACL,KAAK,EAAE,IAAI,EAAExB,KAAK,EAAEC,KAAK,EAAE0B,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;MACpED,UAAU,GAAG,KAAK;MAClB,IAAIC,KAAK,EAAE;QACP,OAAOD,UAAU;MACrB;IACJ;IACA,IAAI,CAACC,KAAK,EAAE;MACR9B,KAAK,CAACiC,IAAI,CAACL,GAAG,EAAEC,UAAU,GAAG1B,KAAK,GAAGC,KAAK,CAAC;IAC/C;IACA,OAAOyB,UAAU;EACrB,CAAC;EACDxB,oBAAoB,CAACQ,SAAS,CAACmB,sBAAsB,GAAG,UAAUE,IAAI,EAAEP,KAAK,EAAExB,KAAK,EAAEC,KAAK,EAAE0B,KAAK,EAAEK,OAAO,EAAE;IACzG,IAAIN,UAAU,GAAG,IAAI;IACrB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAIyB,IAAI,GAAG,IAAI,CAAC3B,KAAK,CAACE,CAAC,CAAC;MACxB,IAAI,CAAC0B,oBAAoB,CAAC1B,CAAC,EAAEuB,IAAI,CAAC1B,QAAQ,EAAEP,MAAM,CAAC;MACnD,IAAI,CAACoC,oBAAoB,CAAC1B,CAAC,EAAEgB,KAAK,CAACnB,QAAQ,EAAEN,OAAO,CAAC;MACrD,IAAID,MAAM,CAAC,CAAC,CAAC,GAAGC,OAAO,CAAC,CAAC,CAAC,IAAID,MAAM,CAAC,CAAC,CAAC,GAAGC,OAAO,CAAC,CAAC,CAAC,EAAE;QAClD2B,UAAU,GAAG,KAAK;QAClB,IAAIC,KAAK,EAAE;UACP,OAAOD,UAAU;QACrB;QACA,IAAIS,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACtC,OAAO,CAAC,CAAC,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAIwC,KAAK,GAAGF,IAAI,CAACC,GAAG,CAACvC,MAAM,CAAC,CAAC,CAAC,GAAGC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAIqC,IAAI,CAACG,GAAG,CAACJ,KAAK,EAAEG,KAAK,CAAC,GAAGrC,KAAK,CAACuC,GAAG,CAAC,CAAC,EAAE;UACtC,IAAIL,KAAK,GAAGG,KAAK,EAAE;YACfzC,KAAK,CAAC4C,KAAK,CAACxC,KAAK,EAAEgC,IAAI,EAAE,CAACE,KAAK,GAAGH,OAAO,CAAC;UAC9C,CAAC,MACI;YACDnC,KAAK,CAAC4C,KAAK,CAACxC,KAAK,EAAEgC,IAAI,EAAEK,KAAK,GAAGN,OAAO,CAAC;UAC7C;QACJ;MACJ,CAAC,MACI,IAAIhC,KAAK,EAAE;QACZ,IAAImC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACtC,OAAO,CAAC,CAAC,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAIwC,KAAK,GAAGF,IAAI,CAACC,GAAG,CAACvC,MAAM,CAAC,CAAC,CAAC,GAAGC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAIqC,IAAI,CAACG,GAAG,CAACJ,KAAK,EAAEG,KAAK,CAAC,GAAGtC,KAAK,CAACwC,GAAG,CAAC,CAAC,EAAE;UACtC,IAAIL,KAAK,GAAGG,KAAK,EAAE;YACfzC,KAAK,CAAC4C,KAAK,CAACzC,KAAK,EAAEiC,IAAI,EAAEE,KAAK,GAAGH,OAAO,CAAC;UAC7C,CAAC,MACI;YACDnC,KAAK,CAAC4C,KAAK,CAACzC,KAAK,EAAEiC,IAAI,EAAE,CAACK,KAAK,GAAGN,OAAO,CAAC;UAC9C;QACJ;MACJ;IACJ;IACA,OAAON,UAAU;EACrB,CAAC;EACDxB,oBAAoB,CAACQ,SAAS,CAACwB,oBAAoB,GAAG,UAAUQ,GAAG,EAAE/B,OAAO,EAAEgC,GAAG,EAAE;IAC/E,IAAIV,IAAI,GAAG,IAAI,CAAC3B,KAAK,CAACoC,GAAG,CAAC;IAC1B,IAAIE,MAAM,GAAG,IAAI,CAACrC,OAAO;IACzB,IAAIsC,IAAI,GAAGlC,OAAO,CAAC,CAAC,CAAC,CAACW,GAAG,CAACW,IAAI,CAAC,GAAGW,MAAM,CAACF,GAAG,CAAC;IAC7C,IAAIH,GAAG,GAAGM,IAAI;IACd,IAAIC,GAAG,GAAGD,IAAI;IACd,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,OAAO,CAACoC,MAAM,EAAEvC,CAAC,EAAE,EAAE;MACrC,IAAIwC,MAAM,GAAGrC,OAAO,CAACH,CAAC,CAAC,CAACc,GAAG,CAACW,IAAI,CAAC,GAAGW,MAAM,CAACF,GAAG,CAAC;MAC/CH,GAAG,GAAGH,IAAI,CAACG,GAAG,CAACS,MAAM,EAAET,GAAG,CAAC;MAC3BO,GAAG,GAAGV,IAAI,CAACU,GAAG,CAACE,MAAM,EAAEF,GAAG,CAAC;IAC/B;IACAH,GAAG,CAAC,CAAC,CAAC,GAAGJ,GAAG;IACZI,GAAG,CAAC,CAAC,CAAC,GAAGG,GAAG;EAChB,CAAC;EACD,OAAO5C,oBAAoB;AAC/B,CAAC,CAAC,CAAE;AACJ,eAAeA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}