{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport default function legendFilter(ecModel) {\n  var legendModels = ecModel.findComponents({\n    mainType: 'legend'\n  });\n  if (legendModels && legendModels.length) {\n    ecModel.filterSeries(function (series) {\n      // If in any legend component the status is not selected.\n      // Because in legend series is assumed selected when it is not in the legend data.\n      for (var i = 0; i < legendModels.length; i++) {\n        if (!legendModels[i].isSelected(series.name)) {\n          return false;\n        }\n      }\n      return true;\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>er", "ecModel", "legend<PERSON><PERSON><PERSON>", "findComponents", "mainType", "length", "filterSeries", "series", "i", "isSelected", "name"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/component/legend/legendFilter.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport default function legendFilter(ecModel) {\n  var legendModels = ecModel.findComponents({\n    mainType: 'legend'\n  });\n  if (legendModels && legendModels.length) {\n    ecModel.filterSeries(function (series) {\n      // If in any legend component the status is not selected.\n      // Because in legend series is assumed selected when it is not in the legend data.\n      for (var i = 0; i < legendModels.length; i++) {\n        if (!legendModels[i].isSelected(series.name)) {\n          return false;\n        }\n      }\n      return true;\n    });\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,YAAYA,CAACC,OAAO,EAAE;EAC5C,IAAIC,YAAY,GAAGD,OAAO,CAACE,cAAc,CAAC;IACxCC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,IAAIF,YAAY,IAAIA,YAAY,CAACG,MAAM,EAAE;IACvCJ,OAAO,CAACK,YAAY,CAAC,UAAUC,MAAM,EAAE;MACrC;MACA;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,YAAY,CAACG,MAAM,EAAEG,CAAC,EAAE,EAAE;QAC5C,IAAI,CAACN,YAAY,CAACM,CAAC,CAAC,CAACC,UAAU,CAACF,MAAM,CAACG,IAAI,CAAC,EAAE;UAC5C,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}