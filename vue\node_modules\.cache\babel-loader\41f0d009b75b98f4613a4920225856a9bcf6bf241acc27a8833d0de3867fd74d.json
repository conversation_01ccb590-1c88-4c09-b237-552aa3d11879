{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The treemap layout implementation was originally copied from\r\n* \"d3.js\" with some modifications made for this project.\r\n* (See more details in the comment of the method \"squarify\" below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { parsePercent, MAX_SAFE_INTEGER } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport * as helper from '../helper/treeHelper.js';\nvar mathMax = Math.max;\nvar mathMin = Math.min;\nvar retrieveValue = zrUtil.retrieve;\nvar each = zrUtil.each;\nvar PATH_BORDER_WIDTH = ['itemStyle', 'borderWidth'];\nvar PATH_GAP_WIDTH = ['itemStyle', 'gapWidth'];\nvar PATH_UPPER_LABEL_SHOW = ['upperLabel', 'show'];\nvar PATH_UPPER_LABEL_HEIGHT = ['upperLabel', 'height'];\n;\n/**\r\n * @public\r\n */\nexport default {\n  seriesType: 'treemap',\n  reset: function (seriesModel, ecModel, api, payload) {\n    // Layout result in each node:\n    // {x, y, width, height, area, borderWidth}\n    var ecWidth = api.getWidth();\n    var ecHeight = api.getHeight();\n    var seriesOption = seriesModel.option;\n    var layoutInfo = layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n    var size = seriesOption.size || []; // Compatible with ec2.\n    var containerWidth = parsePercent(retrieveValue(layoutInfo.width, size[0]), ecWidth);\n    var containerHeight = parsePercent(retrieveValue(layoutInfo.height, size[1]), ecHeight);\n    // Fetch payload info.\n    var payloadType = payload && payload.type;\n    var types = ['treemapZoomToNode', 'treemapRootToNode'];\n    var targetInfo = helper.retrieveTargetInfo(payload, types, seriesModel);\n    var rootRect = payloadType === 'treemapRender' || payloadType === 'treemapMove' ? payload.rootRect : null;\n    var viewRoot = seriesModel.getViewRoot();\n    var viewAbovePath = helper.getPathToRoot(viewRoot);\n    if (payloadType !== 'treemapMove') {\n      var rootSize = payloadType === 'treemapZoomToNode' ? estimateRootSize(seriesModel, targetInfo, viewRoot, containerWidth, containerHeight) : rootRect ? [rootRect.width, rootRect.height] : [containerWidth, containerHeight];\n      var sort_1 = seriesOption.sort;\n      if (sort_1 && sort_1 !== 'asc' && sort_1 !== 'desc') {\n        // Default to be desc order.\n        sort_1 = 'desc';\n      }\n      var options = {\n        squareRatio: seriesOption.squareRatio,\n        sort: sort_1,\n        leafDepth: seriesOption.leafDepth\n      };\n      // layout should be cleared because using updateView but not update.\n      viewRoot.hostTree.clearLayouts();\n      // TODO\n      // optimize: if out of view clip, do not layout.\n      // But take care that if do not render node out of view clip,\n      // how to calculate start po\n      var viewRootLayout_1 = {\n        x: 0,\n        y: 0,\n        width: rootSize[0],\n        height: rootSize[1],\n        area: rootSize[0] * rootSize[1]\n      };\n      viewRoot.setLayout(viewRootLayout_1);\n      squarify(viewRoot, options, false, 0);\n      // Supplement layout.\n      viewRootLayout_1 = viewRoot.getLayout();\n      each(viewAbovePath, function (node, index) {\n        var childValue = (viewAbovePath[index + 1] || viewRoot).getValue();\n        node.setLayout(zrUtil.extend({\n          dataExtent: [childValue, childValue],\n          borderWidth: 0,\n          upperHeight: 0\n        }, viewRootLayout_1));\n      });\n    }\n    var treeRoot = seriesModel.getData().tree.root;\n    treeRoot.setLayout(calculateRootPosition(layoutInfo, rootRect, targetInfo), true);\n    seriesModel.setLayoutInfo(layoutInfo);\n    // FIXME\n    // 现在没有clip功能，暂时取ec高宽。\n    prunning(treeRoot,\n    // Transform to base element coordinate system.\n    new BoundingRect(-layoutInfo.x, -layoutInfo.y, ecWidth, ecHeight), viewAbovePath, viewRoot, 0);\n  }\n};\n/**\r\n * Layout treemap with squarify algorithm.\r\n * The original presentation of this algorithm\r\n * was made by Mark Bruls, Kees Huizing, and Jarke J. van Wijk\r\n * <https://graphics.ethz.ch/teaching/scivis_common/Literature/squarifiedTreeMaps.pdf>.\r\n * The implementation of this algorithm was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3/blob/9cc9a875e636a1dcf36cc1e07bdf77e1ad6e2c74/src/layout/treemap.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * @protected\r\n * @param {module:echarts/data/Tree~TreeNode} node\r\n * @param {Object} options\r\n * @param {string} options.sort 'asc' or 'desc'\r\n * @param {number} options.squareRatio\r\n * @param {boolean} hideChildren\r\n * @param {number} depth\r\n */\nfunction squarify(node, options, hideChildren, depth) {\n  var width;\n  var height;\n  if (node.isRemoved()) {\n    return;\n  }\n  var thisLayout = node.getLayout();\n  width = thisLayout.width;\n  height = thisLayout.height;\n  // Considering border and gap\n  var nodeModel = node.getModel();\n  var borderWidth = nodeModel.get(PATH_BORDER_WIDTH);\n  var halfGapWidth = nodeModel.get(PATH_GAP_WIDTH) / 2;\n  var upperLabelHeight = getUpperLabelHeight(nodeModel);\n  var upperHeight = Math.max(borderWidth, upperLabelHeight);\n  var layoutOffset = borderWidth - halfGapWidth;\n  var layoutOffsetUpper = upperHeight - halfGapWidth;\n  node.setLayout({\n    borderWidth: borderWidth,\n    upperHeight: upperHeight,\n    upperLabelHeight: upperLabelHeight\n  }, true);\n  width = mathMax(width - 2 * layoutOffset, 0);\n  height = mathMax(height - layoutOffset - layoutOffsetUpper, 0);\n  var totalArea = width * height;\n  var viewChildren = initChildren(node, nodeModel, totalArea, options, hideChildren, depth);\n  if (!viewChildren.length) {\n    return;\n  }\n  var rect = {\n    x: layoutOffset,\n    y: layoutOffsetUpper,\n    width: width,\n    height: height\n  };\n  var rowFixedLength = mathMin(width, height);\n  var best = Infinity; // the best row score so far\n  var row = [];\n  row.area = 0;\n  for (var i = 0, len = viewChildren.length; i < len;) {\n    var child = viewChildren[i];\n    row.push(child);\n    row.area += child.getLayout().area;\n    var score = worst(row, rowFixedLength, options.squareRatio);\n    // continue with this orientation\n    if (score <= best) {\n      i++;\n      best = score;\n    }\n    // abort, and try a different orientation\n    else {\n      row.area -= row.pop().getLayout().area;\n      position(row, rowFixedLength, rect, halfGapWidth, false);\n      rowFixedLength = mathMin(rect.width, rect.height);\n      row.length = row.area = 0;\n      best = Infinity;\n    }\n  }\n  if (row.length) {\n    position(row, rowFixedLength, rect, halfGapWidth, true);\n  }\n  if (!hideChildren) {\n    var childrenVisibleMin = nodeModel.get('childrenVisibleMin');\n    if (childrenVisibleMin != null && totalArea < childrenVisibleMin) {\n      hideChildren = true;\n    }\n  }\n  for (var i = 0, len = viewChildren.length; i < len; i++) {\n    squarify(viewChildren[i], options, hideChildren, depth + 1);\n  }\n}\n/**\r\n * Set area to each child, and calculate data extent for visual coding.\r\n */\nfunction initChildren(node, nodeModel, totalArea, options, hideChildren, depth) {\n  var viewChildren = node.children || [];\n  var orderBy = options.sort;\n  orderBy !== 'asc' && orderBy !== 'desc' && (orderBy = null);\n  var overLeafDepth = options.leafDepth != null && options.leafDepth <= depth;\n  // leafDepth has higher priority.\n  if (hideChildren && !overLeafDepth) {\n    return node.viewChildren = [];\n  }\n  // Sort children, order by desc.\n  viewChildren = zrUtil.filter(viewChildren, function (child) {\n    return !child.isRemoved();\n  });\n  sort(viewChildren, orderBy);\n  var info = statistic(nodeModel, viewChildren, orderBy);\n  if (info.sum === 0) {\n    return node.viewChildren = [];\n  }\n  info.sum = filterByThreshold(nodeModel, totalArea, info.sum, orderBy, viewChildren);\n  if (info.sum === 0) {\n    return node.viewChildren = [];\n  }\n  // Set area to each child.\n  for (var i = 0, len = viewChildren.length; i < len; i++) {\n    var area = viewChildren[i].getValue() / info.sum * totalArea;\n    // Do not use setLayout({...}, true), because it is needed to clear last layout.\n    viewChildren[i].setLayout({\n      area: area\n    });\n  }\n  if (overLeafDepth) {\n    viewChildren.length && node.setLayout({\n      isLeafRoot: true\n    }, true);\n    viewChildren.length = 0;\n  }\n  node.viewChildren = viewChildren;\n  node.setLayout({\n    dataExtent: info.dataExtent\n  }, true);\n  return viewChildren;\n}\n/**\r\n * Consider 'visibleMin'. Modify viewChildren and get new sum.\r\n */\nfunction filterByThreshold(nodeModel, totalArea, sum, orderBy, orderedChildren) {\n  // visibleMin is not supported yet when no option.sort.\n  if (!orderBy) {\n    return sum;\n  }\n  var visibleMin = nodeModel.get('visibleMin');\n  var len = orderedChildren.length;\n  var deletePoint = len;\n  // Always travel from little value to big value.\n  for (var i = len - 1; i >= 0; i--) {\n    var value = orderedChildren[orderBy === 'asc' ? len - i - 1 : i].getValue();\n    if (value / sum * totalArea < visibleMin) {\n      deletePoint = i;\n      sum -= value;\n    }\n  }\n  orderBy === 'asc' ? orderedChildren.splice(0, len - deletePoint) : orderedChildren.splice(deletePoint, len - deletePoint);\n  return sum;\n}\n/**\r\n * Sort\r\n */\nfunction sort(viewChildren, orderBy) {\n  if (orderBy) {\n    viewChildren.sort(function (a, b) {\n      var diff = orderBy === 'asc' ? a.getValue() - b.getValue() : b.getValue() - a.getValue();\n      return diff === 0 ? orderBy === 'asc' ? a.dataIndex - b.dataIndex : b.dataIndex - a.dataIndex : diff;\n    });\n  }\n  return viewChildren;\n}\n/**\r\n * Statistic\r\n */\nfunction statistic(nodeModel, children, orderBy) {\n  // Calculate sum.\n  var sum = 0;\n  for (var i = 0, len = children.length; i < len; i++) {\n    sum += children[i].getValue();\n  }\n  // Statistic data extent for latter visual coding.\n  // Notice: data extent should be calculate based on raw children\n  // but not filtered view children, otherwise visual mapping will not\n  // be stable when zoom (where children is filtered by visibleMin).\n  var dimension = nodeModel.get('visualDimension');\n  var dataExtent;\n  // The same as area dimension.\n  if (!children || !children.length) {\n    dataExtent = [NaN, NaN];\n  } else if (dimension === 'value' && orderBy) {\n    dataExtent = [children[children.length - 1].getValue(), children[0].getValue()];\n    orderBy === 'asc' && dataExtent.reverse();\n  }\n  // Other dimension.\n  else {\n    dataExtent = [Infinity, -Infinity];\n    each(children, function (child) {\n      var value = child.getValue(dimension);\n      value < dataExtent[0] && (dataExtent[0] = value);\n      value > dataExtent[1] && (dataExtent[1] = value);\n    });\n  }\n  return {\n    sum: sum,\n    dataExtent: dataExtent\n  };\n}\n/**\r\n * Computes the score for the specified row,\r\n * as the worst aspect ratio.\r\n */\nfunction worst(row, rowFixedLength, ratio) {\n  var areaMax = 0;\n  var areaMin = Infinity;\n  for (var i = 0, area = void 0, len = row.length; i < len; i++) {\n    area = row[i].getLayout().area;\n    if (area) {\n      area < areaMin && (areaMin = area);\n      area > areaMax && (areaMax = area);\n    }\n  }\n  var squareArea = row.area * row.area;\n  var f = rowFixedLength * rowFixedLength * ratio;\n  return squareArea ? mathMax(f * areaMax / squareArea, squareArea / (f * areaMin)) : Infinity;\n}\n/**\r\n * Positions the specified row of nodes. Modifies `rect`.\r\n */\nfunction position(row, rowFixedLength, rect, halfGapWidth, flush) {\n  // When rowFixedLength === rect.width,\n  // it is horizontal subdivision,\n  // rowFixedLength is the width of the subdivision,\n  // rowOtherLength is the height of the subdivision,\n  // and nodes will be positioned from left to right.\n  // wh[idx0WhenH] means: when horizontal,\n  //      wh[idx0WhenH] => wh[0] => 'width'.\n  //      xy[idx1WhenH] => xy[1] => 'y'.\n  var idx0WhenH = rowFixedLength === rect.width ? 0 : 1;\n  var idx1WhenH = 1 - idx0WhenH;\n  var xy = ['x', 'y'];\n  var wh = ['width', 'height'];\n  var last = rect[xy[idx0WhenH]];\n  var rowOtherLength = rowFixedLength ? row.area / rowFixedLength : 0;\n  if (flush || rowOtherLength > rect[wh[idx1WhenH]]) {\n    rowOtherLength = rect[wh[idx1WhenH]]; // over+underflow\n  }\n  for (var i = 0, rowLen = row.length; i < rowLen; i++) {\n    var node = row[i];\n    var nodeLayout = {};\n    var step = rowOtherLength ? node.getLayout().area / rowOtherLength : 0;\n    var wh1 = nodeLayout[wh[idx1WhenH]] = mathMax(rowOtherLength - 2 * halfGapWidth, 0);\n    // We use Math.max/min to avoid negative width/height when considering gap width.\n    var remain = rect[xy[idx0WhenH]] + rect[wh[idx0WhenH]] - last;\n    var modWH = i === rowLen - 1 || remain < step ? remain : step;\n    var wh0 = nodeLayout[wh[idx0WhenH]] = mathMax(modWH - 2 * halfGapWidth, 0);\n    nodeLayout[xy[idx1WhenH]] = rect[xy[idx1WhenH]] + mathMin(halfGapWidth, wh1 / 2);\n    nodeLayout[xy[idx0WhenH]] = last + mathMin(halfGapWidth, wh0 / 2);\n    last += modWH;\n    node.setLayout(nodeLayout, true);\n  }\n  rect[xy[idx1WhenH]] += rowOtherLength;\n  rect[wh[idx1WhenH]] -= rowOtherLength;\n}\n// Return [containerWidth, containerHeight] as default.\nfunction estimateRootSize(seriesModel, targetInfo, viewRoot, containerWidth, containerHeight) {\n  // If targetInfo.node exists, we zoom to the node,\n  // so estimate whole width and height by target node.\n  var currNode = (targetInfo || {}).node;\n  var defaultSize = [containerWidth, containerHeight];\n  if (!currNode || currNode === viewRoot) {\n    return defaultSize;\n  }\n  var parent;\n  var viewArea = containerWidth * containerHeight;\n  var area = viewArea * seriesModel.option.zoomToNodeRatio;\n  while (parent = currNode.parentNode) {\n    // jshint ignore:line\n    var sum = 0;\n    var siblings = parent.children;\n    for (var i = 0, len = siblings.length; i < len; i++) {\n      sum += siblings[i].getValue();\n    }\n    var currNodeValue = currNode.getValue();\n    if (currNodeValue === 0) {\n      return defaultSize;\n    }\n    area *= sum / currNodeValue;\n    // Considering border, suppose aspect ratio is 1.\n    var parentModel = parent.getModel();\n    var borderWidth = parentModel.get(PATH_BORDER_WIDTH);\n    var upperHeight = Math.max(borderWidth, getUpperLabelHeight(parentModel));\n    area += 4 * borderWidth * borderWidth + (3 * borderWidth + upperHeight) * Math.pow(area, 0.5);\n    area > MAX_SAFE_INTEGER && (area = MAX_SAFE_INTEGER);\n    currNode = parent;\n  }\n  area < viewArea && (area = viewArea);\n  var scale = Math.pow(area / viewArea, 0.5);\n  return [containerWidth * scale, containerHeight * scale];\n}\n// Root position based on coord of containerGroup\nfunction calculateRootPosition(layoutInfo, rootRect, targetInfo) {\n  if (rootRect) {\n    return {\n      x: rootRect.x,\n      y: rootRect.y\n    };\n  }\n  var defaultPosition = {\n    x: 0,\n    y: 0\n  };\n  if (!targetInfo) {\n    return defaultPosition;\n  }\n  // If targetInfo is fetched by 'retrieveTargetInfo',\n  // old tree and new tree are the same tree,\n  // so the node still exists and we can visit it.\n  var targetNode = targetInfo.node;\n  var layout = targetNode.getLayout();\n  if (!layout) {\n    return defaultPosition;\n  }\n  // Transform coord from local to container.\n  var targetCenter = [layout.width / 2, layout.height / 2];\n  var node = targetNode;\n  while (node) {\n    var nodeLayout = node.getLayout();\n    targetCenter[0] += nodeLayout.x;\n    targetCenter[1] += nodeLayout.y;\n    node = node.parentNode;\n  }\n  return {\n    x: layoutInfo.width / 2 - targetCenter[0],\n    y: layoutInfo.height / 2 - targetCenter[1]\n  };\n}\n// Mark nodes visible for prunning when visual coding and rendering.\n// Prunning depends on layout and root position, so we have to do it after layout.\nfunction prunning(node, clipRect, viewAbovePath, viewRoot, depth) {\n  var nodeLayout = node.getLayout();\n  var nodeInViewAbovePath = viewAbovePath[depth];\n  var isAboveViewRoot = nodeInViewAbovePath && nodeInViewAbovePath === node;\n  if (nodeInViewAbovePath && !isAboveViewRoot || depth === viewAbovePath.length && node !== viewRoot) {\n    return;\n  }\n  node.setLayout({\n    // isInView means: viewRoot sub tree + viewAbovePath\n    isInView: true,\n    // invisible only means: outside view clip so that the node can not\n    // see but still layout for animation preparation but not render.\n    invisible: !isAboveViewRoot && !clipRect.intersect(nodeLayout),\n    isAboveViewRoot: isAboveViewRoot\n  }, true);\n  // Transform to child coordinate.\n  var childClipRect = new BoundingRect(clipRect.x - nodeLayout.x, clipRect.y - nodeLayout.y, clipRect.width, clipRect.height);\n  each(node.viewChildren || [], function (child) {\n    prunning(child, childClipRect, viewAbovePath, viewRoot, depth + 1);\n  });\n}\nfunction getUpperLabelHeight(model) {\n  return model.get(PATH_UPPER_LABEL_SHOW) ? model.get(PATH_UPPER_LABEL_HEIGHT) : 0;\n}", "map": {"version": 3, "names": ["zrUtil", "BoundingRect", "parsePercent", "MAX_SAFE_INTEGER", "layout", "helper", "mathMax", "Math", "max", "mathMin", "min", "retrieveValue", "retrieve", "each", "PATH_BORDER_WIDTH", "PATH_GAP_WIDTH", "PATH_UPPER_LABEL_SHOW", "PATH_UPPER_LABEL_HEIGHT", "seriesType", "reset", "seriesModel", "ecModel", "api", "payload", "ec<PERSON><PERSON><PERSON>", "getWidth", "ecHeight", "getHeight", "seriesOption", "option", "layoutInfo", "getLayoutRect", "getBoxLayoutParams", "width", "height", "size", "containerWidth", "containerHeight", "payloadType", "type", "types", "targetInfo", "retrieveTargetInfo", "rootRect", "viewRoot", "getViewRoot", "viewAbovePath", "getPathToRoot", "rootSize", "estimateRootSize", "sort_1", "sort", "options", "squareRatio", "leafDepth", "hostTree", "clearLayouts", "viewRootLayout_1", "x", "y", "area", "setLayout", "squarify", "getLayout", "node", "index", "childValue", "getValue", "extend", "dataExtent", "borderWidth", "upperHeight", "treeRoot", "getData", "tree", "root", "calculateRootPosition", "setLayoutInfo", "prunning", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "depth", "isRemoved", "thisLayout", "nodeModel", "getModel", "get", "halfGapWidth", "upperLabelHeight", "getUpperLabelHeight", "layoutOffset", "layoutOffsetUpper", "totalArea", "viewChil<PERSON>n", "initChildren", "length", "rect", "rowFixed<PERSON><PERSON>th", "best", "Infinity", "row", "i", "len", "child", "push", "score", "worst", "pop", "position", "childrenVisibleMin", "children", "orderBy", "overLeafDepth", "filter", "info", "statistic", "sum", "filterByThreshold", "isLeafRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visibleMin", "deletePoint", "value", "splice", "a", "b", "diff", "dataIndex", "dimension", "NaN", "reverse", "ratio", "areaMax", "areaMin", "squareArea", "f", "flush", "idx0WhenH", "idx1WhenH", "xy", "wh", "last", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rowLen", "nodeLayout", "step", "wh1", "remain", "modWH", "wh0", "currNode", "defaultSize", "parent", "viewArea", "zoomToNodeRatio", "parentNode", "siblings", "currNodeValue", "parentModel", "pow", "scale", "defaultPosition", "targetNode", "targetCenter", "clipRect", "nodeInViewAbovePath", "isAboveViewRoot", "isInView", "invisible", "intersect", "childClipRect", "model"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/chart/treemap/treemapLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The treemap layout implementation was originally copied from\r\n* \"d3.js\" with some modifications made for this project.\r\n* (See more details in the comment of the method \"squarify\" below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { parsePercent, MAX_SAFE_INTEGER } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport * as helper from '../helper/treeHelper.js';\nvar mathMax = Math.max;\nvar mathMin = Math.min;\nvar retrieveValue = zrUtil.retrieve;\nvar each = zrUtil.each;\nvar PATH_BORDER_WIDTH = ['itemStyle', 'borderWidth'];\nvar PATH_GAP_WIDTH = ['itemStyle', 'gapWidth'];\nvar PATH_UPPER_LABEL_SHOW = ['upperLabel', 'show'];\nvar PATH_UPPER_LABEL_HEIGHT = ['upperLabel', 'height'];\n;\n/**\r\n * @public\r\n */\nexport default {\n  seriesType: 'treemap',\n  reset: function (seriesModel, ecModel, api, payload) {\n    // Layout result in each node:\n    // {x, y, width, height, area, borderWidth}\n    var ecWidth = api.getWidth();\n    var ecHeight = api.getHeight();\n    var seriesOption = seriesModel.option;\n    var layoutInfo = layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n    var size = seriesOption.size || []; // Compatible with ec2.\n    var containerWidth = parsePercent(retrieveValue(layoutInfo.width, size[0]), ecWidth);\n    var containerHeight = parsePercent(retrieveValue(layoutInfo.height, size[1]), ecHeight);\n    // Fetch payload info.\n    var payloadType = payload && payload.type;\n    var types = ['treemapZoomToNode', 'treemapRootToNode'];\n    var targetInfo = helper.retrieveTargetInfo(payload, types, seriesModel);\n    var rootRect = payloadType === 'treemapRender' || payloadType === 'treemapMove' ? payload.rootRect : null;\n    var viewRoot = seriesModel.getViewRoot();\n    var viewAbovePath = helper.getPathToRoot(viewRoot);\n    if (payloadType !== 'treemapMove') {\n      var rootSize = payloadType === 'treemapZoomToNode' ? estimateRootSize(seriesModel, targetInfo, viewRoot, containerWidth, containerHeight) : rootRect ? [rootRect.width, rootRect.height] : [containerWidth, containerHeight];\n      var sort_1 = seriesOption.sort;\n      if (sort_1 && sort_1 !== 'asc' && sort_1 !== 'desc') {\n        // Default to be desc order.\n        sort_1 = 'desc';\n      }\n      var options = {\n        squareRatio: seriesOption.squareRatio,\n        sort: sort_1,\n        leafDepth: seriesOption.leafDepth\n      };\n      // layout should be cleared because using updateView but not update.\n      viewRoot.hostTree.clearLayouts();\n      // TODO\n      // optimize: if out of view clip, do not layout.\n      // But take care that if do not render node out of view clip,\n      // how to calculate start po\n      var viewRootLayout_1 = {\n        x: 0,\n        y: 0,\n        width: rootSize[0],\n        height: rootSize[1],\n        area: rootSize[0] * rootSize[1]\n      };\n      viewRoot.setLayout(viewRootLayout_1);\n      squarify(viewRoot, options, false, 0);\n      // Supplement layout.\n      viewRootLayout_1 = viewRoot.getLayout();\n      each(viewAbovePath, function (node, index) {\n        var childValue = (viewAbovePath[index + 1] || viewRoot).getValue();\n        node.setLayout(zrUtil.extend({\n          dataExtent: [childValue, childValue],\n          borderWidth: 0,\n          upperHeight: 0\n        }, viewRootLayout_1));\n      });\n    }\n    var treeRoot = seriesModel.getData().tree.root;\n    treeRoot.setLayout(calculateRootPosition(layoutInfo, rootRect, targetInfo), true);\n    seriesModel.setLayoutInfo(layoutInfo);\n    // FIXME\n    // 现在没有clip功能，暂时取ec高宽。\n    prunning(treeRoot,\n    // Transform to base element coordinate system.\n    new BoundingRect(-layoutInfo.x, -layoutInfo.y, ecWidth, ecHeight), viewAbovePath, viewRoot, 0);\n  }\n};\n/**\r\n * Layout treemap with squarify algorithm.\r\n * The original presentation of this algorithm\r\n * was made by Mark Bruls, Kees Huizing, and Jarke J. van Wijk\r\n * <https://graphics.ethz.ch/teaching/scivis_common/Literature/squarifiedTreeMaps.pdf>.\r\n * The implementation of this algorithm was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3/blob/9cc9a875e636a1dcf36cc1e07bdf77e1ad6e2c74/src/layout/treemap.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * @protected\r\n * @param {module:echarts/data/Tree~TreeNode} node\r\n * @param {Object} options\r\n * @param {string} options.sort 'asc' or 'desc'\r\n * @param {number} options.squareRatio\r\n * @param {boolean} hideChildren\r\n * @param {number} depth\r\n */\nfunction squarify(node, options, hideChildren, depth) {\n  var width;\n  var height;\n  if (node.isRemoved()) {\n    return;\n  }\n  var thisLayout = node.getLayout();\n  width = thisLayout.width;\n  height = thisLayout.height;\n  // Considering border and gap\n  var nodeModel = node.getModel();\n  var borderWidth = nodeModel.get(PATH_BORDER_WIDTH);\n  var halfGapWidth = nodeModel.get(PATH_GAP_WIDTH) / 2;\n  var upperLabelHeight = getUpperLabelHeight(nodeModel);\n  var upperHeight = Math.max(borderWidth, upperLabelHeight);\n  var layoutOffset = borderWidth - halfGapWidth;\n  var layoutOffsetUpper = upperHeight - halfGapWidth;\n  node.setLayout({\n    borderWidth: borderWidth,\n    upperHeight: upperHeight,\n    upperLabelHeight: upperLabelHeight\n  }, true);\n  width = mathMax(width - 2 * layoutOffset, 0);\n  height = mathMax(height - layoutOffset - layoutOffsetUpper, 0);\n  var totalArea = width * height;\n  var viewChildren = initChildren(node, nodeModel, totalArea, options, hideChildren, depth);\n  if (!viewChildren.length) {\n    return;\n  }\n  var rect = {\n    x: layoutOffset,\n    y: layoutOffsetUpper,\n    width: width,\n    height: height\n  };\n  var rowFixedLength = mathMin(width, height);\n  var best = Infinity; // the best row score so far\n  var row = [];\n  row.area = 0;\n  for (var i = 0, len = viewChildren.length; i < len;) {\n    var child = viewChildren[i];\n    row.push(child);\n    row.area += child.getLayout().area;\n    var score = worst(row, rowFixedLength, options.squareRatio);\n    // continue with this orientation\n    if (score <= best) {\n      i++;\n      best = score;\n    }\n    // abort, and try a different orientation\n    else {\n      row.area -= row.pop().getLayout().area;\n      position(row, rowFixedLength, rect, halfGapWidth, false);\n      rowFixedLength = mathMin(rect.width, rect.height);\n      row.length = row.area = 0;\n      best = Infinity;\n    }\n  }\n  if (row.length) {\n    position(row, rowFixedLength, rect, halfGapWidth, true);\n  }\n  if (!hideChildren) {\n    var childrenVisibleMin = nodeModel.get('childrenVisibleMin');\n    if (childrenVisibleMin != null && totalArea < childrenVisibleMin) {\n      hideChildren = true;\n    }\n  }\n  for (var i = 0, len = viewChildren.length; i < len; i++) {\n    squarify(viewChildren[i], options, hideChildren, depth + 1);\n  }\n}\n/**\r\n * Set area to each child, and calculate data extent for visual coding.\r\n */\nfunction initChildren(node, nodeModel, totalArea, options, hideChildren, depth) {\n  var viewChildren = node.children || [];\n  var orderBy = options.sort;\n  orderBy !== 'asc' && orderBy !== 'desc' && (orderBy = null);\n  var overLeafDepth = options.leafDepth != null && options.leafDepth <= depth;\n  // leafDepth has higher priority.\n  if (hideChildren && !overLeafDepth) {\n    return node.viewChildren = [];\n  }\n  // Sort children, order by desc.\n  viewChildren = zrUtil.filter(viewChildren, function (child) {\n    return !child.isRemoved();\n  });\n  sort(viewChildren, orderBy);\n  var info = statistic(nodeModel, viewChildren, orderBy);\n  if (info.sum === 0) {\n    return node.viewChildren = [];\n  }\n  info.sum = filterByThreshold(nodeModel, totalArea, info.sum, orderBy, viewChildren);\n  if (info.sum === 0) {\n    return node.viewChildren = [];\n  }\n  // Set area to each child.\n  for (var i = 0, len = viewChildren.length; i < len; i++) {\n    var area = viewChildren[i].getValue() / info.sum * totalArea;\n    // Do not use setLayout({...}, true), because it is needed to clear last layout.\n    viewChildren[i].setLayout({\n      area: area\n    });\n  }\n  if (overLeafDepth) {\n    viewChildren.length && node.setLayout({\n      isLeafRoot: true\n    }, true);\n    viewChildren.length = 0;\n  }\n  node.viewChildren = viewChildren;\n  node.setLayout({\n    dataExtent: info.dataExtent\n  }, true);\n  return viewChildren;\n}\n/**\r\n * Consider 'visibleMin'. Modify viewChildren and get new sum.\r\n */\nfunction filterByThreshold(nodeModel, totalArea, sum, orderBy, orderedChildren) {\n  // visibleMin is not supported yet when no option.sort.\n  if (!orderBy) {\n    return sum;\n  }\n  var visibleMin = nodeModel.get('visibleMin');\n  var len = orderedChildren.length;\n  var deletePoint = len;\n  // Always travel from little value to big value.\n  for (var i = len - 1; i >= 0; i--) {\n    var value = orderedChildren[orderBy === 'asc' ? len - i - 1 : i].getValue();\n    if (value / sum * totalArea < visibleMin) {\n      deletePoint = i;\n      sum -= value;\n    }\n  }\n  orderBy === 'asc' ? orderedChildren.splice(0, len - deletePoint) : orderedChildren.splice(deletePoint, len - deletePoint);\n  return sum;\n}\n/**\r\n * Sort\r\n */\nfunction sort(viewChildren, orderBy) {\n  if (orderBy) {\n    viewChildren.sort(function (a, b) {\n      var diff = orderBy === 'asc' ? a.getValue() - b.getValue() : b.getValue() - a.getValue();\n      return diff === 0 ? orderBy === 'asc' ? a.dataIndex - b.dataIndex : b.dataIndex - a.dataIndex : diff;\n    });\n  }\n  return viewChildren;\n}\n/**\r\n * Statistic\r\n */\nfunction statistic(nodeModel, children, orderBy) {\n  // Calculate sum.\n  var sum = 0;\n  for (var i = 0, len = children.length; i < len; i++) {\n    sum += children[i].getValue();\n  }\n  // Statistic data extent for latter visual coding.\n  // Notice: data extent should be calculate based on raw children\n  // but not filtered view children, otherwise visual mapping will not\n  // be stable when zoom (where children is filtered by visibleMin).\n  var dimension = nodeModel.get('visualDimension');\n  var dataExtent;\n  // The same as area dimension.\n  if (!children || !children.length) {\n    dataExtent = [NaN, NaN];\n  } else if (dimension === 'value' && orderBy) {\n    dataExtent = [children[children.length - 1].getValue(), children[0].getValue()];\n    orderBy === 'asc' && dataExtent.reverse();\n  }\n  // Other dimension.\n  else {\n    dataExtent = [Infinity, -Infinity];\n    each(children, function (child) {\n      var value = child.getValue(dimension);\n      value < dataExtent[0] && (dataExtent[0] = value);\n      value > dataExtent[1] && (dataExtent[1] = value);\n    });\n  }\n  return {\n    sum: sum,\n    dataExtent: dataExtent\n  };\n}\n/**\r\n * Computes the score for the specified row,\r\n * as the worst aspect ratio.\r\n */\nfunction worst(row, rowFixedLength, ratio) {\n  var areaMax = 0;\n  var areaMin = Infinity;\n  for (var i = 0, area = void 0, len = row.length; i < len; i++) {\n    area = row[i].getLayout().area;\n    if (area) {\n      area < areaMin && (areaMin = area);\n      area > areaMax && (areaMax = area);\n    }\n  }\n  var squareArea = row.area * row.area;\n  var f = rowFixedLength * rowFixedLength * ratio;\n  return squareArea ? mathMax(f * areaMax / squareArea, squareArea / (f * areaMin)) : Infinity;\n}\n/**\r\n * Positions the specified row of nodes. Modifies `rect`.\r\n */\nfunction position(row, rowFixedLength, rect, halfGapWidth, flush) {\n  // When rowFixedLength === rect.width,\n  // it is horizontal subdivision,\n  // rowFixedLength is the width of the subdivision,\n  // rowOtherLength is the height of the subdivision,\n  // and nodes will be positioned from left to right.\n  // wh[idx0WhenH] means: when horizontal,\n  //      wh[idx0WhenH] => wh[0] => 'width'.\n  //      xy[idx1WhenH] => xy[1] => 'y'.\n  var idx0WhenH = rowFixedLength === rect.width ? 0 : 1;\n  var idx1WhenH = 1 - idx0WhenH;\n  var xy = ['x', 'y'];\n  var wh = ['width', 'height'];\n  var last = rect[xy[idx0WhenH]];\n  var rowOtherLength = rowFixedLength ? row.area / rowFixedLength : 0;\n  if (flush || rowOtherLength > rect[wh[idx1WhenH]]) {\n    rowOtherLength = rect[wh[idx1WhenH]]; // over+underflow\n  }\n  for (var i = 0, rowLen = row.length; i < rowLen; i++) {\n    var node = row[i];\n    var nodeLayout = {};\n    var step = rowOtherLength ? node.getLayout().area / rowOtherLength : 0;\n    var wh1 = nodeLayout[wh[idx1WhenH]] = mathMax(rowOtherLength - 2 * halfGapWidth, 0);\n    // We use Math.max/min to avoid negative width/height when considering gap width.\n    var remain = rect[xy[idx0WhenH]] + rect[wh[idx0WhenH]] - last;\n    var modWH = i === rowLen - 1 || remain < step ? remain : step;\n    var wh0 = nodeLayout[wh[idx0WhenH]] = mathMax(modWH - 2 * halfGapWidth, 0);\n    nodeLayout[xy[idx1WhenH]] = rect[xy[idx1WhenH]] + mathMin(halfGapWidth, wh1 / 2);\n    nodeLayout[xy[idx0WhenH]] = last + mathMin(halfGapWidth, wh0 / 2);\n    last += modWH;\n    node.setLayout(nodeLayout, true);\n  }\n  rect[xy[idx1WhenH]] += rowOtherLength;\n  rect[wh[idx1WhenH]] -= rowOtherLength;\n}\n// Return [containerWidth, containerHeight] as default.\nfunction estimateRootSize(seriesModel, targetInfo, viewRoot, containerWidth, containerHeight) {\n  // If targetInfo.node exists, we zoom to the node,\n  // so estimate whole width and height by target node.\n  var currNode = (targetInfo || {}).node;\n  var defaultSize = [containerWidth, containerHeight];\n  if (!currNode || currNode === viewRoot) {\n    return defaultSize;\n  }\n  var parent;\n  var viewArea = containerWidth * containerHeight;\n  var area = viewArea * seriesModel.option.zoomToNodeRatio;\n  while (parent = currNode.parentNode) {\n    // jshint ignore:line\n    var sum = 0;\n    var siblings = parent.children;\n    for (var i = 0, len = siblings.length; i < len; i++) {\n      sum += siblings[i].getValue();\n    }\n    var currNodeValue = currNode.getValue();\n    if (currNodeValue === 0) {\n      return defaultSize;\n    }\n    area *= sum / currNodeValue;\n    // Considering border, suppose aspect ratio is 1.\n    var parentModel = parent.getModel();\n    var borderWidth = parentModel.get(PATH_BORDER_WIDTH);\n    var upperHeight = Math.max(borderWidth, getUpperLabelHeight(parentModel));\n    area += 4 * borderWidth * borderWidth + (3 * borderWidth + upperHeight) * Math.pow(area, 0.5);\n    area > MAX_SAFE_INTEGER && (area = MAX_SAFE_INTEGER);\n    currNode = parent;\n  }\n  area < viewArea && (area = viewArea);\n  var scale = Math.pow(area / viewArea, 0.5);\n  return [containerWidth * scale, containerHeight * scale];\n}\n// Root position based on coord of containerGroup\nfunction calculateRootPosition(layoutInfo, rootRect, targetInfo) {\n  if (rootRect) {\n    return {\n      x: rootRect.x,\n      y: rootRect.y\n    };\n  }\n  var defaultPosition = {\n    x: 0,\n    y: 0\n  };\n  if (!targetInfo) {\n    return defaultPosition;\n  }\n  // If targetInfo is fetched by 'retrieveTargetInfo',\n  // old tree and new tree are the same tree,\n  // so the node still exists and we can visit it.\n  var targetNode = targetInfo.node;\n  var layout = targetNode.getLayout();\n  if (!layout) {\n    return defaultPosition;\n  }\n  // Transform coord from local to container.\n  var targetCenter = [layout.width / 2, layout.height / 2];\n  var node = targetNode;\n  while (node) {\n    var nodeLayout = node.getLayout();\n    targetCenter[0] += nodeLayout.x;\n    targetCenter[1] += nodeLayout.y;\n    node = node.parentNode;\n  }\n  return {\n    x: layoutInfo.width / 2 - targetCenter[0],\n    y: layoutInfo.height / 2 - targetCenter[1]\n  };\n}\n// Mark nodes visible for prunning when visual coding and rendering.\n// Prunning depends on layout and root position, so we have to do it after layout.\nfunction prunning(node, clipRect, viewAbovePath, viewRoot, depth) {\n  var nodeLayout = node.getLayout();\n  var nodeInViewAbovePath = viewAbovePath[depth];\n  var isAboveViewRoot = nodeInViewAbovePath && nodeInViewAbovePath === node;\n  if (nodeInViewAbovePath && !isAboveViewRoot || depth === viewAbovePath.length && node !== viewRoot) {\n    return;\n  }\n  node.setLayout({\n    // isInView means: viewRoot sub tree + viewAbovePath\n    isInView: true,\n    // invisible only means: outside view clip so that the node can not\n    // see but still layout for animation preparation but not render.\n    invisible: !isAboveViewRoot && !clipRect.intersect(nodeLayout),\n    isAboveViewRoot: isAboveViewRoot\n  }, true);\n  // Transform to child coordinate.\n  var childClipRect = new BoundingRect(clipRect.x - nodeLayout.x, clipRect.y - nodeLayout.y, clipRect.width, clipRect.height);\n  each(node.viewChildren || [], function (child) {\n    prunning(child, childClipRect, viewAbovePath, viewRoot, depth + 1);\n  });\n}\nfunction getUpperLabelHeight(model) {\n  return model.get(PATH_UPPER_LABEL_SHOW) ? model.get(PATH_UPPER_LABEL_HEIGHT) : 0;\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,sBAAsB;AACrE,OAAO,KAAKC,MAAM,MAAM,sBAAsB;AAC9C,OAAO,KAAKC,MAAM,MAAM,yBAAyB;AACjD,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;AACtB,IAAIC,aAAa,GAAGX,MAAM,CAACY,QAAQ;AACnC,IAAIC,IAAI,GAAGb,MAAM,CAACa,IAAI;AACtB,IAAIC,iBAAiB,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC;AACpD,IAAIC,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC;AAC9C,IAAIC,qBAAqB,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC;AAClD,IAAIC,uBAAuB,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC;AACtD;AACA;AACA;AACA;AACA,eAAe;EACbC,UAAU,EAAE,SAAS;EACrBC,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IACnD;IACA;IACA,IAAIC,OAAO,GAAGF,GAAG,CAACG,QAAQ,CAAC,CAAC;IAC5B,IAAIC,QAAQ,GAAGJ,GAAG,CAACK,SAAS,CAAC,CAAC;IAC9B,IAAIC,YAAY,GAAGR,WAAW,CAACS,MAAM;IACrC,IAAIC,UAAU,GAAG1B,MAAM,CAAC2B,aAAa,CAACX,WAAW,CAACY,kBAAkB,CAAC,CAAC,EAAE;MACtEC,KAAK,EAAEX,GAAG,CAACG,QAAQ,CAAC,CAAC;MACrBS,MAAM,EAAEZ,GAAG,CAACK,SAAS,CAAC;IACxB,CAAC,CAAC;IACF,IAAIQ,IAAI,GAAGP,YAAY,CAACO,IAAI,IAAI,EAAE,CAAC,CAAC;IACpC,IAAIC,cAAc,GAAGlC,YAAY,CAACS,aAAa,CAACmB,UAAU,CAACG,KAAK,EAAEE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEX,OAAO,CAAC;IACpF,IAAIa,eAAe,GAAGnC,YAAY,CAACS,aAAa,CAACmB,UAAU,CAACI,MAAM,EAAEC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAET,QAAQ,CAAC;IACvF;IACA,IAAIY,WAAW,GAAGf,OAAO,IAAIA,OAAO,CAACgB,IAAI;IACzC,IAAIC,KAAK,GAAG,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IACtD,IAAIC,UAAU,GAAGpC,MAAM,CAACqC,kBAAkB,CAACnB,OAAO,EAAEiB,KAAK,EAAEpB,WAAW,CAAC;IACvE,IAAIuB,QAAQ,GAAGL,WAAW,KAAK,eAAe,IAAIA,WAAW,KAAK,aAAa,GAAGf,OAAO,CAACoB,QAAQ,GAAG,IAAI;IACzG,IAAIC,QAAQ,GAAGxB,WAAW,CAACyB,WAAW,CAAC,CAAC;IACxC,IAAIC,aAAa,GAAGzC,MAAM,CAAC0C,aAAa,CAACH,QAAQ,CAAC;IAClD,IAAIN,WAAW,KAAK,aAAa,EAAE;MACjC,IAAIU,QAAQ,GAAGV,WAAW,KAAK,mBAAmB,GAAGW,gBAAgB,CAAC7B,WAAW,EAAEqB,UAAU,EAAEG,QAAQ,EAAER,cAAc,EAAEC,eAAe,CAAC,GAAGM,QAAQ,GAAG,CAACA,QAAQ,CAACV,KAAK,EAAEU,QAAQ,CAACT,MAAM,CAAC,GAAG,CAACE,cAAc,EAAEC,eAAe,CAAC;MAC5N,IAAIa,MAAM,GAAGtB,YAAY,CAACuB,IAAI;MAC9B,IAAID,MAAM,IAAIA,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,EAAE;QACnD;QACAA,MAAM,GAAG,MAAM;MACjB;MACA,IAAIE,OAAO,GAAG;QACZC,WAAW,EAAEzB,YAAY,CAACyB,WAAW;QACrCF,IAAI,EAAED,MAAM;QACZI,SAAS,EAAE1B,YAAY,CAAC0B;MAC1B,CAAC;MACD;MACAV,QAAQ,CAACW,QAAQ,CAACC,YAAY,CAAC,CAAC;MAChC;MACA;MACA;MACA;MACA,IAAIC,gBAAgB,GAAG;QACrBC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJ1B,KAAK,EAAEe,QAAQ,CAAC,CAAC,CAAC;QAClBd,MAAM,EAAEc,QAAQ,CAAC,CAAC,CAAC;QACnBY,IAAI,EAAEZ,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC;MAChC,CAAC;MACDJ,QAAQ,CAACiB,SAAS,CAACJ,gBAAgB,CAAC;MACpCK,QAAQ,CAAClB,QAAQ,EAAEQ,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;MACrC;MACAK,gBAAgB,GAAGb,QAAQ,CAACmB,SAAS,CAAC,CAAC;MACvClD,IAAI,CAACiC,aAAa,EAAE,UAAUkB,IAAI,EAAEC,KAAK,EAAE;QACzC,IAAIC,UAAU,GAAG,CAACpB,aAAa,CAACmB,KAAK,GAAG,CAAC,CAAC,IAAIrB,QAAQ,EAAEuB,QAAQ,CAAC,CAAC;QAClEH,IAAI,CAACH,SAAS,CAAC7D,MAAM,CAACoE,MAAM,CAAC;UAC3BC,UAAU,EAAE,CAACH,UAAU,EAAEA,UAAU,CAAC;UACpCI,WAAW,EAAE,CAAC;UACdC,WAAW,EAAE;QACf,CAAC,EAAEd,gBAAgB,CAAC,CAAC;MACvB,CAAC,CAAC;IACJ;IACA,IAAIe,QAAQ,GAAGpD,WAAW,CAACqD,OAAO,CAAC,CAAC,CAACC,IAAI,CAACC,IAAI;IAC9CH,QAAQ,CAACX,SAAS,CAACe,qBAAqB,CAAC9C,UAAU,EAAEa,QAAQ,EAAEF,UAAU,CAAC,EAAE,IAAI,CAAC;IACjFrB,WAAW,CAACyD,aAAa,CAAC/C,UAAU,CAAC;IACrC;IACA;IACAgD,QAAQ,CAACN,QAAQ;IACjB;IACA,IAAIvE,YAAY,CAAC,CAAC6B,UAAU,CAAC4B,CAAC,EAAE,CAAC5B,UAAU,CAAC6B,CAAC,EAAEnC,OAAO,EAAEE,QAAQ,CAAC,EAAEoB,aAAa,EAAEF,QAAQ,EAAE,CAAC,CAAC;EAChG;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,QAAQA,CAACE,IAAI,EAAEZ,OAAO,EAAE2B,YAAY,EAAEC,KAAK,EAAE;EACpD,IAAI/C,KAAK;EACT,IAAIC,MAAM;EACV,IAAI8B,IAAI,CAACiB,SAAS,CAAC,CAAC,EAAE;IACpB;EACF;EACA,IAAIC,UAAU,GAAGlB,IAAI,CAACD,SAAS,CAAC,CAAC;EACjC9B,KAAK,GAAGiD,UAAU,CAACjD,KAAK;EACxBC,MAAM,GAAGgD,UAAU,CAAChD,MAAM;EAC1B;EACA,IAAIiD,SAAS,GAAGnB,IAAI,CAACoB,QAAQ,CAAC,CAAC;EAC/B,IAAId,WAAW,GAAGa,SAAS,CAACE,GAAG,CAACvE,iBAAiB,CAAC;EAClD,IAAIwE,YAAY,GAAGH,SAAS,CAACE,GAAG,CAACtE,cAAc,CAAC,GAAG,CAAC;EACpD,IAAIwE,gBAAgB,GAAGC,mBAAmB,CAACL,SAAS,CAAC;EACrD,IAAIZ,WAAW,GAAGhE,IAAI,CAACC,GAAG,CAAC8D,WAAW,EAAEiB,gBAAgB,CAAC;EACzD,IAAIE,YAAY,GAAGnB,WAAW,GAAGgB,YAAY;EAC7C,IAAII,iBAAiB,GAAGnB,WAAW,GAAGe,YAAY;EAClDtB,IAAI,CAACH,SAAS,CAAC;IACbS,WAAW,EAAEA,WAAW;IACxBC,WAAW,EAAEA,WAAW;IACxBgB,gBAAgB,EAAEA;EACpB,CAAC,EAAE,IAAI,CAAC;EACRtD,KAAK,GAAG3B,OAAO,CAAC2B,KAAK,GAAG,CAAC,GAAGwD,YAAY,EAAE,CAAC,CAAC;EAC5CvD,MAAM,GAAG5B,OAAO,CAAC4B,MAAM,GAAGuD,YAAY,GAAGC,iBAAiB,EAAE,CAAC,CAAC;EAC9D,IAAIC,SAAS,GAAG1D,KAAK,GAAGC,MAAM;EAC9B,IAAI0D,YAAY,GAAGC,YAAY,CAAC7B,IAAI,EAAEmB,SAAS,EAAEQ,SAAS,EAAEvC,OAAO,EAAE2B,YAAY,EAAEC,KAAK,CAAC;EACzF,IAAI,CAACY,YAAY,CAACE,MAAM,EAAE;IACxB;EACF;EACA,IAAIC,IAAI,GAAG;IACTrC,CAAC,EAAE+B,YAAY;IACf9B,CAAC,EAAE+B,iBAAiB;IACpBzD,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA;EACV,CAAC;EACD,IAAI8D,cAAc,GAAGvF,OAAO,CAACwB,KAAK,EAAEC,MAAM,CAAC;EAC3C,IAAI+D,IAAI,GAAGC,QAAQ,CAAC,CAAC;EACrB,IAAIC,GAAG,GAAG,EAAE;EACZA,GAAG,CAACvC,IAAI,GAAG,CAAC;EACZ,KAAK,IAAIwC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGT,YAAY,CAACE,MAAM,EAAEM,CAAC,GAAGC,GAAG,GAAG;IACnD,IAAIC,KAAK,GAAGV,YAAY,CAACQ,CAAC,CAAC;IAC3BD,GAAG,CAACI,IAAI,CAACD,KAAK,CAAC;IACfH,GAAG,CAACvC,IAAI,IAAI0C,KAAK,CAACvC,SAAS,CAAC,CAAC,CAACH,IAAI;IAClC,IAAI4C,KAAK,GAAGC,KAAK,CAACN,GAAG,EAAEH,cAAc,EAAE5C,OAAO,CAACC,WAAW,CAAC;IAC3D;IACA,IAAImD,KAAK,IAAIP,IAAI,EAAE;MACjBG,CAAC,EAAE;MACHH,IAAI,GAAGO,KAAK;IACd;IACA;IAAA,KACK;MACHL,GAAG,CAACvC,IAAI,IAAIuC,GAAG,CAACO,GAAG,CAAC,CAAC,CAAC3C,SAAS,CAAC,CAAC,CAACH,IAAI;MACtC+C,QAAQ,CAACR,GAAG,EAAEH,cAAc,EAAED,IAAI,EAAET,YAAY,EAAE,KAAK,CAAC;MACxDU,cAAc,GAAGvF,OAAO,CAACsF,IAAI,CAAC9D,KAAK,EAAE8D,IAAI,CAAC7D,MAAM,CAAC;MACjDiE,GAAG,CAACL,MAAM,GAAGK,GAAG,CAACvC,IAAI,GAAG,CAAC;MACzBqC,IAAI,GAAGC,QAAQ;IACjB;EACF;EACA,IAAIC,GAAG,CAACL,MAAM,EAAE;IACda,QAAQ,CAACR,GAAG,EAAEH,cAAc,EAAED,IAAI,EAAET,YAAY,EAAE,IAAI,CAAC;EACzD;EACA,IAAI,CAACP,YAAY,EAAE;IACjB,IAAI6B,kBAAkB,GAAGzB,SAAS,CAACE,GAAG,CAAC,oBAAoB,CAAC;IAC5D,IAAIuB,kBAAkB,IAAI,IAAI,IAAIjB,SAAS,GAAGiB,kBAAkB,EAAE;MAChE7B,YAAY,GAAG,IAAI;IACrB;EACF;EACA,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGT,YAAY,CAACE,MAAM,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IACvDtC,QAAQ,CAAC8B,YAAY,CAACQ,CAAC,CAAC,EAAEhD,OAAO,EAAE2B,YAAY,EAAEC,KAAK,GAAG,CAAC,CAAC;EAC7D;AACF;AACA;AACA;AACA;AACA,SAASa,YAAYA,CAAC7B,IAAI,EAAEmB,SAAS,EAAEQ,SAAS,EAAEvC,OAAO,EAAE2B,YAAY,EAAEC,KAAK,EAAE;EAC9E,IAAIY,YAAY,GAAG5B,IAAI,CAAC6C,QAAQ,IAAI,EAAE;EACtC,IAAIC,OAAO,GAAG1D,OAAO,CAACD,IAAI;EAC1B2D,OAAO,KAAK,KAAK,IAAIA,OAAO,KAAK,MAAM,KAAKA,OAAO,GAAG,IAAI,CAAC;EAC3D,IAAIC,aAAa,GAAG3D,OAAO,CAACE,SAAS,IAAI,IAAI,IAAIF,OAAO,CAACE,SAAS,IAAI0B,KAAK;EAC3E;EACA,IAAID,YAAY,IAAI,CAACgC,aAAa,EAAE;IAClC,OAAO/C,IAAI,CAAC4B,YAAY,GAAG,EAAE;EAC/B;EACA;EACAA,YAAY,GAAG5F,MAAM,CAACgH,MAAM,CAACpB,YAAY,EAAE,UAAUU,KAAK,EAAE;IAC1D,OAAO,CAACA,KAAK,CAACrB,SAAS,CAAC,CAAC;EAC3B,CAAC,CAAC;EACF9B,IAAI,CAACyC,YAAY,EAAEkB,OAAO,CAAC;EAC3B,IAAIG,IAAI,GAAGC,SAAS,CAAC/B,SAAS,EAAES,YAAY,EAAEkB,OAAO,CAAC;EACtD,IAAIG,IAAI,CAACE,GAAG,KAAK,CAAC,EAAE;IAClB,OAAOnD,IAAI,CAAC4B,YAAY,GAAG,EAAE;EAC/B;EACAqB,IAAI,CAACE,GAAG,GAAGC,iBAAiB,CAACjC,SAAS,EAAEQ,SAAS,EAAEsB,IAAI,CAACE,GAAG,EAAEL,OAAO,EAAElB,YAAY,CAAC;EACnF,IAAIqB,IAAI,CAACE,GAAG,KAAK,CAAC,EAAE;IAClB,OAAOnD,IAAI,CAAC4B,YAAY,GAAG,EAAE;EAC/B;EACA;EACA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGT,YAAY,CAACE,MAAM,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IACvD,IAAIxC,IAAI,GAAGgC,YAAY,CAACQ,CAAC,CAAC,CAACjC,QAAQ,CAAC,CAAC,GAAG8C,IAAI,CAACE,GAAG,GAAGxB,SAAS;IAC5D;IACAC,YAAY,CAACQ,CAAC,CAAC,CAACvC,SAAS,CAAC;MACxBD,IAAI,EAAEA;IACR,CAAC,CAAC;EACJ;EACA,IAAImD,aAAa,EAAE;IACjBnB,YAAY,CAACE,MAAM,IAAI9B,IAAI,CAACH,SAAS,CAAC;MACpCwD,UAAU,EAAE;IACd,CAAC,EAAE,IAAI,CAAC;IACRzB,YAAY,CAACE,MAAM,GAAG,CAAC;EACzB;EACA9B,IAAI,CAAC4B,YAAY,GAAGA,YAAY;EAChC5B,IAAI,CAACH,SAAS,CAAC;IACbQ,UAAU,EAAE4C,IAAI,CAAC5C;EACnB,CAAC,EAAE,IAAI,CAAC;EACR,OAAOuB,YAAY;AACrB;AACA;AACA;AACA;AACA,SAASwB,iBAAiBA,CAACjC,SAAS,EAAEQ,SAAS,EAAEwB,GAAG,EAAEL,OAAO,EAAEQ,eAAe,EAAE;EAC9E;EACA,IAAI,CAACR,OAAO,EAAE;IACZ,OAAOK,GAAG;EACZ;EACA,IAAII,UAAU,GAAGpC,SAAS,CAACE,GAAG,CAAC,YAAY,CAAC;EAC5C,IAAIgB,GAAG,GAAGiB,eAAe,CAACxB,MAAM;EAChC,IAAI0B,WAAW,GAAGnB,GAAG;EACrB;EACA,KAAK,IAAID,CAAC,GAAGC,GAAG,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACjC,IAAIqB,KAAK,GAAGH,eAAe,CAACR,OAAO,KAAK,KAAK,GAAGT,GAAG,GAAGD,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,CAACjC,QAAQ,CAAC,CAAC;IAC3E,IAAIsD,KAAK,GAAGN,GAAG,GAAGxB,SAAS,GAAG4B,UAAU,EAAE;MACxCC,WAAW,GAAGpB,CAAC;MACfe,GAAG,IAAIM,KAAK;IACd;EACF;EACAX,OAAO,KAAK,KAAK,GAAGQ,eAAe,CAACI,MAAM,CAAC,CAAC,EAAErB,GAAG,GAAGmB,WAAW,CAAC,GAAGF,eAAe,CAACI,MAAM,CAACF,WAAW,EAAEnB,GAAG,GAAGmB,WAAW,CAAC;EACzH,OAAOL,GAAG;AACZ;AACA;AACA;AACA;AACA,SAAShE,IAAIA,CAACyC,YAAY,EAAEkB,OAAO,EAAE;EACnC,IAAIA,OAAO,EAAE;IACXlB,YAAY,CAACzC,IAAI,CAAC,UAAUwE,CAAC,EAAEC,CAAC,EAAE;MAChC,IAAIC,IAAI,GAAGf,OAAO,KAAK,KAAK,GAAGa,CAAC,CAACxD,QAAQ,CAAC,CAAC,GAAGyD,CAAC,CAACzD,QAAQ,CAAC,CAAC,GAAGyD,CAAC,CAACzD,QAAQ,CAAC,CAAC,GAAGwD,CAAC,CAACxD,QAAQ,CAAC,CAAC;MACxF,OAAO0D,IAAI,KAAK,CAAC,GAAGf,OAAO,KAAK,KAAK,GAAGa,CAAC,CAACG,SAAS,GAAGF,CAAC,CAACE,SAAS,GAAGF,CAAC,CAACE,SAAS,GAAGH,CAAC,CAACG,SAAS,GAAGD,IAAI;IACtG,CAAC,CAAC;EACJ;EACA,OAAOjC,YAAY;AACrB;AACA;AACA;AACA;AACA,SAASsB,SAASA,CAAC/B,SAAS,EAAE0B,QAAQ,EAAEC,OAAO,EAAE;EAC/C;EACA,IAAIK,GAAG,GAAG,CAAC;EACX,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGQ,QAAQ,CAACf,MAAM,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IACnDe,GAAG,IAAIN,QAAQ,CAACT,CAAC,CAAC,CAACjC,QAAQ,CAAC,CAAC;EAC/B;EACA;EACA;EACA;EACA;EACA,IAAI4D,SAAS,GAAG5C,SAAS,CAACE,GAAG,CAAC,iBAAiB,CAAC;EAChD,IAAIhB,UAAU;EACd;EACA,IAAI,CAACwC,QAAQ,IAAI,CAACA,QAAQ,CAACf,MAAM,EAAE;IACjCzB,UAAU,GAAG,CAAC2D,GAAG,EAAEA,GAAG,CAAC;EACzB,CAAC,MAAM,IAAID,SAAS,KAAK,OAAO,IAAIjB,OAAO,EAAE;IAC3CzC,UAAU,GAAG,CAACwC,QAAQ,CAACA,QAAQ,CAACf,MAAM,GAAG,CAAC,CAAC,CAAC3B,QAAQ,CAAC,CAAC,EAAE0C,QAAQ,CAAC,CAAC,CAAC,CAAC1C,QAAQ,CAAC,CAAC,CAAC;IAC/E2C,OAAO,KAAK,KAAK,IAAIzC,UAAU,CAAC4D,OAAO,CAAC,CAAC;EAC3C;EACA;EAAA,KACK;IACH5D,UAAU,GAAG,CAAC6B,QAAQ,EAAE,CAACA,QAAQ,CAAC;IAClCrF,IAAI,CAACgG,QAAQ,EAAE,UAAUP,KAAK,EAAE;MAC9B,IAAImB,KAAK,GAAGnB,KAAK,CAACnC,QAAQ,CAAC4D,SAAS,CAAC;MACrCN,KAAK,GAAGpD,UAAU,CAAC,CAAC,CAAC,KAAKA,UAAU,CAAC,CAAC,CAAC,GAAGoD,KAAK,CAAC;MAChDA,KAAK,GAAGpD,UAAU,CAAC,CAAC,CAAC,KAAKA,UAAU,CAAC,CAAC,CAAC,GAAGoD,KAAK,CAAC;IAClD,CAAC,CAAC;EACJ;EACA,OAAO;IACLN,GAAG,EAAEA,GAAG;IACR9C,UAAU,EAAEA;EACd,CAAC;AACH;AACA;AACA;AACA;AACA;AACA,SAASoC,KAAKA,CAACN,GAAG,EAAEH,cAAc,EAAEkC,KAAK,EAAE;EACzC,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,OAAO,GAAGlC,QAAQ;EACtB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAExC,IAAI,GAAG,KAAK,CAAC,EAAEyC,GAAG,GAAGF,GAAG,CAACL,MAAM,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC7DxC,IAAI,GAAGuC,GAAG,CAACC,CAAC,CAAC,CAACrC,SAAS,CAAC,CAAC,CAACH,IAAI;IAC9B,IAAIA,IAAI,EAAE;MACRA,IAAI,GAAGwE,OAAO,KAAKA,OAAO,GAAGxE,IAAI,CAAC;MAClCA,IAAI,GAAGuE,OAAO,KAAKA,OAAO,GAAGvE,IAAI,CAAC;IACpC;EACF;EACA,IAAIyE,UAAU,GAAGlC,GAAG,CAACvC,IAAI,GAAGuC,GAAG,CAACvC,IAAI;EACpC,IAAI0E,CAAC,GAAGtC,cAAc,GAAGA,cAAc,GAAGkC,KAAK;EAC/C,OAAOG,UAAU,GAAG/H,OAAO,CAACgI,CAAC,GAAGH,OAAO,GAAGE,UAAU,EAAEA,UAAU,IAAIC,CAAC,GAAGF,OAAO,CAAC,CAAC,GAAGlC,QAAQ;AAC9F;AACA;AACA;AACA;AACA,SAASS,QAAQA,CAACR,GAAG,EAAEH,cAAc,EAAED,IAAI,EAAET,YAAY,EAAEiD,KAAK,EAAE;EAChE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,SAAS,GAAGxC,cAAc,KAAKD,IAAI,CAAC9D,KAAK,GAAG,CAAC,GAAG,CAAC;EACrD,IAAIwG,SAAS,GAAG,CAAC,GAAGD,SAAS;EAC7B,IAAIE,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EACnB,IAAIC,EAAE,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;EAC5B,IAAIC,IAAI,GAAG7C,IAAI,CAAC2C,EAAE,CAACF,SAAS,CAAC,CAAC;EAC9B,IAAIK,cAAc,GAAG7C,cAAc,GAAGG,GAAG,CAACvC,IAAI,GAAGoC,cAAc,GAAG,CAAC;EACnE,IAAIuC,KAAK,IAAIM,cAAc,GAAG9C,IAAI,CAAC4C,EAAE,CAACF,SAAS,CAAC,CAAC,EAAE;IACjDI,cAAc,GAAG9C,IAAI,CAAC4C,EAAE,CAACF,SAAS,CAAC,CAAC,CAAC,CAAC;EACxC;EACA,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAE0C,MAAM,GAAG3C,GAAG,CAACL,MAAM,EAAEM,CAAC,GAAG0C,MAAM,EAAE1C,CAAC,EAAE,EAAE;IACpD,IAAIpC,IAAI,GAAGmC,GAAG,CAACC,CAAC,CAAC;IACjB,IAAI2C,UAAU,GAAG,CAAC,CAAC;IACnB,IAAIC,IAAI,GAAGH,cAAc,GAAG7E,IAAI,CAACD,SAAS,CAAC,CAAC,CAACH,IAAI,GAAGiF,cAAc,GAAG,CAAC;IACtE,IAAII,GAAG,GAAGF,UAAU,CAACJ,EAAE,CAACF,SAAS,CAAC,CAAC,GAAGnI,OAAO,CAACuI,cAAc,GAAG,CAAC,GAAGvD,YAAY,EAAE,CAAC,CAAC;IACnF;IACA,IAAI4D,MAAM,GAAGnD,IAAI,CAAC2C,EAAE,CAACF,SAAS,CAAC,CAAC,GAAGzC,IAAI,CAAC4C,EAAE,CAACH,SAAS,CAAC,CAAC,GAAGI,IAAI;IAC7D,IAAIO,KAAK,GAAG/C,CAAC,KAAK0C,MAAM,GAAG,CAAC,IAAII,MAAM,GAAGF,IAAI,GAAGE,MAAM,GAAGF,IAAI;IAC7D,IAAII,GAAG,GAAGL,UAAU,CAACJ,EAAE,CAACH,SAAS,CAAC,CAAC,GAAGlI,OAAO,CAAC6I,KAAK,GAAG,CAAC,GAAG7D,YAAY,EAAE,CAAC,CAAC;IAC1EyD,UAAU,CAACL,EAAE,CAACD,SAAS,CAAC,CAAC,GAAG1C,IAAI,CAAC2C,EAAE,CAACD,SAAS,CAAC,CAAC,GAAGhI,OAAO,CAAC6E,YAAY,EAAE2D,GAAG,GAAG,CAAC,CAAC;IAChFF,UAAU,CAACL,EAAE,CAACF,SAAS,CAAC,CAAC,GAAGI,IAAI,GAAGnI,OAAO,CAAC6E,YAAY,EAAE8D,GAAG,GAAG,CAAC,CAAC;IACjER,IAAI,IAAIO,KAAK;IACbnF,IAAI,CAACH,SAAS,CAACkF,UAAU,EAAE,IAAI,CAAC;EAClC;EACAhD,IAAI,CAAC2C,EAAE,CAACD,SAAS,CAAC,CAAC,IAAII,cAAc;EACrC9C,IAAI,CAAC4C,EAAE,CAACF,SAAS,CAAC,CAAC,IAAII,cAAc;AACvC;AACA;AACA,SAAS5F,gBAAgBA,CAAC7B,WAAW,EAAEqB,UAAU,EAAEG,QAAQ,EAAER,cAAc,EAAEC,eAAe,EAAE;EAC5F;EACA;EACA,IAAIgH,QAAQ,GAAG,CAAC5G,UAAU,IAAI,CAAC,CAAC,EAAEuB,IAAI;EACtC,IAAIsF,WAAW,GAAG,CAAClH,cAAc,EAAEC,eAAe,CAAC;EACnD,IAAI,CAACgH,QAAQ,IAAIA,QAAQ,KAAKzG,QAAQ,EAAE;IACtC,OAAO0G,WAAW;EACpB;EACA,IAAIC,MAAM;EACV,IAAIC,QAAQ,GAAGpH,cAAc,GAAGC,eAAe;EAC/C,IAAIuB,IAAI,GAAG4F,QAAQ,GAAGpI,WAAW,CAACS,MAAM,CAAC4H,eAAe;EACxD,OAAOF,MAAM,GAAGF,QAAQ,CAACK,UAAU,EAAE;IACnC;IACA,IAAIvC,GAAG,GAAG,CAAC;IACX,IAAIwC,QAAQ,GAAGJ,MAAM,CAAC1C,QAAQ;IAC9B,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGsD,QAAQ,CAAC7D,MAAM,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACnDe,GAAG,IAAIwC,QAAQ,CAACvD,CAAC,CAAC,CAACjC,QAAQ,CAAC,CAAC;IAC/B;IACA,IAAIyF,aAAa,GAAGP,QAAQ,CAAClF,QAAQ,CAAC,CAAC;IACvC,IAAIyF,aAAa,KAAK,CAAC,EAAE;MACvB,OAAON,WAAW;IACpB;IACA1F,IAAI,IAAIuD,GAAG,GAAGyC,aAAa;IAC3B;IACA,IAAIC,WAAW,GAAGN,MAAM,CAACnE,QAAQ,CAAC,CAAC;IACnC,IAAId,WAAW,GAAGuF,WAAW,CAACxE,GAAG,CAACvE,iBAAiB,CAAC;IACpD,IAAIyD,WAAW,GAAGhE,IAAI,CAACC,GAAG,CAAC8D,WAAW,EAAEkB,mBAAmB,CAACqE,WAAW,CAAC,CAAC;IACzEjG,IAAI,IAAI,CAAC,GAAGU,WAAW,GAAGA,WAAW,GAAG,CAAC,CAAC,GAAGA,WAAW,GAAGC,WAAW,IAAIhE,IAAI,CAACuJ,GAAG,CAAClG,IAAI,EAAE,GAAG,CAAC;IAC7FA,IAAI,GAAGzD,gBAAgB,KAAKyD,IAAI,GAAGzD,gBAAgB,CAAC;IACpDkJ,QAAQ,GAAGE,MAAM;EACnB;EACA3F,IAAI,GAAG4F,QAAQ,KAAK5F,IAAI,GAAG4F,QAAQ,CAAC;EACpC,IAAIO,KAAK,GAAGxJ,IAAI,CAACuJ,GAAG,CAAClG,IAAI,GAAG4F,QAAQ,EAAE,GAAG,CAAC;EAC1C,OAAO,CAACpH,cAAc,GAAG2H,KAAK,EAAE1H,eAAe,GAAG0H,KAAK,CAAC;AAC1D;AACA;AACA,SAASnF,qBAAqBA,CAAC9C,UAAU,EAAEa,QAAQ,EAAEF,UAAU,EAAE;EAC/D,IAAIE,QAAQ,EAAE;IACZ,OAAO;MACLe,CAAC,EAAEf,QAAQ,CAACe,CAAC;MACbC,CAAC,EAAEhB,QAAQ,CAACgB;IACd,CAAC;EACH;EACA,IAAIqG,eAAe,GAAG;IACpBtG,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC;EACD,IAAI,CAAClB,UAAU,EAAE;IACf,OAAOuH,eAAe;EACxB;EACA;EACA;EACA;EACA,IAAIC,UAAU,GAAGxH,UAAU,CAACuB,IAAI;EAChC,IAAI5D,MAAM,GAAG6J,UAAU,CAAClG,SAAS,CAAC,CAAC;EACnC,IAAI,CAAC3D,MAAM,EAAE;IACX,OAAO4J,eAAe;EACxB;EACA;EACA,IAAIE,YAAY,GAAG,CAAC9J,MAAM,CAAC6B,KAAK,GAAG,CAAC,EAAE7B,MAAM,CAAC8B,MAAM,GAAG,CAAC,CAAC;EACxD,IAAI8B,IAAI,GAAGiG,UAAU;EACrB,OAAOjG,IAAI,EAAE;IACX,IAAI+E,UAAU,GAAG/E,IAAI,CAACD,SAAS,CAAC,CAAC;IACjCmG,YAAY,CAAC,CAAC,CAAC,IAAInB,UAAU,CAACrF,CAAC;IAC/BwG,YAAY,CAAC,CAAC,CAAC,IAAInB,UAAU,CAACpF,CAAC;IAC/BK,IAAI,GAAGA,IAAI,CAAC0F,UAAU;EACxB;EACA,OAAO;IACLhG,CAAC,EAAE5B,UAAU,CAACG,KAAK,GAAG,CAAC,GAAGiI,YAAY,CAAC,CAAC,CAAC;IACzCvG,CAAC,EAAE7B,UAAU,CAACI,MAAM,GAAG,CAAC,GAAGgI,YAAY,CAAC,CAAC;EAC3C,CAAC;AACH;AACA;AACA;AACA,SAASpF,QAAQA,CAACd,IAAI,EAAEmG,QAAQ,EAAErH,aAAa,EAAEF,QAAQ,EAAEoC,KAAK,EAAE;EAChE,IAAI+D,UAAU,GAAG/E,IAAI,CAACD,SAAS,CAAC,CAAC;EACjC,IAAIqG,mBAAmB,GAAGtH,aAAa,CAACkC,KAAK,CAAC;EAC9C,IAAIqF,eAAe,GAAGD,mBAAmB,IAAIA,mBAAmB,KAAKpG,IAAI;EACzE,IAAIoG,mBAAmB,IAAI,CAACC,eAAe,IAAIrF,KAAK,KAAKlC,aAAa,CAACgD,MAAM,IAAI9B,IAAI,KAAKpB,QAAQ,EAAE;IAClG;EACF;EACAoB,IAAI,CAACH,SAAS,CAAC;IACb;IACAyG,QAAQ,EAAE,IAAI;IACd;IACA;IACAC,SAAS,EAAE,CAACF,eAAe,IAAI,CAACF,QAAQ,CAACK,SAAS,CAACzB,UAAU,CAAC;IAC9DsB,eAAe,EAAEA;EACnB,CAAC,EAAE,IAAI,CAAC;EACR;EACA,IAAII,aAAa,GAAG,IAAIxK,YAAY,CAACkK,QAAQ,CAACzG,CAAC,GAAGqF,UAAU,CAACrF,CAAC,EAAEyG,QAAQ,CAACxG,CAAC,GAAGoF,UAAU,CAACpF,CAAC,EAAEwG,QAAQ,CAAClI,KAAK,EAAEkI,QAAQ,CAACjI,MAAM,CAAC;EAC3HrB,IAAI,CAACmD,IAAI,CAAC4B,YAAY,IAAI,EAAE,EAAE,UAAUU,KAAK,EAAE;IAC7CxB,QAAQ,CAACwB,KAAK,EAAEmE,aAAa,EAAE3H,aAAa,EAAEF,QAAQ,EAAEoC,KAAK,GAAG,CAAC,CAAC;EACpE,CAAC,CAAC;AACJ;AACA,SAASQ,mBAAmBA,CAACkF,KAAK,EAAE;EAClC,OAAOA,KAAK,CAACrF,GAAG,CAACrE,qBAAqB,CAAC,GAAG0J,KAAK,CAACrF,GAAG,CAACpE,uBAAuB,CAAC,GAAG,CAAC;AAClF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}