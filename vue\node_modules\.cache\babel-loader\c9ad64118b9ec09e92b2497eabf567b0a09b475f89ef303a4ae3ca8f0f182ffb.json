{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n// 导入各个功能组件\nimport MyBlogsComponent from './MyBlogs.vue';\nimport ComplaintComponent from './Complaint.vue';\nimport ResponseComponent from './Response.vue';\nimport LeavemessComponent from './Leavemess.vue';\nimport ReplyLeavemessComponent from './ReplyLeavemess.vue';\nexport default {\n  name: 'PersonCenter',\n  components: {\n    MyBlogsComponent,\n    ComplaintComponent,\n    ResponseComponent,\n    LeavemessComponent,\n    ReplyLeavemessComponent\n  },\n  data() {\n    const validatePassword = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请确认密码'));\n      } else if (value !== this.user.newPassword) {\n        callback(new Error('确认密码错误'));\n      } else {\n        callback();\n      }\n    };\n    return {\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      activeMenu: 'profile',\n      // 默认显示个人信息\n      dialogVisible: false,\n      rules: {\n        password: [{\n          required: true,\n          message: '请输入原始密码',\n          trigger: 'blur'\n        }],\n        newPassword: [{\n          required: true,\n          message: '请输入新密码',\n          trigger: 'blur'\n        }],\n        confirmPassword: [{\n          validator: validatePassword,\n          required: true,\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  methods: {\n    handleMenuSelect(index) {\n      this.activeMenu = index;\n    },\n    updateUser() {\n      this.user = JSON.parse(localStorage.getItem('xm-user') || '{}');\n      this.$emit('update:user');\n    },\n    update() {\n      // 保存当前的用户信息到数据库\n      this.$request.put('/user/update', this.user).then(res => {\n        if (res.code === '200') {\n          // 成功更新\n          this.$message.success('保存成功');\n          // 更新浏览器缓存里的用户信息\n          localStorage.setItem('xm-user', JSON.stringify(this.user));\n          // 触发父级的数据更新\n          this.$emit('update:user');\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    handleAvatarSuccess(response, file, fileList) {\n      // 把user的头像属性换成上传的图片的链接\n      this.$set(this.user, 'avatar', response.data);\n    },\n    // 修改密码\n    updatePassword() {\n      this.dialogVisible = true;\n    },\n    save() {\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          this.$request.put('/updatePassword', this.user).then(res => {\n            if (res.code === '200') {\n              // 成功更新\n              this.$message.success('修改密码成功');\n              this.$router.push('/login');\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["MyBlogsComponent", "ComplaintComponent", "ResponseComponent", "LeavemessComponent", "ReplyLeavemessComponent", "name", "components", "data", "validatePassword", "rule", "value", "callback", "Error", "user", "newPassword", "JSON", "parse", "localStorage", "getItem", "activeMenu", "dialogVisible", "rules", "password", "required", "message", "trigger", "confirmPassword", "validator", "methods", "handleMenuSelect", "index", "updateUser", "$emit", "update", "$request", "put", "then", "res", "code", "$message", "success", "setItem", "stringify", "error", "msg", "handleAvatarSuccess", "response", "file", "fileList", "$set", "updatePassword", "save", "$refs", "formRef", "validate", "valid", "$router", "push"], "sources": ["src/views/front/Person.vue"], "sourcesContent": ["<template>\r\n  <div class=\"person-container\">\r\n    <!-- 侧边栏 -->\r\n    <div class=\"person-sidebar\">\r\n      <div class=\"sidebar-header\">\r\n        <h3>个人中心</h3>\r\n      </div>\r\n      <el-menu\r\n        :default-active=\"activeMenu\"\r\n        class=\"sidebar-menu\"\r\n        @select=\"handleMenuSelect\">\r\n        <el-menu-item index=\"profile\">\r\n          <i class=\"el-icon-user\"></i>\r\n          <span>个人信息</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"myBlogs\">\r\n          <i class=\"el-icon-edit\"></i>\r\n          <span>我要发帖</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"complaint\">\r\n          <i class=\"el-icon-warning\"></i>\r\n          <span>填写点餐投诉</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"response\">\r\n          <i class=\"el-icon-message\"></i>\r\n          <span>点餐投诉反馈</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"leavemess\">\r\n          <i class=\"el-icon-chat-line-square\"></i>\r\n          <span>咨询留言</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"replyLeavemess\">\r\n          <i class=\"el-icon-chat-dot-square\"></i>\r\n          <span>留言回复</span>\r\n        </el-menu-item>\r\n      </el-menu>\r\n    </div>\r\n\r\n    <!-- 主内容区域 -->\r\n    <div class=\"person-content\">\r\n      <!-- 个人信息页面 -->\r\n      <div v-if=\"activeMenu === 'profile'\" class=\"content-section\">\r\n        <div class=\"section-header\">\r\n          <h2>个人信息</h2>\r\n          <el-button type=\"primary\" @click=\"updatePassword\">修改密码</el-button>\r\n        </div>\r\n        <el-card class=\"profile-card\">\r\n          <el-form :model=\"user\" label-width=\"80px\" style=\"padding-right: 20px\">\r\n            <div style=\"margin: 15px; text-align: center\">\r\n              <el-upload\r\n                  class=\"avatar-uploader\"\r\n                  :action=\"$baseUrl + '/files/upload'\"\r\n                  :show-file-list=\"false\"\r\n                  :on-success=\"handleAvatarSuccess\"\r\n              >\r\n                <img v-if=\"user.avatar\" :src=\"user.avatar\" class=\"avatar\" />\r\n                <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\r\n              </el-upload>\r\n            </div>\r\n            <el-form-item label=\"用户名\" prop=\"username\">\r\n              <el-input v-model=\"user.username\" placeholder=\"用户名\" disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"昵称\" prop=\"name\">\r\n              <el-input v-model=\"user.name\" placeholder=\"昵称\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"电话\" prop=\"phone\">\r\n              <el-input v-model=\"user.phone\" placeholder=\"电话\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"邮箱\" prop=\"email\">\r\n              <el-input v-model=\"user.email\" placeholder=\"邮箱\"></el-input>\r\n            </el-form-item>\r\n            <div style=\"text-align: center; margin-bottom: 20px\">\r\n              <el-button type=\"primary\" @click=\"update\">保 存</el-button>\r\n            </div>\r\n          </el-form>\r\n        </el-card>\r\n      </div>\r\n\r\n      <!-- 我要发帖页面 -->\r\n      <div v-else-if=\"activeMenu === 'myBlogs'\" class=\"content-section\">\r\n        <div class=\"section-header\">\r\n          <h2>我要发帖</h2>\r\n        </div>\r\n        <MyBlogsComponent @update:user=\"updateUser\" />\r\n      </div>\r\n\r\n      <!-- 填写点餐投诉页面 -->\r\n      <div v-else-if=\"activeMenu === 'complaint'\" class=\"content-section\">\r\n        <div class=\"section-header\">\r\n          <h2>填写点餐投诉</h2>\r\n        </div>\r\n        <ComplaintComponent @update:user=\"updateUser\" />\r\n      </div>\r\n\r\n      <!-- 点餐投诉反馈页面 -->\r\n      <div v-else-if=\"activeMenu === 'response'\" class=\"content-section\">\r\n        <div class=\"section-header\">\r\n          <h2>点餐投诉反馈</h2>\r\n        </div>\r\n        <ResponseComponent @update:user=\"updateUser\" />\r\n      </div>\r\n\r\n      <!-- 咨询留言页面 -->\r\n      <div v-else-if=\"activeMenu === 'leavemess'\" class=\"content-section\">\r\n        <div class=\"section-header\">\r\n          <h2>咨询留言</h2>\r\n        </div>\r\n        <LeavemessComponent @update:user=\"updateUser\" />\r\n      </div>\r\n\r\n      <!-- 留言回复页面 -->\r\n      <div v-else-if=\"activeMenu === 'replyLeavemess'\" class=\"content-section\">\r\n        <div class=\"section-header\">\r\n          <h2>留言回复</h2>\r\n        </div>\r\n        <ReplyLeavemessComponent @update:user=\"updateUser\" />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <el-dialog title=\"修改密码\" :visible.sync=\"dialogVisible\" width=\"30%\" :close-on-click-modal=\"false\" destroy-on-close>\r\n      <el-form :model=\"user\" label-width=\"80px\" style=\"padding-right: 20px\" :rules=\"rules\" ref=\"formRef\">\r\n        <el-form-item label=\"原始密码\" prop=\"password\">\r\n          <el-input show-password v-model=\"user.password\" placeholder=\"原始密码\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"新密码\" prop=\"newPassword\">\r\n          <el-input show-password v-model=\"user.newPassword\" placeholder=\"新密码\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\r\n          <el-input show-password v-model=\"user.confirmPassword\" placeholder=\"确认密码\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 导入各个功能组件\r\nimport MyBlogsComponent from './MyBlogs.vue'\r\nimport ComplaintComponent from './Complaint.vue'\r\nimport ResponseComponent from './Response.vue'\r\nimport LeavemessComponent from './Leavemess.vue'\r\nimport ReplyLeavemessComponent from './ReplyLeavemess.vue'\r\n\r\nexport default {\r\n  name: 'PersonCenter',\r\n  components: {\r\n    MyBlogsComponent,\r\n    ComplaintComponent,\r\n    ResponseComponent,\r\n    LeavemessComponent,\r\n    ReplyLeavemessComponent\r\n  },\r\n  data() {\r\n    const validatePassword = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请确认密码'))\r\n      } else if (value !== this.user.newPassword) {\r\n        callback(new Error('确认密码错误'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n    return {\r\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n      activeMenu: 'profile', // 默认显示个人信息\r\n      dialogVisible: false,\r\n      rules: {\r\n        password: [\r\n          { required: true, message: '请输入原始密码', trigger: 'blur' },\r\n        ],\r\n        newPassword: [\r\n          { required: true, message: '请输入新密码', trigger: 'blur' },\r\n        ],\r\n        confirmPassword: [\r\n          { validator: validatePassword, required: true, trigger: 'blur' },\r\n        ],\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    handleMenuSelect(index) {\r\n      this.activeMenu = index\r\n    },\r\n    updateUser() {\r\n      this.user = JSON.parse(localStorage.getItem('xm-user') || '{}')\r\n      this.$emit('update:user')\r\n    },\r\n    update() {\r\n      // 保存当前的用户信息到数据库\r\n      this.$request.put('/user/update', this.user).then(res => {\r\n        if (res.code === '200') {\r\n          // 成功更新\r\n          this.$message.success('保存成功')\r\n          // 更新浏览器缓存里的用户信息\r\n          localStorage.setItem('xm-user', JSON.stringify(this.user))\r\n          // 触发父级的数据更新\r\n          this.$emit('update:user')\r\n        } else {\r\n          this.$message.error(res.msg)\r\n        }\r\n      })\r\n    },\r\n    handleAvatarSuccess(response, file, fileList) {\r\n      // 把user的头像属性换成上传的图片的链接\r\n      this.$set(this.user, 'avatar', response.data)\r\n    },\r\n    // 修改密码\r\n    updatePassword() {\r\n      this.dialogVisible = true\r\n    },\r\n    save() {\r\n      this.$refs.formRef.validate((valid) => {\r\n        if (valid) {\r\n          this.$request.put('/updatePassword', this.user).then(res => {\r\n            if (res.code === '200') {\r\n              // 成功更新\r\n              this.$message.success('修改密码成功')\r\n              this.$router.push('/login')\r\n            } else {\r\n              this.$message.error(res.msg)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.person-container {\r\n  display: flex;\r\n  min-height: calc(100vh - 60px);\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.person-sidebar {\r\n  width: 250px;\r\n  background-color: white;\r\n  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);\r\n  position: fixed;\r\n  height: calc(100vh - 60px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-header {\r\n  padding: 20px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n  text-align: center;\r\n}\r\n\r\n.sidebar-header h3 {\r\n  margin: 0;\r\n  color: #333;\r\n  font-size: 18px;\r\n}\r\n\r\n.sidebar-menu {\r\n  border: none;\r\n}\r\n\r\n.sidebar-menu .el-menu-item {\r\n  height: 50px;\r\n  line-height: 50px;\r\n  margin: 0 10px;\r\n  border-radius: 8px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.sidebar-menu .el-menu-item:hover {\r\n  background-color: #f0f2ff;\r\n  color: #667eea;\r\n}\r\n\r\n.sidebar-menu .el-menu-item.is-active {\r\n  background-color: #667eea;\r\n  color: white;\r\n}\r\n\r\n.person-content {\r\n  flex: 1;\r\n  margin-left: 250px;\r\n  padding: 20px;\r\n}\r\n\r\n.content-section {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.section-header {\r\n  padding: 20px 30px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.section-header h2 {\r\n  margin: 0;\r\n  color: #333;\r\n  font-size: 20px;\r\n}\r\n\r\n.profile-card {\r\n  margin: 20px;\r\n  border: none;\r\n  box-shadow: none;\r\n}\r\n\r\n/* 头像上传样式 */\r\n/deep/.el-form-item__label {\r\n  font-weight: bold;\r\n}\r\n/deep/.el-upload {\r\n  border-radius: 50%;\r\n}\r\n/deep/.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  border-radius: 50%;\r\n}\r\n/deep/.avatar-uploader .el-upload:hover {\r\n  border-color: #409EFF;\r\n}\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 120px;\r\n  height: 120px;\r\n  line-height: 120px;\r\n  text-align: center;\r\n  border-radius: 50%;\r\n}\r\n.avatar {\r\n  width: 120px;\r\n  height: 120px;\r\n  display: block;\r\n  border-radius: 50%;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .person-sidebar {\r\n    width: 200px;\r\n  }\r\n  \r\n  .person-content {\r\n    margin-left: 200px;\r\n    padding: 10px;\r\n  }\r\n}\r\n</style>"], "mappings": ";AA6IA;AACA,OAAAA,gBAAA;AACA,OAAAC,kBAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,kBAAA;AACA,OAAAC,uBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,gBAAA;IACAC,kBAAA;IACAC,iBAAA;IACAC,kBAAA;IACAC;EACA;EACAG,KAAA;IACA,MAAAC,gBAAA,GAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAD,KAAA;QACAC,QAAA,KAAAC,KAAA;MACA,WAAAF,KAAA,UAAAG,IAAA,CAAAC,WAAA;QACAH,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IACA;MACAE,IAAA,EAAAE,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,UAAA;MAAA;MACAC,aAAA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,WAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,eAAA,GACA;UAAAC,SAAA,EAAAnB,gBAAA;UAAAe,QAAA;UAAAE,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA;IACAC,iBAAAC,KAAA;MACA,KAAAX,UAAA,GAAAW,KAAA;IACA;IACAC,WAAA;MACA,KAAAlB,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACA,KAAAc,KAAA;IACA;IACAC,OAAA;MACA;MACA,KAAAC,QAAA,CAAAC,GAAA,sBAAAtB,IAAA,EAAAuB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA;UACA,KAAAC,QAAA,CAAAC,OAAA;UACA;UACAvB,YAAA,CAAAwB,OAAA,YAAA1B,IAAA,CAAA2B,SAAA,MAAA7B,IAAA;UACA;UACA,KAAAmB,KAAA;QACA;UACA,KAAAO,QAAA,CAAAI,KAAA,CAAAN,GAAA,CAAAO,GAAA;QACA;MACA;IACA;IACAC,oBAAAC,QAAA,EAAAC,IAAA,EAAAC,QAAA;MACA;MACA,KAAAC,IAAA,MAAApC,IAAA,YAAAiC,QAAA,CAAAvC,IAAA;IACA;IACA;IACA2C,eAAA;MACA,KAAA9B,aAAA;IACA;IACA+B,KAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAArB,QAAA,CAAAC,GAAA,yBAAAtB,IAAA,EAAAuB,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA,KAAAgB,OAAA,CAAAC,IAAA;YACA;cACA,KAAAlB,QAAA,CAAAI,KAAA,CAAAN,GAAA,CAAAO,GAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}