{"ast": null, "code": "export default {\n  name: \"Freemovies\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前页码\n      pageSize: 12,\n      // 每页显示个数\n      total: 0,\n      // 总记录数\n\n      videoVisible: false,\n      // 是否显示播放视频的弹窗\n      currentVideoUrl: null // 当前要播放的视频地址\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    // 播放视频\n    playVideo(url) {\n      if (!url) {\n        this.$message.warning('视频地址不存在');\n        return;\n      }\n      this.currentVideoUrl = url;\n      this.videoVisible = true;\n    },\n    // 视频弹窗关闭\n    handleVideoClose() {\n      this.currentVideoUrl = null;\n    },\n    // 查询数据\n    load(pageNum) {\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get(\"/freemovies/selectPage\", {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize\n        }\n      }).then(res => {\n        if (res.code === \"200\") {\n          this.tableData = res.data?.list || [];\n          this.total = res.data?.total || 0;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    // 分页组件切换页码\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "videoVisible", "currentVideoUrl", "created", "load", "methods", "playVideo", "url", "$message", "warning", "handleVideoClose", "$request", "get", "params", "then", "res", "code", "list", "error", "msg", "handleCurrentChange"], "sources": ["src/views/front/Freemovies.vue"], "sourcesContent": ["<template>\r\n    <div class=\"freemovies-container\">\r\n        <!-- 推荐列表 -->\r\n        <div class=\"content-section\">\r\n            <div class=\"recommendations-list\">\r\n                <div v-if=\"tableData.length === 0\" class=\"empty-state\">\r\n                    <i class=\"el-icon-star-on\"></i>\r\n                    <h3>暂无推荐内容</h3>\r\n                    <p>还没有美食推荐，敬请期待</p>\r\n                </div>\r\n                \r\n                <div v-else class=\"recommendations-grid\">\r\n                    <div \r\n                        v-for=\"item in tableData\" \r\n                        :key=\"item.id\"\r\n                        class=\"recommendation-card\">\r\n                        \r\n                        <div class=\"card-image-container\">\r\n                            <el-image\r\n                                :src=\"item.sfCoverImage\"\r\n                                fit=\"cover\"\r\n                                class=\"card-image\"\r\n                            ></el-image>\r\n                            <div class=\"card-overlay\">\r\n                                <el-button\r\n                                    type=\"primary\"\r\n                                    size=\"small\"\r\n                                    @click=\"playVideo(item.sfVideoUrl)\"\r\n                                    class=\"play-btn\">\r\n                                    <i class=\"el-icon-video-play\"></i>\r\n                                    播放视频\r\n                                </el-button>\r\n                            </div>\r\n                            <div class=\"recommendation-badge\">\r\n                                <i class=\"el-icon-star-on\"></i>\r\n                                推荐\r\n                            </div>\r\n                        </div>\r\n                        \r\n                        <div class=\"card-content\">\r\n                            <h3 class=\"card-title\">{{ item.name }}</h3>\r\n                            <div class=\"card-description\">\r\n                                {{ item.content }}\r\n                            </div>\r\n                            <div class=\"card-actions\">\r\n                                <el-button\r\n                                    type=\"primary\"\r\n                                    size=\"small\"\r\n                                    @click=\"playVideo(item.sfVideoUrl)\"\r\n                                    class=\"action-btn\">\r\n                                    <i class=\"el-icon-video-play\"></i>\r\n                                    观看视频\r\n                                </el-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 分页 -->\r\n            <div class=\"pagination-section\">\r\n                <el-pagination\r\n                    background\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageNum\"\r\n                    :page-size=\"pageSize\"\r\n                    layout=\"prev, pager, next\"\r\n                    :total=\"total\"\r\n                    class=\"custom-pagination\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 播放视频的弹窗 -->\r\n        <el-dialog\r\n            title=\"美食推荐视频\"\r\n            :visible.sync=\"videoVisible\"\r\n            width=\"70%\"\r\n            @close=\"handleVideoClose\"\r\n            append-to-body\r\n            center\r\n            custom-class=\"video-dialog\"\r\n        >\r\n            <div class=\"video-container\">\r\n                <video\r\n                    v-if=\"currentVideoUrl\"\r\n                    :src=\"currentVideoUrl\"\r\n                    controls\r\n                    autoplay\r\n                    class=\"video-player\"\r\n                ></video>\r\n                <div v-else class=\"video-placeholder\">\r\n                    <i class=\"el-icon-video-camera\"></i>\r\n                    <p>视频加载中...</p>\r\n                </div>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Freemovies\",\r\n    data() {\r\n        return {\r\n            tableData: [],    // 所有的数据\r\n            pageNum: 1,       // 当前页码\r\n            pageSize: 12,     // 每页显示个数\r\n            total: 0,         // 总记录数\r\n\r\n            videoVisible: false,  // 是否显示播放视频的弹窗\r\n            currentVideoUrl: null // 当前要播放的视频地址\r\n        };\r\n    },\r\n    created() {\r\n        this.load(1);\r\n    },\r\n    methods: {\r\n        // 播放视频\r\n        playVideo(url) {\r\n            if (!url) {\r\n                this.$message.warning('视频地址不存在');\r\n                return;\r\n            }\r\n            this.currentVideoUrl = url;\r\n            this.videoVisible = true;\r\n        },\r\n\r\n        // 视频弹窗关闭\r\n        handleVideoClose() {\r\n            this.currentVideoUrl = null;\r\n        },\r\n\r\n\r\n\r\n        // 查询数据\r\n        load(pageNum) {\r\n            if (pageNum) this.pageNum = pageNum;\r\n            this.$request\r\n                .get(\"/freemovies/selectPage\", {\r\n                    params: {\r\n                        pageNum: this.pageNum,\r\n                        pageSize: this.pageSize,\r\n    \r\n                    }\r\n                })\r\n                .then(res => {\r\n                    if (res.code === \"200\") {\r\n                        this.tableData = res.data?.list || [];\r\n                        this.total = res.data?.total || 0;\r\n                    } else {\r\n                        this.$message.error(res.msg);\r\n                    }\r\n                });\r\n        },\r\n\r\n        // 分页组件切换页码\r\n        handleCurrentChange(pageNum) {\r\n            this.load(pageNum);\r\n        }\r\n    }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.freemovies-container {\r\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n    min-height: 100vh;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-section {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 40px 20px;\r\n}\r\n\r\n\r\n\r\n/* 推荐列表 */\r\n.recommendations-list {\r\n    margin-bottom: 40px;\r\n}\r\n\r\n.empty-state {\r\n    text-align: center;\r\n    padding: 80px 20px;\r\n    color: #64748b;\r\n}\r\n\r\n.empty-state i {\r\n    font-size: 64px;\r\n    margin-bottom: 16px;\r\n    color: #fbbf24;\r\n}\r\n\r\n.empty-state h3 {\r\n    font-size: 20px;\r\n    margin-bottom: 8px;\r\n    color: #475569;\r\n}\r\n\r\n.empty-state p {\r\n    margin-bottom: 24px;\r\n}\r\n\r\n.recommendations-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n    gap: 24px;\r\n}\r\n\r\n.recommendation-card {\r\n    background: white;\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n    border: 2px solid transparent;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.recommendation-card:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);\r\n    border-color: #3b82f6;\r\n}\r\n\r\n.card-image-container {\r\n    position: relative;\r\n    height: 220px;\r\n    overflow: hidden;\r\n}\r\n\r\n.card-image {\r\n    width: 100%;\r\n    height: 100%;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.recommendation-card:hover .card-image {\r\n    transform: scale(1.05);\r\n}\r\n\r\n.card-overlay {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.6);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    opacity: 0;\r\n    transition: opacity 0.3s ease;\r\n}\r\n\r\n.recommendation-card:hover .card-overlay {\r\n    opacity: 1;\r\n}\r\n\r\n.play-btn {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    border-radius: 25px;\r\n    padding: 8px 20px;\r\n    font-weight: 600;\r\n    transform: scale(0.9);\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.recommendation-card:hover .play-btn {\r\n    transform: scale(1);\r\n}\r\n\r\n.play-btn:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n}\r\n\r\n.recommendation-badge {\r\n    position: absolute;\r\n    top: 12px;\r\n    right: 12px;\r\n    background: #fbbf24;\r\n    color: white;\r\n    padding: 4px 12px;\r\n    border-radius: 12px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n}\r\n\r\n.recommendation-badge i {\r\n    font-size: 14px;\r\n}\r\n\r\n.card-content {\r\n    padding: 24px;\r\n}\r\n\r\n.card-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #1e293b;\r\n    margin-bottom: 12px;\r\n    line-height: 1.4;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n}\r\n\r\n.card-description {\r\n    font-size: 14px;\r\n    color: #64748b;\r\n    line-height: 1.6;\r\n    margin-bottom: 20px;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 3;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n}\r\n\r\n.card-actions {\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    border-radius: 20px;\r\n    padding: 8px 24px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n/* 分页 */\r\n.pagination-section {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 20px 0;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li {\r\n    background: white;\r\n    border: 1px solid #e5e7eb;\r\n    border-radius: 8px;\r\n    margin: 0 4px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li:hover {\r\n    border-color: #3b82f6;\r\n    color: #3b82f6;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li.active {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    color: white;\r\n}\r\n\r\n/* 视频弹窗 */\r\n.video-dialog >>> .el-dialog {\r\n    border-radius: 16px;\r\n}\r\n\r\n.video-dialog >>> .el-dialog__header {\r\n    background: #f8fafc;\r\n    border-bottom: 1px solid #e2e8f0;\r\n    border-radius: 16px 16px 0 0;\r\n}\r\n\r\n.video-container {\r\n    padding: 0;\r\n    border-radius: 12px;\r\n    overflow: hidden;\r\n    background: #000;\r\n}\r\n\r\n.video-player {\r\n    width: 100%;\r\n    height: auto;\r\n    max-height: 70vh;\r\n    border-radius: 12px;\r\n}\r\n\r\n.video-placeholder {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 300px;\r\n    color: #64748b;\r\n}\r\n\r\n.video-placeholder i {\r\n    font-size: 48px;\r\n    margin-bottom: 16px;\r\n    color: #cbd5e1;\r\n}\r\n\r\n.video-placeholder p {\r\n    font-size: 16px;\r\n    margin: 0;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .recommendations-grid {\r\n        grid-template-columns: 1fr;\r\n    }\r\n    \r\n\r\n    \r\n    .card-image-container {\r\n        height: 180px;\r\n    }\r\n    \r\n    .video-dialog {\r\n        width: 95% !important;\r\n    }\r\n}\r\n</style>"], "mappings": "AAqGA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAAA;;MAEAC,YAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACA;IACAC,UAAAC,GAAA;MACA,KAAAA,GAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAP,eAAA,GAAAK,GAAA;MACA,KAAAN,YAAA;IACA;IAEA;IACAS,iBAAA;MACA,KAAAR,eAAA;IACA;IAIA;IACAE,KAAAN,OAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAa,QAAA,CACAC,GAAA;QACAC,MAAA;UACAf,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA;QAEA;MACA,GACAe,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAnB,SAAA,GAAAkB,GAAA,CAAAnB,IAAA,EAAAqB,IAAA;UACA,KAAAjB,KAAA,GAAAe,GAAA,CAAAnB,IAAA,EAAAI,KAAA;QACA;UACA,KAAAQ,QAAA,CAAAU,KAAA,CAAAH,GAAA,CAAAI,GAAA;QACA;MACA;IACA;IAEA;IACAC,oBAAAtB,OAAA;MACA,KAAAM,IAAA,CAAAN,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}