{"ast": null, "code": "import \"core-js/modules/esnext.iterator.map.js\";\nexport default {\n  name: \"BlogsDetails\",\n  data() {\n    return {\n      blogData: {},\n      // 存储博客详情数据\n      comments: [],\n      // 存储评论数据\n      newComment: \"\",\n      // 新评论的内容\n      submitting: false // 提交状态\n    };\n  },\n  created() {\n    const blogId = this.$route.query.id;\n    if (blogId) {\n      this.loadBlogDetail(blogId);\n      this.loadComments(blogId);\n    } else {\n      this.$message.error(\"缺少博客ID参数\");\n      this.$router.go(-1);\n    }\n  },\n  methods: {\n    // 加载博客详情\n    loadBlogDetail(id) {\n      this.$request.get(`/blogs/selectById/${id}`).then(res => {\n        if (res.code === '200') {\n          this.blogData = res.data;\n          // 更新浏览次数\n          this.updateViews(id);\n        } else {\n          this.$message.error(res.msg);\n        }\n      }).catch(err => {\n        this.$message.error(\"加载博客详情失败\");\n        console.error(err);\n      });\n    },\n    // 更新浏览次数\n    updateViews(id) {\n      this.$request.post(`/blogs/updateViews/${id}`).catch(err => {\n        console.error(\"更新浏览次数失败:\", err);\n      });\n    },\n    // 加载评论列表\n    loadComments(blogId) {\n      this.$request.get(`/pinglun/selectAll?bokeid=${blogId}`).then(res => {\n        if (res.code === '200') {\n          this.comments = res.data.map(comment => ({\n            ...comment,\n            isNew: false\n          }));\n        } else {\n          this.$message.error(res.msg);\n        }\n      }).catch(err => {\n        this.$message.error(\"加载评论失败\");\n        console.error(err);\n      });\n    },\n    // 提交评论\n    submitComment() {\n      if (!this.newComment.trim()) {\n        this.$message.error(\"评论内容不能为空\");\n        return;\n      }\n      this.submitting = true;\n      const commentData = {\n        massage: this.newComment,\n        bokeid: this.blogData.id\n      };\n      this.$request.post('/pinglun/add', commentData).then(res => {\n        if (res.code === '200') {\n          this.$message.success(\"评论成功\");\n          // 重新加载评论\n          this.loadComments(this.blogData.id);\n          // 清空输入框\n          this.newComment = \"\";\n        } else {\n          this.$message.error(res.msg);\n        }\n      }).catch(err => {\n        this.$message.error(\"评论提交失败\");\n        console.error(err);\n      }).finally(() => {\n        this.submitting = false;\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "blogData", "comments", "newComment", "submitting", "created", "blogId", "$route", "query", "id", "loadBlogDetail", "loadComments", "$message", "error", "$router", "go", "methods", "$request", "get", "then", "res", "code", "updateViews", "msg", "catch", "err", "console", "post", "map", "comment", "isNew", "submitComment", "trim", "commentData", "massage", "boke<PERSON>", "success", "finally"], "sources": ["src/views/front/BlogsDetails.vue"], "sourcesContent": ["<template>\r\n    <div class=\"blogs-detail-container\">\r\n        <!-- 返回按钮 -->\r\n        <div class=\"back-section\">\r\n            <el-button \r\n                @click=\"$router.go(-1)\" \r\n                icon=\"el-icon-arrow-left\"\r\n                class=\"back-btn\">\r\n                返回列表\r\n            </el-button>\r\n        </div>\r\n\r\n        <!-- 博客详情卡片 -->\r\n        <div class=\"blog-detail-card\">\r\n            <!-- 博客头部 -->\r\n            <div class=\"blog-header\">\r\n                <div class=\"blog-title-section\">\r\n                    <h1 class=\"blog-title\">{{ blogData.title }}</h1>\r\n                    <div class=\"blog-meta\">\r\n                        <div class=\"meta-item\">\r\n                            <i class=\"el-icon-folder meta-icon\"></i>\r\n                            <span>{{ blogData.categoryname }}</span>\r\n                        </div>\r\n                        <div class=\"meta-item\" v-if=\"blogData.tags\">\r\n                            <i class=\"el-icon-price-tag meta-icon\"></i>\r\n                            <div class=\"tags-container\">\r\n                                <el-tag \r\n                                    v-for=\"tag in blogData.tags.split(',')\" \r\n                                    :key=\"tag\"\r\n                                    size=\"small\"\r\n                                    class=\"tag-item\">\r\n                                    {{ tag }}\r\n                                </el-tag>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"meta-item\">\r\n                            <i class=\"el-icon-time meta-icon\"></i>\r\n                            <span>{{ blogData.createdat }}</span>\r\n                        </div>\r\n                        <div class=\"meta-item\">\r\n                            <i class=\"el-icon-view meta-icon\"></i>\r\n                            <span>{{ blogData.views }} 次浏览</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 博客内容 -->\r\n            <div class=\"blog-content\">\r\n                <div class=\"blog-image-container\" v-if=\"blogData.blogimg\">\r\n                    <el-image\r\n                        :src=\"blogData.blogimg\"\r\n                        fit=\"cover\"\r\n                        class=\"blog-image\"\r\n                        :preview-src-list=\"[blogData.blogimg]\"\r\n                    ></el-image>\r\n                </div>\r\n                <div class=\"blog-text-content\">\r\n                    <p class=\"content-text\">{{ blogData.content }}</p>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 评论区 -->\r\n        <div class=\"comments-section\">\r\n            <div class=\"comments-header\">\r\n                <h3 class=\"comments-title\">\r\n                    <i class=\"el-icon-chat-dot-round\"></i>\r\n                    评论区 ({{ comments.length }})\r\n                </h3>\r\n            </div>\r\n\r\n            <!-- 评论输入框 -->\r\n            <div class=\"comment-form-card\">\r\n                <div class=\"comment-form\">\r\n                    <el-input\r\n                        type=\"textarea\"\r\n                        v-model=\"newComment\"\r\n                        placeholder=\"写下您的评论...\"\r\n                        :rows=\"4\"\r\n                        class=\"comment-input\"\r\n                        maxlength=\"500\"\r\n                        show-word-limit\r\n                    ></el-input>\r\n                    <div class=\"comment-actions\">\r\n                        <el-button \r\n                            type=\"primary\"\r\n                            @click=\"submitComment\"\r\n                            :loading=\"submitting\"\r\n                            class=\"submit-btn\">\r\n                            <i class=\"el-icon-s-promotion\"></i>\r\n                            发表评论\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 评论列表 -->\r\n            <div class=\"comments-list\">\r\n                <div v-if=\"comments.length === 0\" class=\"empty-comments\">\r\n                    <i class=\"el-icon-chat-dot-round\"></i>\r\n                    <h4>暂无评论</h4>\r\n                    <p>快来发表第一条评论吧！</p>\r\n                </div>\r\n\r\n                <div v-else class=\"comments-container\">\r\n                    <div \r\n                        v-for=\"(comment, index) in comments\" \r\n                        :key=\"comment.id\" \r\n                        class=\"comment-item\"\r\n                        :class=\"{ 'comment-highlight': comment.isNew }\">\r\n                        \r\n                        <div class=\"comment-avatar\">\r\n                            <el-avatar \r\n                                :size=\"40\"\r\n                                :src=\"comment.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'\"\r\n                                class=\"avatar\">\r\n                                <i class=\"el-icon-user-solid\"></i>\r\n                            </el-avatar>\r\n                        </div>\r\n                        \r\n                        <div class=\"comment-content\">\r\n                            <div class=\"comment-header\">\r\n                                <div class=\"comment-info\">\r\n                                    <span class=\"comment-username\">{{ comment.yonghuname }}</span>\r\n                                    <span class=\"comment-time\">{{ comment.crearatime }}</span>\r\n                                </div>\r\n                                <div class=\"comment-number\">#{{ index + 1 }}</div>\r\n                            </div>\r\n                            \r\n                            <div class=\"comment-text\">\r\n                                <p>{{ comment.massage }}</p>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"BlogsDetails\",\r\n    data() {\r\n        return {\r\n            blogData: {},   // 存储博客详情数据\r\n            comments: [],   // 存储评论数据\r\n            newComment: \"\", // 新评论的内容\r\n            submitting: false, // 提交状态\r\n        };\r\n    },\r\n    created() {\r\n        const blogId = this.$route.query.id;\r\n        if (blogId) {\r\n            this.loadBlogDetail(blogId);\r\n            this.loadComments(blogId);\r\n        } else {\r\n            this.$message.error(\"缺少博客ID参数\");\r\n            this.$router.go(-1);\r\n        }\r\n    },\r\n    methods: {\r\n        // 加载博客详情\r\n        loadBlogDetail(id) {\r\n            this.$request.get(`/blogs/selectById/${id}`).then(res => {\r\n                if (res.code === '200') {\r\n                    this.blogData = res.data;\r\n                    // 更新浏览次数\r\n                    this.updateViews(id);\r\n                } else {\r\n                    this.$message.error(res.msg);\r\n                }\r\n            }).catch(err => {\r\n                this.$message.error(\"加载博客详情失败\");\r\n                console.error(err);\r\n            });\r\n        },\r\n        \r\n        // 更新浏览次数\r\n        updateViews(id) {\r\n            this.$request.post(`/blogs/updateViews/${id}`).catch(err => {\r\n                console.error(\"更新浏览次数失败:\", err);\r\n            });\r\n        },\r\n        \r\n        // 加载评论列表\r\n        loadComments(blogId) {\r\n            this.$request.get(`/pinglun/selectAll?bokeid=${blogId}`).then(res => {\r\n                if (res.code === '200') {\r\n                    this.comments = res.data.map(comment => ({\r\n                        ...comment,\r\n                        isNew: false\r\n                    }));\r\n                } else {\r\n                    this.$message.error(res.msg);\r\n                }\r\n            }).catch(err => {\r\n                this.$message.error(\"加载评论失败\");\r\n                console.error(err);\r\n            });\r\n        },\r\n        \r\n        // 提交评论\r\n        submitComment() {\r\n            if (!this.newComment.trim()) {\r\n                this.$message.error(\"评论内容不能为空\");\r\n                return;\r\n            }\r\n            \r\n            this.submitting = true;\r\n            const commentData = {\r\n                massage: this.newComment,\r\n                bokeid: this.blogData.id,\r\n            };\r\n            \r\n            this.$request.post('/pinglun/add', commentData).then(res => {\r\n                if (res.code === '200') {\r\n                    this.$message.success(\"评论成功\");\r\n                    // 重新加载评论\r\n                    this.loadComments(this.blogData.id);\r\n                    // 清空输入框\r\n                    this.newComment = \"\";\r\n                } else {\r\n                    this.$message.error(res.msg);\r\n                }\r\n            }).catch(err => {\r\n                this.$message.error(\"评论提交失败\");\r\n                console.error(err);\r\n            }).finally(() => {\r\n                this.submitting = false;\r\n            });\r\n        },\r\n    },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.blogs-detail-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 20px;\r\n    background-color: #f5f7fa;\r\n    min-height: 100vh;\r\n}\r\n\r\n/* 返回按钮 */\r\n.back-section {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.back-btn {\r\n    background-color: #fff;\r\n    border: 1px solid #dcdfe6;\r\n    color: #606266;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.back-btn:hover {\r\n    background-color: #409eff;\r\n    border-color: #409eff;\r\n    color: #fff;\r\n}\r\n\r\n/* 博客详情卡片 */\r\n.blog-detail-card {\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n    overflow: hidden;\r\n    margin-bottom: 30px;\r\n}\r\n\r\n/* 博客头部 */\r\n.blog-header {\r\n    padding: 30px 40px 20px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: #fff;\r\n}\r\n\r\n.blog-title {\r\n    font-size: 28px;\r\n    font-weight: 600;\r\n    margin-bottom: 20px;\r\n    line-height: 1.4;\r\n    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.blog-meta {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 20px;\r\n    align-items: center;\r\n}\r\n\r\n.meta-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    font-size: 14px;\r\n    color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.meta-icon {\r\n    font-size: 16px;\r\n}\r\n\r\n.tags-container {\r\n    display: flex;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.tag-item {\r\n    background: rgba(255, 255, 255, 0.2);\r\n    border: 1px solid rgba(255, 255, 255, 0.3);\r\n    color: #fff;\r\n}\r\n\r\n/* 博客内容 */\r\n.blog-content {\r\n    padding: 40px;\r\n}\r\n\r\n.blog-image-container {\r\n    margin-bottom: 30px;\r\n    text-align: center;\r\n}\r\n\r\n.blog-image {\r\n    width: 100%;\r\n    max-width: 800px;\r\n    height: 400px;\r\n    border-radius: 8px;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n    cursor: pointer;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.blog-image:hover {\r\n    transform: scale(1.02);\r\n}\r\n\r\n.blog-text-content {\r\n    font-size: 16px;\r\n    line-height: 1.8;\r\n    color: #333;\r\n}\r\n\r\n.content-text {\r\n    margin: 0;\r\n    text-align: justify;\r\n    text-indent: 2em;\r\n}\r\n\r\n/* 评论区 */\r\n.comments-section {\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n    overflow: hidden;\r\n}\r\n\r\n.comments-header {\r\n    padding: 25px 30px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: #fff;\r\n}\r\n\r\n.comments-title {\r\n    margin: 0;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n}\r\n\r\n/* 评论输入框 */\r\n.comment-form-card {\r\n    padding: 30px;\r\n    background: #fafbfc;\r\n    border-bottom: 1px solid #e1e6ea;\r\n}\r\n\r\n.comment-form {\r\n    max-width: 800px;\r\n}\r\n\r\n.comment-input {\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.comment-input >>> .el-textarea__inner {\r\n    border-radius: 8px;\r\n    border: 2px solid #e1e6ea;\r\n    transition: border-color 0.3s ease;\r\n    font-size: 14px;\r\n    line-height: 1.6;\r\n}\r\n\r\n.comment-input >>> .el-textarea__inner:focus {\r\n    border-color: #409eff;\r\n    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.comment-actions {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n}\r\n\r\n.submit-btn {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.submit-btn:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n/* 评论列表 */\r\n.comments-list {\r\n    padding: 30px;\r\n}\r\n\r\n.empty-comments {\r\n    text-align: center;\r\n    padding: 60px 20px;\r\n    color: #999;\r\n}\r\n\r\n.empty-comments i {\r\n    font-size: 48px;\r\n    color: #ddd;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.empty-comments h4 {\r\n    font-size: 18px;\r\n    margin-bottom: 8px;\r\n    color: #666;\r\n}\r\n\r\n.comments-container {\r\n    space-y: 20px;\r\n}\r\n\r\n.comment-item {\r\n    display: flex;\r\n    gap: 15px;\r\n    padding: 20px;\r\n    background: #fafbfc;\r\n    border-radius: 12px;\r\n    margin-bottom: 16px;\r\n    border: 2px solid transparent;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.comment-item:hover {\r\n    background: #f0f2f5;\r\n    border-color: #e1e6ea;\r\n}\r\n\r\n.comment-highlight {\r\n    border-color: #409eff;\r\n    background: #f0f8ff;\r\n}\r\n\r\n.comment-avatar {\r\n    flex-shrink: 0;\r\n}\r\n\r\n.avatar {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.comment-content {\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.comment-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.comment-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.comment-username {\r\n    font-weight: 600;\r\n    color: #333;\r\n    font-size: 14px;\r\n}\r\n\r\n.comment-time {\r\n    color: #999;\r\n    font-size: 12px;\r\n}\r\n\r\n.comment-number {\r\n    color: #999;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n}\r\n\r\n.comment-text {\r\n    color: #333;\r\n    line-height: 1.6;\r\n    font-size: 14px;\r\n}\r\n\r\n.comment-text p {\r\n    margin: 0;\r\n    word-wrap: break-word;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .blogs-detail-container {\r\n        padding: 15px;\r\n    }\r\n    \r\n    .blog-header {\r\n        padding: 20px 25px 15px;\r\n    }\r\n    \r\n    .blog-title {\r\n        font-size: 22px;\r\n    }\r\n    \r\n    .blog-meta {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 10px;\r\n    }\r\n    \r\n    .blog-content {\r\n        padding: 25px;\r\n    }\r\n    \r\n    .blog-image {\r\n        height: 250px;\r\n    }\r\n    \r\n    .comment-form-card,\r\n    .comments-list {\r\n        padding: 20px;\r\n    }\r\n    \r\n    .comment-item {\r\n        padding: 15px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .blogs-detail-container {\r\n        padding: 10px;\r\n    }\r\n    \r\n    .blog-header {\r\n        padding: 15px 20px;\r\n    }\r\n    \r\n    .blog-title {\r\n        font-size: 20px;\r\n    }\r\n    \r\n    .blog-content {\r\n        padding: 20px;\r\n    }\r\n    \r\n    .blog-image {\r\n        height: 200px;\r\n    }\r\n    \r\n    .comment-form-card,\r\n    .comments-list {\r\n        padding: 15px;\r\n    }\r\n}\r\n</style>\r\n"], "mappings": ";AA8IA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,QAAA;MAAA;MACAC,QAAA;MAAA;MACAC,UAAA;MAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACA,MAAAC,MAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,EAAA;IACA,IAAAH,MAAA;MACA,KAAAI,cAAA,CAAAJ,MAAA;MACA,KAAAK,YAAA,CAAAL,MAAA;IACA;MACA,KAAAM,QAAA,CAAAC,KAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;EACA;EACAC,OAAA;IACA;IACAN,eAAAD,EAAA;MACA,KAAAQ,QAAA,CAAAC,GAAA,sBAAAT,EAAA,IAAAU,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAApB,QAAA,GAAAmB,GAAA,CAAApB,IAAA;UACA;UACA,KAAAsB,WAAA,CAAAb,EAAA;QACA;UACA,KAAAG,QAAA,CAAAC,KAAA,CAAAO,GAAA,CAAAG,GAAA;QACA;MACA,GAAAC,KAAA,CAAAC,GAAA;QACA,KAAAb,QAAA,CAAAC,KAAA;QACAa,OAAA,CAAAb,KAAA,CAAAY,GAAA;MACA;IACA;IAEA;IACAH,YAAAb,EAAA;MACA,KAAAQ,QAAA,CAAAU,IAAA,uBAAAlB,EAAA,IAAAe,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAb,KAAA,cAAAY,GAAA;MACA;IACA;IAEA;IACAd,aAAAL,MAAA;MACA,KAAAW,QAAA,CAAAC,GAAA,8BAAAZ,MAAA,IAAAa,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAnB,QAAA,GAAAkB,GAAA,CAAApB,IAAA,CAAA4B,GAAA,CAAAC,OAAA;YACA,GAAAA,OAAA;YACAC,KAAA;UACA;QACA;UACA,KAAAlB,QAAA,CAAAC,KAAA,CAAAO,GAAA,CAAAG,GAAA;QACA;MACA,GAAAC,KAAA,CAAAC,GAAA;QACA,KAAAb,QAAA,CAAAC,KAAA;QACAa,OAAA,CAAAb,KAAA,CAAAY,GAAA;MACA;IACA;IAEA;IACAM,cAAA;MACA,UAAA5B,UAAA,CAAA6B,IAAA;QACA,KAAApB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,KAAAT,UAAA;MACA,MAAA6B,WAAA;QACAC,OAAA,OAAA/B,UAAA;QACAgC,MAAA,OAAAlC,QAAA,CAAAQ;MACA;MAEA,KAAAQ,QAAA,CAAAU,IAAA,iBAAAM,WAAA,EAAAd,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAT,QAAA,CAAAwB,OAAA;UACA;UACA,KAAAzB,YAAA,MAAAV,QAAA,CAAAQ,EAAA;UACA;UACA,KAAAN,UAAA;QACA;UACA,KAAAS,QAAA,CAAAC,KAAA,CAAAO,GAAA,CAAAG,GAAA;QACA;MACA,GAAAC,KAAA,CAAAC,GAAA;QACA,KAAAb,QAAA,CAAAC,KAAA;QACAa,OAAA,CAAAb,KAAA,CAAAY,GAAA;MACA,GAAAY,OAAA;QACA,KAAAjC,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}