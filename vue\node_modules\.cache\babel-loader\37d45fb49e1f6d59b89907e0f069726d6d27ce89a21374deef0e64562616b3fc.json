{"ast": null, "code": "export default {\n  name: \"<PERSON><PERSON>\",\n  data() {\n    return {\n      form: {},\n      rules: {\n        username: [{\n          required: true,\n          message: '请输入账号',\n          trigger: 'blur'\n        }],\n        password: [{\n          required: true,\n          message: '请输入密码',\n          trigger: 'blur'\n        }],\n        role: [{\n          required: true,\n          message: '请选择角色',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created() {},\n  methods: {\n    login() {\n      this.$refs['formRef'].validate(valid => {\n        if (valid) {\n          // 验证通过\n          this.$request.post('/login', this.form).then(res => {\n            if (res.code === '200') {\n              let user = res.data;\n              localStorage.setItem(\"xm-user\", JSON.stringify(user)); // 存储用户数据\n              if (user.role === 'USER') {\n                location.href = '/front/home';\n              } else {\n                location.href = '/home';\n              }\n              this.$message.success('登录成功');\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "form", "rules", "username", "required", "message", "trigger", "password", "role", "created", "methods", "login", "$refs", "validate", "valid", "$request", "post", "then", "res", "code", "user", "localStorage", "setItem", "JSON", "stringify", "location", "href", "$message", "success", "error", "msg"], "sources": ["src/views/Login.vue"], "sourcesContent": ["<template>\r\n  <div class=\"container\">\r\n    <div style=\"width: 400px; padding: 30px; background-color: white; border-radius: 5px;\">\r\n      <div style=\"text-align: center; font-size: 20px; margin-bottom: 20px; color: #333\">欢迎登录在线点餐系统</div>\r\n      <el-form :model=\"form\" :rules=\"rules\" ref=\"formRef\">\r\n        <el-form-item prop=\"username\">\r\n          <el-input prefix-icon=\"el-icon-user\" placeholder=\"请输入账号\" v-model=\"form.username\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item prop=\"password\">\r\n          <el-input prefix-icon=\"el-icon-lock\" placeholder=\"请输入密码\" show-password  v-model=\"form.password\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"form.role\" placeholder=\"请选择角色\" style=\"width: 100%\">\r\n            <el-option label=\"管理员\" value=\"ADMIN\"></el-option>\r\n            <el-option label=\"商家\" value=\"BUSINESS\"></el-option>\r\n            <el-option label=\"用户\" value=\"USER\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button style=\"width: 100%; background-color: #AF3939FF; border-color: #AF3939FF; color: white\" @click=\"login\">登 录</el-button>\r\n        </el-form-item>\r\n        <div style=\"display: flex; align-items: center\">\r\n          <div style=\"flex: 1\"></div>\r\n          <div style=\"flex: 1; text-align: right\">\r\n            还没有账号？请 <a href=\"/register\">注册</a>\r\n          </div>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      form: {},\r\n      rules: {\r\n        username: [{ required: true, message: '请输入账号', trigger: 'blur' },],\r\n        password: [{ required: true, message: '请输入密码', trigger: 'blur' },],\r\n        role: [{ required: true, message: '请选择角色', trigger: 'blur' },],\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n\r\n  },\r\n  methods: {\r\n    login() {\r\n      this.$refs['formRef'].validate((valid) => {\r\n        if (valid) {\r\n          // 验证通过\r\n          this.$request.post('/login', this.form).then(res => {\r\n            if (res.code === '200') {\r\n              let user = res.data\r\n              localStorage.setItem(\"xm-user\", JSON.stringify(user))  // 存储用户数据\r\n              if (user.role === 'USER') {\r\n                location.href = '/front/home'\r\n              } else {\r\n                location.href = '/home'\r\n              }\r\n              this.$message.success('登录成功')\r\n            } else {\r\n              this.$message.error(res.msg)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-image: url(\"@/assets/imgs/bg.jpg\");\r\n  background-size: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #666;\r\n}\r\na {\r\n  color: #2a60c9;\r\n}\r\n</style>"], "mappings": "AAiCA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,IAAA;MACAC,KAAA;QACAC,QAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,QAAA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAE,IAAA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAG,QAAA,GAEA;EACAC,OAAA;IACAC,MAAA;MACA,KAAAC,KAAA,YAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAC,QAAA,CAAAC,IAAA,gBAAAf,IAAA,EAAAgB,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA,IAAAC,IAAA,GAAAF,GAAA,CAAAlB,IAAA;cACAqB,YAAA,CAAAC,OAAA,YAAAC,IAAA,CAAAC,SAAA,CAAAJ,IAAA;cACA,IAAAA,IAAA,CAAAZ,IAAA;gBACAiB,QAAA,CAAAC,IAAA;cACA;gBACAD,QAAA,CAAAC,IAAA;cACA;cACA,KAAAC,QAAA,CAAAC,OAAA;YACA;cACA,KAAAD,QAAA,CAAAE,KAAA,CAAAX,GAAA,CAAAY,GAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}