<template>
    <div>
        <div class="search">
            <el-input placeholder="请输入关键字查询" style="width: 200px" v-model="name"></el-input>
            <el-button type="info" plain style="margin-left: 10px" @click="load(1)">查询</el-button>
            <el-button type="warning" plain style="margin-left: 10px" @click="reset">重置</el-button>
        </div>

        <div class="operation">
            <el-button type="primary" plain @click="handleAdd">新增</el-button>
            <el-button type="danger" plain @click="delBatch">批量删除</el-button>
        </div>

        <div class="table">
            <el-table :data="tableData" stripe @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column prop="id" label="序号" width="70" align="center" sortable></el-table-column>
                <el-table-column prop="sfUserName" label="用户名"></el-table-column>
                <el-table-column prop="sfUserId" label="用户ID"></el-table-column>
                <el-table-column prop="tableNumber" label="餐桌号" width="100">
                    <template v-slot="{row}">
                        <span v-if="row.tableNumber">{{ row.tableNumber }}号桌</span>
                        <span v-else style="color: #999;">-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="订单状态">
                    <template v-slot="{row}">
                        <el-tag :type="getStatusTagType(row.status)">
                            {{ row.status }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="sfOrderNumber" label="订单编号"></el-table-column>
                <el-table-column prop="sfCreateTime" label="下单时间"></el-table-column>
                <el-table-column prop="sfRemark" label="用户备注"></el-table-column>
                <el-table-column prop="sfEvaluation" label="用户评价"></el-table-column>
                <el-table-column prop="sfTotalPrice" label="订单价格"></el-table-column>
                <el-table-column label="操作" align="center" width="280">
                    <template v-slot="scope">
                        <div style="display: flex; justify-content: center; gap: 5px;">
                            <el-button size="mini" type="info" plain @click="showOrderDetails(scope.row)">详情</el-button>
                            <el-button size="mini" type="primary" plain @click="handleEdit(scope.row)">编辑</el-button>
                            <el-button
                                    size="mini"
                                    type="warning"
                                    plain
                                    v-if="scope.row.status === '已支付'"
                                    @click="handleStartCooking(scope.row)"
                            >开始制作</el-button>
                            <el-button
                                    size="mini"
                                    type="primary"
                                    plain
                                    v-if="scope.row.status === '制作中'"
                                    @click="handleFinishCooking(scope.row)"
                            >制作完成</el-button>
                            <el-button
                                    size="mini"
                                    type="success"
                                    plain
                                    v-if="scope.row.status === '待取餐'"
                                    @click="handleConfirmPickup(scope.row)"
                            >确认取餐</el-button>
                            <el-button
                                    size="mini"
                                    type="success"
                                    plain
                                    v-if="scope.row.status === '退款中'"
                                    @click="handleRefund(scope.row, '已退款')"
                            >同意退款</el-button>
                            <el-button
                                    size="mini"
                                    type="danger"
                                    plain
                                    v-if="scope.row.status === '退款中'"
                                    @click="handleRefund(scope.row, '已取消')"
                            >拒绝退款</el-button>
                            <el-button size="mini" type="danger" plain @click="del(scope.row.id)">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination">
                <el-pagination
                        background
                        @current-change="handleCurrentChange"
                        :current-page="pageNum"
                        :page-sizes="[5, 10, 20]"
                        :page-size="pageSize"
                        layout="total, prev, pager, next"
                        :total="total">
                </el-pagination>
            </div>
        </div>

        <el-dialog title="订单表" :visible.sync="fromVisible" width="40%" :close-on-click-modal="false" destroy-on-close>
            <el-form :model="form" label-width="100px" style="padding-right: 50px" :rules="rules" ref="formRef">
                <el-form-item label="用户名" prop="sfUserName">
                    <el-input v-model="form.sfUserName" placeholder="用户名"></el-input>
                </el-form-item>
                <el-form-item label="用户ID" prop="sfUserId">
                    <el-input v-model="form.sfUserId" placeholder="用户ID"></el-input>
                </el-form-item>
                <el-form-item label="订单状态" prop="status">
                    <el-select v-model="form.status" placeholder="请选择订单状态" style="width: 100%">
                        <el-option label="待支付" value="待支付"></el-option>
                        <el-option label="已支付" value="已支付"></el-option>
                        <el-option label="配送中" value="配送中"></el-option>
                        <el-option label="已完成" value="已完成"></el-option>
                        <el-option label="已取消" value="已取消"></el-option>
                        <el-option label="退款中" value="退款中"></el-option>
                        <el-option label="已退款" value="已退款"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="订单编号" prop="sfOrderNumber">
                    <el-input v-model="form.sfOrderNumber" placeholder="订单编号"></el-input>
                </el-form-item>
                <el-form-item label="下单时间" prop="sfCreateTime">
                    <el-date-picker
                            v-model="form.sfCreateTime"
                            type="datetime"
                            placeholder="选择下单时间"
                            style="width: 100%">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="用户备注" prop="sfRemark">
                    <el-input v-model="form.sfRemark" placeholder="用户备注"></el-input>
                </el-form-item>
                <el-form-item label="用户评价" prop="sfEvaluation">
                    <el-input v-model="form.sfEvaluation" placeholder="用户评价"></el-input>
                </el-form-item>
                <el-form-item label="订单价格" prop="sfTotalPrice">
                    <el-input-number v-model="form.sfTotalPrice" :precision="2" :step="0.1" :min="0" style="width: 100%"></el-input-number>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="fromVisible = false">取 消</el-button>
                <el-button type="primary" @click="save">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "Dingdan",
    data() {
        return {
            tableData: [],  // 所有的数据
            pageNum: 1,   // 当前的页码
            pageSize: 10,  // 每页显示的个数
            total: 0,
            name: null,
            fromVisible: false,
            form: {},
            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
            rules: {
                sfUserName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
                sfUserId: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
                status: [{ required: true, message: '请选择订单状态', trigger: 'change' }],
                sfOrderNumber: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],
                sfCreateTime: [{ required: true, message: '请选择下单时间', trigger: 'change' }],
                sfTotalPrice: [{ required: true, message: '请输入订单价格', trigger: 'blur' }],
            },
            ids: []
        }
    },
    created() {
        this.load(1)
    },
    methods: {
        getStatusTagType(status) {
            switch (status) {
                case '待支付': return 'danger';
                case '已支付': return 'warning';
                case '制作中': return '';
                case '待取餐': return 'primary';
                case '已完成': return 'success';
                case '已取消': return 'info';
                case '退款中': return 'danger';
                case '已退款': return 'info';
                default: return '';
            }
        },
        // 处理退款操作
        handleRefund(row, status) {
            const action = status === '已退款' ? '同意退款' : '拒绝退款';
            this.$confirm(`确定要${action}吗?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const refundData = {
                    ...row,
                    status: status
                }

                this.$request({
                    url: '/dingdan/update',
                    method: 'PUT',
                    data: refundData
                }).then(res => {
                    if (res.code === '200') {
                        this.$message.success(`${action}成功`)
                        this.load(1) // 重新加载数据
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {
                this.$message.info(`已取消${action}`)
            })
        },

        // 显示订单详情
        showOrderDetails(order) {
            this.$request.get(`/dingdan/details/${order.id}`).then(res => {
                if (res.code === '200') {
                    const orderItems = res.data || []
                    if (orderItems.length === 0) {
                        this.$message.info('该订单暂无详情信息')
                        return
                    }
                    
                    // 构建详情显示内容
                    let detailsHtml = '<div style="max-height: 400px; overflow-y: auto;">'
                    detailsHtml += `<h4>订单编号：${order.sfOrderNumber}</h4>`
                    detailsHtml += `<p><strong>订单状态：</strong>${order.status}</p>`
                    detailsHtml += `<p><strong>用户：</strong>${order.sfUserName}</p>`
                    detailsHtml += `<p><strong>下单时间：</strong>${order.sfCreateTime}</p>`
                    if (order.sfRemark) {
                        detailsHtml += `<p><strong>用户备注：</strong>${order.sfRemark}</p>`
                    }
                    if (order.sfEvaluation) {
                        detailsHtml += `<p><strong>用户评价：</strong>${order.sfEvaluation}</p>`
                    }
                    detailsHtml += '<hr style="margin: 15px 0;">'
                    detailsHtml += '<h5>商品明细：</h5>'
                    detailsHtml += '<table style="width: 100%; border-collapse: collapse;">'
                    detailsHtml += '<tr style="background: #f5f5f5;"><th style="padding: 8px; border: 1px solid #ddd;">商品名称</th><th style="padding: 8px; border: 1px solid #ddd;">单价</th><th style="padding: 8px; border: 1px solid #ddd;">数量</th><th style="padding: 8px; border: 1px solid #ddd;">小计</th></tr>'
                    
                    let totalAmount = 0
                    orderItems.forEach(item => {
                        totalAmount += parseFloat(item.subtotal || 0)
                        detailsHtml += `<tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">${item.foodName}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">¥${item.foodPrice}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${item.quantity}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">¥${item.subtotal}</td>
                        </tr>`
                    })
                    
                    detailsHtml += `<tr style="background: #f9f9f9; font-weight: bold;">
                        <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;">订单总额：</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">¥${totalAmount.toFixed(2)}</td>
                    </tr>`
                    detailsHtml += '</table></div>'
                    
                    this.$alert(detailsHtml, '订单详情', {
                        dangerouslyUseHTMLString: true,
                        customClass: 'order-details-dialog',
                        confirmButtonText: '关闭'
                    })
                } else {
                    this.$message.error('获取订单详情失败')
                }
            }).catch(() => {
                this.$message.error('获取订单详情失败')
            })
        },

        // 开始制作
        handleStartCooking(row) {
            const tableInfo = row.tableNumber ? ` (${row.tableNumber}号桌)` : ''
            this.$confirm(`确认开始制作订单 ${row.sfOrderNumber}${tableInfo} 吗？`, '开始制作确认', {
                confirmButtonText: '开始制作',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$request.put('/dingdan/startCooking/' + row.id).then(res => {
                    if (res.code === '200') {
                        this.$message.success(`订单 ${row.sfOrderNumber}${tableInfo} 已开始制作`)
                        this.load(1)
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {
                this.$message.info('已取消制作操作')
            })
        },

        // 制作完成
        handleFinishCooking(row) {
            const tableInfo = row.tableNumber ? ` (${row.tableNumber}号桌)` : ''
            this.$confirm(`确认订单 ${row.sfOrderNumber}${tableInfo} 制作完成吗？`, '制作完成确认', {
                confirmButtonText: '制作完成',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$request.put('/dingdan/finishCooking/' + row.id).then(res => {
                    if (res.code === '200') {
                        this.$message.success(`订单 ${row.sfOrderNumber}${tableInfo} 制作完成，请通知顾客取餐`)
                        this.load(1)
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {
                this.$message.info('已取消制作完成操作')
            })
        },

        // 确认取餐
        handleConfirmPickup(row) {
            const tableInfo = row.tableNumber ? ` (${row.tableNumber}号桌)` : ''
            this.$confirm(`确认订单 ${row.sfOrderNumber}${tableInfo} 顾客已取餐吗？`, '确认取餐', {
                confirmButtonText: '确认取餐',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$request.put('/dingdan/confirmPickup/' + row.id).then(res => {
                    if (res.code === '200') {
                        this.$message.success(`订单 ${row.sfOrderNumber}${tableInfo} 已完成`)
                        this.load(1)
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {
                this.$message.info('已取消取餐确认操作')
            })
        },

        // 处理完成订单操作
        handleComplete(row) {
            this.$confirm('确认要完成订单吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const completeData = {
                    ...row,
                    status: '已完成'
                }

                this.$request({
                    url: '/dingdan/update',
                    method: 'PUT',
                    data: completeData
                }).then(res => {
                    if (res.code === '200') {
                        this.$message.success('订单完成')
                        this.load(1) // 重新加载数据
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {
                this.$message.info('已取消完成操作')
            })
        },

        handleAdd() {
            this.form = {
                status: '待支付',
                sfCreateTime: new Date()
            }
            this.fromVisible = true
        },
        handleEdit(row) {
            this.form = JSON.parse(JSON.stringify(row))
            this.fromVisible = true
        },
        save() {
            this.$refs.formRef.validate((valid) => {
                if (valid) {
                    // 格式化下单时间
                    if (this.form.sfCreateTime instanceof Date) {
                        this.form.sfCreateTime = this.formatDateTime(this.form.sfCreateTime)
                    }

                    this.$request({
                        url: this.form.id ? '/dingdan/update' : '/dingdan/add',
                        method: this.form.id ? 'PUT' : 'POST',
                        data: this.form
                    }).then(res => {
                        if (res.code === '200') {
                            this.$message.success('保存成功')
                            this.load(1)
                            this.fromVisible = false
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                }
            })
        },
        formatDateTime(date) {
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            const hours = String(date.getHours()).padStart(2, '0')
            const minutes = String(date.getMinutes()).padStart(2, '0')
            const seconds = String(date.getSeconds()).padStart(2, '0')

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
        },
        del(id) {
            this.$confirm('您确定删除吗？', '确认删除', {type: "warning"}).then(response => {
                this.$request.delete('/dingdan/delete/' + id).then(res => {
                    if (res.code === '200') {
                        this.$message.success('操作成功')
                        this.load(1)
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {})
        },
        handleSelectionChange(rows) {
            this.ids = rows.map(v => v.id)
        },
        delBatch() {
            if (!this.ids.length) {
                this.$message.warning('请选择数据')
                return
            }
            this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: "warning"}).then(response => {
                this.$request.delete('/dingdan/delete/batch', {data: this.ids}).then(res => {
                    if (res.code === '200') {
                        this.$message.success('操作成功')
                        this.load(1)
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {})
        },
        load(pageNum) {
            if (pageNum) this.pageNum = pageNum
            this.$request.get('/dingdan/selectPage', {
                params: {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,
                    name: this.name,
                }
            }).then(res => {
                if (res.code === '200') {
                    // 过滤掉购物车数据，只显示真正的订单
                    const allData = res.data?.list || []
                    this.tableData = allData.filter(item => 
                        item.status !== '未付款' && !item.sfCartStatus
                    )
                    this.total = this.tableData.length
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        reset() {
            this.name = null
            this.load(1)
        },
        handleCurrentChange(pageNum) {
            this.load(pageNum)
        },
    }
}
</script>

<style scoped>
.search {
    margin-bottom: 20px;
}
.operation {
    margin-bottom: 20px;
}
.pagination {
    margin-top: 20px;
    text-align: center;
}

/* 订单详情对话框样式 */
.order-details-dialog >>> .el-message-box {
    min-width: 600px;
    max-width: 80vw;
}

.order-details-dialog >>> .el-message-box__content {
    padding: 20px;
    text-align: left;
}

.order-details-dialog >>> table {
    margin-top: 10px;
}

.order-details-dialog >>> th {
    background: #f8fafc !important;
    font-weight: 600;
    color: #374151;
}

.order-details-dialog >>> td {
    color: #6b7280;
}

.order-details-dialog >>> tr:nth-child(even) {
    background: #f9fafb;
}
</style>