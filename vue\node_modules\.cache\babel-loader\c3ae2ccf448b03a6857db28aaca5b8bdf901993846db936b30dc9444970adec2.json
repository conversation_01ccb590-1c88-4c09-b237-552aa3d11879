{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { trim, isArray, each, reduce } from 'zrender/lib/core/util.js';\nimport { retrieveVisualColorForTooltipMarker, createTooltipMarkup } from './tooltipMarkup.js';\nimport { retrieveRawValue } from '../../data/helper/dataProvider.js';\nimport { isNameSpecified } from '../../util/model.js';\nexport function defaultSeriesFormatTooltip(opt) {\n  var series = opt.series;\n  var dataIndex = opt.dataIndex;\n  var multipleSeries = opt.multipleSeries;\n  var data = series.getData();\n  var tooltipDims = data.mapDimensionsAll('defaultedTooltip');\n  var tooltipDimLen = tooltipDims.length;\n  var value = series.getRawValue(dataIndex);\n  var isValueArr = isArray(value);\n  var markerColor = retrieveVisualColorForTooltipMarker(series, dataIndex);\n  // Complicated rule for pretty tooltip.\n  var inlineValue;\n  var inlineValueType;\n  var subBlocks;\n  var sortParam;\n  if (tooltipDimLen > 1 || isValueArr && !tooltipDimLen) {\n    var formatArrResult = formatTooltipArrayValue(value, series, dataIndex, tooltipDims, markerColor);\n    inlineValue = formatArrResult.inlineValues;\n    inlineValueType = formatArrResult.inlineValueTypes;\n    subBlocks = formatArrResult.blocks;\n    // Only support tooltip sort by the first inline value. It's enough in most cases.\n    sortParam = formatArrResult.inlineValues[0];\n  } else if (tooltipDimLen) {\n    var dimInfo = data.getDimensionInfo(tooltipDims[0]);\n    sortParam = inlineValue = retrieveRawValue(data, dataIndex, tooltipDims[0]);\n    inlineValueType = dimInfo.type;\n  } else {\n    sortParam = inlineValue = isValueArr ? value[0] : value;\n  }\n  // Do not show generated series name. It might not be readable.\n  var seriesNameSpecified = isNameSpecified(series);\n  var seriesName = seriesNameSpecified && series.name || '';\n  var itemName = data.getName(dataIndex);\n  var inlineName = multipleSeries ? seriesName : itemName;\n  return createTooltipMarkup('section', {\n    header: seriesName,\n    // When series name is not specified, do not show a header line with only '-'.\n    // This case always happens in tooltip.trigger: 'item'.\n    noHeader: multipleSeries || !seriesNameSpecified,\n    sortParam: sortParam,\n    blocks: [createTooltipMarkup('nameValue', {\n      markerType: 'item',\n      markerColor: markerColor,\n      // Do not mix display seriesName and itemName in one tooltip,\n      // which might confuses users.\n      name: inlineName,\n      // name dimension might be auto assigned, where the name might\n      // be not readable. So we check trim here.\n      noName: !trim(inlineName),\n      value: inlineValue,\n      valueType: inlineValueType,\n      dataIndex: dataIndex\n    })].concat(subBlocks || [])\n  });\n}\nfunction formatTooltipArrayValue(value, series, dataIndex, tooltipDims, colorStr) {\n  // check: category-no-encode-has-axis-data in dataset.html\n  var data = series.getData();\n  var isValueMultipleLine = reduce(value, function (isValueMultipleLine, val, idx) {\n    var dimItem = data.getDimensionInfo(idx);\n    return isValueMultipleLine = isValueMultipleLine || dimItem && dimItem.tooltip !== false && dimItem.displayName != null;\n  }, false);\n  var inlineValues = [];\n  var inlineValueTypes = [];\n  var blocks = [];\n  tooltipDims.length ? each(tooltipDims, function (dim) {\n    setEachItem(retrieveRawValue(data, dataIndex, dim), dim);\n  })\n  // By default, all dims is used on tooltip.\n  : each(value, setEachItem);\n  function setEachItem(val, dim) {\n    var dimInfo = data.getDimensionInfo(dim);\n    // If `dimInfo.tooltip` is not set, show tooltip.\n    if (!dimInfo || dimInfo.otherDims.tooltip === false) {\n      return;\n    }\n    if (isValueMultipleLine) {\n      blocks.push(createTooltipMarkup('nameValue', {\n        markerType: 'subItem',\n        markerColor: colorStr,\n        name: dimInfo.displayName,\n        value: val,\n        valueType: dimInfo.type\n      }));\n    } else {\n      inlineValues.push(val);\n      inlineValueTypes.push(dimInfo.type);\n    }\n  }\n  return {\n    inlineValues: inlineValues,\n    inlineValueTypes: inlineValueTypes,\n    blocks: blocks\n  };\n}", "map": {"version": 3, "names": ["trim", "isArray", "each", "reduce", "retrieveVisualColorForTooltipMarker", "createTooltipMarkup", "retrieveRawValue", "isNameSpecified", "defaultSeriesFormatTooltip", "opt", "series", "dataIndex", "multipleSeries", "data", "getData", "tooltipDims", "mapDimensionsAll", "tooltipDimLen", "length", "value", "getRawValue", "isValueArr", "markerColor", "inlineValue", "inlineValueType", "subBlocks", "sortParam", "formatArrResult", "formatTooltipArrayValue", "inlineValues", "inlineValueTypes", "blocks", "dimInfo", "getDimensionInfo", "type", "seriesNameSpecified", "seriesName", "name", "itemName", "getName", "inlineName", "header", "<PERSON><PERSON><PERSON><PERSON>", "markerType", "noName", "valueType", "concat", "colorStr", "isValueMultipleLine", "val", "idx", "dimItem", "tooltip", "displayName", "dim", "setEachItem", "otherDims", "push"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/component/tooltip/seriesFormatTooltip.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { trim, isArray, each, reduce } from 'zrender/lib/core/util.js';\nimport { retrieveVisualColorForTooltipMarker, createTooltipMarkup } from './tooltipMarkup.js';\nimport { retrieveRawValue } from '../../data/helper/dataProvider.js';\nimport { isNameSpecified } from '../../util/model.js';\nexport function defaultSeriesFormatTooltip(opt) {\n  var series = opt.series;\n  var dataIndex = opt.dataIndex;\n  var multipleSeries = opt.multipleSeries;\n  var data = series.getData();\n  var tooltipDims = data.mapDimensionsAll('defaultedTooltip');\n  var tooltipDimLen = tooltipDims.length;\n  var value = series.getRawValue(dataIndex);\n  var isValueArr = isArray(value);\n  var markerColor = retrieveVisualColorForTooltipMarker(series, dataIndex);\n  // Complicated rule for pretty tooltip.\n  var inlineValue;\n  var inlineValueType;\n  var subBlocks;\n  var sortParam;\n  if (tooltipDimLen > 1 || isValueArr && !tooltipDimLen) {\n    var formatArrResult = formatTooltipArrayValue(value, series, dataIndex, tooltipDims, markerColor);\n    inlineValue = formatArrResult.inlineValues;\n    inlineValueType = formatArrResult.inlineValueTypes;\n    subBlocks = formatArrResult.blocks;\n    // Only support tooltip sort by the first inline value. It's enough in most cases.\n    sortParam = formatArrResult.inlineValues[0];\n  } else if (tooltipDimLen) {\n    var dimInfo = data.getDimensionInfo(tooltipDims[0]);\n    sortParam = inlineValue = retrieveRawValue(data, dataIndex, tooltipDims[0]);\n    inlineValueType = dimInfo.type;\n  } else {\n    sortParam = inlineValue = isValueArr ? value[0] : value;\n  }\n  // Do not show generated series name. It might not be readable.\n  var seriesNameSpecified = isNameSpecified(series);\n  var seriesName = seriesNameSpecified && series.name || '';\n  var itemName = data.getName(dataIndex);\n  var inlineName = multipleSeries ? seriesName : itemName;\n  return createTooltipMarkup('section', {\n    header: seriesName,\n    // When series name is not specified, do not show a header line with only '-'.\n    // This case always happens in tooltip.trigger: 'item'.\n    noHeader: multipleSeries || !seriesNameSpecified,\n    sortParam: sortParam,\n    blocks: [createTooltipMarkup('nameValue', {\n      markerType: 'item',\n      markerColor: markerColor,\n      // Do not mix display seriesName and itemName in one tooltip,\n      // which might confuses users.\n      name: inlineName,\n      // name dimension might be auto assigned, where the name might\n      // be not readable. So we check trim here.\n      noName: !trim(inlineName),\n      value: inlineValue,\n      valueType: inlineValueType,\n      dataIndex: dataIndex\n    })].concat(subBlocks || [])\n  });\n}\nfunction formatTooltipArrayValue(value, series, dataIndex, tooltipDims, colorStr) {\n  // check: category-no-encode-has-axis-data in dataset.html\n  var data = series.getData();\n  var isValueMultipleLine = reduce(value, function (isValueMultipleLine, val, idx) {\n    var dimItem = data.getDimensionInfo(idx);\n    return isValueMultipleLine = isValueMultipleLine || dimItem && dimItem.tooltip !== false && dimItem.displayName != null;\n  }, false);\n  var inlineValues = [];\n  var inlineValueTypes = [];\n  var blocks = [];\n  tooltipDims.length ? each(tooltipDims, function (dim) {\n    setEachItem(retrieveRawValue(data, dataIndex, dim), dim);\n  })\n  // By default, all dims is used on tooltip.\n  : each(value, setEachItem);\n  function setEachItem(val, dim) {\n    var dimInfo = data.getDimensionInfo(dim);\n    // If `dimInfo.tooltip` is not set, show tooltip.\n    if (!dimInfo || dimInfo.otherDims.tooltip === false) {\n      return;\n    }\n    if (isValueMultipleLine) {\n      blocks.push(createTooltipMarkup('nameValue', {\n        markerType: 'subItem',\n        markerColor: colorStr,\n        name: dimInfo.displayName,\n        value: val,\n        valueType: dimInfo.type\n      }));\n    } else {\n      inlineValues.push(val);\n      inlineValueTypes.push(dimInfo.type);\n    }\n  }\n  return {\n    inlineValues: inlineValues,\n    inlineValueTypes: inlineValueTypes,\n    blocks: blocks\n  };\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,OAAO,EAAEC,IAAI,EAAEC,MAAM,QAAQ,0BAA0B;AACtE,SAASC,mCAAmC,EAAEC,mBAAmB,QAAQ,oBAAoB;AAC7F,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,eAAe,QAAQ,qBAAqB;AACrD,OAAO,SAASC,0BAA0BA,CAACC,GAAG,EAAE;EAC9C,IAAIC,MAAM,GAAGD,GAAG,CAACC,MAAM;EACvB,IAAIC,SAAS,GAAGF,GAAG,CAACE,SAAS;EAC7B,IAAIC,cAAc,GAAGH,GAAG,CAACG,cAAc;EACvC,IAAIC,IAAI,GAAGH,MAAM,CAACI,OAAO,CAAC,CAAC;EAC3B,IAAIC,WAAW,GAAGF,IAAI,CAACG,gBAAgB,CAAC,kBAAkB,CAAC;EAC3D,IAAIC,aAAa,GAAGF,WAAW,CAACG,MAAM;EACtC,IAAIC,KAAK,GAAGT,MAAM,CAACU,WAAW,CAACT,SAAS,CAAC;EACzC,IAAIU,UAAU,GAAGpB,OAAO,CAACkB,KAAK,CAAC;EAC/B,IAAIG,WAAW,GAAGlB,mCAAmC,CAACM,MAAM,EAAEC,SAAS,CAAC;EACxE;EACA,IAAIY,WAAW;EACf,IAAIC,eAAe;EACnB,IAAIC,SAAS;EACb,IAAIC,SAAS;EACb,IAAIT,aAAa,GAAG,CAAC,IAAII,UAAU,IAAI,CAACJ,aAAa,EAAE;IACrD,IAAIU,eAAe,GAAGC,uBAAuB,CAACT,KAAK,EAAET,MAAM,EAAEC,SAAS,EAAEI,WAAW,EAAEO,WAAW,CAAC;IACjGC,WAAW,GAAGI,eAAe,CAACE,YAAY;IAC1CL,eAAe,GAAGG,eAAe,CAACG,gBAAgB;IAClDL,SAAS,GAAGE,eAAe,CAACI,MAAM;IAClC;IACAL,SAAS,GAAGC,eAAe,CAACE,YAAY,CAAC,CAAC,CAAC;EAC7C,CAAC,MAAM,IAAIZ,aAAa,EAAE;IACxB,IAAIe,OAAO,GAAGnB,IAAI,CAACoB,gBAAgB,CAAClB,WAAW,CAAC,CAAC,CAAC,CAAC;IACnDW,SAAS,GAAGH,WAAW,GAAGjB,gBAAgB,CAACO,IAAI,EAAEF,SAAS,EAAEI,WAAW,CAAC,CAAC,CAAC,CAAC;IAC3ES,eAAe,GAAGQ,OAAO,CAACE,IAAI;EAChC,CAAC,MAAM;IACLR,SAAS,GAAGH,WAAW,GAAGF,UAAU,GAAGF,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;EACzD;EACA;EACA,IAAIgB,mBAAmB,GAAG5B,eAAe,CAACG,MAAM,CAAC;EACjD,IAAI0B,UAAU,GAAGD,mBAAmB,IAAIzB,MAAM,CAAC2B,IAAI,IAAI,EAAE;EACzD,IAAIC,QAAQ,GAAGzB,IAAI,CAAC0B,OAAO,CAAC5B,SAAS,CAAC;EACtC,IAAI6B,UAAU,GAAG5B,cAAc,GAAGwB,UAAU,GAAGE,QAAQ;EACvD,OAAOjC,mBAAmB,CAAC,SAAS,EAAE;IACpCoC,MAAM,EAAEL,UAAU;IAClB;IACA;IACAM,QAAQ,EAAE9B,cAAc,IAAI,CAACuB,mBAAmB;IAChDT,SAAS,EAAEA,SAAS;IACpBK,MAAM,EAAE,CAAC1B,mBAAmB,CAAC,WAAW,EAAE;MACxCsC,UAAU,EAAE,MAAM;MAClBrB,WAAW,EAAEA,WAAW;MACxB;MACA;MACAe,IAAI,EAAEG,UAAU;MAChB;MACA;MACAI,MAAM,EAAE,CAAC5C,IAAI,CAACwC,UAAU,CAAC;MACzBrB,KAAK,EAAEI,WAAW;MAClBsB,SAAS,EAAErB,eAAe;MAC1Bb,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC,CAACmC,MAAM,CAACrB,SAAS,IAAI,EAAE;EAC5B,CAAC,CAAC;AACJ;AACA,SAASG,uBAAuBA,CAACT,KAAK,EAAET,MAAM,EAAEC,SAAS,EAAEI,WAAW,EAAEgC,QAAQ,EAAE;EAChF;EACA,IAAIlC,IAAI,GAAGH,MAAM,CAACI,OAAO,CAAC,CAAC;EAC3B,IAAIkC,mBAAmB,GAAG7C,MAAM,CAACgB,KAAK,EAAE,UAAU6B,mBAAmB,EAAEC,GAAG,EAAEC,GAAG,EAAE;IAC/E,IAAIC,OAAO,GAAGtC,IAAI,CAACoB,gBAAgB,CAACiB,GAAG,CAAC;IACxC,OAAOF,mBAAmB,GAAGA,mBAAmB,IAAIG,OAAO,IAAIA,OAAO,CAACC,OAAO,KAAK,KAAK,IAAID,OAAO,CAACE,WAAW,IAAI,IAAI;EACzH,CAAC,EAAE,KAAK,CAAC;EACT,IAAIxB,YAAY,GAAG,EAAE;EACrB,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAIC,MAAM,GAAG,EAAE;EACfhB,WAAW,CAACG,MAAM,GAAGhB,IAAI,CAACa,WAAW,EAAE,UAAUuC,GAAG,EAAE;IACpDC,WAAW,CAACjD,gBAAgB,CAACO,IAAI,EAAEF,SAAS,EAAE2C,GAAG,CAAC,EAAEA,GAAG,CAAC;EAC1D,CAAC;EACD;EAAA,EACEpD,IAAI,CAACiB,KAAK,EAAEoC,WAAW,CAAC;EAC1B,SAASA,WAAWA,CAACN,GAAG,EAAEK,GAAG,EAAE;IAC7B,IAAItB,OAAO,GAAGnB,IAAI,CAACoB,gBAAgB,CAACqB,GAAG,CAAC;IACxC;IACA,IAAI,CAACtB,OAAO,IAAIA,OAAO,CAACwB,SAAS,CAACJ,OAAO,KAAK,KAAK,EAAE;MACnD;IACF;IACA,IAAIJ,mBAAmB,EAAE;MACvBjB,MAAM,CAAC0B,IAAI,CAACpD,mBAAmB,CAAC,WAAW,EAAE;QAC3CsC,UAAU,EAAE,SAAS;QACrBrB,WAAW,EAAEyB,QAAQ;QACrBV,IAAI,EAAEL,OAAO,CAACqB,WAAW;QACzBlC,KAAK,EAAE8B,GAAG;QACVJ,SAAS,EAAEb,OAAO,CAACE;MACrB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLL,YAAY,CAAC4B,IAAI,CAACR,GAAG,CAAC;MACtBnB,gBAAgB,CAAC2B,IAAI,CAACzB,OAAO,CAACE,IAAI,CAAC;IACrC;EACF;EACA,OAAO;IACLL,YAAY,EAAEA,YAAY;IAC1BC,gBAAgB,EAAEA,gBAAgB;IAClCC,MAAM,EAAEA;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}