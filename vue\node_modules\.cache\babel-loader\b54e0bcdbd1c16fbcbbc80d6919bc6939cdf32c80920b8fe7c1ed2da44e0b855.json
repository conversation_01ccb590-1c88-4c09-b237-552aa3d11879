{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"freemovies-container\"\n  }, [_c(\"div\", {\n    staticClass: \"content-section\"\n  }, [_c(\"div\", {\n    staticClass: \"recommendations-list\"\n  }, [_vm.tableData.length === 0 ? _c(\"div\", {\n    staticClass: \"empty-state\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-star-on\"\n  }), _c(\"h3\", [_vm._v(\"暂无推荐内容\")]), _c(\"p\", [_vm._v(\"还没有美食推荐，敬请期待\")])]) : _c(\"div\", {\n    staticClass: \"recommendations-grid\"\n  }, _vm._l(_vm.tableData, function (item) {\n    return _c(\"div\", {\n      key: item.id,\n      staticClass: \"recommendation-card\"\n    }, [_c(\"div\", {\n      staticClass: \"card-image-container\"\n    }, [_c(\"el-image\", {\n      staticClass: \"card-image\",\n      attrs: {\n        src: item.sfCoverImage,\n        fit: \"cover\"\n      }\n    }), _c(\"div\", {\n      staticClass: \"card-overlay\"\n    }, [_c(\"el-button\", {\n      staticClass: \"play-btn\",\n      attrs: {\n        type: \"primary\",\n        size: \"small\"\n      },\n      on: {\n        click: function ($event) {\n          return _vm.playVideo(item.sfVideoUrl);\n        }\n      }\n    }, [_c(\"i\", {\n      staticClass: \"el-icon-video-play\"\n    }), _vm._v(\" 播放视频 \")])], 1), _vm._m(0, true)], 1), _c(\"div\", {\n      staticClass: \"card-content\"\n    }, [_c(\"h3\", {\n      staticClass: \"card-title\"\n    }, [_vm._v(_vm._s(item.name))]), _c(\"div\", {\n      staticClass: \"card-description\"\n    }, [_vm._v(\" \" + _vm._s(item.content) + \" \")]), _c(\"div\", {\n      staticClass: \"card-actions\"\n    }, [_c(\"el-button\", {\n      staticClass: \"action-btn\",\n      attrs: {\n        type: \"primary\",\n        size: \"small\"\n      },\n      on: {\n        click: function ($event) {\n          return _vm.playVideo(item.sfVideoUrl);\n        }\n      }\n    }, [_c(\"i\", {\n      staticClass: \"el-icon-video-play\"\n    }), _vm._v(\" 观看视频 \")])], 1)])]);\n  }), 0)]), _c(\"div\", {\n    staticClass: \"pagination-section\"\n  }, [_c(\"el-pagination\", {\n    staticClass: \"custom-pagination\",\n    attrs: {\n      background: \"\",\n      \"current-page\": _vm.pageNum,\n      \"page-size\": _vm.pageSize,\n      layout: \"prev, pager, next\",\n      total: _vm.total\n    },\n    on: {\n      \"current-change\": _vm.handleCurrentChange\n    }\n  })], 1)]), _c(\"el-dialog\", {\n    attrs: {\n      title: \"美食推荐视频\",\n      visible: _vm.videoVisible,\n      width: \"70%\",\n      \"append-to-body\": \"\",\n      center: \"\",\n      \"custom-class\": \"video-dialog\"\n    },\n    on: {\n      \"update:visible\": function ($event) {\n        _vm.videoVisible = $event;\n      },\n      close: _vm.handleVideoClose\n    }\n  }, [_c(\"div\", {\n    staticClass: \"video-container\"\n  }, [_vm.currentVideoUrl ? _c(\"video\", {\n    staticClass: \"video-player\",\n    attrs: {\n      src: _vm.currentVideoUrl,\n      controls: \"\",\n      autoplay: \"\"\n    }\n  }) : _c(\"div\", {\n    staticClass: \"video-placeholder\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-video-camera\"\n  }), _c(\"p\", [_vm._v(\"视频加载中...\")])])])])], 1);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"recommendation-badge\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-star-on\"\n  }), _vm._v(\" 推荐 \")]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "tableData", "length", "_v", "_l", "item", "key", "id", "attrs", "src", "sfCoverImage", "fit", "type", "size", "on", "click", "$event", "playVideo", "sfVideoUrl", "_m", "_s", "name", "content", "background", "pageNum", "pageSize", "layout", "total", "handleCurrentChange", "title", "visible", "videoVisible", "width", "center", "update:visible", "close", "handleVideoClose", "currentVideoUrl", "controls", "autoplay", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/src/views/front/Freemovies.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"freemovies-container\" },\n    [\n      _c(\"div\", { staticClass: \"content-section\" }, [\n        _c(\"div\", { staticClass: \"recommendations-list\" }, [\n          _vm.tableData.length === 0\n            ? _c(\"div\", { staticClass: \"empty-state\" }, [\n                _c(\"i\", { staticClass: \"el-icon-star-on\" }),\n                _c(\"h3\", [_vm._v(\"暂无推荐内容\")]),\n                _c(\"p\", [_vm._v(\"还没有美食推荐，敬请期待\")]),\n              ])\n            : _c(\n                \"div\",\n                { staticClass: \"recommendations-grid\" },\n                _vm._l(_vm.tableData, function (item) {\n                  return _c(\n                    \"div\",\n                    { key: item.id, staticClass: \"recommendation-card\" },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"card-image-container\" },\n                        [\n                          _c(\"el-image\", {\n                            staticClass: \"card-image\",\n                            attrs: { src: item.sfCoverImage, fit: \"cover\" },\n                          }),\n                          _c(\n                            \"div\",\n                            { staticClass: \"card-overlay\" },\n                            [\n                              _c(\n                                \"el-button\",\n                                {\n                                  staticClass: \"play-btn\",\n                                  attrs: { type: \"primary\", size: \"small\" },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.playVideo(item.sfVideoUrl)\n                                    },\n                                  },\n                                },\n                                [\n                                  _c(\"i\", {\n                                    staticClass: \"el-icon-video-play\",\n                                  }),\n                                  _vm._v(\" 播放视频 \"),\n                                ]\n                              ),\n                            ],\n                            1\n                          ),\n                          _vm._m(0, true),\n                        ],\n                        1\n                      ),\n                      _c(\"div\", { staticClass: \"card-content\" }, [\n                        _c(\"h3\", { staticClass: \"card-title\" }, [\n                          _vm._v(_vm._s(item.name)),\n                        ]),\n                        _c(\"div\", { staticClass: \"card-description\" }, [\n                          _vm._v(\" \" + _vm._s(item.content) + \" \"),\n                        ]),\n                        _c(\n                          \"div\",\n                          { staticClass: \"card-actions\" },\n                          [\n                            _c(\n                              \"el-button\",\n                              {\n                                staticClass: \"action-btn\",\n                                attrs: { type: \"primary\", size: \"small\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.playVideo(item.sfVideoUrl)\n                                  },\n                                },\n                              },\n                              [\n                                _c(\"i\", { staticClass: \"el-icon-video-play\" }),\n                                _vm._v(\" 观看视频 \"),\n                              ]\n                            ),\n                          ],\n                          1\n                        ),\n                      ]),\n                    ]\n                  )\n                }),\n                0\n              ),\n        ]),\n        _c(\n          \"div\",\n          { staticClass: \"pagination-section\" },\n          [\n            _c(\"el-pagination\", {\n              staticClass: \"custom-pagination\",\n              attrs: {\n                background: \"\",\n                \"current-page\": _vm.pageNum,\n                \"page-size\": _vm.pageSize,\n                layout: \"prev, pager, next\",\n                total: _vm.total,\n              },\n              on: { \"current-change\": _vm.handleCurrentChange },\n            }),\n          ],\n          1\n        ),\n      ]),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"美食推荐视频\",\n            visible: _vm.videoVisible,\n            width: \"70%\",\n            \"append-to-body\": \"\",\n            center: \"\",\n            \"custom-class\": \"video-dialog\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.videoVisible = $event\n            },\n            close: _vm.handleVideoClose,\n          },\n        },\n        [\n          _c(\"div\", { staticClass: \"video-container\" }, [\n            _vm.currentVideoUrl\n              ? _c(\"video\", {\n                  staticClass: \"video-player\",\n                  attrs: {\n                    src: _vm.currentVideoUrl,\n                    controls: \"\",\n                    autoplay: \"\",\n                  },\n                })\n              : _c(\"div\", { staticClass: \"video-placeholder\" }, [\n                  _c(\"i\", { staticClass: \"el-icon-video-camera\" }),\n                  _c(\"p\", [_vm._v(\"视频加载中...\")]),\n                ]),\n          ]),\n        ]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"recommendation-badge\" }, [\n      _c(\"i\", { staticClass: \"el-icon-star-on\" }),\n      _vm._v(\" 推荐 \"),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACjDH,GAAG,CAACI,SAAS,CAACC,MAAM,KAAK,CAAC,GACtBJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC3CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BL,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAClC,CAAC,GACFL,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAuB,CAAC,EACvCH,GAAG,CAACO,EAAE,CAACP,GAAG,CAACI,SAAS,EAAE,UAAUI,IAAI,EAAE;IACpC,OAAOP,EAAE,CACP,KAAK,EACL;MAAEQ,GAAG,EAAED,IAAI,CAACE,EAAE;MAAEP,WAAW,EAAE;IAAsB,CAAC,EACpD,CACEF,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,UAAU,EAAE;MACbE,WAAW,EAAE,YAAY;MACzBQ,KAAK,EAAE;QAAEC,GAAG,EAAEJ,IAAI,CAACK,YAAY;QAAEC,GAAG,EAAE;MAAQ;IAChD,CAAC,CAAC,EACFb,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAe,CAAC,EAC/B,CACEF,EAAE,CACA,WAAW,EACX;MACEE,WAAW,EAAE,UAAU;MACvBQ,KAAK,EAAE;QAAEI,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACzCC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOnB,GAAG,CAACoB,SAAS,CAACZ,IAAI,CAACa,UAAU,CAAC;QACvC;MACF;IACF,CAAC,EACD,CACEpB,EAAE,CAAC,GAAG,EAAE;MACNE,WAAW,EAAE;IACf,CAAC,CAAC,EACFH,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAEpB,CAAC,CACF,EACD,CACF,CAAC,EACDN,GAAG,CAACsB,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAChB,EACD,CACF,CAAC,EACDrB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACtCH,GAAG,CAACM,EAAE,CAACN,GAAG,CAACuB,EAAE,CAACf,IAAI,CAACgB,IAAI,CAAC,CAAC,CAC1B,CAAC,EACFvB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAmB,CAAC,EAAE,CAC7CH,GAAG,CAACM,EAAE,CAAC,GAAG,GAAGN,GAAG,CAACuB,EAAE,CAACf,IAAI,CAACiB,OAAO,CAAC,GAAG,GAAG,CAAC,CACzC,CAAC,EACFxB,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAe,CAAC,EAC/B,CACEF,EAAE,CACA,WAAW,EACX;MACEE,WAAW,EAAE,YAAY;MACzBQ,KAAK,EAAE;QAAEI,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACzCC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOnB,GAAG,CAACoB,SAAS,CAACZ,IAAI,CAACa,UAAU,CAAC;QACvC;MACF;IACF,CAAC,EACD,CACEpB,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAqB,CAAC,CAAC,EAC9CH,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAEpB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACN,CAAC,EACFL,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CAAC,eAAe,EAAE;IAClBE,WAAW,EAAE,mBAAmB;IAChCQ,KAAK,EAAE;MACLe,UAAU,EAAE,EAAE;MACd,cAAc,EAAE1B,GAAG,CAAC2B,OAAO;MAC3B,WAAW,EAAE3B,GAAG,CAAC4B,QAAQ;MACzBC,MAAM,EAAE,mBAAmB;MAC3BC,KAAK,EAAE9B,GAAG,CAAC8B;IACb,CAAC;IACDb,EAAE,EAAE;MAAE,gBAAgB,EAAEjB,GAAG,CAAC+B;IAAoB;EAClD,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,EACF9B,EAAE,CACA,WAAW,EACX;IACEU,KAAK,EAAE;MACLqB,KAAK,EAAE,QAAQ;MACfC,OAAO,EAAEjC,GAAG,CAACkC,YAAY;MACzBC,KAAK,EAAE,KAAK;MACZ,gBAAgB,EAAE,EAAE;MACpBC,MAAM,EAAE,EAAE;MACV,cAAc,EAAE;IAClB,CAAC;IACDnB,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAAoB,CAAUlB,MAAM,EAAE;QAClCnB,GAAG,CAACkC,YAAY,GAAGf,MAAM;MAC3B,CAAC;MACDmB,KAAK,EAAEtC,GAAG,CAACuC;IACb;EACF,CAAC,EACD,CACEtC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACwC,eAAe,GACfvC,EAAE,CAAC,OAAO,EAAE;IACVE,WAAW,EAAE,cAAc;IAC3BQ,KAAK,EAAE;MACLC,GAAG,EAAEZ,GAAG,CAACwC,eAAe;MACxBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC,GACFzC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,CAAC,EAChDF,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAC9B,CAAC,CACP,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIqC,eAAe,GAAG,CACpB,YAAY;EACV,IAAI3C,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,EAAE,CACxDF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC3CH,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CACf,CAAC;AACJ,CAAC,CACF;AACDP,MAAM,CAAC6C,aAAa,GAAG,IAAI;AAE3B,SAAS7C,MAAM,EAAE4C,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}