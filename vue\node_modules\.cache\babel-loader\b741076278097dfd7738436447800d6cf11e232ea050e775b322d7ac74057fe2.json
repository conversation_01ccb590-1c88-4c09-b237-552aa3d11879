{"ast": null, "code": "import \"core-js/modules/esnext.iterator.map.js\";\nexport default {\n  name: \"Leavemess\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前的页码\n      pageSize: 10,\n      // 每页显示的个数\n      total: 0,\n      sfQuestion: '',\n      // 搜索关键字，针对评论问题\n      fromVisible: false,\n      form: {},\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      rules: {\n        sfUserId: [{\n          required: true,\n          message: '请输入用户ID',\n          trigger: 'blur'\n        }],\n        sfQuestion: [{\n          required: true,\n          message: '请输入评论问题',\n          trigger: 'blur'\n        }],\n        reply: [{\n          required: true,\n          message: '请输入评论回复',\n          trigger: 'blur'\n        }],\n        sfImage: [{\n          type: 'url',\n          message: '请输入有效的图片URL',\n          trigger: 'blur'\n        }]\n      },\n      ids: [],\n      successVisible: false // 控制成功提交提示的显示\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    /**\r\n     * 根据行数据返回对应的类名\r\n     * @param {Object} row - 当前行数据\r\n     * @param {Number} index - 行索引\r\n     * @returns {String} - 类名\r\n     */\n    tableRowClassName(row, index) {\n      return !row.reply ? 'no-reply' : '';\n    },\n    /**\r\n     * 新增数据\r\n     */\n    handleAdd() {\n      this.form = {}; // 新增数据的时候清空数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    /**\r\n     * 编辑数据（回复评论）\r\n     * @param {Object} row - 当前行数据\r\n     */\n    handleEdit(row) {\n      this.form = JSON.parse(JSON.stringify(row)); // 深拷贝数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    /**\r\n     * 保存数据（新增或更新）\r\n     */\n    save() {\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          this.$request({\n            url: this.form.id ? '/leavemess/update' : '/leavemess/add',\n            method: this.form.id ? 'PUT' : 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              // 表示成功保存\n              this.$message.success('保存成功');\n              this.load(1);\n              this.fromVisible = false;\n              this.successVisible = true; // 显示提交成功弹窗\n            } else {\n              this.$message.error(res.msg); // 弹出错误的信息\n            }\n          }).catch(() => {\n            this.$message.error('请求失败');\n          });\n        }\n      });\n    },\n    /**\r\n     * 单个删除\r\n     * @param {Number} id - 数据ID\r\n     */\n    del(id) {\n      this.$confirm('您确定删除吗？', '确认删除', {\n        type: \"warning\"\n      }).then(() => {\n        this.$request.delete('/leavemess/delete/' + id).then(res => {\n          if (res.code === '200') {\n            // 表示操作成功\n            this.$message.success('删除成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg); // 弹出错误的信息\n          }\n        }).catch(() => {\n          this.$message.error('删除失败');\n        });\n      }).catch(() => {\n        // 用户取消删除操作\n      });\n    },\n    /**\r\n     * 处理表格选中的数据\r\n     * @param {Array} rows - 当前选中的所有行数据\r\n     */\n    handleSelectionChange(rows) {\n      this.ids = rows.map(v => v.id);\n    },\n    /**\r\n     * 批量删除\r\n     */\n    delBatch() {\n      if (!this.ids.length) {\n        this.$message.warning('请选择要删除的数据');\n        return;\n      }\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {\n        type: \"warning\"\n      }).then(() => {\n        this.$request.delete('/leavemess/delete/batch', {\n          data: this.ids\n        }).then(res => {\n          if (res.code === '200') {\n            // 表示操作成功\n            this.$message.success('批量删除成功');\n            this.load(1);\n            this.ids = []; // 清空已删除的ID\n          } else {\n            this.$message.error(res.msg); // 弹出错误的信息\n          }\n        }).catch(() => {\n          this.$message.error('批量删除失败');\n        });\n      }).catch(() => {\n        // 用户取消删除操作\n      });\n    },\n    /**\r\n     * 加载数据（分页查询）\r\n     * @param {Number} pageNum - 要加载的页码\r\n     */\n    load(pageNum) {\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/leavemess/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          sfQuestion: this.sfQuestion // 修改为 sfQuestion\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list || [];\n          this.total = res.data?.total || 0;\n        } else {\n          this.$message.error(res.msg);\n        }\n      }).catch(() => {\n        this.$message.error('加载数据失败');\n      });\n    },\n    /**\r\n     * 重置搜索条件并重新加载数据\r\n     */\n    reset() {\n      this.sfQuestion = '';\n      this.load(1);\n    },\n    /**\r\n     * 处理分页器页码变化\r\n     * @param {Number} pageNum - 当前选择的页码\r\n     */\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    },\n    /**\r\n     * 处理图片上传成功\r\n     * @param {Object} response - 上传成功的响应\r\n     * @param {File} file - 上传的文件\r\n     * @param {Array} fileList - 文件列表\r\n     */\n    handleImageSuccess(response, file, fileList) {\n      if (response.code === '200') {\n        this.form.sfImage = response.data.url || response.data; // 根据后端返回的数据结构调整\n        this.$message.success('图片上传成功');\n      } else {\n        this.$message.error('图片上传失败');\n      }\n    },\n    /**\r\n     * 图片上传前的验证\r\n     * @param {File} file - 上传的文件\r\n     * @returns {Boolean} - 是否允许上传\r\n     */\n    beforeUpload(file) {\n      const isImage = file.type.startsWith('image/');\n      const isLt2M = file.size / 1024 / 1024 < 2;\n      if (!isImage) {\n        this.$message.error('上传图片只能是 JPG/PNG 格式!');\n      }\n      if (!isLt2M) {\n        this.$message.error('上传图片大小不能超过 2MB!');\n      }\n      return isImage && isLt2M;\n    },\n    /**\r\n     * 预览图片\r\n     * @param {String} url - 图片URL\r\n     */\n    previewImage(url) {\n      this.$refs.imagePreview.handlePreview(url);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "sfQuestion", "fromVisible", "form", "user", "JSON", "parse", "localStorage", "getItem", "rules", "sfUserId", "required", "message", "trigger", "reply", "sfImage", "type", "ids", "successVisible", "created", "load", "methods", "tableRowClassName", "row", "index", "handleAdd", "handleEdit", "stringify", "save", "$refs", "formRef", "validate", "valid", "$request", "url", "id", "method", "then", "res", "code", "$message", "success", "error", "msg", "catch", "del", "$confirm", "delete", "handleSelectionChange", "rows", "map", "v", "delBatch", "length", "warning", "get", "params", "list", "reset", "handleCurrentChange", "handleImageSuccess", "response", "file", "fileList", "beforeUpload", "isImage", "startsWith", "isLt2M", "size", "previewImage", "imagePreview", "handlePreview"], "sources": ["src/views/manager/Leavemess.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <!-- 搜索栏 -->\r\n        <div class=\"search\">\r\n            <el-input\r\n                placeholder=\"请输入留言问题查询\"\r\n                style=\"width: 200px\"\r\n                v-model=\"sfQuestion\">\r\n            </el-input>\r\n            <el-button type=\"info\" plain style=\"margin-left: 10px\" @click=\"load(1)\">查询</el-button>\r\n            <el-button type=\"warning\" plain style=\"margin-left: 10px\" @click=\"reset\">重置</el-button>\r\n        </div>\r\n\r\n        <!-- 操作按钮 -->\r\n        <div class=\"operation\">\r\n            <el-button type=\"primary\" plain @click=\"handleAdd\">新增</el-button>\r\n            <el-button type=\"danger\" plain @click=\"delBatch\">批量删除</el-button>\r\n        </div>\r\n\r\n        <!-- 数据表格 -->\r\n        <div class=\"table\">\r\n            <el-table\r\n                :data=\"tableData\"\r\n                stripe\r\n                @selection-change=\"handleSelectionChange\"\r\n                style=\"width: 100%\"\r\n                :row-class-name=\"tableRowClassName\"> <!-- 添加row-class-name -->\r\n                <!-- 选择列 -->\r\n                <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n\r\n                <!-- 序号列 -->\r\n                <el-table-column prop=\"id\" label=\"序号\" width=\"70\" align=\"center\" sortable></el-table-column>\r\n\r\n                <!-- 用户ID列 -->\r\n                <el-table-column prop=\"sfUserId\" label=\"用户ID\" align=\"center\"></el-table-column>\r\n\r\n                <!-- 评论问题列 -->\r\n                <el-table-column prop=\"sfQuestion\" label=\"评论问题\" align=\"center\"></el-table-column>\r\n\r\n                <!-- 评论回复列 -->\r\n                <el-table-column prop=\"reply\" label=\"评论回复\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                        <span v-if=\"scope.row.reply\">{{ scope.row.reply }}</span>\r\n                        <span v-else style=\"color: #f56c6c;\">暂无回复</span>\r\n                    </template>\r\n                </el-table-column>\r\n\r\n                <!-- 评论图片列 -->\r\n                <el-table-column prop=\"sfImage\" label=\"评论图片\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                        <el-image\r\n                            v-if=\"scope.row.sfImage\"\r\n                            :src=\"scope.row.sfImage\"\r\n                            fit=\"cover\"\r\n                            style=\"width: 50px; height: 50px; cursor: pointer;\"\r\n                            @click=\"previewImage(scope.row.sfImage)\">\r\n                        </el-image>\r\n                        <span v-else>暂无图片</span>\r\n                    </template>\r\n                </el-table-column>\r\n\r\n                <!-- 评论时间列 -->\r\n                <el-table-column prop=\"sfLeaveTime\" label=\"评论时间\" align=\"center\"></el-table-column>\r\n\r\n                <!-- 回复时间列 -->\r\n                <el-table-column prop=\"sfReplyTime\" label=\"回复时间\" align=\"center\"></el-table-column>\r\n\r\n                <!-- 操作列 -->\r\n                <el-table-column label=\"操作\" align=\"center\" width=\"180\">\r\n                    <template slot-scope=\"scope\">\r\n                        <el-button size=\"mini\" type=\"primary\" plain @click=\"handleEdit(scope.row)\">回复</el-button>\r\n                        <el-button size=\"mini\" type=\"danger\" plain @click=\"del(scope.row.id)\">删除</el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 无数据提示 -->\r\n            <el-empty v-if=\"tableData.length === 0\" description=\"暂无评论\"></el-empty>\r\n\r\n            <!-- 分页器 -->\r\n            <div class=\"pagination\">\r\n                <el-pagination\r\n                    background\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageNum\"\r\n                    :page-sizes=\"[5, 10, 20]\"\r\n                    :page-size=\"pageSize\"\r\n                    layout=\"total, prev, pager, next\"\r\n                    :total=\"total\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 弹窗 -->\r\n        <el-dialog title=\"评论管理\" :visible.sync=\"fromVisible\" width=\"40%\" :close-on-click-modal=\"false\" destroy-on-close>\r\n            <el-form :model=\"form\" label-width=\"100px\" style=\"padding-right: 50px\" :rules=\"rules\" ref=\"formRef\">\r\n                <el-form-item label=\"用户ID\" prop=\"sfUserId\">\r\n                    <el-input v-model=\"form.sfUserId\" placeholder=\"用户ID\" class=\"input-field\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"评论问题\" prop=\"sfQuestion\">\r\n                    <el-input v-model=\"form.sfQuestion\" placeholder=\"评论问题\" class=\"input-field\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"评论回复\" prop=\"reply\">\r\n                    <el-input v-model=\"form.reply\" placeholder=\"评论回复\" class=\"input-field\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"评论图片\" prop=\"sfImage\">\r\n                    <el-upload\r\n                        class=\"avatar-uploader\"\r\n                        :action=\"$baseUrl + '/files/upload'\"\r\n                        :show-file-list=\"false\"\r\n                        :on-success=\"handleImageSuccess\"\r\n                        :before-upload=\"beforeUpload\">\r\n                        <el-button type=\"primary\" class=\"upload-btn\">点击上传图片</el-button>\r\n                    </el-upload>\r\n                    <!-- 显示上传成功后的图片 -->\r\n                    <el-image\r\n                        v-if=\"form.sfImage\"\r\n                        class=\"uploaded-image\"\r\n                        :src=\"form.sfImage\"\r\n                        :preview-src-list=\"[form.sfImage]\">\r\n                    </el-image>\r\n                </el-form-item>\r\n                <el-form-item label=\"评论时间\" prop=\"sfLeaveTime\">\r\n                    <el-input v-model=\"form.sfLeaveTime\" placeholder=\"评论时间\" disabled></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"回复时间\" prop=\"sfReplyTime\">\r\n                    <el-input v-model=\"form.sfReplyTime\" placeholder=\"回复时间\" disabled></el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"fromVisible = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 图片预览组件（隐藏） -->\r\n        <el-image ref=\"imagePreview\" style=\"display: none;\"></el-image>\r\n\r\n        <!-- 提交成功提示 -->\r\n        <el-dialog :visible.sync=\"successVisible\" title=\"评论提交成功\" width=\"30%\" :close-on-click-modal=\"false\" destroy-on-close>\r\n            <p>用户评论已经回复</p>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"successVisible = false\">确认</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Leavemess\",\r\n    data() {\r\n        return {\r\n            tableData: [],  // 所有的数据\r\n            pageNum: 1,     // 当前的页码\r\n            pageSize: 10,   // 每页显示的个数\r\n            total: 0,\r\n            sfQuestion: '',   // 搜索关键字，针对评论问题\r\n            fromVisible: false,\r\n            form: {},\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n            rules: {\r\n                sfUserId: [\r\n                    { required: true, message: '请输入用户ID', trigger: 'blur' },\r\n                ],\r\n                sfQuestion: [\r\n                    { required: true, message: '请输入评论问题', trigger: 'blur' },\r\n                ],\r\n                reply: [\r\n                    { required: true, message: '请输入评论回复', trigger: 'blur' },\r\n                ],\r\n                sfImage: [\r\n                    { type: 'url', message: '请输入有效的图片URL', trigger: 'blur' },\r\n                ],\r\n            },\r\n            ids: [],\r\n            successVisible: false, // 控制成功提交提示的显示\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)\r\n    },\r\n    methods: {\r\n        /**\r\n         * 根据行数据返回对应的类名\r\n         * @param {Object} row - 当前行数据\r\n         * @param {Number} index - 行索引\r\n         * @returns {String} - 类名\r\n         */\r\n        tableRowClassName(row, index) {\r\n            return !row.reply ? 'no-reply' : '';\r\n        },\r\n        /**\r\n         * 新增数据\r\n         */\r\n        handleAdd() {\r\n            this.form = {}  // 新增数据的时候清空数据\r\n            this.fromVisible = true   // 打开弹窗\r\n        },\r\n        /**\r\n         * 编辑数据（回复评论）\r\n         * @param {Object} row - 当前行数据\r\n         */\r\n        handleEdit(row) {\r\n            this.form = JSON.parse(JSON.stringify(row))  // 深拷贝数据\r\n            this.fromVisible = true   // 打开弹窗\r\n        },\r\n        /**\r\n         * 保存数据（新增或更新）\r\n         */\r\n        save() {\r\n            this.$refs.formRef.validate((valid) => {\r\n                if (valid) {\r\n                    this.$request({\r\n                        url: this.form.id ? '/leavemess/update' : '/leavemess/add',\r\n                        method: this.form.id ? 'PUT' : 'POST',\r\n                        data: this.form\r\n                    }).then(res => {\r\n                        if (res.code === '200') {  // 表示成功保存\r\n                            this.$message.success('保存成功')\r\n                            this.load(1)\r\n                            this.fromVisible = false\r\n                            this.successVisible = true // 显示提交成功弹窗\r\n                        } else {\r\n                            this.$message.error(res.msg)  // 弹出错误的信息\r\n                        }\r\n                    }).catch(() => {\r\n                        this.$message.error('请求失败')\r\n                    })\r\n                }\r\n            })\r\n        },\r\n        /**\r\n         * 单个删除\r\n         * @param {Number} id - 数据ID\r\n         */\r\n        del(id) {\r\n            this.$confirm('您确定删除吗？', '确认删除', {type: \"warning\"}).then(() => {\r\n                this.$request.delete('/leavemess/delete/' + id).then(res => {\r\n                    if (res.code === '200') {   // 表示操作成功\r\n                        this.$message.success('删除成功')\r\n                        this.load(1)\r\n                    } else {\r\n                        this.$message.error(res.msg)  // 弹出错误的信息\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('删除失败')\r\n                })\r\n            }).catch(() => {\r\n                // 用户取消删除操作\r\n            })\r\n        },\r\n        /**\r\n         * 处理表格选中的数据\r\n         * @param {Array} rows - 当前选中的所有行数据\r\n         */\r\n        handleSelectionChange(rows) {\r\n            this.ids = rows.map(v => v.id)\r\n        },\r\n        /**\r\n         * 批量删除\r\n         */\r\n        delBatch() {\r\n            if (!this.ids.length) {\r\n                this.$message.warning('请选择要删除的数据')\r\n                return\r\n            }\r\n            this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: \"warning\"}).then(() => {\r\n                this.$request.delete('/leavemess/delete/batch', { data: this.ids }).then(res => {\r\n                    if (res.code === '200') {   // 表示操作成功\r\n                        this.$message.success('批量删除成功')\r\n                        this.load(1)\r\n                        this.ids = []  // 清空已删除的ID\r\n                    } else {\r\n                        this.$message.error(res.msg)  // 弹出错误的信息\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('批量删除失败')\r\n                })\r\n            }).catch(() => {\r\n                // 用户取消删除操作\r\n            })\r\n        },\r\n        /**\r\n         * 加载数据（分页查询）\r\n         * @param {Number} pageNum - 要加载的页码\r\n         */\r\n        load(pageNum) {\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/leavemess/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n                    sfQuestion: this.sfQuestion, // 修改为 sfQuestion\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list || []\r\n                    this.total = res.data?.total || 0\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('加载数据失败')\r\n            })\r\n        },\r\n        /**\r\n         * 重置搜索条件并重新加载数据\r\n         */\r\n        reset() {\r\n            this.sfQuestion = ''\r\n            this.load(1)\r\n        },\r\n        /**\r\n         * 处理分页器页码变化\r\n         * @param {Number} pageNum - 当前选择的页码\r\n         */\r\n        handleCurrentChange(pageNum) {\r\n            this.load(pageNum)\r\n        },\r\n        /**\r\n         * 处理图片上传成功\r\n         * @param {Object} response - 上传成功的响应\r\n         * @param {File} file - 上传的文件\r\n         * @param {Array} fileList - 文件列表\r\n         */\r\n        handleImageSuccess(response, file, fileList) {\r\n            if (response.code === '200') {\r\n                this.form.sfImage = response.data.url || response.data;  // 根据后端返回的数据结构调整\r\n                this.$message.success('图片上传成功')\r\n            } else {\r\n                this.$message.error('图片上传失败')\r\n            }\r\n        },\r\n        /**\r\n         * 图片上传前的验证\r\n         * @param {File} file - 上传的文件\r\n         * @returns {Boolean} - 是否允许上传\r\n         */\r\n        beforeUpload(file) {\r\n            const isImage = file.type.startsWith('image/')\r\n            const isLt2M = file.size / 1024 / 1024 < 2\r\n\r\n            if (!isImage) {\r\n                this.$message.error('上传图片只能是 JPG/PNG 格式!')\r\n            }\r\n            if (!isLt2M) {\r\n                this.$message.error('上传图片大小不能超过 2MB!')\r\n            }\r\n            return isImage && isLt2M\r\n        },\r\n        /**\r\n         * 预览图片\r\n         * @param {String} url - 图片URL\r\n         */\r\n        previewImage(url) {\r\n            this.$refs.imagePreview.handlePreview(url)\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.complaint-container {\r\n    background-color: #f4f7fc;\r\n    min-height: 10vh;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    padding: 20px;\r\n}\r\n\r\n.form-container {\r\n    background-color: #ffffff;\r\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n    border-radius: 8px;\r\n    width: 100%;\r\n    max-width: 800px;  /* 调整宽度，适应表单 */\r\n    padding: 40px;  /* 增加表单内边距 */\r\n}\r\n\r\n.input-field {\r\n    border-radius: 6px;\r\n    background-color: #f9f9f9;\r\n    border: 1px solid #e4e7ed;\r\n}\r\n\r\n.input-field:focus {\r\n    border-color: #409eff;\r\n}\r\n\r\n.upload-btn {\r\n    background-color: #409eff;\r\n    border-color: #409eff;\r\n    color: white;\r\n    border-radius: 4px;\r\n}\r\n\r\n.upload-btn:hover {\r\n    background-color: #66b1ff;\r\n}\r\n\r\n.uploaded-image {\r\n    width: 100px;\r\n    height: 100px;\r\n    margin-top: 10px;\r\n    border-radius: 8px;\r\n}\r\n\r\n.form-footer {\r\n    text-align: right;\r\n    margin-top: 20px;\r\n}\r\n\r\n.submit-btn {\r\n    background-color: #409eff;\r\n    border-color: #409eff;\r\n    color: white;\r\n    border-radius: 4px;\r\n    width: 120px;  /* 调整按钮宽度 */\r\n}\r\n\r\n.submit-btn:hover {\r\n    background-color: #66b1ff;\r\n}\r\n\r\n.dialog-footer {\r\n    text-align: right;\r\n}\r\n\r\n.search {\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.operation {\r\n    margin-bottom: 20px;\r\n    display: flex;\r\n    gap: 10px;\r\n}\r\n\r\n.table {\r\n    overflow-x: auto;\r\n}\r\n\r\n.pagination {\r\n    margin-top: 20px;\r\n    text-align: right;\r\n}\r\n\r\n.el-empty {\r\n    margin-top: 20px;\r\n}\r\n\r\n/* 新增样式 */\r\n.no-reply {\r\n    background-color: #ffe6e6; /* 淡红色背景 */\r\n}\r\n\r\n.no-reply .el-table__cell {\r\n    color: #f56c6c; /* 深红色文本 */\r\n}\r\n</style>\r\n"], "mappings": ";AAqJA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MACAC,UAAA;MAAA;MACAC,WAAA;MACAC,IAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,UAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,KAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,OAAA,GACA;UAAAC,IAAA;UAAAJ,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAI,GAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACA;AACA;AACA;AACA;AACA;AACA;IACAC,kBAAAC,GAAA,EAAAC,KAAA;MACA,QAAAD,GAAA,CAAAT,KAAA;IACA;IACA;AACA;AACA;IACAW,UAAA;MACA,KAAAtB,IAAA;MACA,KAAAD,WAAA;IACA;IACA;AACA;AACA;AACA;IACAwB,WAAAH,GAAA;MACA,KAAApB,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAsB,SAAA,CAAAJ,GAAA;MACA,KAAArB,WAAA;IACA;IACA;AACA;AACA;IACA0B,KAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,QAAA;YACAC,GAAA,OAAA/B,IAAA,CAAAgC,EAAA;YACAC,MAAA,OAAAjC,IAAA,CAAAgC,EAAA;YACAvC,IAAA,OAAAO;UACA,GAAAkC,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cAAA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA,KAAArB,IAAA;cACA,KAAAlB,WAAA;cACA,KAAAgB,cAAA;YACA;cACA,KAAAsB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;YACA;UACA,GAAAC,KAAA;YACA,KAAAJ,QAAA,CAAAE,KAAA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAG,IAAAV,EAAA;MACA,KAAAW,QAAA;QAAA9B,IAAA;MAAA,GAAAqB,IAAA;QACA,KAAAJ,QAAA,CAAAc,MAAA,wBAAAZ,EAAA,EAAAE,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAArB,IAAA;UACA;YACA,KAAAoB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA,GAAAC,KAAA;UACA,KAAAJ,QAAA,CAAAE,KAAA;QACA;MACA,GAAAE,KAAA;QACA;MAAA,CACA;IACA;IACA;AACA;AACA;AACA;IACAI,sBAAAC,IAAA;MACA,KAAAhC,GAAA,GAAAgC,IAAA,CAAAC,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAhB,EAAA;IACA;IACA;AACA;AACA;IACAiB,SAAA;MACA,UAAAnC,GAAA,CAAAoC,MAAA;QACA,KAAAb,QAAA,CAAAc,OAAA;QACA;MACA;MACA,KAAAR,QAAA;QAAA9B,IAAA;MAAA,GAAAqB,IAAA;QACA,KAAAJ,QAAA,CAAAc,MAAA;UAAAnD,IAAA,OAAAqB;QAAA,GAAAoB,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAArB,IAAA;YACA,KAAAH,GAAA;UACA;YACA,KAAAuB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA,GAAAC,KAAA;UACA,KAAAJ,QAAA,CAAAE,KAAA;QACA;MACA,GAAAE,KAAA;QACA;MAAA,CACA;IACA;IACA;AACA;AACA;AACA;IACAxB,KAAAtB,OAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAmC,QAAA,CAAAsB,GAAA;QACAC,MAAA;UACA1D,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAE,UAAA,OAAAA,UAAA;QACA;MACA,GAAAoC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAA1C,SAAA,GAAAyC,GAAA,CAAA1C,IAAA,EAAA6D,IAAA;UACA,KAAAzD,KAAA,GAAAsC,GAAA,CAAA1C,IAAA,EAAAI,KAAA;QACA;UACA,KAAAwC,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAC,KAAA;QACA,KAAAJ,QAAA,CAAAE,KAAA;MACA;IACA;IACA;AACA;AACA;IACAgB,MAAA;MACA,KAAAzD,UAAA;MACA,KAAAmB,IAAA;IACA;IACA;AACA;AACA;AACA;IACAuC,oBAAA7D,OAAA;MACA,KAAAsB,IAAA,CAAAtB,OAAA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACA8D,mBAAAC,QAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,IAAAF,QAAA,CAAAtB,IAAA;QACA,KAAApC,IAAA,CAAAY,OAAA,GAAA8C,QAAA,CAAAjE,IAAA,CAAAsC,GAAA,IAAA2B,QAAA,CAAAjE,IAAA;QACA,KAAA4C,QAAA,CAAAC,OAAA;MACA;QACA,KAAAD,QAAA,CAAAE,KAAA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAsB,aAAAF,IAAA;MACA,MAAAG,OAAA,GAAAH,IAAA,CAAA9C,IAAA,CAAAkD,UAAA;MACA,MAAAC,MAAA,GAAAL,IAAA,CAAAM,IAAA;MAEA,KAAAH,OAAA;QACA,KAAAzB,QAAA,CAAAE,KAAA;MACA;MACA,KAAAyB,MAAA;QACA,KAAA3B,QAAA,CAAAE,KAAA;MACA;MACA,OAAAuB,OAAA,IAAAE,MAAA;IACA;IACA;AACA;AACA;AACA;IACAE,aAAAnC,GAAA;MACA,KAAAL,KAAA,CAAAyC,YAAA,CAAAC,aAAA,CAAArC,GAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}