<template>
    <div class="leavemess-container">
        <!-- 评论表单区域 -->
        <el-form
            :model="form"
            label-width="100px"
            class="form-container"
            :rules="rules"
            ref="formRef">
            <!-- 问题输入框 -->
            <el-form-item label="问题" prop="sfQuestion">
                <el-input
                    v-model="form.sfQuestion"
                    placeholder="请输入您的问题"
                    type="textarea"
                    :rows="10"
                    class="input-field large-input">
                </el-input>
            </el-form-item>

            <!-- 图片上传 -->
            <el-form-item label="图片" prop="sfImage">
                <el-upload
                    class="avatar-uploader"
                    :action="$baseUrl + '/files/upload'"
                    :show-file-list="false"
                    :on-success="handleImageSuccess"
                    :before-upload="beforeUpload">
                    <el-button type="primary" class="upload-btn">点击上传图片</el-button>
                </el-upload>
                <!-- 显示上传成功后的图片 -->
                <el-image
                    v-if="form.sfImage"
                    class="uploaded-image"
                    :src="form.sfImage"
                    :preview-src-list="[form.sfImage]">
                </el-image>
            </el-form-item>

            <!-- 提交按钮 -->
            <div class="form-footer">
                <el-button type="primary" class="submit-btn" @click="save" :loading="loading">提交留言</el-button>
            </div>
        </el-form>

        <!-- 提交成功提示 -->
        <el-dialog
            :visible.sync="successVisible"
            title="评论提交成功"
            width="30%"
            :close-on-click-modal="false"
            destroy-on-close>
            <p>您的留言已成功提交，我们会尽快处理。</p>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="successVisible = false">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "Leavemess",
    data() {
        return {
            successVisible: false,
            loading: false,
            form: {
                sfUserId: '',
                sfQuestion: '',
                reply: '',
                sfImage: '',
                sfLeaveTime: '',
                sfReplyTime: '',
            },
            rules: {
                sfQuestion: [
                    { required: true, message: '请输入评论问题', trigger: 'blur' },
                ],
            },
        }
    },
    methods: {
        save() {
            this.$refs.formRef.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    this.$request({
                        url: '/leavemess/add',
                        method: 'POST',
                        data: this.form
                    }).then(res => {
                        if (res.code === '200') {
                            this.successVisible = true;
                            this.form = {
                                sfUserId: '',
                                sfQuestion: '',
                                reply: '',
                                sfImage: '',
                                sfLeaveTime: '',
                                sfReplyTime: '',
                            }
                            this.$refs.formRef.resetFields();
                        } else {
                            this.$message.error(res.msg);
                        }
                    }).catch(() => {
                        this.$message.error('请求失败');
                    }).finally(() => {
                        this.loading = false;
                    })
                }
            })
        },
        handleImageSuccess(response) {
            if (response.code === '200') {
                this.form.sfImage = response.data.url || response.data;
                this.$message.success('图片上传成功');
            } else {
                this.$message.error('图片上传失败');
            }
        },
        beforeUpload(file) {
            const isImage = file.type.startsWith('image/');
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isImage) {
                this.$message.error('上传图片只能是 JPG/PNG 格式!');
            }
            if (!isLt2M) {
                this.$message.error('上传图片大小不能超过 2MB!');
            }
            return isImage && isLt2M;
        },
    }
}
</script>

<style scoped>
.leavemess-container {
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.form-container {
    background: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    width: 100%;
    max-width: 720px;
    padding: 24px;
    transition: box-shadow 0.3s, transform 0.3s;
    border: 1px solid #ebebeb;
}

.form-container:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.el-form-item__label {
    color: #333;
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 8px;
}

.input-field {
    border-radius: 8px;
    background: #fdfdfd;
    border: 1px solid #dcdfe6;
    transition: border-color 0.3s, box-shadow 0.3s;
    font-size: 14px;
    padding: 8px 12px;
}

.input-field:focus {
    border-color: #409eff;
    box-shadow: 0 0 5px rgba(64, 158, 255, 0.4);
    outline: none;
}

.large-input {
    min-height: 120px;
    resize: none;
    line-height: 1.6;
}

.upload-btn {
    display: inline-block;
    background-color: #409eff;
    border: 1px solid #409eff;
    color: #fff;
    font-size: 14px;
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.3s;
}

.upload-btn:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
    box-shadow: 0 2px 6px rgba(102, 177, 255, 0.3);
}

.uploaded-image {
    display: block;
    margin-top: 10px;
    border-radius: 8px;
    border: 1px solid #ebebeb;
    width: 100px;
    height: 100px;
    object-fit: cover;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.submit-btn {
    background: linear-gradient(90deg, #ff7e5f, #feb47b);
    border: none;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    border-radius: 8px;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.3s;
    width: 140px;
    margin-left: auto;
    display: block;
}

.submit-btn:hover {
    background: linear-gradient(90deg, #feb47b, #ff7e5f);
    box-shadow: 0 4px 10px rgba(255, 126, 95, 0.4);
}

.dialog-footer {
    text-align: right;
    padding-top: 10px;
}

.form-footer {
    text-align: right;
    margin-top: 20px;
}
</style>
