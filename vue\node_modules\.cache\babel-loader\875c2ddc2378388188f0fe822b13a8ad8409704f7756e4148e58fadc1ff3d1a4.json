{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.find.js\";\nexport default {\n  name: \"GoodsList\",\n  data() {\n    return {\n      tableData: [],\n      // 商品数据\n      pageNum: 1,\n      // 当前页码\n      pageSize: 12,\n      // 每页12条\n      total: 0,\n      // 总数\n      name: null,\n      // 搜索关键词\n\n      // 分类相关\n      categories: [],\n      // 分类列表\n      selectedCategoryId: null,\n      // 选中的分类ID\n      categoriesLoading: false,\n      // 分类加载状态\n      goodsLoading: false,\n      // 商品加载状态\n      loadingRequest: null,\n      // 当前加载请求\n      searchTimer: null,\n      // 搜索防抖定时器\n      sidebarCollapsed: false,\n      // 侧边栏折叠状态\n\n      detailVisible: false,\n      // 详情弹窗显示\n      currentGoods: null,\n      // 当前查看的商品\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      // 当前登录用户\n\n      // 购物车相关\n      cartDialogVisible: false,\n      cartForm: {\n        goodsId: null,\n        goodsName: '',\n        goodsPrice: 0,\n        quantity: 1,\n        remark: ''\n      }\n    };\n  },\n  created() {\n    this.load(1);\n    this.loadCategories();\n  },\n  beforeDestroy() {\n    // 清理定时器\n    if (this.searchTimer) {\n      clearTimeout(this.searchTimer);\n    }\n\n    // 取消未完成的请求\n    if (this.loadingRequest) {\n      this.loadingRequest.cancel && this.loadingRequest.cancel();\n    }\n  },\n  methods: {\n    // 格式化时间为年月日时分秒\n    formatDateTime(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      const hours = String(d.getHours()).padStart(2, '0');\n      const minutes = String(d.getMinutes()).padStart(2, '0');\n      const seconds = String(d.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n    load(pageNum) {\n      // 加载商品数据\n      if (pageNum) this.pageNum = pageNum;\n      this.goodsLoading = true;\n      this.$request.get('/foods/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          name: this.name,\n          categoryId: this.selectedCategoryId\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list || [];\n          this.total = res.data?.total || 0;\n        } else {\n          this.$message.error(res.msg || '加载商品失败，请重试');\n        }\n      }).catch(err => {\n        console.error('加载商品失败:', err);\n        this.$message.error('网络异常，请检查网络连接后重试');\n      }).finally(() => {\n        this.goodsLoading = false;\n      });\n    },\n    showDetail(item) {\n      // 显示商品详情\n      this.currentGoods = item;\n      this.detailVisible = true;\n    },\n    // 显示购物车弹窗\n    showCartDialog(goods) {\n      // 检查用户是否登录\n      if (!this.user.id) {\n        this.$message.warning('请先登录后再加入购物车');\n        this.$router.push('/login');\n        return;\n      }\n      this.cartForm = {\n        goodsId: goods.id,\n        goodsName: goods.name,\n        goodsPrice: parseFloat(goods.sfPrice) || 0,\n        quantity: 1,\n        remark: ''\n      };\n      this.cartDialogVisible = true;\n    },\n    // 确认加入购物车\n    confirmAddToCart() {\n      const cartData = {\n        sfUserName: this.user.name || '匿名用户',\n        sfUserId: this.user.id || 0,\n        sfProductIds: this.cartForm.goodsId.toString(),\n        status: '购物车',\n        // 明确标识为购物车状态\n        sfCartStatus: '已加入购物车',\n        // 购物车标识\n        sfTotalPrice: (this.cartForm.goodsPrice * this.cartForm.quantity).toFixed(2),\n        // 小计金额\n        sfQuantity: this.cartForm.quantity,\n        // 商品数量\n        sfRemark: this.cartForm.remark || '',\n        // 用户输入的备注\n        sfCreateTime: this.formatDateTime(new Date())\n      };\n\n      // 调用购物车专用接口\n      this.$request.post('/dingdan/addToCart', cartData).then(res => {\n        if (res.code === '200') {\n          this.$message.success(`已将 ${this.cartForm.quantity} 件商品加入购物车！`);\n          this.cartDialogVisible = false;\n          this.resetCartForm();\n        } else {\n          this.$message.error(res.msg || '加入购物车失败');\n        }\n      }).catch(err => {\n        console.error('加入购物车失败:', err);\n        this.$message.error('加入购物车失败，请重试');\n      });\n    },\n    // 重置购物车表单\n    resetCartForm() {\n      this.cartForm = {\n        goodsId: null,\n        goodsName: '',\n        goodsPrice: 0,\n        quantity: 1,\n        remark: ''\n      };\n    },\n    handleCurrentChange(pageNum) {\n      // 分页变化\n      this.load(pageNum);\n    },\n    // 加载分类数据\n    loadCategories() {\n      this.categoriesLoading = true;\n      this.$request.get('/category/selectEnabled').then(res => {\n        if (res.code === '200') {\n          this.categories = res.data || [];\n        } else {\n          console.error('加载分类失败:', res.msg);\n          this.$message.warning('分类加载失败，但不影响商品浏览');\n        }\n      }).catch(err => {\n        console.error('加载分类失败:', err);\n        this.$message.warning('分类加载失败，但不影响商品浏览');\n      }).finally(() => {\n        this.categoriesLoading = false;\n      });\n    },\n    // 选择分类\n    selectCategory(categoryId) {\n      if (this.selectedCategoryId === categoryId) {\n        return; // 避免重复选择\n      }\n\n      // 如果正在加载，取消之前的请求\n      if (this.loadingRequest) {\n        this.loadingRequest.cancel && this.loadingRequest.cancel();\n      }\n      this.selectedCategoryId = categoryId;\n\n      // 提供用户反馈\n      const categoryName = categoryId === null ? '全部商品' : this.categories.find(c => c.id === categoryId)?.name || '未知分类';\n\n      // 使用 Toast 提示而不是 Message，避免干扰用户\n      this.$message({\n        message: `正在加载${categoryName}...`,\n        type: 'info',\n        duration: 1000\n      });\n      this.load(1); // 重新加载第一页数据\n    },\n    // 搜索输入防抖处理\n    handleSearchInput() {\n      // 清除之前的定时器\n      if (this.searchTimer) {\n        clearTimeout(this.searchTimer);\n      }\n\n      // 设置新的定时器，500ms后执行搜索\n      this.searchTimer = setTimeout(() => {\n        this.load(1);\n      }, 500);\n    },\n    // 切换侧边栏\n    toggleSidebar() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "categories", "selectedCategoryId", "categoriesLoading", "goodsLoading", "loadingRequest", "searchTimer", "sidebarCollapsed", "detailVisible", "currentGoods", "user", "JSON", "parse", "localStorage", "getItem", "cartDialogVisible", "cartForm", "goodsId", "goodsName", "goodsPrice", "quantity", "remark", "created", "load", "loadCategories", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "cancel", "methods", "formatDateTime", "date", "d", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "$request", "get", "params", "categoryId", "then", "res", "code", "list", "$message", "error", "msg", "catch", "err", "console", "finally", "showDetail", "item", "showCartDialog", "goods", "id", "warning", "$router", "push", "parseFloat", "sfPrice", "confirmAddToCart", "cartData", "sfUserName", "sfUserId", "sfProductIds", "toString", "status", "sfCartStatus", "sfTotalPrice", "toFixed", "sfQuantity", "sfRemark", "sfCreateTime", "post", "success", "resetCartForm", "handleCurrentChange", "selectCategory", "categoryName", "find", "c", "message", "type", "duration", "handleSearchInput", "setTimeout", "toggleSidebar"], "sources": ["src/views/front/Home.vue"], "sourcesContent": ["<template>\r\n    <div class=\"home-container\">\r\n        <!-- 搜索栏 -->\r\n        <div class=\"search-section\">\r\n            <div class=\"search-container\">\r\n                <el-input\r\n                    placeholder=\"搜索您想要的美食...\"\r\n                    v-model=\"name\"\r\n                    class=\"search-input\"\r\n                    clearable\r\n                    size=\"large\"\r\n                    @keyup.enter.native=\"load(1)\"\r\n                    @input=\"handleSearchInput\"\r\n                >\r\n                    <el-button\r\n                        slot=\"append\"\r\n                        icon=\"el-icon-search\"\r\n                        @click=\"load(1)\"\r\n                        class=\"search-btn\"\r\n                    ></el-button>\r\n                </el-input>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 主要内容区 -->\r\n        <div class=\"main-content\">\r\n            <!-- 左侧分类菜单 -->\r\n            <div class=\"sidebar-container\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\r\n                <!-- 菜单切换按钮 -->\r\n                <div class=\"sidebar-toggle\" @click=\"toggleSidebar\">\r\n                    <i :class=\"sidebarCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'\"></i>\r\n                </div>\r\n                \r\n                <!-- 分类菜单 -->\r\n                <div class=\"category-sidebar\">\r\n                    <div class=\"sidebar-header\">\r\n                        <i class=\"el-icon-menu\"></i>\r\n                        <span v-show=\"!sidebarCollapsed\" class=\"sidebar-title\">商品分类</span>\r\n                    </div>\r\n                    \r\n                    <div v-if=\"categoriesLoading\" class=\"category-loading\">\r\n                        <i class=\"el-icon-loading\"></i>\r\n                        <span v-show=\"!sidebarCollapsed\">加载中...</span>\r\n                    </div>\r\n                    \r\n                    <div v-else class=\"category-menu\">\r\n                        <!-- 全部分类 -->\r\n                        <div \r\n                            class=\"category-menu-item\"\r\n                            :class=\"{ 'active': selectedCategoryId === null }\"\r\n                            @click=\"selectCategory(null)\"\r\n                        >\r\n                            <div class=\"menu-item-icon\">\r\n                                <i class=\"el-icon-s-grid\"></i>\r\n                            </div>\r\n                            <span v-show=\"!sidebarCollapsed\" class=\"menu-item-text\">全部商品</span>\r\n                            <div v-show=\"selectedCategoryId === null && !sidebarCollapsed\" class=\"menu-item-indicator\"></div>\r\n                        </div>\r\n                        \r\n                        <!-- 具体分类 -->\r\n                        <div \r\n                            class=\"category-menu-item\"\r\n                            :class=\"{ 'active': selectedCategoryId === category.id }\"\r\n                            v-for=\"category in categories\"\r\n                            :key=\"category.id\"\r\n                            @click=\"selectCategory(category.id)\"\r\n                        >\r\n                            <div class=\"menu-item-icon\">\r\n                                <el-image \r\n                                    v-if=\"category.icon\" \r\n                                    :src=\"category.icon\" \r\n                                    fit=\"cover\"\r\n                                    class=\"category-menu-image\"\r\n                                ></el-image>\r\n                                <i v-else class=\"el-icon-dish\"></i>\r\n                            </div>\r\n                            <span v-show=\"!sidebarCollapsed\" class=\"menu-item-text\">{{ category.name }}</span>\r\n                            <div v-show=\"selectedCategoryId === category.id && !sidebarCollapsed\" class=\"menu-item-indicator\"></div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 右侧内容区域 -->\r\n            <div class=\"content-area\" :class=\"{ 'content-expanded': sidebarCollapsed }\">\r\n                <!-- 商品展示区 -->\r\n                <div class=\"content-section\">\r\n                    <div class=\"section-header\">\r\n                        <h2 class=\"section-title\">精选美食</h2>\r\n                        <p class=\"section-subtitle\">为您精心挑选的优质美食</p>\r\n                    </div>\r\n                    \r\n                    <div v-if=\"goodsLoading\" class=\"loading-state\">\r\n                        <i class=\"el-icon-loading\"></i>\r\n                        <p>加载中...</p>\r\n                    </div>\r\n                    \r\n                    <div v-else-if=\"tableData.length === 0\" class=\"empty-state\">\r\n                        <i class=\"el-icon-dish\"></i>\r\n                        <h3>暂无商品</h3>\r\n                        <p v-if=\"selectedCategoryId\">该分类下暂无商品，试试其他分类吧</p>\r\n                        <p v-else-if=\"name\">没有找到相关商品，试试其他关键词吧</p>\r\n                        <p v-else>暂无商品信息，敬请期待</p>\r\n                    </div>\r\n                    \r\n                    <div v-else class=\"goods-grid\">\r\n                        <div\r\n                            class=\"goods-card\"\r\n                            v-for=\"item in tableData\"\r\n                            :key=\"item.id\"\r\n                            @click=\"showDetail(item)\"\r\n                        >\r\n                            <div class=\"card-image-container\">\r\n                                <el-image\r\n                                    :src=\"item.sfImage\"\r\n                                    fit=\"cover\"\r\n                                    class=\"card-image\"\r\n                                    lazy\r\n                                    :placeholder=\"'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7liqDovb3kuK08L3RleHQ+Cjwvc3ZnPg=='\"\r\n                                >\r\n                                    <div slot=\"error\" class=\"image-error\">\r\n                                        <i class=\"el-icon-picture-outline\"></i>\r\n                                        <span>图片加载失败</span>\r\n                                    </div>\r\n                                </el-image>\r\n                                <div class=\"card-overlay\">\r\n                                    <el-button\r\n                                        type=\"primary\"\r\n                                        size=\"small\"\r\n                                        circle\r\n                                        icon=\"el-icon-view\"\r\n                                        class=\"view-btn\"\r\n                                    ></el-button>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"card-content\">\r\n                                <h3 class=\"card-title\">{{ item.name }}</h3>\r\n                                <div class=\"card-price\">\r\n                                    <span class=\"price-symbol\">¥</span>\r\n                                    <span class=\"price-number\">{{ item.sfPrice }}</span>\r\n                                </div>\r\n                                <div class=\"card-actions\">\r\n                                    <el-button\r\n                                        type=\"primary\"\r\n                                        size=\"small\"\r\n                                        @click.stop=\"showCartDialog(item)\"\r\n                                        class=\"cart-btn\"\r\n                                    >\r\n                                        <i class=\"el-icon-shopping-cart-2\"></i>\r\n                                        加入购物车\r\n                                    </el-button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 分页 -->\r\n                <div class=\"pagination-section\">\r\n                    <el-pagination\r\n                        background\r\n                        @current-change=\"handleCurrentChange\"\r\n                        :current-page=\"pageNum\"\r\n                        :page-size=\"pageSize\"\r\n                        layout=\"prev, pager, next\"\r\n                        :total=\"total\"\r\n                        :pager-count=\"5\"\r\n                        prev-text=\"上一页\"\r\n                        next-text=\"下一页\"\r\n                        class=\"custom-pagination\"\r\n                    >\r\n                    </el-pagination>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 商品详情弹窗 -->\r\n        <el-dialog\r\n            :visible.sync=\"detailVisible\"\r\n            width=\"70%\"\r\n            top=\"5vh\"\r\n            custom-class=\"detail-dialog\"\r\n            :close-on-click-modal=\"false\"\r\n        >\r\n            <div class=\"detail-container\" v-if=\"currentGoods\">\r\n                <div class=\"detail-left\">\r\n                    <div class=\"detail-image-container\">\r\n                        <el-image\r\n                            :src=\"currentGoods.sfImage\"\r\n                            fit=\"contain\"\r\n                            class=\"detail-image\"\r\n                        ></el-image>\r\n                    </div>\r\n                </div>\r\n                <div class=\"detail-right\">\r\n                    <div class=\"detail-header\">\r\n                        <h2 class=\"detail-title\">{{ currentGoods.name }}</h2>\r\n                        <div class=\"detail-price\">\r\n                            <span class=\"price-symbol\">¥</span>\r\n                            <span class=\"price-number\">{{ currentGoods.sfPrice }}</span>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div class=\"detail-info\">\r\n                        <div class=\"info-card\">\r\n                            <div class=\"info-item\">\r\n                                <i class=\"el-icon-goods info-icon\"></i>\r\n                                <div class=\"info-content\">\r\n                                    <span class=\"info-label\">商品类型</span>\r\n                                    <span class=\"info-value\">{{ currentGoods.foodtyope }}</span>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"info-item\">\r\n                                <i class=\"el-icon-box info-icon\"></i>\r\n                                <div class=\"info-content\">\r\n                                    <span class=\"info-label\">库存状态</span>\r\n                                    <span class=\"info-value\">{{ currentGoods.amount }}件</span>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"info-item\">\r\n                                <i class=\"el-icon-check info-icon\"></i>\r\n                                <div class=\"info-content\">\r\n                                    <span class=\"info-label\">上架状态</span>\r\n                                    <span class=\"info-value\">{{ currentGoods.fstatus }}</span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div class=\"detail-description\">\r\n                        <h3 class=\"desc-title\">商品描述</h3>\r\n                        <p class=\"desc-content\">{{ currentGoods.sfDescription }}</p>\r\n                    </div>\r\n                    \r\n                    <div class=\"detail-actions\">\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            size=\"large\"\r\n                            @click=\"showCartDialog(currentGoods)\"\r\n                            class=\"action-btn cart-action\"\r\n                        >\r\n                            <i class=\"el-icon-shopping-cart-2\"></i>\r\n                            加入购物车\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 加入购物车弹窗 -->\r\n        <el-dialog\r\n            title=\"加入购物车\"\r\n            :visible.sync=\"cartDialogVisible\"\r\n            width=\"40%\"\r\n            :close-on-click-modal=\"false\"\r\n            custom-class=\"cart-dialog\"\r\n        >\r\n            <div class=\"cart-form\">\r\n                <el-form :model=\"cartForm\" label-width=\"80px\">\r\n                    <el-form-item label=\"商品名称\">\r\n                        <el-input v-model=\"cartForm.goodsName\" disabled class=\"form-input\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"商品价格\">\r\n                        <el-input v-model=\"cartForm.goodsPrice\" disabled class=\"form-input\">\r\n                            <template slot=\"prepend\">¥</template>\r\n                        </el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"购买数量\" prop=\"quantity\">\r\n                        <el-input-number\r\n                            v-model=\"cartForm.quantity\"\r\n                            :min=\"1\"\r\n                            :max=\"99\"\r\n                            size=\"large\"\r\n                            class=\"quantity-input\"\r\n                        ></el-input-number>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"备注信息\" prop=\"remark\">\r\n                        <el-input\r\n                            type=\"textarea\"\r\n                            v-model=\"cartForm.remark\"\r\n                            placeholder=\"请输入备注信息（可选）\"\r\n                            :rows=\"3\"\r\n                            maxlength=\"200\"\r\n                            show-word-limit>\r\n                        </el-input>\r\n                    </el-form-item>\r\n                </el-form>\r\n                \r\n                <!-- 小计显示 -->\r\n                <div class=\"cart-summary\">\r\n                    <div class=\"summary-item\">\r\n                        <span class=\"summary-label\">小计：</span>\r\n                        <span class=\"summary-price\">¥{{ (cartForm.goodsPrice * cartForm.quantity).toFixed(2) }}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"cartDialogVisible = false\" class=\"cancel-btn\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"confirmAddToCart\" class=\"confirm-btn\">\r\n                    <i class=\"el-icon-shopping-cart-2\"></i>\r\n                    确认加入购物车\r\n                </el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"GoodsList\",\r\n    data() {\r\n        return {\r\n            tableData: [],  // 商品数据\r\n            pageNum: 1,     // 当前页码\r\n            pageSize: 12,   // 每页12条\r\n            total: 0,       // 总数\r\n            name: null,     // 搜索关键词\r\n            \r\n            // 分类相关\r\n            categories: [],           // 分类列表\r\n            selectedCategoryId: null, // 选中的分类ID\r\n            categoriesLoading: false, // 分类加载状态\r\n            goodsLoading: false,      // 商品加载状态\r\n            loadingRequest: null,     // 当前加载请求\r\n            searchTimer: null,        // 搜索防抖定时器\r\n            sidebarCollapsed: false,  // 侧边栏折叠状态\r\n            \r\n            detailVisible: false, // 详情弹窗显示\r\n            currentGoods: null,   // 当前查看的商品\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'), // 当前登录用户\r\n\r\n            // 购物车相关\r\n            cartDialogVisible: false,\r\n            cartForm: {\r\n                goodsId: null,\r\n                goodsName: '',\r\n                goodsPrice: 0,\r\n                quantity: 1,\r\n                remark: ''\r\n            }\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)\r\n        this.loadCategories()\r\n    },\r\n    beforeDestroy() {\r\n        // 清理定时器\r\n        if (this.searchTimer) {\r\n            clearTimeout(this.searchTimer)\r\n        }\r\n        \r\n        // 取消未完成的请求\r\n        if (this.loadingRequest) {\r\n            this.loadingRequest.cancel && this.loadingRequest.cancel()\r\n        }\r\n    },\r\n    methods: {\r\n        // 格式化时间为年月日时分秒\r\n        formatDateTime(date) {\r\n            const d = new Date(date);\r\n            const year = d.getFullYear();\r\n            const month = String(d.getMonth() + 1).padStart(2, '0');\r\n            const day = String(d.getDate()).padStart(2, '0');\r\n            const hours = String(d.getHours()).padStart(2, '0');\r\n            const minutes = String(d.getMinutes()).padStart(2, '0');\r\n            const seconds = String(d.getSeconds()).padStart(2, '0');\r\n            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n        },\r\n\r\n        load(pageNum) {  // 加载商品数据\r\n            if (pageNum) this.pageNum = pageNum\r\n            \r\n            this.goodsLoading = true\r\n            this.$request.get('/foods/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n                    name: this.name,\r\n                    categoryId: this.selectedCategoryId,\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list || []\r\n                    this.total = res.data?.total || 0\r\n                } else {\r\n                    this.$message.error(res.msg || '加载商品失败，请重试')\r\n                }\r\n            }).catch(err => {\r\n                console.error('加载商品失败:', err)\r\n                this.$message.error('网络异常，请检查网络连接后重试')\r\n            }).finally(() => {\r\n                this.goodsLoading = false\r\n            })\r\n        },\r\n\r\n        showDetail(item) {  // 显示商品详情\r\n            this.currentGoods = item\r\n            this.detailVisible = true\r\n        },\r\n\r\n        // 显示购物车弹窗\r\n        showCartDialog(goods) {\r\n            // 检查用户是否登录\r\n            if (!this.user.id) {\r\n                this.$message.warning('请先登录后再加入购物车')\r\n                this.$router.push('/login')\r\n                return\r\n            }\r\n\r\n            this.cartForm = {\r\n                goodsId: goods.id,\r\n                goodsName: goods.name,\r\n                goodsPrice: parseFloat(goods.sfPrice) || 0,\r\n                quantity: 1,\r\n                remark: ''\r\n            }\r\n            this.cartDialogVisible = true\r\n        },\r\n\r\n        // 确认加入购物车\r\n        confirmAddToCart() {\r\n            const cartData = {\r\n                sfUserName: this.user.name || '匿名用户',\r\n                sfUserId: this.user.id || 0,\r\n                sfProductIds: this.cartForm.goodsId.toString(),\r\n                status: '购物车', // 明确标识为购物车状态\r\n                sfCartStatus: '已加入购物车', // 购物车标识\r\n                sfTotalPrice: (this.cartForm.goodsPrice * this.cartForm.quantity).toFixed(2), // 小计金额\r\n                sfQuantity: this.cartForm.quantity, // 商品数量\r\n                sfRemark: this.cartForm.remark || '', // 用户输入的备注\r\n                sfCreateTime: this.formatDateTime(new Date())\r\n            }\r\n\r\n            // 调用购物车专用接口\r\n            this.$request.post('/dingdan/addToCart', cartData).then(res => {\r\n                if (res.code === '200') {\r\n                    this.$message.success(`已将 ${this.cartForm.quantity} 件商品加入购物车！`)\r\n                    this.cartDialogVisible = false\r\n                    this.resetCartForm()\r\n                } else {\r\n                    this.$message.error(res.msg || '加入购物车失败')\r\n                }\r\n            }).catch(err => {\r\n                console.error('加入购物车失败:', err)\r\n                this.$message.error('加入购物车失败，请重试')\r\n            })\r\n        },\r\n\r\n        // 重置购物车表单\r\n        resetCartForm() {\r\n            this.cartForm = {\r\n                goodsId: null,\r\n                goodsName: '',\r\n                goodsPrice: 0,\r\n                quantity: 1,\r\n                remark: ''\r\n            }\r\n        },\r\n\r\n        handleCurrentChange(pageNum) {  // 分页变化\r\n            this.load(pageNum)\r\n        },\r\n\r\n        // 加载分类数据\r\n        loadCategories() {\r\n            this.categoriesLoading = true\r\n            this.$request.get('/category/selectEnabled').then(res => {\r\n                if (res.code === '200') {\r\n                    this.categories = res.data || []\r\n                } else {\r\n                    console.error('加载分类失败:', res.msg)\r\n                    this.$message.warning('分类加载失败，但不影响商品浏览')\r\n                }\r\n            }).catch(err => {\r\n                console.error('加载分类失败:', err)\r\n                this.$message.warning('分类加载失败，但不影响商品浏览')\r\n            }).finally(() => {\r\n                this.categoriesLoading = false\r\n            })\r\n        },\r\n\r\n        // 选择分类\r\n        selectCategory(categoryId) {\r\n            if (this.selectedCategoryId === categoryId) {\r\n                return // 避免重复选择\r\n            }\r\n            \r\n            // 如果正在加载，取消之前的请求\r\n            if (this.loadingRequest) {\r\n                this.loadingRequest.cancel && this.loadingRequest.cancel()\r\n            }\r\n            \r\n            this.selectedCategoryId = categoryId\r\n            \r\n            // 提供用户反馈\r\n            const categoryName = categoryId === null ? '全部商品' : \r\n                this.categories.find(c => c.id === categoryId)?.name || '未知分类'\r\n            \r\n            // 使用 Toast 提示而不是 Message，避免干扰用户\r\n            this.$message({\r\n                message: `正在加载${categoryName}...`,\r\n                type: 'info',\r\n                duration: 1000\r\n            })\r\n            \r\n            this.load(1) // 重新加载第一页数据\r\n        },\r\n\r\n        // 搜索输入防抖处理\r\n        handleSearchInput() {\r\n            // 清除之前的定时器\r\n            if (this.searchTimer) {\r\n                clearTimeout(this.searchTimer)\r\n            }\r\n            \r\n            // 设置新的定时器，500ms后执行搜索\r\n            this.searchTimer = setTimeout(() => {\r\n                this.load(1)\r\n            }, 500)\r\n        },\r\n\r\n        // 切换侧边栏\r\n        toggleSidebar() {\r\n            this.sidebarCollapsed = !this.sidebarCollapsed\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home-container {\r\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n    min-height: 100vh;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n    padding: 40px 0;\r\n    background: white;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    z-index: 10;\r\n    position: relative;\r\n}\r\n\r\n.search-container {\r\n    max-width: 600px;\r\n    margin: 0 auto;\r\n    padding: 0 20px;\r\n}\r\n\r\n.search-input {\r\n    width: 100%;\r\n}\r\n\r\n.search-input >>> .el-input__inner {\r\n    height: 50px;\r\n    border-radius: 25px;\r\n    border: 2px solid #e5e7eb;\r\n    padding-left: 20px;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.search-input >>> .el-input__inner:focus {\r\n    border-color: #3b82f6;\r\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.search-btn {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    color: white;\r\n    border-radius: 0 25px 25px 0;\r\n    padding: 0 20px;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.search-btn:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n    display: flex;\r\n    min-height: calc(100vh - 140px);\r\n}\r\n\r\n/* 左侧分类菜单 */\r\n.sidebar-container {\r\n    width: 280px;\r\n    background: white;\r\n    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    position: relative;\r\n    z-index: 5;\r\n}\r\n\r\n.sidebar-container.sidebar-collapsed {\r\n    width: 80px;\r\n}\r\n\r\n.sidebar-toggle {\r\n    position: absolute;\r\n    top: 20px;\r\n    right: -15px;\r\n    width: 30px;\r\n    height: 30px;\r\n    background: #3b82f6;\r\n    color: white;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    font-size: 14px;\r\n    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\r\n    transition: all 0.3s ease;\r\n    z-index: 10;\r\n}\r\n\r\n.sidebar-toggle:hover {\r\n    background: #1e40af;\r\n    transform: scale(1.1);\r\n}\r\n\r\n.category-sidebar {\r\n    height: 100%;\r\n    overflow-y: auto;\r\n    padding: 20px 0;\r\n}\r\n\r\n.sidebar-header {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 20px 20px 20px;\r\n    border-bottom: 1px solid #e2e8f0;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.sidebar-header i {\r\n    font-size: 20px;\r\n    color: #3b82f6;\r\n    margin-right: 12px;\r\n}\r\n\r\n.sidebar-title {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #1e293b;\r\n    transition: opacity 0.3s ease;\r\n}\r\n\r\n.sidebar-collapsed .sidebar-title {\r\n    opacity: 0;\r\n}\r\n\r\n/* 分类菜单 */\r\n.category-menu {\r\n    padding: 0 10px;\r\n}\r\n\r\n.category-menu-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    margin-bottom: 8px;\r\n    border-radius: 12px;\r\n    cursor: pointer;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.category-menu-item:hover {\r\n    background: #f1f5f9;\r\n    transform: translateX(4px);\r\n}\r\n\r\n.category-menu-item.active {\r\n    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);\r\n    color: white;\r\n    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.category-menu-item.active:hover {\r\n    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);\r\n}\r\n\r\n.menu-item-icon {\r\n    width: 32px;\r\n    height: 32px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 12px;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.menu-item-icon i {\r\n    font-size: 18px;\r\n    color: #64748b;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.category-menu-item.active .menu-item-icon i {\r\n    color: white;\r\n}\r\n\r\n.category-menu-image {\r\n    width: 28px;\r\n    height: 28px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n}\r\n\r\n.menu-item-text {\r\n    flex: 1;\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    color: #475569;\r\n    transition: all 0.3s ease;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.category-menu-item.active .menu-item-text {\r\n    color: white;\r\n    font-weight: 600;\r\n}\r\n\r\n.menu-item-indicator {\r\n    width: 4px;\r\n    height: 20px;\r\n    background: white;\r\n    border-radius: 2px;\r\n    margin-left: 8px;\r\n    opacity: 0.8;\r\n}\r\n\r\n/* 分类加载状态 */\r\n.category-loading {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 40px 20px;\r\n    color: #64748b;\r\n    flex-direction: column;\r\n}\r\n\r\n.sidebar-collapsed .category-loading {\r\n    flex-direction: column;\r\n}\r\n\r\n.category-loading i {\r\n    font-size: 24px;\r\n    margin-bottom: 8px;\r\n    color: #3b82f6;\r\n    animation: rotating 2s linear infinite;\r\n}\r\n\r\n.sidebar-collapsed .category-loading i {\r\n    margin-right: 0;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.category-loading span {\r\n    font-size: 12px;\r\n    transition: opacity 0.3s ease;\r\n}\r\n\r\n/* 右侧内容区域 */\r\n.content-area {\r\n    flex: 1;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    margin-left: 20px;\r\n}\r\n\r\n.content-area.content-expanded {\r\n    margin-left: 20px;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-section {\r\n    padding: 40px 20px;\r\n}\r\n\r\n.section-header {\r\n    text-align: center;\r\n    margin-bottom: 40px;\r\n}\r\n\r\n.section-title {\r\n    font-size: 32px;\r\n    font-weight: 700;\r\n    color: #1e40af;\r\n    margin-bottom: 12px;\r\n}\r\n\r\n.section-subtitle {\r\n    font-size: 16px;\r\n    color: #64748b;\r\n    margin: 0;\r\n}\r\n\r\n/* 商品加载状态 */\r\n.loading-state {\r\n    text-align: center;\r\n    padding: 80px 20px;\r\n    color: #64748b;\r\n}\r\n\r\n.loading-state i {\r\n    font-size: 48px;\r\n    margin-bottom: 16px;\r\n    color: #3b82f6;\r\n    animation: rotating 2s linear infinite;\r\n}\r\n\r\n.loading-state p {\r\n    font-size: 16px;\r\n    margin: 0;\r\n}\r\n\r\n/* 旋转动画 */\r\n@keyframes rotating {\r\n    0% {\r\n        transform: rotate(0deg);\r\n    }\r\n    100% {\r\n        transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n/* 图片错误状态 */\r\n.image-error {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    background: #f5f5f5;\r\n    color: #999;\r\n}\r\n\r\n.image-error i {\r\n    font-size: 32px;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.image-error span {\r\n    font-size: 12px;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n    text-align: center;\r\n    padding: 80px 20px;\r\n    color: #64748b;\r\n}\r\n\r\n.empty-state i {\r\n    font-size: 64px;\r\n    margin-bottom: 16px;\r\n    color: #3b82f6;\r\n}\r\n\r\n.empty-state h3 {\r\n    font-size: 20px;\r\n    margin-bottom: 8px;\r\n    color: #475569;\r\n}\r\n\r\n.empty-state p {\r\n    margin-bottom: 0;\r\n    font-size: 16px;\r\n}\r\n\r\n/* 商品网格 */\r\n.goods-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 24px;\r\n    margin-bottom: 40px;\r\n}\r\n\r\n.goods-card {\r\n    background: white;\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    cursor: pointer;\r\n    border: 1px solid #f1f5f9;\r\n}\r\n\r\n.goods-card:hover {\r\n    transform: translateY(-8px);\r\n    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);\r\n    border-color: #3b82f6;\r\n}\r\n\r\n.card-image-container {\r\n    position: relative;\r\n    height: 200px;\r\n    overflow: hidden;\r\n}\r\n\r\n.card-image {\r\n    width: 100%;\r\n    height: 100%;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.goods-card:hover .card-image {\r\n    transform: scale(1.05);\r\n}\r\n\r\n.card-overlay {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.5);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    opacity: 0;\r\n    transition: opacity 0.3s ease;\r\n}\r\n\r\n.goods-card:hover .card-overlay {\r\n    opacity: 1;\r\n}\r\n\r\n.view-btn {\r\n    background: white;\r\n    color: #3b82f6;\r\n    border: none;\r\n    width: 50px;\r\n    height: 50px;\r\n    font-size: 20px;\r\n}\r\n\r\n.card-content {\r\n    padding: 20px;\r\n}\r\n\r\n.card-title {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #1e293b;\r\n    margin-bottom: 12px;\r\n    height: 48px;\r\n    overflow: hidden;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    line-height: 1.5;\r\n}\r\n\r\n.card-price {\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.price-symbol {\r\n    color: #3b82f6;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n\r\n.price-number {\r\n    color: #3b82f6;\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n}\r\n\r\n.card-actions {\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n\r\n.cart-btn {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    color: white;\r\n    border-radius: 20px;\r\n    padding: 8px 20px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.cart-btn:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n/* 分页区域 */\r\n.pagination-section {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 40px 0;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li {\r\n    background: white;\r\n    border: 1px solid #e5e7eb;\r\n    border-radius: 8px;\r\n    margin: 0 4px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li:hover {\r\n    border-color: #3b82f6;\r\n    color: #3b82f6;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li.active {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    color: white;\r\n}\r\n\r\n/* 详情弹窗 */\r\n.detail-dialog >>> .el-dialog {\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n}\r\n\r\n.detail-dialog >>> .el-dialog__header {\r\n    display: none;\r\n}\r\n\r\n.detail-dialog >>> .el-dialog__body {\r\n    padding: 0;\r\n}\r\n\r\n.detail-container {\r\n    display: flex;\r\n    min-height: 500px;\r\n}\r\n\r\n.detail-left {\r\n    flex: 1;\r\n    background: #f8fafc;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 40px;\r\n}\r\n\r\n.detail-image-container {\r\n    width: 100%;\r\n    max-width: 400px;\r\n}\r\n\r\n.detail-image {\r\n    width: 100%;\r\n    height: 400px;\r\n    border-radius: 12px;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.detail-right {\r\n    flex: 1;\r\n    padding: 40px;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.detail-header {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.detail-title {\r\n    font-size: 28px;\r\n    font-weight: 700;\r\n    color: #1e293b;\r\n    margin-bottom: 16px;\r\n    line-height: 1.3;\r\n}\r\n\r\n.detail-price {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.detail-price .price-symbol {\r\n    color: #3b82f6;\r\n    font-size: 24px;\r\n    font-weight: 600;\r\n}\r\n\r\n.detail-price .price-number {\r\n    color: #3b82f6;\r\n    font-size: 36px;\r\n    font-weight: 700;\r\n}\r\n\r\n.detail-info {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.info-card {\r\n    background: #f8fafc;\r\n    border-radius: 12px;\r\n    padding: 20px;\r\n    border: 1px solid #e2e8f0;\r\n}\r\n\r\n.info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.info-item:last-child {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.info-icon {\r\n    width: 40px;\r\n    height: 40px;\r\n    background: #3b82f6;\r\n    color: white;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 16px;\r\n    font-size: 16px;\r\n}\r\n\r\n.info-content {\r\n    flex: 1;\r\n}\r\n\r\n.info-label {\r\n    display: block;\r\n    font-size: 14px;\r\n    color: #64748b;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.info-value {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #1e293b;\r\n}\r\n\r\n.detail-description {\r\n    margin-bottom: 40px;\r\n    flex: 1;\r\n}\r\n\r\n.desc-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #1e293b;\r\n    margin-bottom: 12px;\r\n}\r\n\r\n.desc-content {\r\n    font-size: 15px;\r\n    color: #475569;\r\n    line-height: 1.7;\r\n    margin: 0;\r\n}\r\n\r\n.detail-actions {\r\n    display: flex;\r\n    gap: 16px;\r\n}\r\n\r\n.action-btn {\r\n    flex: 1;\r\n    height: 50px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    border-radius: 12px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.cart-action {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n}\r\n\r\n.cart-action:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.buy-action {\r\n    background: #ef4444;\r\n    border-color: #ef4444;\r\n}\r\n\r\n.buy-action:hover {\r\n    background: #dc2626;\r\n    border-color: #dc2626;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n/* 订单弹窗 */\r\n.order-dialog >>> .el-dialog {\r\n    border-radius: 16px;\r\n}\r\n\r\n.order-dialog >>> .el-dialog__header {\r\n    background: #f8fafc;\r\n    border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.order-form {\r\n    padding: 20px 0;\r\n}\r\n\r\n.form-input >>> .el-input__inner,\r\n.form-textarea >>> .el-textarea__inner {\r\n    border-radius: 8px;\r\n    border: 1px solid #e2e8f0;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.form-input >>> .el-input__inner:focus,\r\n.form-textarea >>> .el-textarea__inner:focus {\r\n    border-color: #3b82f6;\r\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.dialog-footer {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    gap: 12px;\r\n}\r\n\r\n.cancel-btn {\r\n    border-radius: 8px;\r\n    padding: 10px 20px;\r\n}\r\n\r\n.confirm-btn {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    border-radius: 8px;\r\n    padding: 10px 20px;\r\n}\r\n\r\n.confirm-btn:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n}\r\n\r\n/* 购物车弹窗样式 */\r\n.cart-dialog >>> .el-dialog {\r\n    border-radius: 16px;\r\n}\r\n\r\n.cart-dialog >>> .el-dialog__header {\r\n    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);\r\n    color: white;\r\n    border-bottom: none;\r\n}\r\n\r\n.cart-dialog >>> .el-dialog__title {\r\n    color: white;\r\n    font-weight: 600;\r\n}\r\n\r\n.cart-form {\r\n    padding: 20px 0;\r\n}\r\n\r\n.quantity-input {\r\n    width: 120px;\r\n}\r\n\r\n.quantity-input >>> .el-input__inner {\r\n    text-align: center;\r\n    font-weight: 600;\r\n}\r\n\r\n.cart-summary {\r\n    margin-top: 20px;\r\n    padding: 16px;\r\n    background: #f8fafc;\r\n    border-radius: 8px;\r\n    border: 1px solid #e2e8f0;\r\n}\r\n\r\n.summary-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.summary-label {\r\n    font-size: 16px;\r\n    color: #475569;\r\n    font-weight: 500;\r\n}\r\n\r\n.summary-price {\r\n    font-size: 20px;\r\n    color: #3b82f6;\r\n    font-weight: 700;\r\n}\r\n\r\n.confirm-btn {\r\n    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);\r\n    border: none;\r\n    border-radius: 8px;\r\n    padding: 10px 20px;\r\n    font-weight: 600;\r\n}\r\n\r\n.confirm-btn:hover {\r\n    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);\r\n    transform: translateY(-2px);\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1024px) {\r\n    .sidebar-container {\r\n        width: 240px;\r\n    }\r\n    \r\n    .sidebar-container.sidebar-collapsed {\r\n        width: 70px;\r\n    }\r\n    \r\n    .goods-grid {\r\n        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .main-content {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .sidebar-container {\r\n        width: 100%;\r\n        height: auto;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    }\r\n    \r\n    .sidebar-container.sidebar-collapsed {\r\n        width: 100%;\r\n        height: 60px;\r\n        overflow: hidden;\r\n    }\r\n    \r\n    .category-sidebar {\r\n        padding: 10px 0;\r\n    }\r\n    \r\n    .sidebar-collapsed .category-menu {\r\n        display: none;\r\n    }\r\n    \r\n    .content-area {\r\n        margin-left: 0;\r\n    }\r\n    \r\n    .content-area.content-expanded {\r\n        margin-left: 0;\r\n    }\r\n    \r\n    .goods-grid {\r\n        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n        gap: 16px;\r\n    }\r\n    \r\n    .detail-container {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .detail-left {\r\n        padding: 20px;\r\n    }\r\n    \r\n    .detail-right {\r\n        padding: 20px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .content-section {\r\n        padding: 20px 16px;\r\n    }\r\n    \r\n    .goods-grid {\r\n        grid-template-columns: 1fr;\r\n    }\r\n    \r\n    .search-container {\r\n        padding: 0 16px;\r\n    }\r\n}\r\n</style>"], "mappings": ";;;AAoTA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAAA;MACAL,IAAA;MAAA;;MAEA;MACAM,UAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;MAAA;MACAC,WAAA;MAAA;MACAC,gBAAA;MAAA;;MAEAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MAAA;;MAEA;MACAC,iBAAA;MACAC,QAAA;QACAC,OAAA;QACAC,SAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;IACA,KAAAC,cAAA;EACA;EACAC,cAAA;IACA;IACA,SAAAnB,WAAA;MACAoB,YAAA,MAAApB,WAAA;IACA;;IAEA;IACA,SAAAD,cAAA;MACA,KAAAA,cAAA,CAAAsB,MAAA,SAAAtB,cAAA,CAAAsB,MAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,eAAAC,IAAA;MACA,MAAAC,CAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,MAAAG,IAAA,GAAAF,CAAA,CAAAG,WAAA;MACA,MAAAC,KAAA,GAAAC,MAAA,CAAAL,CAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,MAAAC,GAAA,GAAAH,MAAA,CAAAL,CAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,MAAAG,KAAA,GAAAL,MAAA,CAAAL,CAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,MAAAK,OAAA,GAAAP,MAAA,CAAAL,CAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,MAAAO,OAAA,GAAAT,MAAA,CAAAL,CAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAAL,IAAA,IAAAE,KAAA,IAAAI,GAAA,IAAAE,KAAA,IAAAE,OAAA,IAAAE,OAAA;IACA;IAEAtB,KAAAzB,OAAA;MAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MAEA,KAAAM,YAAA;MACA,KAAA2C,QAAA,CAAAC,GAAA;QACAC,MAAA;UACAnD,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAJ,IAAA,OAAAA,IAAA;UACAuD,UAAA,OAAAhD;QACA;MACA,GAAAiD,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAxD,SAAA,GAAAuD,GAAA,CAAAxD,IAAA,EAAA0D,IAAA;UACA,KAAAtD,KAAA,GAAAoD,GAAA,CAAAxD,IAAA,EAAAI,KAAA;QACA;UACA,KAAAuD,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAC,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAJ,KAAA,YAAAG,GAAA;QACA,KAAAJ,QAAA,CAAAC,KAAA;MACA,GAAAK,OAAA;QACA,KAAAzD,YAAA;MACA;IACA;IAEA0D,WAAAC,IAAA;MAAA;MACA,KAAAtD,YAAA,GAAAsD,IAAA;MACA,KAAAvD,aAAA;IACA;IAEA;IACAwD,eAAAC,KAAA;MACA;MACA,UAAAvD,IAAA,CAAAwD,EAAA;QACA,KAAAX,QAAA,CAAAY,OAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;QACA;MACA;MAEA,KAAArD,QAAA;QACAC,OAAA,EAAAgD,KAAA,CAAAC,EAAA;QACAhD,SAAA,EAAA+C,KAAA,CAAAtE,IAAA;QACAwB,UAAA,EAAAmD,UAAA,CAAAL,KAAA,CAAAM,OAAA;QACAnD,QAAA;QACAC,MAAA;MACA;MACA,KAAAN,iBAAA;IACA;IAEA;IACAyD,iBAAA;MACA,MAAAC,QAAA;QACAC,UAAA,OAAAhE,IAAA,CAAAf,IAAA;QACAgF,QAAA,OAAAjE,IAAA,CAAAwD,EAAA;QACAU,YAAA,OAAA5D,QAAA,CAAAC,OAAA,CAAA4D,QAAA;QACAC,MAAA;QAAA;QACAC,YAAA;QAAA;QACAC,YAAA,QAAAhE,QAAA,CAAAG,UAAA,QAAAH,QAAA,CAAAI,QAAA,EAAA6D,OAAA;QAAA;QACAC,UAAA,OAAAlE,QAAA,CAAAI,QAAA;QAAA;QACA+D,QAAA,OAAAnE,QAAA,CAAAK,MAAA;QAAA;QACA+D,YAAA,OAAAvD,cAAA,KAAAG,IAAA;MACA;;MAEA;MACA,KAAAe,QAAA,CAAAsC,IAAA,uBAAAZ,QAAA,EAAAtB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAE,QAAA,CAAA+B,OAAA,YAAAtE,QAAA,CAAAI,QAAA;UACA,KAAAL,iBAAA;UACA,KAAAwE,aAAA;QACA;UACA,KAAAhC,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAC,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAJ,KAAA,aAAAG,GAAA;QACA,KAAAJ,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACA+B,cAAA;MACA,KAAAvE,QAAA;QACAC,OAAA;QACAC,SAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;MACA;IACA;IAEAmE,oBAAA1F,OAAA;MAAA;MACA,KAAAyB,IAAA,CAAAzB,OAAA;IACA;IAEA;IACA0B,eAAA;MACA,KAAArB,iBAAA;MACA,KAAA4C,QAAA,CAAAC,GAAA,4BAAAG,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAApD,UAAA,GAAAmD,GAAA,CAAAxD,IAAA;QACA;UACAgE,OAAA,CAAAJ,KAAA,YAAAJ,GAAA,CAAAK,GAAA;UACA,KAAAF,QAAA,CAAAY,OAAA;QACA;MACA,GAAAT,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAJ,KAAA,YAAAG,GAAA;QACA,KAAAJ,QAAA,CAAAY,OAAA;MACA,GAAAN,OAAA;QACA,KAAA1D,iBAAA;MACA;IACA;IAEA;IACAsF,eAAAvC,UAAA;MACA,SAAAhD,kBAAA,KAAAgD,UAAA;QACA;MACA;;MAEA;MACA,SAAA7C,cAAA;QACA,KAAAA,cAAA,CAAAsB,MAAA,SAAAtB,cAAA,CAAAsB,MAAA;MACA;MAEA,KAAAzB,kBAAA,GAAAgD,UAAA;;MAEA;MACA,MAAAwC,YAAA,GAAAxC,UAAA,qBACA,KAAAjD,UAAA,CAAA0F,IAAA,CAAAC,CAAA,IAAAA,CAAA,CAAA1B,EAAA,KAAAhB,UAAA,GAAAvD,IAAA;;MAEA;MACA,KAAA4D,QAAA;QACAsC,OAAA,SAAAH,YAAA;QACAI,IAAA;QACAC,QAAA;MACA;MAEA,KAAAxE,IAAA;IACA;IAEA;IACAyE,kBAAA;MACA;MACA,SAAA1F,WAAA;QACAoB,YAAA,MAAApB,WAAA;MACA;;MAEA;MACA,KAAAA,WAAA,GAAA2F,UAAA;QACA,KAAA1E,IAAA;MACA;IACA;IAEA;IACA2E,cAAA;MACA,KAAA3F,gBAAA,SAAAA,gBAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}