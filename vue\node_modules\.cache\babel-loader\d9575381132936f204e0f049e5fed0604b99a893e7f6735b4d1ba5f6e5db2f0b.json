{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { install as installAxisPointer } from '../axisPointer/install.js';\nimport { use } from '../../extension.js';\nimport TooltipModel from './TooltipModel.js';\nimport TooltipView from './TooltipView.js';\nimport { noop } from 'zrender/lib/core/util.js';\nexport function install(registers) {\n  use(installAxisPointer);\n  registers.registerComponentModel(TooltipModel);\n  registers.registerComponentView(TooltipView);\n  /**\r\n   * @action\r\n   * @property {string} type\r\n   * @property {number} seriesIndex\r\n   * @property {number} dataIndex\r\n   * @property {number} [x]\r\n   * @property {number} [y]\r\n   */\n  registers.registerAction({\n    type: 'showTip',\n    event: 'showTip',\n    update: 'tooltip:manuallyShowTip'\n  }, noop);\n  registers.registerAction({\n    type: 'hideTip',\n    event: 'hideTip',\n    update: 'tooltip:manuallyHideTip'\n  }, noop);\n}", "map": {"version": 3, "names": ["install", "installAxisPointer", "use", "TooltipModel", "TooltipView", "noop", "registers", "registerComponentModel", "registerComponentView", "registerAction", "type", "event", "update"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/component/tooltip/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { install as installAxisPointer } from '../axisPointer/install.js';\nimport { use } from '../../extension.js';\nimport TooltipModel from './TooltipModel.js';\nimport TooltipView from './TooltipView.js';\nimport { noop } from 'zrender/lib/core/util.js';\nexport function install(registers) {\n  use(installAxisPointer);\n  registers.registerComponentModel(TooltipModel);\n  registers.registerComponentView(TooltipView);\n  /**\r\n   * @action\r\n   * @property {string} type\r\n   * @property {number} seriesIndex\r\n   * @property {number} dataIndex\r\n   * @property {number} [x]\r\n   * @property {number} [y]\r\n   */\n  registers.registerAction({\n    type: 'showTip',\n    event: 'showTip',\n    update: 'tooltip:manuallyShowTip'\n  }, noop);\n  registers.registerAction({\n    type: 'hideTip',\n    event: 'hideTip',\n    update: 'tooltip:manuallyHideTip'\n  }, noop);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,2BAA2B;AACzE,SAASC,GAAG,QAAQ,oBAAoB;AACxC,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,OAAO,SAASL,OAAOA,CAACM,SAAS,EAAE;EACjCJ,GAAG,CAACD,kBAAkB,CAAC;EACvBK,SAAS,CAACC,sBAAsB,CAACJ,YAAY,CAAC;EAC9CG,SAAS,CAACE,qBAAqB,CAACJ,WAAW,CAAC;EAC5C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,SAAS,CAACG,cAAc,CAAC;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE;EACV,CAAC,EAAEP,IAAI,CAAC;EACRC,SAAS,CAACG,cAAc,CAAC;IACvBC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE;EACV,CAAC,EAAEP,IAAI,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}