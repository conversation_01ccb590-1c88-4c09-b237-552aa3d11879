{"ast": null, "code": "export default {\n  name: \"Leavemess\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前的页码\n      pageSize: 10,\n      // 每页显示的个数\n      total: 0\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    /**\r\n     * 根据行数据返回对应的类名\r\n     * @param {Object} row - 当前行数据\r\n     * @param {Number} index - 行索引\r\n     * @returns {String} - 类名\r\n     */\n    tableRowClassName(row, index) {\n      return !row.reply ? 'no-reply' : '';\n    },\n    /**\r\n     * 加载数据（分页查询）\r\n     * @param {Number} pageNum - 要加载的页码\r\n     */\n    load(pageNum) {\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/leavemess/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list || [];\n          this.total = res.data?.total || 0;\n        } else {\n          this.$message.error(res.msg);\n        }\n      }).catch(() => {\n        this.$message.error('加载数据失败');\n      });\n    },\n    /**\r\n     * 重置搜索条件并重新加载数据\r\n     */\n\n    /**\r\n     * 处理分页器页码变化\r\n     * @param {Number} pageNum - 当前选择的页码\r\n     */\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    },\n    /**\r\n     * 预览图片\r\n     * @param {String} url - 图片URL\r\n     */\n    previewImage(url) {\n      this.$refs.imagePreview.handlePreview(url);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "created", "load", "methods", "tableRowClassName", "row", "index", "reply", "$request", "get", "params", "then", "res", "code", "list", "$message", "error", "msg", "catch", "handleCurrentChange", "previewImage", "url", "$refs", "imagePreview", "handlePreview"], "sources": ["src/views/front/ReplyLeavemess.vue"], "sourcesContent": ["<template>\r\n    <div class=\"leavemess-container\">\r\n        <!-- 数据表格 -->\r\n        <div class=\"table-container\">\r\n            <el-table\r\n                :data=\"tableData\"\r\n                stripe\r\n                style=\"width: 100%\"\r\n                :row-class-name=\"tableRowClassName\"\r\n                class=\"custom-table\">\r\n\r\n                <!-- 序号列 -->\r\n                <el-table-column prop=\"id\" label=\"序号\" width=\"70\" align=\"center\" sortable></el-table-column>\r\n\r\n                <!-- 用户ID列 -->\r\n                <el-table-column prop=\"sfUserId\" label=\"用户ID\" align=\"center\"></el-table-column>\r\n\r\n                <!-- 问题列 -->\r\n                <el-table-column prop=\"sfQuestion\" label=\"问题\" align=\"center\"></el-table-column>\r\n\r\n                <!-- 评论回复列 -->\r\n                <el-table-column prop=\"reply\" label=\"评论回复\" align=\"center\">\r\n                    <template #default=\"scope\">\r\n                        <span v-if=\"scope.row.reply\">{{ scope.row.reply }}</span>\r\n                        <span v-else class=\"no-reply-text\">暂无回复</span>\r\n                    </template>\r\n                </el-table-column>\r\n\r\n                <!-- 图片列 -->\r\n                <el-table-column prop=\"sfImage\" label=\"图片\" align=\"center\">\r\n                    <template #default=\"scope\">\r\n                        <el-image\r\n                            v-if=\"scope.row.sfImage\"\r\n                            :src=\"scope.row.sfImage\"\r\n                            fit=\"cover\"\r\n                            class=\"message-image\"\r\n                            @click=\"previewImage(scope.row.sfImage)\">\r\n                        </el-image>\r\n                        <span v-else>暂无图片</span>\r\n                    </template>\r\n                </el-table-column>\r\n\r\n                <!-- 时间列 -->\r\n                <el-table-column prop=\"sfLeaveTime\" label=\"时间\" align=\"center\"></el-table-column>\r\n\r\n                <!-- 回复时间列 -->\r\n                <el-table-column prop=\"replytime\" label=\"回复时间\" align=\"center\"></el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 无数据提示 -->\r\n            <el-empty v-if=\"tableData.length === 0\" description=\"暂无评论\"></el-empty>\r\n\r\n            <!-- 分页器 -->\r\n            <div class=\"pagination\">\r\n                <el-pagination\r\n                    background\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageNum\"\r\n                    :page-sizes=\"[5, 10, 20]\"\r\n                    :page-size=\"pageSize\"\r\n                    layout=\"total, prev, pager, next\"\r\n                    :total=\"total\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 图片预览组件（隐藏） -->\r\n        <el-image ref=\"imagePreview\" style=\"display: none;\"></el-image>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Leavemess\",\r\n    data() {\r\n        return {\r\n            tableData: [],  // 所有的数据\r\n            pageNum: 1,     // 当前的页码\r\n            pageSize: 10,   // 每页显示的个数\r\n            total: 0,\r\n\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)\r\n    },\r\n    methods: {\r\n        /**\r\n         * 根据行数据返回对应的类名\r\n         * @param {Object} row - 当前行数据\r\n         * @param {Number} index - 行索引\r\n         * @returns {String} - 类名\r\n         */\r\n        tableRowClassName(row, index) {\r\n            return !row.reply ? 'no-reply' : '';\r\n        },\r\n        /**\r\n         * 加载数据（分页查询）\r\n         * @param {Number} pageNum - 要加载的页码\r\n         */\r\n        load(pageNum) {\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/leavemess/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list || []\r\n                    this.total = res.data?.total || 0\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('加载数据失败')\r\n            })\r\n        },\r\n        /**\r\n         * 重置搜索条件并重新加载数据\r\n         */\r\n\r\n        /**\r\n         * 处理分页器页码变化\r\n         * @param {Number} pageNum - 当前选择的页码\r\n         */\r\n        handleCurrentChange(pageNum) {\r\n            this.load(pageNum)\r\n        },\r\n        /**\r\n         * 预览图片\r\n         * @param {String} url - 图片URL\r\n         */\r\n        previewImage(url) {\r\n            this.$refs.imagePreview.handlePreview(url)\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.leavemess-container {\r\n    padding: 20px;\r\n    background-color: #f5f7fa;\r\n    font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 搜索栏样式 */\r\n.search {\r\n    margin-bottom: 25px;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 15px;\r\n}\r\n\r\n\r\n\r\n/* 表格样式 */\r\n.table-container {\r\n    background-color: #fff;\r\n    padding: 20px;\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.05);\r\n    transition: box-shadow 0.3s;\r\n}\r\n\r\n.table-container:hover {\r\n    box-shadow: 0 6px 16px rgba(0,0,0,0.1);\r\n}\r\n\r\n.custom-table .el-table__header {\r\n    background-color: #f0f2f5;\r\n    color: #333;\r\n    font-weight: bold;\r\n}\r\n\r\n.custom-table .el-table__cell {\r\n    color: #555;\r\n    transition: background-color 0.3s;\r\n}\r\n\r\n.custom-table .el-table__row:hover .el-table__cell {\r\n    background-color: #f9f9f9;\r\n}\r\n\r\n/* 无回复行样式 */\r\n.no-reply {\r\n    background-color: #fff5f5; /* 淡红色背景 */\r\n}\r\n\r\n.no-reply .el-table__cell {\r\n    color: #f56c6c;\r\n    font-weight: bold;\r\n}\r\n\r\n/* “暂无回复”文本样式 */\r\n.no-reply-text {\r\n    color: #f56c6c;\r\n    font-style: italic;\r\n}\r\n\r\n/* 评论图片样式 */\r\n.message-image {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 8px;\r\n    object-fit: cover;\r\n    cursor: pointer;\r\n    transition: transform 0.3s, box-shadow 0.3s;\r\n}\r\n\r\n.message-image:hover {\r\n    transform: scale(1.05);\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\r\n}\r\n\r\n/* 分页器样式 */\r\n.pagination {\r\n    margin-top: 25px;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n}\r\n\r\n/* 空状态样式 */\r\n.el-empty {\r\n    margin: 40px 0;\r\n    color: #909399;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .search {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n    }\r\n\r\n\r\n}\r\n</style>\r\n"], "mappings": "AAwEA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;IAEA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACA;AACA;AACA;AACA;AACA;AACA;IACAC,kBAAAC,GAAA,EAAAC,KAAA;MACA,QAAAD,GAAA,CAAAE,KAAA;IACA;IACA;AACA;AACA;AACA;IACAL,KAAAJ,OAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAU,QAAA,CAAAC,GAAA;QACAC,MAAA;UACAZ,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA;QAEA;MACA,GAAAY,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAhB,SAAA,GAAAe,GAAA,CAAAhB,IAAA,EAAAkB,IAAA;UACA,KAAAd,KAAA,GAAAY,GAAA,CAAAhB,IAAA,EAAAI,KAAA;QACA;UACA,KAAAe,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAC,KAAA;QACA,KAAAH,QAAA,CAAAC,KAAA;MACA;IACA;IACA;AACA;AACA;;IAEA;AACA;AACA;AACA;IACAG,oBAAArB,OAAA;MACA,KAAAI,IAAA,CAAAJ,OAAA;IACA;IACA;AACA;AACA;AACA;IACAsB,aAAAC,GAAA;MACA,KAAAC,KAAA,CAAAC,YAAA,CAAAC,aAAA,CAAAH,GAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}