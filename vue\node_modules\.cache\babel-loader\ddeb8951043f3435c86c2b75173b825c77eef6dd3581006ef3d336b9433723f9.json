{"ast": null, "code": "import \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.filter.js\";\nimport \"core-js/modules/esnext.iterator.for-each.js\";\nimport \"core-js/modules/esnext.iterator.map.js\";\nexport default {\n  name: \"<PERSON><PERSON><PERSON>\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前的页码\n      pageSize: 10,\n      // 每页显示的个数\n      total: 0,\n      name: null,\n      fromVisible: false,\n      form: {},\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      rules: {\n        sfUserName: [{\n          required: true,\n          message: '请输入用户名',\n          trigger: 'blur'\n        }],\n        sfUserId: [{\n          required: true,\n          message: '请输入用户ID',\n          trigger: 'blur'\n        }],\n        status: [{\n          required: true,\n          message: '请选择订单状态',\n          trigger: 'change'\n        }],\n        sfOrderNumber: [{\n          required: true,\n          message: '请输入订单编号',\n          trigger: 'blur'\n        }],\n        sfCreateTime: [{\n          required: true,\n          message: '请选择下单时间',\n          trigger: 'change'\n        }],\n        sfTotalPrice: [{\n          required: true,\n          message: '请输入订单价格',\n          trigger: 'blur'\n        }]\n      },\n      ids: []\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    getStatusTagType(status) {\n      switch (status) {\n        case '待支付':\n          return 'danger';\n        case '已支付':\n          return 'warning';\n        case '配送中':\n          return '';\n        case '已完成':\n          return 'success';\n        case '已取消':\n          return 'info';\n        case '退款中':\n          return 'danger';\n        case '已退款':\n          return 'info';\n        default:\n          return '';\n      }\n    },\n    // 处理退款操作\n    handleRefund(row, status) {\n      const action = status === '已退款' ? '同意退款' : '拒绝退款';\n      this.$confirm(`确定要${action}吗?`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const refundData = {\n          ...row,\n          status: status\n        };\n        this.$request({\n          url: '/dingdan/update',\n          method: 'PUT',\n          data: refundData\n        }).then(res => {\n          if (res.code === '200') {\n            this.$message.success(`${action}成功`);\n            this.load(1); // 重新加载数据\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {\n        this.$message.info(`已取消${action}`);\n      });\n    },\n    // 显示订单详情\n    showOrderDetails(order) {\n      this.$request.get(`/dingdan/details/${order.id}`).then(res => {\n        if (res.code === '200') {\n          const orderItems = res.data || [];\n          if (orderItems.length === 0) {\n            this.$message.info('该订单暂无详情信息');\n            return;\n          }\n\n          // 构建详情显示内容\n          let detailsHtml = '<div style=\"max-height: 400px; overflow-y: auto;\">';\n          detailsHtml += `<h4>订单编号：${order.sfOrderNumber}</h4>`;\n          detailsHtml += `<p><strong>订单状态：</strong>${order.status}</p>`;\n          detailsHtml += `<p><strong>用户：</strong>${order.sfUserName}</p>`;\n          detailsHtml += `<p><strong>下单时间：</strong>${order.sfCreateTime}</p>`;\n          if (order.sfRemark) {\n            detailsHtml += `<p><strong>用户备注：</strong>${order.sfRemark}</p>`;\n          }\n          if (order.sfEvaluation) {\n            detailsHtml += `<p><strong>用户评价：</strong>${order.sfEvaluation}</p>`;\n          }\n          detailsHtml += '<hr style=\"margin: 15px 0;\">';\n          detailsHtml += '<h5>商品明细：</h5>';\n          detailsHtml += '<table style=\"width: 100%; border-collapse: collapse;\">';\n          detailsHtml += '<tr style=\"background: #f5f5f5;\"><th style=\"padding: 8px; border: 1px solid #ddd;\">商品名称</th><th style=\"padding: 8px; border: 1px solid #ddd;\">单价</th><th style=\"padding: 8px; border: 1px solid #ddd;\">数量</th><th style=\"padding: 8px; border: 1px solid #ddd;\">小计</th></tr>';\n          let totalAmount = 0;\n          orderItems.forEach(item => {\n            totalAmount += parseFloat(item.subtotal || 0);\n            detailsHtml += `<tr>\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">${item.foodName}</td>\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">¥${item.foodPrice}</td>\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">${item.quantity}</td>\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">¥${item.subtotal}</td>\n                        </tr>`;\n          });\n          detailsHtml += `<tr style=\"background: #f9f9f9; font-weight: bold;\">\n                        <td colspan=\"3\" style=\"padding: 8px; border: 1px solid #ddd; text-align: right;\">订单总额：</td>\n                        <td style=\"padding: 8px; border: 1px solid #ddd;\">¥${totalAmount.toFixed(2)}</td>\n                    </tr>`;\n          detailsHtml += '</table></div>';\n          this.$alert(detailsHtml, '订单详情', {\n            dangerouslyUseHTMLString: true,\n            customClass: 'order-details-dialog',\n            confirmButtonText: '关闭'\n          });\n        } else {\n          this.$message.error('获取订单详情失败');\n        }\n      }).catch(() => {\n        this.$message.error('获取订单详情失败');\n      });\n    },\n    // 处理出餐操作\n    handleServeMeal(row) {\n      this.$confirm('确认要出餐吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const serveData = {\n          ...row,\n          status: '配送中'\n        };\n        this.$request({\n          url: '/dingdan/update',\n          method: 'PUT',\n          data: serveData\n        }).then(res => {\n          if (res.code === '200') {\n            this.$message.success('出餐成功');\n            this.load(1); // 重新加载数据\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {\n        this.$message.info('已取消出餐');\n      });\n    },\n    handleAdd() {\n      this.form = {\n        status: '待支付',\n        sfCreateTime: new Date()\n      };\n      this.fromVisible = true;\n    },\n    handleEdit(row) {\n      this.form = JSON.parse(JSON.stringify(row));\n      this.fromVisible = true;\n    },\n    save() {\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          // 格式化下单时间\n          if (this.form.sfCreateTime instanceof Date) {\n            this.form.sfCreateTime = this.formatDateTime(this.form.sfCreateTime);\n          }\n          this.$request({\n            url: this.form.id ? '/dingdan/update' : '/dingdan/add',\n            method: this.form.id ? 'PUT' : 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              this.$message.success('保存成功');\n              this.load(1);\n              this.fromVisible = false;\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    },\n    formatDateTime(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      const seconds = String(date.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n    del(id) {\n      this.$confirm('您确定删除吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/dingdan/delete/' + id).then(res => {\n          if (res.code === '200') {\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {});\n    },\n    handleSelectionChange(rows) {\n      this.ids = rows.map(v => v.id);\n    },\n    delBatch() {\n      if (!this.ids.length) {\n        this.$message.warning('请选择数据');\n        return;\n      }\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/dingdan/delete/batch', {\n          data: this.ids\n        }).then(res => {\n          if (res.code === '200') {\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {});\n    },\n    load(pageNum) {\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/dingdan/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          name: this.name\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          // 过滤掉购物车数据，只显示真正的订单\n          const allData = res.data?.list || [];\n          this.tableData = allData.filter(item => item.status !== '未付款' && !item.sfCartStatus);\n          this.total = this.tableData.length;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    reset() {\n      this.name = null;\n      this.load(1);\n    },\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "fromVisible", "form", "user", "JSON", "parse", "localStorage", "getItem", "rules", "sfUserName", "required", "message", "trigger", "sfUserId", "status", "sfOrderNumber", "sfCreateTime", "sfTotalPrice", "ids", "created", "load", "methods", "getStatusTagType", "handleRefund", "row", "action", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "refundData", "$request", "url", "method", "res", "code", "$message", "success", "error", "msg", "catch", "info", "showOrderDetails", "order", "get", "id", "orderItems", "length", "detailsHtml", "sfRemark", "sfEvaluation", "totalAmount", "for<PERSON>ach", "item", "parseFloat", "subtotal", "foodName", "foodPrice", "quantity", "toFixed", "$alert", "dangerouslyUseHTMLString", "customClass", "handleServeMeal", "serveData", "handleAdd", "Date", "handleEdit", "stringify", "save", "$refs", "formRef", "validate", "valid", "formatDateTime", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "del", "response", "delete", "handleSelectionChange", "rows", "map", "v", "delBatch", "warning", "params", "allData", "list", "filter", "sfCartStatus", "reset", "handleCurrentChange"], "sources": ["src/views/manager/Dingdan.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"search\">\r\n            <el-input placeholder=\"请输入关键字查询\" style=\"width: 200px\" v-model=\"name\"></el-input>\r\n            <el-button type=\"info\" plain style=\"margin-left: 10px\" @click=\"load(1)\">查询</el-button>\r\n            <el-button type=\"warning\" plain style=\"margin-left: 10px\" @click=\"reset\">重置</el-button>\r\n        </div>\r\n\r\n        <div class=\"operation\">\r\n            <el-button type=\"primary\" plain @click=\"handleAdd\">新增</el-button>\r\n            <el-button type=\"danger\" plain @click=\"delBatch\">批量删除</el-button>\r\n        </div>\r\n\r\n        <div class=\"table\">\r\n            <el-table :data=\"tableData\" stripe @selection-change=\"handleSelectionChange\">\r\n                <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n                <el-table-column prop=\"id\" label=\"序号\" width=\"70\" align=\"center\" sortable></el-table-column>\r\n                <el-table-column prop=\"sfUserName\" label=\"用户名\"></el-table-column>\r\n                <el-table-column prop=\"sfUserId\" label=\"用户ID\"></el-table-column>\r\n                <el-table-column prop=\"status\" label=\"订单状态\">\r\n                    <template v-slot=\"{row}\">\r\n                        <el-tag :type=\"getStatusTagType(row.status)\">\r\n                            {{ row.status }}\r\n                        </el-tag>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column prop=\"sfOrderNumber\" label=\"订单编号\"></el-table-column>\r\n                <el-table-column prop=\"sfCreateTime\" label=\"下单时间\"></el-table-column>\r\n                <el-table-column prop=\"sfRemark\" label=\"用户备注\"></el-table-column>\r\n                <el-table-column prop=\"sfEvaluation\" label=\"用户评价\"></el-table-column>\r\n                <el-table-column prop=\"sfTotalPrice\" label=\"订单价格\"></el-table-column>\r\n                <el-table-column label=\"操作\" align=\"center\" width=\"280\">\r\n                    <template v-slot=\"scope\">\r\n                        <div style=\"display: flex; justify-content: center; gap: 5px;\">\r\n                            <el-button size=\"mini\" type=\"info\" plain @click=\"showOrderDetails(scope.row)\">详情</el-button>\r\n                            <el-button size=\"mini\" type=\"primary\" plain @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n                            <el-button\r\n                                    size=\"mini\"\r\n                                    type=\"warning\"\r\n                                    plain\r\n                                    v-if=\"scope.row.status === '已支付'\"\r\n                                    @click=\"handleServeMeal(scope.row)\"\r\n                            >出餐</el-button>\r\n                            <el-button\r\n                                    size=\"mini\"\r\n                                    type=\"success\"\r\n                                    plain\r\n                                    v-if=\"scope.row.status === '退款中'\"\r\n                                    @click=\"handleRefund(scope.row, '已退款')\"\r\n                            >同意退款</el-button>\r\n                            <el-button\r\n                                    size=\"mini\"\r\n                                    type=\"danger\"\r\n                                    plain\r\n                                    v-if=\"scope.row.status === '退款中'\"\r\n                                    @click=\"handleRefund(scope.row, '已取消')\"\r\n                            >拒绝退款</el-button>\r\n                            <el-button size=\"mini\" type=\"danger\" plain @click=\"del(scope.row.id)\">删除</el-button>\r\n                        </div>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n\r\n            <div class=\"pagination\">\r\n                <el-pagination\r\n                        background\r\n                        @current-change=\"handleCurrentChange\"\r\n                        :current-page=\"pageNum\"\r\n                        :page-sizes=\"[5, 10, 20]\"\r\n                        :page-size=\"pageSize\"\r\n                        layout=\"total, prev, pager, next\"\r\n                        :total=\"total\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <el-dialog title=\"订单表\" :visible.sync=\"fromVisible\" width=\"40%\" :close-on-click-modal=\"false\" destroy-on-close>\r\n            <el-form :model=\"form\" label-width=\"100px\" style=\"padding-right: 50px\" :rules=\"rules\" ref=\"formRef\">\r\n                <el-form-item label=\"用户名\" prop=\"sfUserName\">\r\n                    <el-input v-model=\"form.sfUserName\" placeholder=\"用户名\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"用户ID\" prop=\"sfUserId\">\r\n                    <el-input v-model=\"form.sfUserId\" placeholder=\"用户ID\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"订单状态\" prop=\"status\">\r\n                    <el-select v-model=\"form.status\" placeholder=\"请选择订单状态\" style=\"width: 100%\">\r\n                        <el-option label=\"待支付\" value=\"待支付\"></el-option>\r\n                        <el-option label=\"已支付\" value=\"已支付\"></el-option>\r\n                        <el-option label=\"配送中\" value=\"配送中\"></el-option>\r\n                        <el-option label=\"已完成\" value=\"已完成\"></el-option>\r\n                        <el-option label=\"已取消\" value=\"已取消\"></el-option>\r\n                        <el-option label=\"退款中\" value=\"退款中\"></el-option>\r\n                        <el-option label=\"已退款\" value=\"已退款\"></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"订单编号\" prop=\"sfOrderNumber\">\r\n                    <el-input v-model=\"form.sfOrderNumber\" placeholder=\"订单编号\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"下单时间\" prop=\"sfCreateTime\">\r\n                    <el-date-picker\r\n                            v-model=\"form.sfCreateTime\"\r\n                            type=\"datetime\"\r\n                            placeholder=\"选择下单时间\"\r\n                            style=\"width: 100%\">\r\n                    </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"用户备注\" prop=\"sfRemark\">\r\n                    <el-input v-model=\"form.sfRemark\" placeholder=\"用户备注\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"用户评价\" prop=\"sfEvaluation\">\r\n                    <el-input v-model=\"form.sfEvaluation\" placeholder=\"用户评价\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"订单价格\" prop=\"sfTotalPrice\">\r\n                    <el-input-number v-model=\"form.sfTotalPrice\" :precision=\"2\" :step=\"0.1\" :min=\"0\" style=\"width: 100%\"></el-input-number>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"fromVisible = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Dingdan\",\r\n    data() {\r\n        return {\r\n            tableData: [],  // 所有的数据\r\n            pageNum: 1,   // 当前的页码\r\n            pageSize: 10,  // 每页显示的个数\r\n            total: 0,\r\n            name: null,\r\n            fromVisible: false,\r\n            form: {},\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n            rules: {\r\n                sfUserName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],\r\n                sfUserId: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],\r\n                status: [{ required: true, message: '请选择订单状态', trigger: 'change' }],\r\n                sfOrderNumber: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],\r\n                sfCreateTime: [{ required: true, message: '请选择下单时间', trigger: 'change' }],\r\n                sfTotalPrice: [{ required: true, message: '请输入订单价格', trigger: 'blur' }],\r\n            },\r\n            ids: []\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)\r\n    },\r\n    methods: {\r\n        getStatusTagType(status) {\r\n            switch (status) {\r\n                case '待支付': return 'danger';\r\n                case '已支付': return 'warning';\r\n                case '配送中': return '';\r\n                case '已完成': return 'success';\r\n                case '已取消': return 'info';\r\n                case '退款中': return 'danger';\r\n                case '已退款': return 'info';\r\n                default: return '';\r\n            }\r\n        },\r\n        // 处理退款操作\r\n        handleRefund(row, status) {\r\n            const action = status === '已退款' ? '同意退款' : '拒绝退款';\r\n            this.$confirm(`确定要${action}吗?`, '提示', {\r\n                confirmButtonText: '确定',\r\n                cancelButtonText: '取消',\r\n                type: 'warning'\r\n            }).then(() => {\r\n                const refundData = {\r\n                    ...row,\r\n                    status: status\r\n                }\r\n\r\n                this.$request({\r\n                    url: '/dingdan/update',\r\n                    method: 'PUT',\r\n                    data: refundData\r\n                }).then(res => {\r\n                    if (res.code === '200') {\r\n                        this.$message.success(`${action}成功`)\r\n                        this.load(1) // 重新加载数据\r\n                    } else {\r\n                        this.$message.error(res.msg)\r\n                    }\r\n                })\r\n            }).catch(() => {\r\n                this.$message.info(`已取消${action}`)\r\n            })\r\n        },\r\n\r\n        // 显示订单详情\r\n        showOrderDetails(order) {\r\n            this.$request.get(`/dingdan/details/${order.id}`).then(res => {\r\n                if (res.code === '200') {\r\n                    const orderItems = res.data || []\r\n                    if (orderItems.length === 0) {\r\n                        this.$message.info('该订单暂无详情信息')\r\n                        return\r\n                    }\r\n                    \r\n                    // 构建详情显示内容\r\n                    let detailsHtml = '<div style=\"max-height: 400px; overflow-y: auto;\">'\r\n                    detailsHtml += `<h4>订单编号：${order.sfOrderNumber}</h4>`\r\n                    detailsHtml += `<p><strong>订单状态：</strong>${order.status}</p>`\r\n                    detailsHtml += `<p><strong>用户：</strong>${order.sfUserName}</p>`\r\n                    detailsHtml += `<p><strong>下单时间：</strong>${order.sfCreateTime}</p>`\r\n                    if (order.sfRemark) {\r\n                        detailsHtml += `<p><strong>用户备注：</strong>${order.sfRemark}</p>`\r\n                    }\r\n                    if (order.sfEvaluation) {\r\n                        detailsHtml += `<p><strong>用户评价：</strong>${order.sfEvaluation}</p>`\r\n                    }\r\n                    detailsHtml += '<hr style=\"margin: 15px 0;\">'\r\n                    detailsHtml += '<h5>商品明细：</h5>'\r\n                    detailsHtml += '<table style=\"width: 100%; border-collapse: collapse;\">'\r\n                    detailsHtml += '<tr style=\"background: #f5f5f5;\"><th style=\"padding: 8px; border: 1px solid #ddd;\">商品名称</th><th style=\"padding: 8px; border: 1px solid #ddd;\">单价</th><th style=\"padding: 8px; border: 1px solid #ddd;\">数量</th><th style=\"padding: 8px; border: 1px solid #ddd;\">小计</th></tr>'\r\n                    \r\n                    let totalAmount = 0\r\n                    orderItems.forEach(item => {\r\n                        totalAmount += parseFloat(item.subtotal || 0)\r\n                        detailsHtml += `<tr>\r\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">${item.foodName}</td>\r\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">¥${item.foodPrice}</td>\r\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">${item.quantity}</td>\r\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">¥${item.subtotal}</td>\r\n                        </tr>`\r\n                    })\r\n                    \r\n                    detailsHtml += `<tr style=\"background: #f9f9f9; font-weight: bold;\">\r\n                        <td colspan=\"3\" style=\"padding: 8px; border: 1px solid #ddd; text-align: right;\">订单总额：</td>\r\n                        <td style=\"padding: 8px; border: 1px solid #ddd;\">¥${totalAmount.toFixed(2)}</td>\r\n                    </tr>`\r\n                    detailsHtml += '</table></div>'\r\n                    \r\n                    this.$alert(detailsHtml, '订单详情', {\r\n                        dangerouslyUseHTMLString: true,\r\n                        customClass: 'order-details-dialog',\r\n                        confirmButtonText: '关闭'\r\n                    })\r\n                } else {\r\n                    this.$message.error('获取订单详情失败')\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('获取订单详情失败')\r\n            })\r\n        },\r\n\r\n        // 处理出餐操作\r\n        handleServeMeal(row) {\r\n            this.$confirm('确认要出餐吗?', '提示', {\r\n                confirmButtonText: '确定',\r\n                cancelButtonText: '取消',\r\n                type: 'warning'\r\n            }).then(() => {\r\n                const serveData = {\r\n                    ...row,\r\n                    status: '配送中'\r\n                }\r\n\r\n                this.$request({\r\n                    url: '/dingdan/update',\r\n                    method: 'PUT',\r\n                    data: serveData\r\n                }).then(res => {\r\n                    if (res.code === '200') {\r\n                        this.$message.success('出餐成功')\r\n                        this.load(1) // 重新加载数据\r\n                    } else {\r\n                        this.$message.error(res.msg)\r\n                    }\r\n                })\r\n            }).catch(() => {\r\n                this.$message.info('已取消出餐')\r\n            })\r\n        },\r\n        handleAdd() {\r\n            this.form = {\r\n                status: '待支付',\r\n                sfCreateTime: new Date()\r\n            }\r\n            this.fromVisible = true\r\n        },\r\n        handleEdit(row) {\r\n            this.form = JSON.parse(JSON.stringify(row))\r\n            this.fromVisible = true\r\n        },\r\n        save() {\r\n            this.$refs.formRef.validate((valid) => {\r\n                if (valid) {\r\n                    // 格式化下单时间\r\n                    if (this.form.sfCreateTime instanceof Date) {\r\n                        this.form.sfCreateTime = this.formatDateTime(this.form.sfCreateTime)\r\n                    }\r\n\r\n                    this.$request({\r\n                        url: this.form.id ? '/dingdan/update' : '/dingdan/add',\r\n                        method: this.form.id ? 'PUT' : 'POST',\r\n                        data: this.form\r\n                    }).then(res => {\r\n                        if (res.code === '200') {\r\n                            this.$message.success('保存成功')\r\n                            this.load(1)\r\n                            this.fromVisible = false\r\n                        } else {\r\n                            this.$message.error(res.msg)\r\n                        }\r\n                    })\r\n                }\r\n            })\r\n        },\r\n        formatDateTime(date) {\r\n            const year = date.getFullYear()\r\n            const month = String(date.getMonth() + 1).padStart(2, '0')\r\n            const day = String(date.getDate()).padStart(2, '0')\r\n            const hours = String(date.getHours()).padStart(2, '0')\r\n            const minutes = String(date.getMinutes()).padStart(2, '0')\r\n            const seconds = String(date.getSeconds()).padStart(2, '0')\r\n\r\n            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n        },\r\n        del(id) {\r\n            this.$confirm('您确定删除吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n                this.$request.delete('/dingdan/delete/' + id).then(res => {\r\n                    if (res.code === '200') {\r\n                        this.$message.success('操作成功')\r\n                        this.load(1)\r\n                    } else {\r\n                        this.$message.error(res.msg)\r\n                    }\r\n                })\r\n            }).catch(() => {})\r\n        },\r\n        handleSelectionChange(rows) {\r\n            this.ids = rows.map(v => v.id)\r\n        },\r\n        delBatch() {\r\n            if (!this.ids.length) {\r\n                this.$message.warning('请选择数据')\r\n                return\r\n            }\r\n            this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n                this.$request.delete('/dingdan/delete/batch', {data: this.ids}).then(res => {\r\n                    if (res.code === '200') {\r\n                        this.$message.success('操作成功')\r\n                        this.load(1)\r\n                    } else {\r\n                        this.$message.error(res.msg)\r\n                    }\r\n                })\r\n            }).catch(() => {})\r\n        },\r\n        load(pageNum) {\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/dingdan/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n                    name: this.name,\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    // 过滤掉购物车数据，只显示真正的订单\r\n                    const allData = res.data?.list || []\r\n                    this.tableData = allData.filter(item => \r\n                        item.status !== '未付款' && !item.sfCartStatus\r\n                    )\r\n                    this.total = this.tableData.length\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            })\r\n        },\r\n        reset() {\r\n            this.name = null\r\n            this.load(1)\r\n        },\r\n        handleCurrentChange(pageNum) {\r\n            this.load(pageNum)\r\n        },\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.search {\r\n    margin-bottom: 20px;\r\n}\r\n.operation {\r\n    margin-bottom: 20px;\r\n}\r\n.pagination {\r\n    margin-top: 20px;\r\n    text-align: center;\r\n}\r\n\r\n/* 订单详情对话框样式 */\r\n.order-details-dialog >>> .el-message-box {\r\n    min-width: 600px;\r\n    max-width: 80vw;\r\n}\r\n\r\n.order-details-dialog >>> .el-message-box__content {\r\n    padding: 20px;\r\n    text-align: left;\r\n}\r\n\r\n.order-details-dialog >>> table {\r\n    margin-top: 10px;\r\n}\r\n\r\n.order-details-dialog >>> th {\r\n    background: #f8fafc !important;\r\n    font-weight: 600;\r\n    color: #374151;\r\n}\r\n\r\n.order-details-dialog >>> td {\r\n    color: #6b7280;\r\n}\r\n\r\n.order-details-dialog >>> tr:nth-child(even) {\r\n    background: #f9fafb;\r\n}\r\n</style>"], "mappings": ";;;;AA6HA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MACAL,IAAA;MACAM,WAAA;MACAC,IAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,KAAA;QACAC,UAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,QAAA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAE,MAAA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAG,aAAA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAI,YAAA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAK,YAAA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAM,GAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAC,iBAAAR,MAAA;MACA,QAAAA,MAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IACA;IACAS,aAAAC,GAAA,EAAAV,MAAA;MACA,MAAAW,MAAA,GAAAX,MAAA;MACA,KAAAY,QAAA,OAAAD,MAAA;QACAE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA,MAAAC,UAAA;UACA,GAAAP,GAAA;UACAV,MAAA,EAAAA;QACA;QAEA,KAAAkB,QAAA;UACAC,GAAA;UACAC,MAAA;UACAtC,IAAA,EAAAmC;QACA,GAAAD,IAAA,CAAAK,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAAC,QAAA,CAAAC,OAAA,IAAAb,MAAA;YACA,KAAAL,IAAA;UACA;YACA,KAAAiB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAC,KAAA;QACA,KAAAJ,QAAA,CAAAK,IAAA,OAAAjB,MAAA;MACA;IACA;IAEA;IACAkB,iBAAAC,KAAA;MACA,KAAAZ,QAAA,CAAAa,GAAA,qBAAAD,KAAA,CAAAE,EAAA,IAAAhB,IAAA,CAAAK,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,MAAAW,UAAA,GAAAZ,GAAA,CAAAvC,IAAA;UACA,IAAAmD,UAAA,CAAAC,MAAA;YACA,KAAAX,QAAA,CAAAK,IAAA;YACA;UACA;;UAEA;UACA,IAAAO,WAAA;UACAA,WAAA,gBAAAL,KAAA,CAAA7B,aAAA;UACAkC,WAAA,gCAAAL,KAAA,CAAA9B,MAAA;UACAmC,WAAA,8BAAAL,KAAA,CAAAnC,UAAA;UACAwC,WAAA,gCAAAL,KAAA,CAAA5B,YAAA;UACA,IAAA4B,KAAA,CAAAM,QAAA;YACAD,WAAA,gCAAAL,KAAA,CAAAM,QAAA;UACA;UACA,IAAAN,KAAA,CAAAO,YAAA;YACAF,WAAA,gCAAAL,KAAA,CAAAO,YAAA;UACA;UACAF,WAAA;UACAA,WAAA;UACAA,WAAA;UACAA,WAAA;UAEA,IAAAG,WAAA;UACAL,UAAA,CAAAM,OAAA,CAAAC,IAAA;YACAF,WAAA,IAAAG,UAAA,CAAAD,IAAA,CAAAE,QAAA;YACAP,WAAA;AACA,gFAAAK,IAAA,CAAAG,QAAA;AACA,iFAAAH,IAAA,CAAAI,SAAA;AACA,gFAAAJ,IAAA,CAAAK,QAAA;AACA,iFAAAL,IAAA,CAAAE,QAAA;AACA;UACA;UAEAP,WAAA;AACA;AACA,6EAAAG,WAAA,CAAAQ,OAAA;AACA;UACAX,WAAA;UAEA,KAAAY,MAAA,CAAAZ,WAAA;YACAa,wBAAA;YACAC,WAAA;YACApC,iBAAA;UACA;QACA;UACA,KAAAU,QAAA,CAAAE,KAAA;QACA;MACA,GAAAE,KAAA;QACA,KAAAJ,QAAA,CAAAE,KAAA;MACA;IACA;IAEA;IACAyB,gBAAAxC,GAAA;MACA,KAAAE,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA,MAAAmC,SAAA;UACA,GAAAzC,GAAA;UACAV,MAAA;QACA;QAEA,KAAAkB,QAAA;UACAC,GAAA;UACAC,MAAA;UACAtC,IAAA,EAAAqE;QACA,GAAAnC,IAAA,CAAAK,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAlB,IAAA;UACA;YACA,KAAAiB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAC,KAAA;QACA,KAAAJ,QAAA,CAAAK,IAAA;MACA;IACA;IACAwB,UAAA;MACA,KAAAhE,IAAA;QACAY,MAAA;QACAE,YAAA,MAAAmD,IAAA;MACA;MACA,KAAAlE,WAAA;IACA;IACAmE,WAAA5C,GAAA;MACA,KAAAtB,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAiE,SAAA,CAAA7C,GAAA;MACA,KAAAvB,WAAA;IACA;IACAqE,KAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,SAAAxE,IAAA,CAAAc,YAAA,YAAAmD,IAAA;YACA,KAAAjE,IAAA,CAAAc,YAAA,QAAA2D,cAAA,MAAAzE,IAAA,CAAAc,YAAA;UACA;UAEA,KAAAgB,QAAA;YACAC,GAAA,OAAA/B,IAAA,CAAA4C,EAAA;YACAZ,MAAA,OAAAhC,IAAA,CAAA4C,EAAA;YACAlD,IAAA,OAAAM;UACA,GAAA4B,IAAA,CAAAK,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA,KAAAlB,IAAA;cACA,KAAAnB,WAAA;YACA;cACA,KAAAoC,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;YACA;UACA;QACA;MACA;IACA;IACAmC,eAAAC,IAAA;MACA,MAAAC,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,MAAAC,KAAA,GAAAC,MAAA,CAAAJ,IAAA,CAAAK,QAAA,QAAAC,QAAA;MACA,MAAAC,GAAA,GAAAH,MAAA,CAAAJ,IAAA,CAAAQ,OAAA,IAAAF,QAAA;MACA,MAAAG,KAAA,GAAAL,MAAA,CAAAJ,IAAA,CAAAU,QAAA,IAAAJ,QAAA;MACA,MAAAK,OAAA,GAAAP,MAAA,CAAAJ,IAAA,CAAAY,UAAA,IAAAN,QAAA;MACA,MAAAO,OAAA,GAAAT,MAAA,CAAAJ,IAAA,CAAAc,UAAA,IAAAR,QAAA;MAEA,UAAAL,IAAA,IAAAE,KAAA,IAAAI,GAAA,IAAAE,KAAA,IAAAE,OAAA,IAAAE,OAAA;IACA;IACAE,IAAA7C,EAAA;MACA,KAAApB,QAAA;QAAAG,IAAA;MAAA,GAAAC,IAAA,CAAA8D,QAAA;QACA,KAAA5D,QAAA,CAAA6D,MAAA,sBAAA/C,EAAA,EAAAhB,IAAA,CAAAK,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAlB,IAAA;UACA;YACA,KAAAiB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAC,KAAA;IACA;IACAqD,sBAAAC,IAAA;MACA,KAAA7E,GAAA,GAAA6E,IAAA,CAAAC,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAnD,EAAA;IACA;IACAoD,SAAA;MACA,UAAAhF,GAAA,CAAA8B,MAAA;QACA,KAAAX,QAAA,CAAA8D,OAAA;QACA;MACA;MACA,KAAAzE,QAAA;QAAAG,IAAA;MAAA,GAAAC,IAAA,CAAA8D,QAAA;QACA,KAAA5D,QAAA,CAAA6D,MAAA;UAAAjG,IAAA,OAAAsB;QAAA,GAAAY,IAAA,CAAAK,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAlB,IAAA;UACA;YACA,KAAAiB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAC,KAAA;IACA;IACArB,KAAAtB,OAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAkC,QAAA,CAAAa,GAAA;QACAuD,MAAA;UACAtG,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAJ,IAAA,OAAAA;QACA;MACA,GAAAmC,IAAA,CAAAK,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA;UACA,MAAAiE,OAAA,GAAAlE,GAAA,CAAAvC,IAAA,EAAA0G,IAAA;UACA,KAAAzG,SAAA,GAAAwG,OAAA,CAAAE,MAAA,CAAAjD,IAAA,IACAA,IAAA,CAAAxC,MAAA,eAAAwC,IAAA,CAAAkD,YACA;UACA,KAAAxG,KAAA,QAAAH,SAAA,CAAAmD,MAAA;QACA;UACA,KAAAX,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IACAiE,MAAA;MACA,KAAA9G,IAAA;MACA,KAAAyB,IAAA;IACA;IACAsF,oBAAA5G,OAAA;MACA,KAAAsB,IAAA,CAAAtB,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}