{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: \"Manager\",\n  data() {\n    return {\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}')\n    };\n  },\n  created() {\n    if (!this.user.id) {\n      this.$router.push('/front/home');\n    }\n  },\n  methods: {\n    updateUser() {\n      this.user = JSON.parse(localStorage.getItem('xm-user') || '{}');\n    },\n    goToPerson() {\n      if (this.user.role === 'ADMIN') {\n        this.$router.push('/adminPerson');\n      }\n      if (this.user.role === 'BUSINESS') {\n        this.$router.push('/businessPerson');\n      }\n    },\n    logout() {\n      localStorage.removeItem('xm-user');\n      this.$router.push('/login');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "user", "JSON", "parse", "localStorage", "getItem", "created", "id", "$router", "push", "methods", "updateUser", "go<PERSON><PERSON><PERSON><PERSON>", "role", "logout", "removeItem"], "sources": ["src/views/Manager.vue"], "sourcesContent": ["<template>\r\n    <div class=\"manager-container\">\r\n        <!--  头部  -->\r\n        <div class=\"manager-header\">\r\n            <div class=\"manager-header-left\">\r\n                <img src=\"@/assets/imgs/logo.png\" style=\"border-radius: 10px\" />\r\n                <div class=\"title\">后台管理系统</div>\r\n            </div>\r\n\r\n            <div class=\"manager-header-center\">\r\n                <el-breadcrumb separator-class=\"el-icon-arrow-right\">\r\n                    <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\r\n                    <el-breadcrumb-item :to=\"{ path: $route.path }\">{{ $route.meta.name }}</el-breadcrumb-item>\r\n                </el-breadcrumb>\r\n            </div>\r\n\r\n            <div class=\"manager-header-right\">\r\n                <el-dropdown placement=\"bottom\">\r\n                    <div class=\"avatar\">\r\n                        <img :src=\"user.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'\" />\r\n                        <div>{{ user.name ||  '管理员' }}</div>\r\n                    </div>\r\n                    <el-dropdown-menu slot=\"dropdown\">\r\n                        <el-dropdown-item @click.native=\"goToPerson\">个人信息</el-dropdown-item>\r\n                        <el-dropdown-item @click.native=\"$router.push('/password')\">修改密码</el-dropdown-item>\r\n                        <el-dropdown-item @click.native=\"logout\">退出登录</el-dropdown-item>\r\n                    </el-dropdown-menu>\r\n                </el-dropdown>\r\n            </div>\r\n        </div>\r\n\r\n        <!--  主体  -->\r\n        <div class=\"manager-main\">\r\n            <!--  侧边栏  -->\r\n            <div class=\"manager-main-left\">\r\n                <el-menu\r\n                    :default-openeds=\"['info', 'admin', 'business', 'user', 'foods', 'dingdan', 'complaint', 'blogs', 'pinglun', 'leavemess', 'freemovies']\"\r\n                    router\r\n                    style=\"border: none\"\r\n                    :default-active=\"$route.path\"\r\n                >\r\n                    <el-menu-item index=\"/home\">\r\n                        <i class=\"el-icon-s-home\"></i>\r\n                        <span slot=\"title\">系统首页</span>\r\n                    </el-menu-item>\r\n\r\n                    <el-submenu index=\"info\">\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-s-management\"></i><span>信息管理</span>\r\n                        </template>\r\n                        <el-menu-item v-if=\"user.role === 'ADMIN'\" index=\"/notice\">\r\n                            <i class=\"el-icon-s-flag\"></i>\r\n                            <span slot=\"title\">公告信息</span>\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n                    <el-submenu index=\"admin\" v-if=\"user.role === 'ADMIN'\">\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-user-solid\"></i><span>管理员信息</span>\r\n                        </template>\r\n                        <el-menu-item index=\"/admin\">\r\n                            <i class=\"el-icon-s-custom\"></i>\r\n                            <span slot=\"title\">管理员信息</span>\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n                    <el-submenu index=\"business\" v-if=\"user.role === 'ADMIN'\">\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-s-shop\"></i><span>商家管理</span>\r\n                        </template>\r\n                        <el-menu-item index=\"/business\">\r\n                            <i class=\"el-icon-s-order\"></i>\r\n                            <span slot=\"title\">商家信息</span>\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n                    <el-submenu index=\"user\" v-if=\"user.role === 'ADMIN'\">\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-s-custom\"></i><span>用户管理</span>\r\n                        </template>\r\n                        <el-menu-item index=\"/user\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span slot=\"title\">用户信息</span>\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n\r\n                    <el-submenu index=\"foods\" v-if=\"user.role === 'ADMIN'\">\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-s-custom\"></i><span>食物管理</span>\r\n                        </template>\r\n                        <el-menu-item index=\"/foods\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span slot=\"title\">食物信息</span>\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n\r\n                    <el-submenu index=\"dingdan\" v-if=\"user.role === 'ADMIN'\">\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-s-custom\"></i><span>订单管理</span>\r\n                        </template>\r\n                        <el-menu-item index=\"/dingdan\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span slot=\"title\">订单信息</span>\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n\r\n                    <!-- 投诉管理 (仅管理员可见) -->\r\n                    <el-submenu index=\"complaint\" v-if=\"user.role === 'ADMIN'\">\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-warning\"></i>\r\n                            <span>投诉管理</span>\r\n                        </template>\r\n                        <el-menu-item index=\"/complaint\">\r\n                            <i class=\"el-icon-warning\"></i>\r\n                            <span>投诉信息</span>\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n\r\n                    <el-submenu index=\"blogs\" >\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-folder\"></i>\r\n                            <span>系统讨论</span>\r\n                        </template>\r\n                        <el-menu-item index=\"/blogs\">\r\n                            <i class=\"el-icon-folder-opened\"></i>\r\n                            <span>系统讨论</span>\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n                    <el-submenu index=\"pinglun\">\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-chat-dot-square\"></i>\r\n                            <span>系统评论管理</span>\r\n                        </template>\r\n                        <el-menu-item index=\"/pinglun\">\r\n                            <i class=\"el-icon-chat-dot-square\"></i>\r\n                            <span>系统评论信息</span>\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n                    <el-submenu index=\"leavemess\">\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-message\"></i>\r\n                            <span>咨询评论</span>\r\n                        </template>\r\n                        <el-menu-item index=\"/leavemess\">\r\n                            评论信息\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n                    <el-submenu index=\"freemovies\">\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-notebook-2\"></i>\r\n                            <span>点餐推荐模块</span>\r\n                        </template>\r\n                        <el-menu-item index=\"/freemovies\">\r\n                            <i class=\"el-icon-notebook-1\"></i>\r\n                            <span>点餐推荐管理</span>\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n                    <el-submenu index=\"complaint\" v-if=\"user.role === 'ADMIN'\">\r\n                        <template slot=\"title\">\r\n                            <i class=\"el-icon-warning-outline\"></i>\r\n                            <span>点餐投诉模块</span>\r\n                        </template>\r\n                        <el-menu-item index=\"/complaint\">\r\n                            <i class=\"el-icon-warning\"></i>\r\n                            <span>点餐投诉信息</span>\r\n                        </el-menu-item>\r\n                    </el-submenu>\r\n\r\n                </el-menu>\r\n            </div>\r\n\r\n            <!--  数据表格  -->\r\n            <div class=\"manager-main-right\">\r\n                <router-view @update:user=\"updateUser\" />\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Manager\",\r\n    data() {\r\n        return {\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n        }\r\n    },\r\n    created() {\r\n        if (!this.user.id) {\r\n            this.$router.push('/front/home')\r\n        }\r\n    },\r\n    methods: {\r\n        updateUser() {\r\n            this.user = JSON.parse(localStorage.getItem('xm-user') || '{}')\r\n        },\r\n        goToPerson() {\r\n            if (this.user.role === 'ADMIN') {\r\n                this.$router.push('/adminPerson')\r\n            }\r\n            if (this.user.role === 'BUSINESS') {\r\n                this.$router.push('/businessPerson')\r\n            }\r\n        },\r\n        logout() {\r\n            localStorage.removeItem('xm-user')\r\n            this.$router.push('/login')\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n@import \"@/assets/css/manager.css\";\r\n</style>"], "mappings": ";AA4LA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA;EACA;EACAC,QAAA;IACA,UAAAL,IAAA,CAAAM,EAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;EACA;EACAC,OAAA;IACAC,WAAA;MACA,KAAAV,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA;IACAO,WAAA;MACA,SAAAX,IAAA,CAAAY,IAAA;QACA,KAAAL,OAAA,CAAAC,IAAA;MACA;MACA,SAAAR,IAAA,CAAAY,IAAA;QACA,KAAAL,OAAA,CAAAC,IAAA;MACA;IACA;IACAK,OAAA;MACAV,YAAA,CAAAW,UAAA;MACA,KAAAP,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}