{"ast": null, "code": "import \"core-js/modules/esnext.iterator.map.js\";\nexport default {\n  name: \"Foods\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前的页码\n      pageSize: 10,\n      // 每页显示的个数\n      total: 0,\n      name: null,\n      categoryId: null,\n      // 分类筛选\n      categories: [],\n      // 分类选项\n      fromVisible: false,\n      form: {},\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      rules: {},\n      ids: []\n    };\n  },\n  created() {\n    this.load(1);\n    this.loadCategories();\n  },\n  methods: {\n    handleAdd() {\n      // 新增数据\n      this.form = {}; // 新增数据的时候清空数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    handleEdit(row) {\n      // 编辑数据\n      this.form = JSON.parse(JSON.stringify(row)); // 给form对象赋值  注意要深拷贝数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    save() {\n      // 保存按钮触发的逻辑  它会触发新增或者更新\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          this.$request({\n            url: this.form.id ? '/foods/update' : '/foods/add',\n            method: this.form.id ? 'PUT' : 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              // 表示成功保存\n              this.$message.success('保存成功');\n              this.load(1);\n              this.fromVisible = false;\n            } else {\n              this.$message.error(res.msg); // 弹出错误的信息\n            }\n          });\n        }\n      });\n    },\n    handleImgSuccess(response, file, fileList) {\n      this.form.sfImage = response.data;\n    },\n    del(id) {\n      // 单个删除\n      this.$confirm('您确定删除吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/foods/delete/' + id).then(res => {\n          if (res.code === '200') {\n            // 表示操作成功\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg); // 弹出错误的信息\n          }\n        });\n      }).catch(() => {});\n    },\n    handleSelectionChange(rows) {\n      // 当前选中的所有的行数据\n      this.ids = rows.map(v => v.id);\n    },\n    delBatch() {\n      // 批量删除\n      if (!this.ids.length) {\n        this.$message.warning('请选择数据');\n        return;\n      }\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/foods/delete/batch', {\n          data: this.ids\n        }).then(res => {\n          if (res.code === '200') {\n            // 表示操作成功\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg); // 弹出错误的信息\n          }\n        });\n      }).catch(() => {});\n    },\n    load(pageNum) {\n      // 分页查询\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/foods/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          name: this.name,\n          categoryId: this.categoryId\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list;\n          this.total = res.data?.total;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    reset() {\n      this.name = null;\n      this.categoryId = null;\n      this.load(1);\n    },\n    loadCategories() {\n      // 加载分类选项\n      this.$request.get('/category/selectEnabled').then(res => {\n        if (res.code === '200') {\n          this.categories = res.data || [];\n        } else {\n          console.error('加载分类失败:', res.msg);\n        }\n      }).catch(err => {\n        console.error('加载分类失败:', err);\n      });\n    },\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "categoryId", "categories", "fromVisible", "form", "user", "JSON", "parse", "localStorage", "getItem", "rules", "ids", "created", "load", "loadCategories", "methods", "handleAdd", "handleEdit", "row", "stringify", "save", "$refs", "formRef", "validate", "valid", "$request", "url", "id", "method", "then", "res", "code", "$message", "success", "error", "msg", "handleImgSuccess", "response", "file", "fileList", "sfImage", "del", "$confirm", "type", "delete", "catch", "handleSelectionChange", "rows", "map", "v", "delBatch", "length", "warning", "get", "params", "list", "reset", "console", "err", "handleCurrentChange"], "sources": ["src/views/manager/Foods.vue"], "sourcesContent": ["\r\n<template>\r\n  <div>\r\n    <div class=\"search\">\r\n      <el-input placeholder=\"请输入关键字查询\" style=\"width: 200px\" v-model=\"name\"></el-input>\r\n      <el-select v-model=\"categoryId\" placeholder=\"请选择分类\" style=\"width: 150px; margin-left: 10px\" clearable>\r\n        <el-option \r\n          v-for=\"category in categories\" \r\n          :key=\"category.id\" \r\n          :label=\"category.name\" \r\n          :value=\"category.id\">\r\n        </el-option>\r\n      </el-select>\r\n      <el-button type=\"info\" plain style=\"margin-left: 10px\" @click=\"load(1)\">查询</el-button>\r\n      <el-button type=\"warning\" plain style=\"margin-left: 10px\" @click=\"reset\">重置</el-button>\r\n    </div>\r\n\r\n    <div class=\"operation\">\r\n      <el-button type=\"primary\" plain @click=\"handleAdd\">新增</el-button>\r\n      <el-button type=\"danger\" plain @click=\"delBatch\">批量删除</el-button>\r\n    </div>\r\n\r\n    <div class=\"table\">\r\n      <el-table :data=\"tableData\" strip @selection-change=\"handleSelectionChange\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"id\" label=\"序号\" width=\"70\" align=\"center\" sortable></el-table-column>\r\n        <el-table-column prop=\"name\" label=\"食品名称\"></el-table-column>\r\n        <el-table-column prop=\"sfImage\" label=\"食物照片\" align=\"center\">\r\n          <template v-slot=\"scope\">\r\n            <div style=\"display: flex; justify-content: center; align-items: center; height: 40px;\">\r\n              <el-image v-if=\"scope.row.sfImage\"\r\n                        style=\"width: 40px; height: 40px;\"\r\n                        :src=\"scope.row.sfImage\"\r\n                        :preview-src-list=\"[scope.row.sfImage]\">\r\n              </el-image>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"sfDescription\" label=\"食物描述\"></el-table-column>\r\n        <el-table-column prop=\"categoryName\" label=\"食物分类\">\r\n          <template v-slot=\"scope\">\r\n            {{ scope.row.categoryName || scope.row.sfCategory || '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"sfPrice\" label=\"食物价格\"></el-table-column>\r\n        <el-table-column prop=\"sfShelfStatus\" label=\"上架状态\"></el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"180\">\r\n          <template v-slot=\"scope\">\r\n            <el-button size=\"mini\" type=\"primary\" plain @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button size=\"mini\" type=\"danger\" plain @click=\"del(scope.row.id)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"pagination\">\r\n        <el-pagination\r\n            background\r\n            @current-change=\"handleCurrentChange\"\r\n            :current-page=\"pageNum\"\r\n            :page-sizes=\"[5, 10, 20]\"\r\n            :page-size=\"pageSize\"\r\n            layout=\"total, prev, pager, next\"\r\n            :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog title=\"商品表\" :visible.sync=\"fromVisible\" width=\"40%\" :close-on-click-modal=\"false\" destroy-on-close>\r\n      <el-form :model=\"form\" label-width=\"100px\" style=\"padding-right: 50px\" :rules=\"rules\" ref=\"formRef\">\r\n        <el-form-item label=\"食品名称\" prop=\"name\">\r\n          <el-input v-model=\"form.name\" placeholder=\"食品名称\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"食物照片\" prop=\"sfImage\">\r\n          <el-upload\r\n              :action=\"$baseUrl + '/files/upload'\"\r\n              :headers=\"{ token: user.token }\"\r\n              list-type=\"picture\"\r\n              :on-success=\"handleImgSuccess\">\r\n            <el-button type=\"primary\">上传</el-button>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item label=\"食物描述\" prop=\"sfDescription\">\r\n          <el-input v-model=\"form.sfDescription\" placeholder=\"食物描述\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"食物分类\" prop=\"categoryId\">\r\n          <el-select v-model=\"form.categoryId\" placeholder=\"请选择分类\" style=\"width: 100%\" clearable>\r\n            <el-option \r\n              v-for=\"category in categories\" \r\n              :key=\"category.id\" \r\n              :label=\"category.name\" \r\n              :value=\"category.id\">\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"食物类型（兼容）\" prop=\"sfCategory\">\r\n          <el-input v-model=\"form.sfCategory\" placeholder=\"食物类型（保持兼容性）\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"食物价格\" prop=\"sfPrice\">\r\n          <el-input v-model=\"form.sfPrice\" placeholder=\"食物价格\"></el-input>\r\n        </el-form-item>\r\n\r\n          <el-form-item label=\"上架状态\" prop=\"sfShelfStatus\">\r\n              <el-select v-model=\"form.sfShelfStatus\" placeholder=\"请选择上架状态\" clearable>\r\n                  <el-option label=\"上架\" value=\"上架\"></el-option>\r\n                  <el-option label=\"下架\" value=\"下架\"></el-option>\r\n              </el-select>\r\n          </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"fromVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Foods\",\r\n  data() {\r\n    return {\r\n      tableData: [],  // 所有的数据\r\n      pageNum: 1,   // 当前的页码\r\n      pageSize: 10,  // 每页显示的个数\r\n      total: 0,\r\n      name: null,\r\n      categoryId: null,  // 分类筛选\r\n      categories: [],  // 分类选项\r\n      fromVisible: false,\r\n      form: {},\r\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n      rules: {},\r\n      ids: []\r\n    }\r\n  },\r\n  created() {\r\n    this.load(1)\r\n    this.loadCategories()\r\n  },\r\n  methods: {\r\n    handleAdd() {   // 新增数据\r\n      this.form = {}  // 新增数据的时候清空数据\r\n      this.fromVisible = true   // 打开弹窗\r\n    },\r\n    handleEdit(row) {   // 编辑数据\r\n      this.form = JSON.parse(JSON.stringify(row))  // 给form对象赋值  注意要深拷贝数据\r\n      this.fromVisible = true   // 打开弹窗\r\n    },\r\n    save() {   // 保存按钮触发的逻辑  它会触发新增或者更新\r\n      this.$refs.formRef.validate((valid) => {\r\n        if (valid) {\r\n          this.$request({\r\n            url: this.form.id ? '/foods/update' : '/foods/add',\r\n            method: this.form.id ? 'PUT' : 'POST',\r\n            data: this.form\r\n          }).then(res => {\r\n            if (res.code === '200') {  // 表示成功保存\r\n              this.$message.success('保存成功')\r\n              this.load(1)\r\n              this.fromVisible = false\r\n            } else {\r\n              this.$message.error(res.msg)  // 弹出错误的信息\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleImgSuccess(response, file, fileList) {\r\n      this.form.sfImage = response.data\r\n    },\r\n    del(id) {   // 单个删除\r\n      this.$confirm('您确定删除吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n        this.$request.delete('/foods/delete/' + id).then(res => {\r\n          if (res.code === '200') {   // 表示操作成功\r\n            this.$message.success('操作成功')\r\n            this.load(1)\r\n          } else {\r\n            this.$message.error(res.msg)  // 弹出错误的信息\r\n          }\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleSelectionChange(rows) {   // 当前选中的所有的行数据\r\n      this.ids = rows.map(v => v.id)\r\n    },\r\n    delBatch() {   // 批量删除\r\n      if (!this.ids.length) {\r\n        this.$message.warning('请选择数据')\r\n        return\r\n      }\r\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n        this.$request.delete('/foods/delete/batch', {data: this.ids}).then(res => {\r\n          if (res.code === '200') {   // 表示操作成功\r\n            this.$message.success('操作成功')\r\n            this.load(1)\r\n          } else {\r\n            this.$message.error(res.msg)  // 弹出错误的信息\r\n          }\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    load(pageNum) {  // 分页查询\r\n      if (pageNum) this.pageNum = pageNum\r\n      this.$request.get('/foods/selectPage', {\r\n        params: {\r\n          pageNum: this.pageNum,\r\n          pageSize: this.pageSize,\r\n          name: this.name,\r\n          categoryId: this.categoryId,\r\n        }\r\n      }).then(res => {\r\n        if (res.code === '200') {\r\n          this.tableData = res.data?.list\r\n          this.total = res.data?.total\r\n        } else {\r\n          this.$message.error(res.msg)\r\n        }\r\n      })\r\n    },\r\n    reset() {\r\n      this.name = null\r\n      this.categoryId = null\r\n      this.load(1)\r\n    },\r\n    loadCategories() {  // 加载分类选项\r\n      this.$request.get('/category/selectEnabled').then(res => {\r\n        if (res.code === '200') {\r\n          this.categories = res.data || []\r\n        } else {\r\n          console.error('加载分类失败:', res.msg)\r\n        }\r\n      }).catch(err => {\r\n        console.error('加载分类失败:', err)\r\n      })\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.load(pageNum)\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n</style>"], "mappings": ";AAqHA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MACAL,IAAA;MACAM,UAAA;MAAA;MACAC,UAAA;MAAA;MACAC,WAAA;MACAC,IAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,KAAA;MACAC,GAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAC,UAAA;MAAA;MACA,KAAAZ,IAAA;MACA,KAAAD,WAAA;IACA;IACAc,WAAAC,GAAA;MAAA;MACA,KAAAd,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAa,SAAA,CAAAD,GAAA;MACA,KAAAf,WAAA;IACA;IACAiB,KAAA;MAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,QAAA;YACAC,GAAA,OAAAtB,IAAA,CAAAuB,EAAA;YACAC,MAAA,OAAAxB,IAAA,CAAAuB,EAAA;YACA/B,IAAA,OAAAQ;UACA,GAAAyB,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cAAA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA,KAAApB,IAAA;cACA,KAAAV,WAAA;YACA;cACA,KAAA6B,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;YACA;UACA;QACA;MACA;IACA;IACAC,iBAAAC,QAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAAnC,IAAA,CAAAoC,OAAA,GAAAH,QAAA,CAAAzC,IAAA;IACA;IACA6C,IAAAd,EAAA;MAAA;MACA,KAAAe,QAAA;QAAAC,IAAA;MAAA,GAAAd,IAAA,CAAAQ,QAAA;QACA,KAAAZ,QAAA,CAAAmB,MAAA,oBAAAjB,EAAA,EAAAE,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAApB,IAAA;UACA;YACA,KAAAmB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAU,KAAA,QACA;IACA;IACAC,sBAAAC,IAAA;MAAA;MACA,KAAApC,GAAA,GAAAoC,IAAA,CAAAC,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAtB,EAAA;IACA;IACAuB,SAAA;MAAA;MACA,UAAAvC,GAAA,CAAAwC,MAAA;QACA,KAAAnB,QAAA,CAAAoB,OAAA;QACA;MACA;MACA,KAAAV,QAAA;QAAAC,IAAA;MAAA,GAAAd,IAAA,CAAAQ,QAAA;QACA,KAAAZ,QAAA,CAAAmB,MAAA;UAAAhD,IAAA,OAAAe;QAAA,GAAAkB,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAApB,IAAA;UACA;YACA,KAAAmB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAU,KAAA,QACA;IACA;IACAhC,KAAAf,OAAA;MAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAA2B,QAAA,CAAA4B,GAAA;QACAC,MAAA;UACAxD,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAJ,IAAA,OAAAA,IAAA;UACAM,UAAA,OAAAA;QACA;MACA,GAAA4B,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAlC,SAAA,GAAAiC,GAAA,CAAAlC,IAAA,EAAA2D,IAAA;UACA,KAAAvD,KAAA,GAAA8B,GAAA,CAAAlC,IAAA,EAAAI,KAAA;QACA;UACA,KAAAgC,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IACAqB,MAAA;MACA,KAAA7D,IAAA;MACA,KAAAM,UAAA;MACA,KAAAY,IAAA;IACA;IACAC,eAAA;MAAA;MACA,KAAAW,QAAA,CAAA4B,GAAA,4BAAAxB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAA7B,UAAA,GAAA4B,GAAA,CAAAlC,IAAA;QACA;UACA6D,OAAA,CAAAvB,KAAA,YAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAU,KAAA,CAAAa,GAAA;QACAD,OAAA,CAAAvB,KAAA,YAAAwB,GAAA;MACA;IACA;IACAC,oBAAA7D,OAAA;MACA,KAAAe,IAAA,CAAAf,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}