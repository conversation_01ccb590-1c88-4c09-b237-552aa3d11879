{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { BoundingRect, OrientedBoundingRect } from '../util/graphic.js';\nexport function prepareLayoutList(input) {\n  var list = [];\n  for (var i = 0; i < input.length; i++) {\n    var rawItem = input[i];\n    if (rawItem.defaultAttr.ignore) {\n      continue;\n    }\n    var label = rawItem.label;\n    var transform = label.getComputedTransform();\n    // NOTE: Get bounding rect after getComputedTransform, or label may not been updated by the host el.\n    var localRect = label.getBoundingRect();\n    var isAxisAligned = !transform || transform[1] < 1e-5 && transform[2] < 1e-5;\n    var minMargin = label.style.margin || 0;\n    var globalRect = localRect.clone();\n    globalRect.applyTransform(transform);\n    globalRect.x -= minMargin / 2;\n    globalRect.y -= minMargin / 2;\n    globalRect.width += minMargin;\n    globalRect.height += minMargin;\n    var obb = isAxisAligned ? new OrientedBoundingRect(localRect, transform) : null;\n    list.push({\n      label: label,\n      labelLine: rawItem.labelLine,\n      rect: globalRect,\n      localRect: localRect,\n      obb: obb,\n      priority: rawItem.priority,\n      defaultAttr: rawItem.defaultAttr,\n      layoutOption: rawItem.computedLayoutOption,\n      axisAligned: isAxisAligned,\n      transform: transform\n    });\n  }\n  return list;\n}\nfunction shiftLayout(list, xyDim, sizeDim, minBound, maxBound, balanceShift) {\n  var len = list.length;\n  if (len < 2) {\n    return;\n  }\n  list.sort(function (a, b) {\n    return a.rect[xyDim] - b.rect[xyDim];\n  });\n  var lastPos = 0;\n  var delta;\n  var adjusted = false;\n  var shifts = [];\n  var totalShifts = 0;\n  for (var i = 0; i < len; i++) {\n    var item = list[i];\n    var rect = item.rect;\n    delta = rect[xyDim] - lastPos;\n    if (delta < 0) {\n      // shiftForward(i, len, -delta);\n      rect[xyDim] -= delta;\n      item.label[xyDim] -= delta;\n      adjusted = true;\n    }\n    var shift = Math.max(-delta, 0);\n    shifts.push(shift);\n    totalShifts += shift;\n    lastPos = rect[xyDim] + rect[sizeDim];\n  }\n  if (totalShifts > 0 && balanceShift) {\n    // Shift back to make the distribution more equally.\n    shiftList(-totalShifts / len, 0, len);\n  }\n  // TODO bleedMargin?\n  var first = list[0];\n  var last = list[len - 1];\n  var minGap;\n  var maxGap;\n  updateMinMaxGap();\n  // If ends exceed two bounds, squeeze at most 80%, then take the gap of two bounds.\n  minGap < 0 && squeezeGaps(-minGap, 0.8);\n  maxGap < 0 && squeezeGaps(maxGap, 0.8);\n  updateMinMaxGap();\n  takeBoundsGap(minGap, maxGap, 1);\n  takeBoundsGap(maxGap, minGap, -1);\n  // Handle bailout when there is not enough space.\n  updateMinMaxGap();\n  if (minGap < 0) {\n    squeezeWhenBailout(-minGap);\n  }\n  if (maxGap < 0) {\n    squeezeWhenBailout(maxGap);\n  }\n  function updateMinMaxGap() {\n    minGap = first.rect[xyDim] - minBound;\n    maxGap = maxBound - last.rect[xyDim] - last.rect[sizeDim];\n  }\n  function takeBoundsGap(gapThisBound, gapOtherBound, moveDir) {\n    if (gapThisBound < 0) {\n      // Move from other gap if can.\n      var moveFromMaxGap = Math.min(gapOtherBound, -gapThisBound);\n      if (moveFromMaxGap > 0) {\n        shiftList(moveFromMaxGap * moveDir, 0, len);\n        var remained = moveFromMaxGap + gapThisBound;\n        if (remained < 0) {\n          squeezeGaps(-remained * moveDir, 1);\n        }\n      } else {\n        squeezeGaps(-gapThisBound * moveDir, 1);\n      }\n    }\n  }\n  function shiftList(delta, start, end) {\n    if (delta !== 0) {\n      adjusted = true;\n    }\n    for (var i = start; i < end; i++) {\n      var item = list[i];\n      var rect = item.rect;\n      rect[xyDim] += delta;\n      item.label[xyDim] += delta;\n    }\n  }\n  // Squeeze gaps if the labels exceed margin.\n  function squeezeGaps(delta, maxSqeezePercent) {\n    var gaps = [];\n    var totalGaps = 0;\n    for (var i = 1; i < len; i++) {\n      var prevItemRect = list[i - 1].rect;\n      var gap = Math.max(list[i].rect[xyDim] - prevItemRect[xyDim] - prevItemRect[sizeDim], 0);\n      gaps.push(gap);\n      totalGaps += gap;\n    }\n    if (!totalGaps) {\n      return;\n    }\n    var squeezePercent = Math.min(Math.abs(delta) / totalGaps, maxSqeezePercent);\n    if (delta > 0) {\n      for (var i = 0; i < len - 1; i++) {\n        // Distribute the shift delta to all gaps.\n        var movement = gaps[i] * squeezePercent;\n        // Forward\n        shiftList(movement, 0, i + 1);\n      }\n    } else {\n      // Backward\n      for (var i = len - 1; i > 0; i--) {\n        // Distribute the shift delta to all gaps.\n        var movement = gaps[i - 1] * squeezePercent;\n        shiftList(-movement, i, len);\n      }\n    }\n  }\n  /**\r\n   * Squeeze to allow overlap if there is no more space available.\r\n   * Let other overlapping strategy like hideOverlap do the job instead of keep exceeding the bounds.\r\n   */\n  function squeezeWhenBailout(delta) {\n    var dir = delta < 0 ? -1 : 1;\n    delta = Math.abs(delta);\n    var moveForEachLabel = Math.ceil(delta / (len - 1));\n    for (var i = 0; i < len - 1; i++) {\n      if (dir > 0) {\n        // Forward\n        shiftList(moveForEachLabel, 0, i + 1);\n      } else {\n        // Backward\n        shiftList(-moveForEachLabel, len - i - 1, len);\n      }\n      delta -= moveForEachLabel;\n      if (delta <= 0) {\n        return;\n      }\n    }\n  }\n  return adjusted;\n}\n/**\r\n * Adjust labels on x direction to avoid overlap.\r\n */\nexport function shiftLayoutOnX(list, leftBound, rightBound,\n// If average the shifts on all labels and add them to 0\n// TODO: Not sure if should enable it.\n// Pros: The angle of lines will distribute more equally\n// Cons: In some layout. It may not what user wanted. like in pie. the label of last sector is usually changed unexpectedly.\nbalanceShift) {\n  return shiftLayout(list, 'x', 'width', leftBound, rightBound, balanceShift);\n}\n/**\r\n * Adjust labels on y direction to avoid overlap.\r\n */\nexport function shiftLayoutOnY(list, topBound, bottomBound,\n// If average the shifts on all labels and add them to 0\nbalanceShift) {\n  return shiftLayout(list, 'y', 'height', topBound, bottomBound, balanceShift);\n}\nexport function hideOverlap(labelList) {\n  var displayedLabels = [];\n  // TODO, render overflow visible first, put in the displayedLabels.\n  labelList.sort(function (a, b) {\n    return b.priority - a.priority;\n  });\n  var globalRect = new BoundingRect(0, 0, 0, 0);\n  function hideEl(el) {\n    if (!el.ignore) {\n      // Show on emphasis.\n      var emphasisState = el.ensureState('emphasis');\n      if (emphasisState.ignore == null) {\n        emphasisState.ignore = false;\n      }\n    }\n    el.ignore = true;\n  }\n  for (var i = 0; i < labelList.length; i++) {\n    var labelItem = labelList[i];\n    var isAxisAligned = labelItem.axisAligned;\n    var localRect = labelItem.localRect;\n    var transform = labelItem.transform;\n    var label = labelItem.label;\n    var labelLine = labelItem.labelLine;\n    globalRect.copy(labelItem.rect);\n    // Add a threshold because layout may be aligned precisely.\n    globalRect.width -= 0.1;\n    globalRect.height -= 0.1;\n    globalRect.x += 0.05;\n    globalRect.y += 0.05;\n    var obb = labelItem.obb;\n    var overlapped = false;\n    for (var j = 0; j < displayedLabels.length; j++) {\n      var existsTextCfg = displayedLabels[j];\n      // Fast rejection.\n      if (!globalRect.intersect(existsTextCfg.rect)) {\n        continue;\n      }\n      if (isAxisAligned && existsTextCfg.axisAligned) {\n        // Is overlapped\n        overlapped = true;\n        break;\n      }\n      if (!existsTextCfg.obb) {\n        // If self is not axis aligned. But other is.\n        existsTextCfg.obb = new OrientedBoundingRect(existsTextCfg.localRect, existsTextCfg.transform);\n      }\n      if (!obb) {\n        // If self is axis aligned. But other is not.\n        obb = new OrientedBoundingRect(localRect, transform);\n      }\n      if (obb.intersect(existsTextCfg.obb)) {\n        overlapped = true;\n        break;\n      }\n    }\n    // TODO Callback to determine if this overlap should be handled?\n    if (overlapped) {\n      hideEl(label);\n      labelLine && hideEl(labelLine);\n    } else {\n      label.attr('ignore', labelItem.defaultAttr.ignore);\n      labelLine && labelLine.attr('ignore', labelItem.defaultAttr.labelGuideIgnore);\n      displayedLabels.push(labelItem);\n    }\n  }\n}", "map": {"version": 3, "names": ["BoundingRect", "OrientedBoundingRect", "prepareLayoutList", "input", "list", "i", "length", "rawItem", "defaultAttr", "ignore", "label", "transform", "getComputedTransform", "localRect", "getBoundingRect", "isAxisAligned", "<PERSON><PERSON><PERSON><PERSON>", "style", "margin", "globalRect", "clone", "applyTransform", "x", "y", "width", "height", "obb", "push", "labelLine", "rect", "priority", "layoutOption", "computedLayoutOption", "axisAligned", "shiftLayout", "xyDim", "sizeDim", "minBound", "maxBound", "balanceShift", "len", "sort", "a", "b", "lastPos", "delta", "adjusted", "shifts", "totalShifts", "item", "shift", "Math", "max", "shiftList", "first", "last", "minGap", "maxGap", "updateMinMaxGap", "squeezeGaps", "takeBoundsGap", "squeezeWhenBailout", "gapThisBound", "gapOtherBound", "moveDir", "moveFromMaxGap", "min", "remained", "start", "end", "maxSqeezePercent", "gaps", "totalGaps", "prevItemRect", "gap", "squeezePercent", "abs", "movement", "dir", "moveForEachLabel", "ceil", "shiftLayoutOnX", "leftBound", "rightBound", "shiftLayoutOnY", "topBound", "bottomBound", "hideOverlap", "labelList", "displayed<PERSON><PERSON><PERSON>", "hideEl", "el", "emphasisState", "ensureState", "labelItem", "copy", "overlapped", "j", "existsTextCfg", "intersect", "attr", "labelGuideIgnore"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/label/labelLayoutHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { BoundingRect, OrientedBoundingRect } from '../util/graphic.js';\nexport function prepareLayoutList(input) {\n  var list = [];\n  for (var i = 0; i < input.length; i++) {\n    var rawItem = input[i];\n    if (rawItem.defaultAttr.ignore) {\n      continue;\n    }\n    var label = rawItem.label;\n    var transform = label.getComputedTransform();\n    // NOTE: Get bounding rect after getComputedTransform, or label may not been updated by the host el.\n    var localRect = label.getBoundingRect();\n    var isAxisAligned = !transform || transform[1] < 1e-5 && transform[2] < 1e-5;\n    var minMargin = label.style.margin || 0;\n    var globalRect = localRect.clone();\n    globalRect.applyTransform(transform);\n    globalRect.x -= minMargin / 2;\n    globalRect.y -= minMargin / 2;\n    globalRect.width += minMargin;\n    globalRect.height += minMargin;\n    var obb = isAxisAligned ? new OrientedBoundingRect(localRect, transform) : null;\n    list.push({\n      label: label,\n      labelLine: rawItem.labelLine,\n      rect: globalRect,\n      localRect: localRect,\n      obb: obb,\n      priority: rawItem.priority,\n      defaultAttr: rawItem.defaultAttr,\n      layoutOption: rawItem.computedLayoutOption,\n      axisAligned: isAxisAligned,\n      transform: transform\n    });\n  }\n  return list;\n}\nfunction shiftLayout(list, xyDim, sizeDim, minBound, maxBound, balanceShift) {\n  var len = list.length;\n  if (len < 2) {\n    return;\n  }\n  list.sort(function (a, b) {\n    return a.rect[xyDim] - b.rect[xyDim];\n  });\n  var lastPos = 0;\n  var delta;\n  var adjusted = false;\n  var shifts = [];\n  var totalShifts = 0;\n  for (var i = 0; i < len; i++) {\n    var item = list[i];\n    var rect = item.rect;\n    delta = rect[xyDim] - lastPos;\n    if (delta < 0) {\n      // shiftForward(i, len, -delta);\n      rect[xyDim] -= delta;\n      item.label[xyDim] -= delta;\n      adjusted = true;\n    }\n    var shift = Math.max(-delta, 0);\n    shifts.push(shift);\n    totalShifts += shift;\n    lastPos = rect[xyDim] + rect[sizeDim];\n  }\n  if (totalShifts > 0 && balanceShift) {\n    // Shift back to make the distribution more equally.\n    shiftList(-totalShifts / len, 0, len);\n  }\n  // TODO bleedMargin?\n  var first = list[0];\n  var last = list[len - 1];\n  var minGap;\n  var maxGap;\n  updateMinMaxGap();\n  // If ends exceed two bounds, squeeze at most 80%, then take the gap of two bounds.\n  minGap < 0 && squeezeGaps(-minGap, 0.8);\n  maxGap < 0 && squeezeGaps(maxGap, 0.8);\n  updateMinMaxGap();\n  takeBoundsGap(minGap, maxGap, 1);\n  takeBoundsGap(maxGap, minGap, -1);\n  // Handle bailout when there is not enough space.\n  updateMinMaxGap();\n  if (minGap < 0) {\n    squeezeWhenBailout(-minGap);\n  }\n  if (maxGap < 0) {\n    squeezeWhenBailout(maxGap);\n  }\n  function updateMinMaxGap() {\n    minGap = first.rect[xyDim] - minBound;\n    maxGap = maxBound - last.rect[xyDim] - last.rect[sizeDim];\n  }\n  function takeBoundsGap(gapThisBound, gapOtherBound, moveDir) {\n    if (gapThisBound < 0) {\n      // Move from other gap if can.\n      var moveFromMaxGap = Math.min(gapOtherBound, -gapThisBound);\n      if (moveFromMaxGap > 0) {\n        shiftList(moveFromMaxGap * moveDir, 0, len);\n        var remained = moveFromMaxGap + gapThisBound;\n        if (remained < 0) {\n          squeezeGaps(-remained * moveDir, 1);\n        }\n      } else {\n        squeezeGaps(-gapThisBound * moveDir, 1);\n      }\n    }\n  }\n  function shiftList(delta, start, end) {\n    if (delta !== 0) {\n      adjusted = true;\n    }\n    for (var i = start; i < end; i++) {\n      var item = list[i];\n      var rect = item.rect;\n      rect[xyDim] += delta;\n      item.label[xyDim] += delta;\n    }\n  }\n  // Squeeze gaps if the labels exceed margin.\n  function squeezeGaps(delta, maxSqeezePercent) {\n    var gaps = [];\n    var totalGaps = 0;\n    for (var i = 1; i < len; i++) {\n      var prevItemRect = list[i - 1].rect;\n      var gap = Math.max(list[i].rect[xyDim] - prevItemRect[xyDim] - prevItemRect[sizeDim], 0);\n      gaps.push(gap);\n      totalGaps += gap;\n    }\n    if (!totalGaps) {\n      return;\n    }\n    var squeezePercent = Math.min(Math.abs(delta) / totalGaps, maxSqeezePercent);\n    if (delta > 0) {\n      for (var i = 0; i < len - 1; i++) {\n        // Distribute the shift delta to all gaps.\n        var movement = gaps[i] * squeezePercent;\n        // Forward\n        shiftList(movement, 0, i + 1);\n      }\n    } else {\n      // Backward\n      for (var i = len - 1; i > 0; i--) {\n        // Distribute the shift delta to all gaps.\n        var movement = gaps[i - 1] * squeezePercent;\n        shiftList(-movement, i, len);\n      }\n    }\n  }\n  /**\r\n   * Squeeze to allow overlap if there is no more space available.\r\n   * Let other overlapping strategy like hideOverlap do the job instead of keep exceeding the bounds.\r\n   */\n  function squeezeWhenBailout(delta) {\n    var dir = delta < 0 ? -1 : 1;\n    delta = Math.abs(delta);\n    var moveForEachLabel = Math.ceil(delta / (len - 1));\n    for (var i = 0; i < len - 1; i++) {\n      if (dir > 0) {\n        // Forward\n        shiftList(moveForEachLabel, 0, i + 1);\n      } else {\n        // Backward\n        shiftList(-moveForEachLabel, len - i - 1, len);\n      }\n      delta -= moveForEachLabel;\n      if (delta <= 0) {\n        return;\n      }\n    }\n  }\n  return adjusted;\n}\n/**\r\n * Adjust labels on x direction to avoid overlap.\r\n */\nexport function shiftLayoutOnX(list, leftBound, rightBound,\n// If average the shifts on all labels and add them to 0\n// TODO: Not sure if should enable it.\n// Pros: The angle of lines will distribute more equally\n// Cons: In some layout. It may not what user wanted. like in pie. the label of last sector is usually changed unexpectedly.\nbalanceShift) {\n  return shiftLayout(list, 'x', 'width', leftBound, rightBound, balanceShift);\n}\n/**\r\n * Adjust labels on y direction to avoid overlap.\r\n */\nexport function shiftLayoutOnY(list, topBound, bottomBound,\n// If average the shifts on all labels and add them to 0\nbalanceShift) {\n  return shiftLayout(list, 'y', 'height', topBound, bottomBound, balanceShift);\n}\nexport function hideOverlap(labelList) {\n  var displayedLabels = [];\n  // TODO, render overflow visible first, put in the displayedLabels.\n  labelList.sort(function (a, b) {\n    return b.priority - a.priority;\n  });\n  var globalRect = new BoundingRect(0, 0, 0, 0);\n  function hideEl(el) {\n    if (!el.ignore) {\n      // Show on emphasis.\n      var emphasisState = el.ensureState('emphasis');\n      if (emphasisState.ignore == null) {\n        emphasisState.ignore = false;\n      }\n    }\n    el.ignore = true;\n  }\n  for (var i = 0; i < labelList.length; i++) {\n    var labelItem = labelList[i];\n    var isAxisAligned = labelItem.axisAligned;\n    var localRect = labelItem.localRect;\n    var transform = labelItem.transform;\n    var label = labelItem.label;\n    var labelLine = labelItem.labelLine;\n    globalRect.copy(labelItem.rect);\n    // Add a threshold because layout may be aligned precisely.\n    globalRect.width -= 0.1;\n    globalRect.height -= 0.1;\n    globalRect.x += 0.05;\n    globalRect.y += 0.05;\n    var obb = labelItem.obb;\n    var overlapped = false;\n    for (var j = 0; j < displayedLabels.length; j++) {\n      var existsTextCfg = displayedLabels[j];\n      // Fast rejection.\n      if (!globalRect.intersect(existsTextCfg.rect)) {\n        continue;\n      }\n      if (isAxisAligned && existsTextCfg.axisAligned) {\n        // Is overlapped\n        overlapped = true;\n        break;\n      }\n      if (!existsTextCfg.obb) {\n        // If self is not axis aligned. But other is.\n        existsTextCfg.obb = new OrientedBoundingRect(existsTextCfg.localRect, existsTextCfg.transform);\n      }\n      if (!obb) {\n        // If self is axis aligned. But other is not.\n        obb = new OrientedBoundingRect(localRect, transform);\n      }\n      if (obb.intersect(existsTextCfg.obb)) {\n        overlapped = true;\n        break;\n      }\n    }\n    // TODO Callback to determine if this overlap should be handled?\n    if (overlapped) {\n      hideEl(label);\n      labelLine && hideEl(labelLine);\n    } else {\n      label.attr('ignore', labelItem.defaultAttr.ignore);\n      labelLine && labelLine.attr('ignore', labelItem.defaultAttr.labelGuideIgnore);\n      displayedLabels.push(labelItem);\n    }\n  }\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAY,EAAEC,oBAAoB,QAAQ,oBAAoB;AACvE,OAAO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACvC,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIE,OAAO,GAAGJ,KAAK,CAACE,CAAC,CAAC;IACtB,IAAIE,OAAO,CAACC,WAAW,CAACC,MAAM,EAAE;MAC9B;IACF;IACA,IAAIC,KAAK,GAAGH,OAAO,CAACG,KAAK;IACzB,IAAIC,SAAS,GAAGD,KAAK,CAACE,oBAAoB,CAAC,CAAC;IAC5C;IACA,IAAIC,SAAS,GAAGH,KAAK,CAACI,eAAe,CAAC,CAAC;IACvC,IAAIC,aAAa,GAAG,CAACJ,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,IAAIA,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IAC5E,IAAIK,SAAS,GAAGN,KAAK,CAACO,KAAK,CAACC,MAAM,IAAI,CAAC;IACvC,IAAIC,UAAU,GAAGN,SAAS,CAACO,KAAK,CAAC,CAAC;IAClCD,UAAU,CAACE,cAAc,CAACV,SAAS,CAAC;IACpCQ,UAAU,CAACG,CAAC,IAAIN,SAAS,GAAG,CAAC;IAC7BG,UAAU,CAACI,CAAC,IAAIP,SAAS,GAAG,CAAC;IAC7BG,UAAU,CAACK,KAAK,IAAIR,SAAS;IAC7BG,UAAU,CAACM,MAAM,IAAIT,SAAS;IAC9B,IAAIU,GAAG,GAAGX,aAAa,GAAG,IAAId,oBAAoB,CAACY,SAAS,EAAEF,SAAS,CAAC,GAAG,IAAI;IAC/EP,IAAI,CAACuB,IAAI,CAAC;MACRjB,KAAK,EAAEA,KAAK;MACZkB,SAAS,EAAErB,OAAO,CAACqB,SAAS;MAC5BC,IAAI,EAAEV,UAAU;MAChBN,SAAS,EAAEA,SAAS;MACpBa,GAAG,EAAEA,GAAG;MACRI,QAAQ,EAAEvB,OAAO,CAACuB,QAAQ;MAC1BtB,WAAW,EAAED,OAAO,CAACC,WAAW;MAChCuB,YAAY,EAAExB,OAAO,CAACyB,oBAAoB;MAC1CC,WAAW,EAAElB,aAAa;MAC1BJ,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EACA,OAAOP,IAAI;AACb;AACA,SAAS8B,WAAWA,CAAC9B,IAAI,EAAE+B,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,YAAY,EAAE;EAC3E,IAAIC,GAAG,GAAGpC,IAAI,CAACE,MAAM;EACrB,IAAIkC,GAAG,GAAG,CAAC,EAAE;IACX;EACF;EACApC,IAAI,CAACqC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACxB,OAAOD,CAAC,CAACb,IAAI,CAACM,KAAK,CAAC,GAAGQ,CAAC,CAACd,IAAI,CAACM,KAAK,CAAC;EACtC,CAAC,CAAC;EACF,IAAIS,OAAO,GAAG,CAAC;EACf,IAAIC,KAAK;EACT,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,WAAW,GAAG,CAAC;EACnB,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,GAAG,EAAEnC,CAAC,EAAE,EAAE;IAC5B,IAAI4C,IAAI,GAAG7C,IAAI,CAACC,CAAC,CAAC;IAClB,IAAIwB,IAAI,GAAGoB,IAAI,CAACpB,IAAI;IACpBgB,KAAK,GAAGhB,IAAI,CAACM,KAAK,CAAC,GAAGS,OAAO;IAC7B,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb;MACAhB,IAAI,CAACM,KAAK,CAAC,IAAIU,KAAK;MACpBI,IAAI,CAACvC,KAAK,CAACyB,KAAK,CAAC,IAAIU,KAAK;MAC1BC,QAAQ,GAAG,IAAI;IACjB;IACA,IAAII,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACP,KAAK,EAAE,CAAC,CAAC;IAC/BE,MAAM,CAACpB,IAAI,CAACuB,KAAK,CAAC;IAClBF,WAAW,IAAIE,KAAK;IACpBN,OAAO,GAAGf,IAAI,CAACM,KAAK,CAAC,GAAGN,IAAI,CAACO,OAAO,CAAC;EACvC;EACA,IAAIY,WAAW,GAAG,CAAC,IAAIT,YAAY,EAAE;IACnC;IACAc,SAAS,CAAC,CAACL,WAAW,GAAGR,GAAG,EAAE,CAAC,EAAEA,GAAG,CAAC;EACvC;EACA;EACA,IAAIc,KAAK,GAAGlD,IAAI,CAAC,CAAC,CAAC;EACnB,IAAImD,IAAI,GAAGnD,IAAI,CAACoC,GAAG,GAAG,CAAC,CAAC;EACxB,IAAIgB,MAAM;EACV,IAAIC,MAAM;EACVC,eAAe,CAAC,CAAC;EACjB;EACAF,MAAM,GAAG,CAAC,IAAIG,WAAW,CAAC,CAACH,MAAM,EAAE,GAAG,CAAC;EACvCC,MAAM,GAAG,CAAC,IAAIE,WAAW,CAACF,MAAM,EAAE,GAAG,CAAC;EACtCC,eAAe,CAAC,CAAC;EACjBE,aAAa,CAACJ,MAAM,EAAEC,MAAM,EAAE,CAAC,CAAC;EAChCG,aAAa,CAACH,MAAM,EAAED,MAAM,EAAE,CAAC,CAAC,CAAC;EACjC;EACAE,eAAe,CAAC,CAAC;EACjB,IAAIF,MAAM,GAAG,CAAC,EAAE;IACdK,kBAAkB,CAAC,CAACL,MAAM,CAAC;EAC7B;EACA,IAAIC,MAAM,GAAG,CAAC,EAAE;IACdI,kBAAkB,CAACJ,MAAM,CAAC;EAC5B;EACA,SAASC,eAAeA,CAAA,EAAG;IACzBF,MAAM,GAAGF,KAAK,CAACzB,IAAI,CAACM,KAAK,CAAC,GAAGE,QAAQ;IACrCoB,MAAM,GAAGnB,QAAQ,GAAGiB,IAAI,CAAC1B,IAAI,CAACM,KAAK,CAAC,GAAGoB,IAAI,CAAC1B,IAAI,CAACO,OAAO,CAAC;EAC3D;EACA,SAASwB,aAAaA,CAACE,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAE;IAC3D,IAAIF,YAAY,GAAG,CAAC,EAAE;MACpB;MACA,IAAIG,cAAc,GAAGd,IAAI,CAACe,GAAG,CAACH,aAAa,EAAE,CAACD,YAAY,CAAC;MAC3D,IAAIG,cAAc,GAAG,CAAC,EAAE;QACtBZ,SAAS,CAACY,cAAc,GAAGD,OAAO,EAAE,CAAC,EAAExB,GAAG,CAAC;QAC3C,IAAI2B,QAAQ,GAAGF,cAAc,GAAGH,YAAY;QAC5C,IAAIK,QAAQ,GAAG,CAAC,EAAE;UAChBR,WAAW,CAAC,CAACQ,QAAQ,GAAGH,OAAO,EAAE,CAAC,CAAC;QACrC;MACF,CAAC,MAAM;QACLL,WAAW,CAAC,CAACG,YAAY,GAAGE,OAAO,EAAE,CAAC,CAAC;MACzC;IACF;EACF;EACA,SAASX,SAASA,CAACR,KAAK,EAAEuB,KAAK,EAAEC,GAAG,EAAE;IACpC,IAAIxB,KAAK,KAAK,CAAC,EAAE;MACfC,QAAQ,GAAG,IAAI;IACjB;IACA,KAAK,IAAIzC,CAAC,GAAG+D,KAAK,EAAE/D,CAAC,GAAGgE,GAAG,EAAEhE,CAAC,EAAE,EAAE;MAChC,IAAI4C,IAAI,GAAG7C,IAAI,CAACC,CAAC,CAAC;MAClB,IAAIwB,IAAI,GAAGoB,IAAI,CAACpB,IAAI;MACpBA,IAAI,CAACM,KAAK,CAAC,IAAIU,KAAK;MACpBI,IAAI,CAACvC,KAAK,CAACyB,KAAK,CAAC,IAAIU,KAAK;IAC5B;EACF;EACA;EACA,SAASc,WAAWA,CAACd,KAAK,EAAEyB,gBAAgB,EAAE;IAC5C,IAAIC,IAAI,GAAG,EAAE;IACb,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,GAAG,EAAEnC,CAAC,EAAE,EAAE;MAC5B,IAAIoE,YAAY,GAAGrE,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC,CAACwB,IAAI;MACnC,IAAI6C,GAAG,GAAGvB,IAAI,CAACC,GAAG,CAAChD,IAAI,CAACC,CAAC,CAAC,CAACwB,IAAI,CAACM,KAAK,CAAC,GAAGsC,YAAY,CAACtC,KAAK,CAAC,GAAGsC,YAAY,CAACrC,OAAO,CAAC,EAAE,CAAC,CAAC;MACxFmC,IAAI,CAAC5C,IAAI,CAAC+C,GAAG,CAAC;MACdF,SAAS,IAAIE,GAAG;IAClB;IACA,IAAI,CAACF,SAAS,EAAE;MACd;IACF;IACA,IAAIG,cAAc,GAAGxB,IAAI,CAACe,GAAG,CAACf,IAAI,CAACyB,GAAG,CAAC/B,KAAK,CAAC,GAAG2B,SAAS,EAAEF,gBAAgB,CAAC;IAC5E,IAAIzB,KAAK,GAAG,CAAC,EAAE;MACb,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,GAAG,GAAG,CAAC,EAAEnC,CAAC,EAAE,EAAE;QAChC;QACA,IAAIwE,QAAQ,GAAGN,IAAI,CAAClE,CAAC,CAAC,GAAGsE,cAAc;QACvC;QACAtB,SAAS,CAACwB,QAAQ,EAAE,CAAC,EAAExE,CAAC,GAAG,CAAC,CAAC;MAC/B;IACF,CAAC,MAAM;MACL;MACA,KAAK,IAAIA,CAAC,GAAGmC,GAAG,GAAG,CAAC,EAAEnC,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAChC;QACA,IAAIwE,QAAQ,GAAGN,IAAI,CAAClE,CAAC,GAAG,CAAC,CAAC,GAAGsE,cAAc;QAC3CtB,SAAS,CAAC,CAACwB,QAAQ,EAAExE,CAAC,EAAEmC,GAAG,CAAC;MAC9B;IACF;EACF;EACA;AACF;AACA;AACA;EACE,SAASqB,kBAAkBA,CAAChB,KAAK,EAAE;IACjC,IAAIiC,GAAG,GAAGjC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC5BA,KAAK,GAAGM,IAAI,CAACyB,GAAG,CAAC/B,KAAK,CAAC;IACvB,IAAIkC,gBAAgB,GAAG5B,IAAI,CAAC6B,IAAI,CAACnC,KAAK,IAAIL,GAAG,GAAG,CAAC,CAAC,CAAC;IACnD,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,GAAG,GAAG,CAAC,EAAEnC,CAAC,EAAE,EAAE;MAChC,IAAIyE,GAAG,GAAG,CAAC,EAAE;QACX;QACAzB,SAAS,CAAC0B,gBAAgB,EAAE,CAAC,EAAE1E,CAAC,GAAG,CAAC,CAAC;MACvC,CAAC,MAAM;QACL;QACAgD,SAAS,CAAC,CAAC0B,gBAAgB,EAAEvC,GAAG,GAAGnC,CAAC,GAAG,CAAC,EAAEmC,GAAG,CAAC;MAChD;MACAK,KAAK,IAAIkC,gBAAgB;MACzB,IAAIlC,KAAK,IAAI,CAAC,EAAE;QACd;MACF;IACF;EACF;EACA,OAAOC,QAAQ;AACjB;AACA;AACA;AACA;AACA,OAAO,SAASmC,cAAcA,CAAC7E,IAAI,EAAE8E,SAAS,EAAEC,UAAU;AAC1D;AACA;AACA;AACA;AACA5C,YAAY,EAAE;EACZ,OAAOL,WAAW,CAAC9B,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE8E,SAAS,EAAEC,UAAU,EAAE5C,YAAY,CAAC;AAC7E;AACA;AACA;AACA;AACA,OAAO,SAAS6C,cAAcA,CAAChF,IAAI,EAAEiF,QAAQ,EAAEC,WAAW;AAC1D;AACA/C,YAAY,EAAE;EACZ,OAAOL,WAAW,CAAC9B,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAEiF,QAAQ,EAAEC,WAAW,EAAE/C,YAAY,CAAC;AAC9E;AACA,OAAO,SAASgD,WAAWA,CAACC,SAAS,EAAE;EACrC,IAAIC,eAAe,GAAG,EAAE;EACxB;EACAD,SAAS,CAAC/C,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC7B,OAAOA,CAAC,CAACb,QAAQ,GAAGY,CAAC,CAACZ,QAAQ;EAChC,CAAC,CAAC;EACF,IAAIX,UAAU,GAAG,IAAInB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC7C,SAAS0F,MAAMA,CAACC,EAAE,EAAE;IAClB,IAAI,CAACA,EAAE,CAAClF,MAAM,EAAE;MACd;MACA,IAAImF,aAAa,GAAGD,EAAE,CAACE,WAAW,CAAC,UAAU,CAAC;MAC9C,IAAID,aAAa,CAACnF,MAAM,IAAI,IAAI,EAAE;QAChCmF,aAAa,CAACnF,MAAM,GAAG,KAAK;MAC9B;IACF;IACAkF,EAAE,CAAClF,MAAM,GAAG,IAAI;EAClB;EACA,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,SAAS,CAAClF,MAAM,EAAED,CAAC,EAAE,EAAE;IACzC,IAAIyF,SAAS,GAAGN,SAAS,CAACnF,CAAC,CAAC;IAC5B,IAAIU,aAAa,GAAG+E,SAAS,CAAC7D,WAAW;IACzC,IAAIpB,SAAS,GAAGiF,SAAS,CAACjF,SAAS;IACnC,IAAIF,SAAS,GAAGmF,SAAS,CAACnF,SAAS;IACnC,IAAID,KAAK,GAAGoF,SAAS,CAACpF,KAAK;IAC3B,IAAIkB,SAAS,GAAGkE,SAAS,CAAClE,SAAS;IACnCT,UAAU,CAAC4E,IAAI,CAACD,SAAS,CAACjE,IAAI,CAAC;IAC/B;IACAV,UAAU,CAACK,KAAK,IAAI,GAAG;IACvBL,UAAU,CAACM,MAAM,IAAI,GAAG;IACxBN,UAAU,CAACG,CAAC,IAAI,IAAI;IACpBH,UAAU,CAACI,CAAC,IAAI,IAAI;IACpB,IAAIG,GAAG,GAAGoE,SAAS,CAACpE,GAAG;IACvB,IAAIsE,UAAU,GAAG,KAAK;IACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,eAAe,CAACnF,MAAM,EAAE2F,CAAC,EAAE,EAAE;MAC/C,IAAIC,aAAa,GAAGT,eAAe,CAACQ,CAAC,CAAC;MACtC;MACA,IAAI,CAAC9E,UAAU,CAACgF,SAAS,CAACD,aAAa,CAACrE,IAAI,CAAC,EAAE;QAC7C;MACF;MACA,IAAId,aAAa,IAAImF,aAAa,CAACjE,WAAW,EAAE;QAC9C;QACA+D,UAAU,GAAG,IAAI;QACjB;MACF;MACA,IAAI,CAACE,aAAa,CAACxE,GAAG,EAAE;QACtB;QACAwE,aAAa,CAACxE,GAAG,GAAG,IAAIzB,oBAAoB,CAACiG,aAAa,CAACrF,SAAS,EAAEqF,aAAa,CAACvF,SAAS,CAAC;MAChG;MACA,IAAI,CAACe,GAAG,EAAE;QACR;QACAA,GAAG,GAAG,IAAIzB,oBAAoB,CAACY,SAAS,EAAEF,SAAS,CAAC;MACtD;MACA,IAAIe,GAAG,CAACyE,SAAS,CAACD,aAAa,CAACxE,GAAG,CAAC,EAAE;QACpCsE,UAAU,GAAG,IAAI;QACjB;MACF;IACF;IACA;IACA,IAAIA,UAAU,EAAE;MACdN,MAAM,CAAChF,KAAK,CAAC;MACbkB,SAAS,IAAI8D,MAAM,CAAC9D,SAAS,CAAC;IAChC,CAAC,MAAM;MACLlB,KAAK,CAAC0F,IAAI,CAAC,QAAQ,EAAEN,SAAS,CAACtF,WAAW,CAACC,MAAM,CAAC;MAClDmB,SAAS,IAAIA,SAAS,CAACwE,IAAI,CAAC,QAAQ,EAAEN,SAAS,CAACtF,WAAW,CAAC6F,gBAAgB,CAAC;MAC7EZ,eAAe,CAAC9D,IAAI,CAACmE,SAAS,CAAC;IACjC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}