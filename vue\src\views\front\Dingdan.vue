<template>
    <div class="orders-container">
        <!-- 操作按钮 -->
        <div class="content-section">
            <div class="operation-section">
                <el-button 
                    type="danger" 
                    size="medium"
                    @click="delBatch"
                    :disabled="!ids.length"
                    class="batch-delete-btn">
                    <i class="el-icon-delete"></i>
                    批量删除 ({{ ids.length }})
                </el-button>
            </div>

            <!-- 订单列表 -->
            <div class="orders-list">
                <div v-if="filteredOrders.length === 0" class="empty-state">
                    <i class="el-icon-document"></i>
                    <h3>暂无订单</h3>
                    <p>您还没有任何订单记录</p>
                </div>
                
                <div v-else class="orders-grid">
                    <div 
                        v-for="order in filteredOrders" 
                        :key="order.id"
                        class="order-card"
                        :class="{ 'selected': selectedOrders.includes(order.id) }"
                        @click="toggleSelection(order)">
                        
                        <div class="order-header">
                            <div class="order-info">
                                <div class="order-id">订单编号：{{ order.sfOrderNumber }}</div>
                                <div class="order-time">{{ order.sfCreateTime }}</div>
                            </div>
                            <div class="order-status">
                                <el-tag 
                                    :type="getStatusTagType(order.status)"
                                    size="medium"
                                    class="status-tag">
                                    {{ order.status }}
                                </el-tag>
                            </div>
                        </div>

                        <div class="order-content">
                            <div class="order-details">
                                <div class="detail-item">
                                    <i class="el-icon-user detail-icon"></i>
                                    <span class="detail-label">用户：</span>
                                    <span class="detail-value">{{ order.sfUserName }}</span>
                                </div>
                                <div class="detail-item" v-if="order.tableNumber">
                                    <i class="el-icon-office-building detail-icon"></i>
                                    <span class="detail-label">餐桌：</span>
                                    <span class="detail-value">{{ order.tableNumber }}号桌</span>
                                </div>
                                <div class="detail-item" v-if="order.sfRemark">
                                    <i class="el-icon-chat-line-square detail-icon"></i>
                                    <span class="detail-label">备注：</span>
                                    <span class="detail-value">{{ order.sfRemark }}</span>
                                </div>
                                <div class="detail-item" v-if="order.sfEvaluation">
                                    <i class="el-icon-star-on detail-icon"></i>
                                    <span class="detail-label">评价：</span>
                                    <span class="detail-value">{{ order.sfEvaluation }}</span>
                                </div>
                            </div>
                            
                            <div class="order-price">
                                <div class="price-label">订单金额</div>
                                <div class="price-value">¥{{ order.sfTotalPrice }}</div>
                            </div>
                        </div>

                        <div class="order-actions">
                            <el-button
                                type="info"
                                size="small"
                                @click.stop="showOrderDetails(order)"
                                class="action-btn">
                                <i class="el-icon-view"></i>
                                查看详情
                            </el-button>
                            <el-button
                                v-if="order.status === '配送中' && !order.sfEvaluation"
                                type="primary"
                                size="small"
                                @click.stop="showCommentDialog(order)"
                                class="action-btn">
                                <i class="el-icon-edit"></i>
                                评价
                            </el-button>
                            <el-button
                                v-if="order.status === '配送中' || order.status === '已完成'"
                                type="warning"
                                size="small"
                                @click.stop="applyRefund(order)"
                                class="action-btn">
                                <i class="el-icon-refresh-left"></i>
                                申请退款
                            </el-button>
                            <el-button
                                type="danger"
                                size="small"
                                @click.stop="del(order.id)"
                                class="action-btn">
                                <i class="el-icon-delete"></i>
                                删除
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-section">
                <el-pagination
                    background
                    @current-change="handleCurrentChange"
                    :current-page="pageNum"
                    :page-size="pageSize"
                    layout="prev, pager, next"
                    :total="total"
                    class="custom-pagination">
                </el-pagination>
            </div>
        </div>

        <!-- 评价对话框 -->
        <el-dialog 
            title="订单评价" 
            :visible.sync="commentDialogVisible" 
            width="500px" 
            :close-on-click-modal="false"
            custom-class="comment-dialog">
            <div class="comment-form">
                <div class="order-summary">
                    <div class="summary-item">
                        <span class="summary-label">订单编号：</span>
                        <span class="summary-value">{{ commentForm.sfOrderNumber }}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">商品名称：</span>
                        <span class="summary-value">{{ commentForm.goodsName }}</span>
                    </div>
                </div>
                
                <el-form :model="commentForm" label-width="80px">
                    <el-form-item label="评价内容" prop="sfEvaluation">
                        <el-input
                            type="textarea"
                            v-model="commentForm.sfEvaluation"
                            placeholder="请输入您的评价，分享您的用餐体验..."
                            :rows="4"
                            maxlength="200"
                            show-word-limit
                            class="comment-textarea">
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="commentDialogVisible = false" class="cancel-btn">取消</el-button>
                <el-button type="primary" @click="submitComment" class="submit-btn">提交评价</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "Dingdan",
    data() {
        return {
            tableData: [],  // 所有的数据
            pageNum: 1,   // 当前的页码
            pageSize: 10,  // 每页显示的个数
            total: 0,

            fromVisible: false,
            form: {},
            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
            rules: {
                sfUserName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
                sfUserId: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
                status: [{ required: true, message: '请选择订单状态', trigger: 'change' }],
                sfOrderNumber: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],
                sfCreateTime: [{ required: true, message: '请选择下单时间', trigger: 'change' }],
                sfTotalPrice: [{ required: true, message: '请输入订单价格', trigger: 'blur' }],
            },
            ids: [],
            selectedOrders: [],

            // 评价相关
            commentDialogVisible: false,
            commentForm: {
                id: null,
                sfOrderNumber: '',
                goodsName: '',
                sfEvaluation: ''
            }
        }
    },
    computed: {
        filteredOrders() {
            // 只显示真正的订单，排除购物车数据
            return this.tableData.filter(item => 
                item.status !== '未付款' && 
                !item.sfCartStatus // 排除有购物车状态的记录
            )
        }
    },
    created() {
        this.load(1)
    },
    methods: {
        toggleSelection(order) {
            const index = this.selectedOrders.indexOf(order.id)
            if (index > -1) {
                this.selectedOrders.splice(index, 1)
            } else {
                this.selectedOrders.push(order.id)
            }
            this.ids = this.selectedOrders
        },

        getStatusTagType(status) {
            switch (status) {
                case '待支付': return 'danger';
                case '已支付': return 'warning';
                case '制作中': return '';
                case '待取餐': return 'primary';
                case '已完成': return 'success';
                case '已取消': return 'info';
                case '退款中': return 'danger';
                case '已退款': return 'info';
                default: return '';
            }
        },

        // 显示评价对话框
        showCommentDialog(row) {
            // 获取订单详情来显示商品名称
            this.$request.get(`/dingdan/details/${row.id}`).then(res => {
                let goodsName = '订单商品'
                if (res.code === '200' && res.data && res.data.length > 0) {
                    if (res.data.length === 1) {
                        goodsName = res.data[0].foodName
                    } else {
                        goodsName = `${res.data[0].foodName} 等${res.data.length}件商品`
                    }
                }
                
                this.commentForm = {
                    id: row.id,
                    sfOrderNumber: row.sfOrderNumber,
                    goodsName: goodsName,
                    sfEvaluation: row.sfEvaluation || ''
                }
                this.commentDialogVisible = true
            }).catch(() => {
                // 如果获取失败，使用默认名称
                this.commentForm = {
                    id: row.id,
                    sfOrderNumber: row.sfOrderNumber,
                    goodsName: '订单商品',
                    sfEvaluation: row.sfEvaluation || ''
                }
                this.commentDialogVisible = true
            })
        },

        // 提交评价
        submitComment() {
            if (!this.commentForm.sfEvaluation.trim()) {
                this.$message.warning('请填写评价内容')
                return
            }

            this.$request.put('/dingdan/update', {
                id: this.commentForm.id,
                sfEvaluation: this.commentForm.sfEvaluation
            }).then(res => {
                if (res.code === '200') {
                    this.$message.success('评价提交成功')
                    this.commentDialogVisible = false
                    this.load(this.pageNum) // 刷新列表
                } else {
                    this.$message.error(res.msg || '评价提交失败')
                }
            }).catch(() => {
                this.$message.error('评价提交失败，请重试')
            })
        },

        // 申请退款方法
        applyRefund(row) {
            this.$confirm('确定要申请退款吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$request.put('/dingdan/update', {
                    ...row,
                    status: '退款中'
                }).then(res => {
                    if (res.code === '200') {
                        this.$message.success('退款申请已提交，等待商家处理！')
                        this.load(this.pageNum)
                    } else {
                        this.$message.error(res.msg || '退款申请失败')
                    }
                }).catch(() => {
                    this.$message.error('退款申请失败，请重试')
                })
            }).catch(() => {
                this.$message.info('已取消退款申请')
            })
        },

        // 显示订单详情
        showOrderDetails(order) {
            this.$request.get(`/dingdan/details/${order.id}`).then(res => {
                if (res.code === '200') {
                    const orderItems = res.data || []
                    if (orderItems.length === 0) {
                        this.$message.info('该订单暂无详情信息')
                        return
                    }
                    
                    // 构建详情显示内容
                    let detailsHtml = '<div style="max-height: 400px; overflow-y: auto;">'
                    detailsHtml += `<h4>订单编号：${order.sfOrderNumber}</h4>`
                    detailsHtml += '<table style="width: 100%; border-collapse: collapse;">'
                    detailsHtml += '<tr style="background: #f5f5f5;"><th style="padding: 8px; border: 1px solid #ddd;">商品名称</th><th style="padding: 8px; border: 1px solid #ddd;">单价</th><th style="padding: 8px; border: 1px solid #ddd;">数量</th><th style="padding: 8px; border: 1px solid #ddd;">小计</th></tr>'
                    
                    orderItems.forEach(item => {
                        detailsHtml += `<tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">${item.foodName}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">¥${item.foodPrice}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${item.quantity}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">¥${item.subtotal}</td>
                        </tr>`
                    })
                    
                    detailsHtml += '</table></div>'
                    
                    this.$alert(detailsHtml, '订单详情', {
                        dangerouslyUseHTMLString: true,
                        customClass: 'order-details-dialog'
                    })
                } else {
                    this.$message.error('获取订单详情失败')
                }
            }).catch(() => {
                this.$message.error('获取订单详情失败')
            })
        },

        handleAdd() {
            this.form = {
                status: '已支付', // 默认状态设为已支付
                sfCreateTime: new Date()
            }
            this.fromVisible = true
        },

        handleEdit(row) {
            this.form = JSON.parse(JSON.stringify(row))
            this.fromVisible = true
        },

        save() {
            this.$refs.formRef.validate((valid) => {
                if (valid) {
                    if (this.form.sfCreateTime instanceof Date) {
                        this.form.sfCreateTime = this.formatDateTime(this.form.sfCreateTime);
                    }

                    this.$request({
                        url: this.form.id ? '/dingdan/update' : '/dingdan/add',
                        method: this.form.id ? 'PUT' : 'POST',
                        data: this.form
                    }).then(res => {
                        if (res.code === '200') {
                            this.$message.success('保存成功')
                            this.load(1)
                            this.fromVisible = false
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                }
            })
        },

        formatDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },

        del(id) {
            this.$confirm('您确定删除吗？', '确认删除', {type: "warning"}).then(response => {
                this.$request.delete('/dingdan/delete/' + id).then(res => {
                    if (res.code === '200') {
                        this.$message.success('操作成功')
                        this.load(1)
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {})
        },

        handleSelectionChange(rows) {
            this.ids = rows.map(v => v.id)
        },

        delBatch() {
            if (!this.ids.length) {
                this.$message.warning('请选择数据')
                return
            }
            this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: "warning"}).then(response => {
                this.$request.delete('/dingdan/delete/batch', {data: this.ids}).then(res => {
                    if (res.code === '200') {
                        this.$message.success('操作成功')
                        this.selectedOrders = []
                        this.load(1)
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {})
        },

        load(pageNum) {
            if (pageNum) this.pageNum = pageNum
            this.$request.get('/dingdan/selectPages', {
                params: {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,

                }
            }).then(res => {
                if (res.code === '200') {
                    this.tableData = res.data?.list || []
                    this.total = res.data?.total || 0
                } else {
                    this.$message.error(res.msg)
                }
            })
        },

        handleCurrentChange(pageNum) {
            this.load(pageNum)
        },
    }
}
</script>

<style scoped>
.orders-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    min-height: 100vh;
}

/* 内容区域 */
.content-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}



/* 操作区域 */
.operation-section {
    margin-bottom: 30px;
}

.batch-delete-btn {
    background: #ef4444;
    border-color: #ef4444;
    border-radius: 8px;
    font-weight: 500;
}

.batch-delete-btn:hover {
    background: #dc2626;
    border-color: #dc2626;
}

.batch-delete-btn:disabled {
    background: #e5e7eb;
    border-color: #e5e7eb;
    color: #9ca3af;
}

/* 订单列表 */
.orders-list {
    margin-bottom: 40px;
}

.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #64748b;
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 16px;
    color: #cbd5e1;
}

.empty-state h3 {
    font-size: 20px;
    margin-bottom: 8px;
    color: #475569;
}

.orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
}

.order-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.order-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);
    border-color: #3b82f6;
}

.order-card.selected {
    border-color: #3b82f6;
    background: #f0f9ff;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.order-info {
    flex: 1;
}

.order-id {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.order-time {
    font-size: 14px;
    color: #64748b;
}

.order-status {
    flex-shrink: 0;
}

.status-tag {
    font-weight: 500;
    border-radius: 12px;
    padding: 4px 12px;
}

.order-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.order-details {
    flex: 1;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-icon {
    width: 16px;
    color: #3b82f6;
    margin-right: 8px;
}

.detail-label {
    color: #64748b;
    margin-right: 8px;
}

.detail-value {
    color: #1e293b;
    font-weight: 500;
}

.order-price {
    text-align: right;
    flex-shrink: 0;
    margin-left: 20px;
}

.price-label {
    font-size: 12px;
    color: #64748b;
    margin-bottom: 4px;
}

.price-value {
    font-size: 20px;
    font-weight: 700;
    color: #3b82f6;
}

.order-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.action-btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
}

/* 分页 */
.pagination-section {
    display: flex;
    justify-content: center;
    padding: 20px 0;
}

.custom-pagination >>> .el-pager li {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin: 0 4px;
    transition: all 0.3s ease;
}

.custom-pagination >>> .el-pager li:hover {
    border-color: #3b82f6;
    color: #3b82f6;
}

.custom-pagination >>> .el-pager li.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

/* 评价对话框 */
.comment-dialog >>> .el-dialog {
    border-radius: 16px;
}

.comment-dialog >>> .el-dialog__header {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.comment-form {
    padding: 20px 0;
}

.order-summary {
    background: #f8fafc;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
}

.summary-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
}

.summary-item:last-child {
    margin-bottom: 0;
}

.summary-label {
    color: #64748b;
    margin-right: 8px;
}

.summary-value {
    color: #1e293b;
    font-weight: 500;
}

.comment-textarea >>> .el-textarea__inner {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.comment-textarea >>> .el-textarea__inner:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.cancel-btn {
    border-radius: 8px;
    padding: 10px 20px;
}

.submit-btn {
    background: #3b82f6;
    border-color: #3b82f6;
    border-radius: 8px;
    padding: 10px 20px;
}

.submit-btn:hover {
    background: #1e40af;
    border-color: #1e40af;
}

/* 订单详情对话框样式 */
.order-details-dialog >>> .el-message-box {
    min-width: 500px;
    max-width: 80vw;
}

.order-details-dialog >>> .el-message-box__content {
    padding: 20px;
}

.order-details-dialog >>> table {
    margin-top: 10px;
}

.order-details-dialog >>> th {
    background: #f8fafc !important;
    font-weight: 600;
    color: #374151;
}

.order-details-dialog >>> td {
    color: #6b7280;
}

.order-details-dialog >>> tr:nth-child(even) {
    background: #f9fafb;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .orders-grid {
        grid-template-columns: 1fr;
    }
    

    
    .order-content {
        flex-direction: column;
    }
    
    .order-price {
        margin-left: 0;
        margin-top: 16px;
        text-align: left;
    }
}
</style>