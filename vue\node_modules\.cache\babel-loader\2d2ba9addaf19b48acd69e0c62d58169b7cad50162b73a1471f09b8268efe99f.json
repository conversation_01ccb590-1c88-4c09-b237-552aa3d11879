{"ast": null, "code": "'use strict';\n\nexports.__esModule = true;\nvar _locale = require('element-ui/lib/locale');\nexports.default = {\n  methods: {\n    t: function t() {\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return _locale.t.apply(this, args);\n    }\n  }\n};", "map": {"version": 3, "names": ["exports", "__esModule", "_locale", "require", "default", "methods", "t", "_len", "arguments", "length", "args", "Array", "_key", "apply"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/element-ui/lib/mixins/locale.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nvar _locale = require('element-ui/lib/locale');\n\nexports.default = {\n  methods: {\n    t: function t() {\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      return _locale.t.apply(this, args);\n    }\n  }\n};"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzB,IAAIC,OAAO,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAE9CH,OAAO,CAACI,OAAO,GAAG;EAChBC,OAAO,EAAE;IACPC,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MACd,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAGC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;QACnFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;MAC9B;MAEA,OAAOV,OAAO,CAACI,CAAC,CAACO,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IACpC;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}