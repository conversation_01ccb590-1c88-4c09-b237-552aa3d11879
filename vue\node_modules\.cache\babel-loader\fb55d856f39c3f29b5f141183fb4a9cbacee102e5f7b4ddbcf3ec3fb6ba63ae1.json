{"ast": null, "code": "export default {\n  data() {\n    let businessId = this.$route.query.id;\n    return {\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      businessId: businessId,\n      goodsData: [],\n      businessData: {}\n    };\n  },\n  mounted() {\n    this.loadBusiness();\n    this.loadGoods();\n  },\n  // methods：本页面所有的点击事件或者其他函数定义区\n  methods: {\n    loadBusiness() {\n      this.$request.get('/business/selectById/' + this.businessId).then(res => {\n        if (res.code === '200') {\n          this.businessData = res.data;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    loadGoods() {\n      this.$request.get('/goods/selectByBusinessId?id=' + this.businessId).then(res => {\n        if (res.code === '200') {\n          this.goodsData = res.data;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    navTo(url) {\n      location.href = url;\n    }\n  }\n};", "map": {"version": 3, "names": ["data", "businessId", "$route", "query", "id", "user", "JSON", "parse", "localStorage", "getItem", "goodsData", "businessData", "mounted", "loadBusiness", "loadGoods", "methods", "$request", "get", "then", "res", "code", "$message", "error", "msg", "navTo", "url", "location", "href"], "sources": ["src/views/front/Business.vue"], "sourcesContent": ["<template>\n  <div class=\"main-content\">\n    <div style=\"width: 60%; margin: 30px auto; border-radius: 20px\">\n      <div style=\"height: 100px; padding: 0 10px; display: flex; align-items: center; border-radius: 25px; background-color: white;\">\n        <img :src=\"businessData.avatar\" alt=\"\" style=\"height: 60px; width: 60px; border-radius: 50%\">\n        <div style=\"width: 220px; margin: 0 30px 0 15px; font-size: 20px; font-weight: bold;\">\n          <div style=\"height: 30px; line-height: 30px\">{{businessData.name}}</div>\n          <img src=\"@/assets/imgs/icon.png\" alt=\"\" style=\"height: 25px; margin-top: 5px\">\n        </div>\n        <div style=\"width: 150px; height: 100px; padding: 20px\">\n          <div style=\"font-size: 16px; height: 30px; line-height: 30px; color: #7F7F7FFF\">店铺电话</div>\n          <div style=\"font-size: 16px; height: 30px; line-height: 30px; \">{{businessData.phone}}</div>\n        </div>\n        <div style=\"width: 150px; height: 100px; padding: 20px\">\n          <div style=\"font-size: 16px; height: 30px; line-height: 30px; color: #7F7F7FFF\">店铺邮箱</div>\n          <div style=\"font-size: 16px; height: 30px; line-height: 30px; \">{{businessData.email}}</div>\n        </div>\n        <div style=\"flex: 1; height: 100px; padding: 20px\">\n          <div style=\"height: 60px; line-height: 30px; font-size: 16px; color: #000000FF; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;\">\n            店铺介绍：{{businessData.description}}\n          </div>\n        </div>\n      </div>\n      <div style=\"border-radius: 20px; padding: 0 20px; background-color: white; margin-top: 20px\">\n        <div style=\"font-size: 18px; color: #000000FF; line-height: 80px; border-bottom: #cccccc 1px solid\">本店所有商品（{{goodsData.length}}件）</div>\n        <div style=\"margin-top: 20px\">\n          <el-row>\n            <el-col :span=\"5\" style=\"margin-bottom: 20px\" v-for=\"item in goodsData\">\n              <img :src=\"item.img\" alt=\"\" style=\"width: 100%; height: 150px; border-radius: 10px; border: #cccccc 1px solid\" @click=\"navTo('/front/detail?id=' + item.id)\">\n              <div style=\"margin-top: 10px; font-weight: 500; font-size: 16px; width: 160px; color: #000000FF; text-overflow: ellipsis; overflow: hidden; white-space: nowrap;\">{{item.name}}</div>\n              <div style=\"margin-top: 5px; font-size: 20px; color: #FF5000FF\">￥ {{ item.price }} / {{ item.unit }}</div>\n            </el-col>\n          </el-row>\n        </div>\n      </div>\n\n    </div>\n  </div>\n</template>\n\n<script>\n\nexport default {\n\n  data() {\n    let businessId = this.$route.query.id\n    return {\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      businessId: businessId,\n      goodsData: [],\n      businessData: {}\n    }\n  },\n  mounted() {\n    this.loadBusiness()\n    this.loadGoods()\n  },\n  // methods：本页面所有的点击事件或者其他函数定义区\n  methods: {\n    loadBusiness() {\n      this.$request.get('/business/selectById/' + this.businessId).then(res => {\n        if (res.code === '200') {\n          this.businessData = res.data\n        } else {\n          this.$message.error(res.msg)\n        }\n      })\n    },\n    loadGoods() {\n      this.$request.get('/goods/selectByBusinessId?id=' + this.businessId).then(res => {\n        if (res.code === '200') {\n          this.goodsData = res.data\n        } else {\n          this.$message.error(res.msg)\n        }\n      })\n    },\n    navTo(url) {\n      location.href = url\n    }\n  }\n}\n</script>\n\n<style scoped>\n.el-col-5{\n  width: 20%;\n  max-width: 20%;\n  padding: 10px 10px;\n}\n</style>\n"], "mappings": "AA0CA;EAEAA,KAAA;IACA,IAAAC,UAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,EAAA;IACA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAR,UAAA,EAAAA,UAAA;MACAS,SAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,SAAA;EACA;EACA;EACAC,OAAA;IACAF,aAAA;MACA,KAAAG,QAAA,CAAAC,GAAA,gCAAAhB,UAAA,EAAAiB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAT,YAAA,GAAAQ,GAAA,CAAAnB,IAAA;QACA;UACA,KAAAqB,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAI,GAAA;QACA;MACA;IACA;IACAT,UAAA;MACA,KAAAE,QAAA,CAAAC,GAAA,wCAAAhB,UAAA,EAAAiB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAV,SAAA,GAAAS,GAAA,CAAAnB,IAAA;QACA;UACA,KAAAqB,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAI,GAAA;QACA;MACA;IACA;IACAC,MAAAC,GAAA;MACAC,QAAA,CAAAC,IAAA,GAAAF,GAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}