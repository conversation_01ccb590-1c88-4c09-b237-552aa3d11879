{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar coordinateSystemCreators = {};\nvar CoordinateSystemManager = /** @class */function () {\n  function CoordinateSystemManager() {\n    this._coordinateSystems = [];\n  }\n  CoordinateSystemManager.prototype.create = function (ecModel, api) {\n    var coordinateSystems = [];\n    zrUtil.each(coordinateSystemCreators, function (creator, type) {\n      var list = creator.create(ecModel, api);\n      coordinateSystems = coordinateSystems.concat(list || []);\n    });\n    this._coordinateSystems = coordinateSystems;\n  };\n  CoordinateSystemManager.prototype.update = function (ecModel, api) {\n    zrUtil.each(this._coordinateSystems, function (coordSys) {\n      coordSys.update && coordSys.update(ecModel, api);\n    });\n  };\n  CoordinateSystemManager.prototype.getCoordinateSystems = function () {\n    return this._coordinateSystems.slice();\n  };\n  CoordinateSystemManager.register = function (type, creator) {\n    coordinateSystemCreators[type] = creator;\n  };\n  CoordinateSystemManager.get = function (type) {\n    return coordinateSystemCreators[type];\n  };\n  return CoordinateSystemManager;\n}();\nexport default CoordinateSystemManager;", "map": {"version": 3, "names": ["zrUtil", "coordinateSystemCreators", "CoordinateSystemManager", "_coordinateSystems", "prototype", "create", "ecModel", "api", "coordinateSystems", "each", "creator", "type", "list", "concat", "update", "coordSys", "getCoordinateSystems", "slice", "register", "get"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/core/CoordinateSystem.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar coordinateSystemCreators = {};\nvar CoordinateSystemManager = /** @class */function () {\n  function CoordinateSystemManager() {\n    this._coordinateSystems = [];\n  }\n  CoordinateSystemManager.prototype.create = function (ecModel, api) {\n    var coordinateSystems = [];\n    zrUtil.each(coordinateSystemCreators, function (creator, type) {\n      var list = creator.create(ecModel, api);\n      coordinateSystems = coordinateSystems.concat(list || []);\n    });\n    this._coordinateSystems = coordinateSystems;\n  };\n  CoordinateSystemManager.prototype.update = function (ecModel, api) {\n    zrUtil.each(this._coordinateSystems, function (coordSys) {\n      coordSys.update && coordSys.update(ecModel, api);\n    });\n  };\n  CoordinateSystemManager.prototype.getCoordinateSystems = function () {\n    return this._coordinateSystems.slice();\n  };\n  CoordinateSystemManager.register = function (type, creator) {\n    coordinateSystemCreators[type] = creator;\n  };\n  CoordinateSystemManager.get = function (type) {\n    return coordinateSystemCreators[type];\n  };\n  return CoordinateSystemManager;\n}();\nexport default CoordinateSystemManager;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,IAAIC,wBAAwB,GAAG,CAAC,CAAC;AACjC,IAAIC,uBAAuB,GAAG,aAAa,YAAY;EACrD,SAASA,uBAAuBA,CAAA,EAAG;IACjC,IAAI,CAACC,kBAAkB,GAAG,EAAE;EAC9B;EACAD,uBAAuB,CAACE,SAAS,CAACC,MAAM,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IACjE,IAAIC,iBAAiB,GAAG,EAAE;IAC1BR,MAAM,CAACS,IAAI,CAACR,wBAAwB,EAAE,UAAUS,OAAO,EAAEC,IAAI,EAAE;MAC7D,IAAIC,IAAI,GAAGF,OAAO,CAACL,MAAM,CAACC,OAAO,EAAEC,GAAG,CAAC;MACvCC,iBAAiB,GAAGA,iBAAiB,CAACK,MAAM,CAACD,IAAI,IAAI,EAAE,CAAC;IAC1D,CAAC,CAAC;IACF,IAAI,CAACT,kBAAkB,GAAGK,iBAAiB;EAC7C,CAAC;EACDN,uBAAuB,CAACE,SAAS,CAACU,MAAM,GAAG,UAAUR,OAAO,EAAEC,GAAG,EAAE;IACjEP,MAAM,CAACS,IAAI,CAAC,IAAI,CAACN,kBAAkB,EAAE,UAAUY,QAAQ,EAAE;MACvDA,QAAQ,CAACD,MAAM,IAAIC,QAAQ,CAACD,MAAM,CAACR,OAAO,EAAEC,GAAG,CAAC;IAClD,CAAC,CAAC;EACJ,CAAC;EACDL,uBAAuB,CAACE,SAAS,CAACY,oBAAoB,GAAG,YAAY;IACnE,OAAO,IAAI,CAACb,kBAAkB,CAACc,KAAK,CAAC,CAAC;EACxC,CAAC;EACDf,uBAAuB,CAACgB,QAAQ,GAAG,UAAUP,IAAI,EAAED,OAAO,EAAE;IAC1DT,wBAAwB,CAACU,IAAI,CAAC,GAAGD,OAAO;EAC1C,CAAC;EACDR,uBAAuB,CAACiB,GAAG,GAAG,UAAUR,IAAI,EAAE;IAC5C,OAAOV,wBAAwB,CAACU,IAAI,CAAC;EACvC,CAAC;EACD,OAAOT,uBAAuB;AAChC,CAAC,CAAC,CAAC;AACH,eAAeA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}