<template>
    <div class="leavemess-container">
        <!-- 数据表格 -->
        <div class="table-container">
            <el-table
                :data="tableData"
                stripe
                style="width: 100%"
                :row-class-name="tableRowClassName"
                class="custom-table">

                <!-- 序号列 -->
                <el-table-column prop="id" label="序号" width="70" align="center" sortable></el-table-column>

                <!-- 用户ID列 -->
                <el-table-column prop="sfUserId" label="用户ID" align="center"></el-table-column>

                <!-- 问题列 -->
                <el-table-column prop="sfQuestion" label="问题" align="center"></el-table-column>

                <!-- 留言回复列 -->
                <el-table-column prop="reply" label="留言回复" align="center">
                    <template #default="scope">
                        <span v-if="scope.row.reply">{{ scope.row.reply }}</span>
                        <span v-else class="no-reply-text">暂无回复</span>
                    </template>
                </el-table-column>

                <!-- 图片列 -->
                <el-table-column prop="sfImage" label="图片" align="center">
                    <template #default="scope">
                        <el-image
                            v-if="scope.row.sfImage"
                            :src="scope.row.sfImage"
                            fit="cover"
                            class="message-image"
                            @click="previewImage(scope.row.sfImage)">
                        </el-image>
                        <span v-else>暂无图片</span>
                    </template>
                </el-table-column>

                <!-- 时间列 -->
                <el-table-column prop="sfLeaveTime" label="时间" align="center"></el-table-column>

                <!-- 回复时间列 -->
                <el-table-column prop="replytime" label="回复时间" align="center"></el-table-column>
            </el-table>

            <!-- 无数据提示 -->
            <el-empty v-if="tableData.length === 0" description="暂无回复"></el-empty>

            <!-- 分页器 -->
            <div class="pagination">
                <el-pagination
                    background
                    @current-change="handleCurrentChange"
                    :current-page="pageNum"
                    :page-sizes="[5, 10, 20]"
                    :page-size="pageSize"
                    layout="total, prev, pager, next"
                    :total="total">
                </el-pagination>
            </div>
        </div>

        <!-- 图片预览组件（隐藏） -->
        <el-image ref="imagePreview" style="display: none;"></el-image>
    </div>
</template>

<script>
export default {
    name: "Leavemess",
    data() {
        return {
            tableData: [],  // 所有的数据
            pageNum: 1,     // 当前的页码
            pageSize: 10,   // 每页显示的个数
            total: 0,

        }
    },
    created() {
        this.load(1)
    },
    methods: {
        /**
         * 根据行数据返回对应的类名
         * @param {Object} row - 当前行数据
         * @param {Number} index - 行索引
         * @returns {String} - 类名
         */
        tableRowClassName(row, index) {
            return !row.reply ? 'no-reply' : '';
        },
        /**
         * 加载数据（分页查询）
         * @param {Number} pageNum - 要加载的页码
         */
        load(pageNum) {
            if (pageNum) this.pageNum = pageNum
            this.$request.get('/leavemess/selectPage', {
                params: {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,

                }
            }).then(res => {
                if (res.code === '200') {
                    this.tableData = res.data?.list || []
                    this.total = res.data?.total || 0
                } else {
                    this.$message.error(res.msg)
                }
            }).catch(() => {
                this.$message.error('加载数据失败')
            })
        },
        /**
         * 重置搜索条件并重新加载数据
         */

        /**
         * 处理分页器页码变化
         * @param {Number} pageNum - 当前选择的页码
         */
        handleCurrentChange(pageNum) {
            this.load(pageNum)
        },
        /**
         * 预览图片
         * @param {String} url - 图片URL
         */
        previewImage(url) {
            this.$refs.imagePreview.handlePreview(url)
        }
    }
}
</script>

<style scoped>
.leavemess-container {
    padding: 20px;
    background-color: #f5f7fa;
    font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* 搜索栏样式 */
.search {
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}



/* 表格样式 */
.table-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    transition: box-shadow 0.3s;
}

.table-container:hover {
    box-shadow: 0 6px 16px rgba(0,0,0,0.1);
}

.custom-table .el-table__header {
    background-color: #f0f2f5;
    color: #333;
    font-weight: bold;
}

.custom-table .el-table__cell {
    color: #555;
    transition: background-color 0.3s;
}

.custom-table .el-table__row:hover .el-table__cell {
    background-color: #f9f9f9;
}

/* 无回复行样式 */
.no-reply {
    background-color: #fff5f5; /* 淡红色背景 */
}

.no-reply .el-table__cell {
    color: #f56c6c;
    font-weight: bold;
}

/* “暂无回复”文本样式 */
.no-reply-text {
    color: #f56c6c;
    font-style: italic;
}

/* 图片样式 */
.message-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
}

.message-image:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 分页器样式 */
.pagination {
    margin-top: 25px;
    display: flex;
    justify-content: flex-end;
}

/* 空状态样式 */
.el-empty {
    margin: 40px 0;
    color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search {
        flex-direction: column;
        align-items: flex-start;
    }


}
</style>
