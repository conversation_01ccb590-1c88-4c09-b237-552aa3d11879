{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", [_c(\"div\", {\n    staticClass: \"card\",\n    staticStyle: {\n      padding: \"15px\"\n    }\n  }, [_vm._v(\" 您好，\" + _vm._s(_vm.user?.name) + \"！欢迎使用本系统 \")]), _c(\"div\", {\n    staticStyle: {\n      display: \"flex\",\n      margin: \"10px 0\",\n      gap: \"20px\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"card\",\n    staticStyle: {\n      flex: \"1\",\n      padding: \"20px\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      \"margin-bottom\": \"15px\",\n      \"font-size\": \"16px\",\n      \"font-weight\": \"bold\"\n    }\n  }, [_vm._v(\"订单状态分布 (饼图)\")]), _c(\"div\", {\n    ref: \"statusPieChart\",\n    staticStyle: {\n      height: \"300px\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"card\",\n    staticStyle: {\n      flex: \"1\",\n      padding: \"20px\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      \"margin-bottom\": \"15px\",\n      \"font-size\": \"16px\",\n      \"font-weight\": \"bold\"\n    }\n  }, [_vm._v(\"订单状态分布 (玫瑰图)\")]), _c(\"div\", {\n    ref: \"statusRoseChart\",\n    staticStyle: {\n      height: \"300px\"\n    }\n  })])]), _c(\"div\", {\n    staticStyle: {\n      display: \"flex\",\n      margin: \"10px 0\",\n      gap: \"20px\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"card\",\n    staticStyle: {\n      flex: \"1\",\n      padding: \"20px\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      \"margin-bottom\": \"15px\",\n      \"font-size\": \"16px\",\n      \"font-weight\": \"bold\"\n    }\n  }, [_vm._v(\"每月订单量统计 (柱状图)\")]), _c(\"div\", {\n    ref: \"monthlyBarChart\",\n    staticStyle: {\n      height: \"300px\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"card\",\n    staticStyle: {\n      flex: \"1\",\n      padding: \"20px\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      \"margin-bottom\": \"15px\",\n      \"font-size\": \"16px\",\n      \"font-weight\": \"bold\"\n    }\n  }, [_vm._v(\"每月订单量统计 (折线图)\")]), _c(\"div\", {\n    ref: \"monthlyLineChart\",\n    staticStyle: {\n      height: \"300px\"\n    }\n  })])])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "padding", "_v", "_s", "user", "name", "display", "margin", "gap", "flex", "ref", "height", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/src/views/manager/Home.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", [\n    _c(\"div\", { staticClass: \"card\", staticStyle: { padding: \"15px\" } }, [\n      _vm._v(\" 您好，\" + _vm._s(_vm.user?.name) + \"！欢迎使用本系统 \"),\n    ]),\n    _c(\n      \"div\",\n      { staticStyle: { display: \"flex\", margin: \"10px 0\", gap: \"20px\" } },\n      [\n        _c(\n          \"div\",\n          { staticClass: \"card\", staticStyle: { flex: \"1\", padding: \"20px\" } },\n          [\n            _c(\n              \"div\",\n              {\n                staticStyle: {\n                  \"margin-bottom\": \"15px\",\n                  \"font-size\": \"16px\",\n                  \"font-weight\": \"bold\",\n                },\n              },\n              [_vm._v(\"订单状态分布 (饼图)\")]\n            ),\n            _c(\"div\", {\n              ref: \"statusPieChart\",\n              staticStyle: { height: \"300px\" },\n            }),\n          ]\n        ),\n        _c(\n          \"div\",\n          { staticClass: \"card\", staticStyle: { flex: \"1\", padding: \"20px\" } },\n          [\n            _c(\n              \"div\",\n              {\n                staticStyle: {\n                  \"margin-bottom\": \"15px\",\n                  \"font-size\": \"16px\",\n                  \"font-weight\": \"bold\",\n                },\n              },\n              [_vm._v(\"订单状态分布 (玫瑰图)\")]\n            ),\n            _c(\"div\", {\n              ref: \"statusRoseChart\",\n              staticStyle: { height: \"300px\" },\n            }),\n          ]\n        ),\n      ]\n    ),\n    _c(\n      \"div\",\n      { staticStyle: { display: \"flex\", margin: \"10px 0\", gap: \"20px\" } },\n      [\n        _c(\n          \"div\",\n          { staticClass: \"card\", staticStyle: { flex: \"1\", padding: \"20px\" } },\n          [\n            _c(\n              \"div\",\n              {\n                staticStyle: {\n                  \"margin-bottom\": \"15px\",\n                  \"font-size\": \"16px\",\n                  \"font-weight\": \"bold\",\n                },\n              },\n              [_vm._v(\"每月订单量统计 (柱状图)\")]\n            ),\n            _c(\"div\", {\n              ref: \"monthlyBarChart\",\n              staticStyle: { height: \"300px\" },\n            }),\n          ]\n        ),\n        _c(\n          \"div\",\n          { staticClass: \"card\", staticStyle: { flex: \"1\", padding: \"20px\" } },\n          [\n            _c(\n              \"div\",\n              {\n                staticStyle: {\n                  \"margin-bottom\": \"15px\",\n                  \"font-size\": \"16px\",\n                  \"font-weight\": \"bold\",\n                },\n              },\n              [_vm._v(\"每月订单量统计 (折线图)\")]\n            ),\n            _c(\"div\", {\n              ref: \"monthlyLineChart\",\n              staticStyle: { height: \"300px\" },\n            }),\n          ]\n        ),\n      ]\n    ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE,CACfA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE,MAAM;IAAEC,WAAW,EAAE;MAAEC,OAAO,EAAE;IAAO;EAAE,CAAC,EAAE,CACnEL,GAAG,CAACM,EAAE,CAAC,MAAM,GAAGN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,IAAI,EAAEC,IAAI,CAAC,GAAG,WAAW,CAAC,CACtD,CAAC,EACFR,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;MAAEM,OAAO,EAAE,MAAM;MAAEC,MAAM,EAAE,QAAQ;MAAEC,GAAG,EAAE;IAAO;EAAE,CAAC,EACnE,CACEX,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,MAAM;IAAEC,WAAW,EAAE;MAAES,IAAI,EAAE,GAAG;MAAER,OAAO,EAAE;IAAO;EAAE,CAAC,EACpE,CACEJ,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACX,eAAe,EAAE,MAAM;MACvB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CAACJ,GAAG,CAACM,EAAE,CAAC,aAAa,CAAC,CACxB,CAAC,EACDL,EAAE,CAAC,KAAK,EAAE;IACRa,GAAG,EAAE,gBAAgB;IACrBV,WAAW,EAAE;MAAEW,MAAM,EAAE;IAAQ;EACjC,CAAC,CAAC,CAEN,CAAC,EACDd,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,MAAM;IAAEC,WAAW,EAAE;MAAES,IAAI,EAAE,GAAG;MAAER,OAAO,EAAE;IAAO;EAAE,CAAC,EACpE,CACEJ,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACX,eAAe,EAAE,MAAM;MACvB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CAACJ,GAAG,CAACM,EAAE,CAAC,cAAc,CAAC,CACzB,CAAC,EACDL,EAAE,CAAC,KAAK,EAAE;IACRa,GAAG,EAAE,iBAAiB;IACtBV,WAAW,EAAE;MAAEW,MAAM,EAAE;IAAQ;EACjC,CAAC,CAAC,CAEN,CAAC,CAEL,CAAC,EACDd,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;MAAEM,OAAO,EAAE,MAAM;MAAEC,MAAM,EAAE,QAAQ;MAAEC,GAAG,EAAE;IAAO;EAAE,CAAC,EACnE,CACEX,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,MAAM;IAAEC,WAAW,EAAE;MAAES,IAAI,EAAE,GAAG;MAAER,OAAO,EAAE;IAAO;EAAE,CAAC,EACpE,CACEJ,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACX,eAAe,EAAE,MAAM;MACvB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CAACJ,GAAG,CAACM,EAAE,CAAC,eAAe,CAAC,CAC1B,CAAC,EACDL,EAAE,CAAC,KAAK,EAAE;IACRa,GAAG,EAAE,iBAAiB;IACtBV,WAAW,EAAE;MAAEW,MAAM,EAAE;IAAQ;EACjC,CAAC,CAAC,CAEN,CAAC,EACDd,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,MAAM;IAAEC,WAAW,EAAE;MAAES,IAAI,EAAE,GAAG;MAAER,OAAO,EAAE;IAAO;EAAE,CAAC,EACpE,CACEJ,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACX,eAAe,EAAE,MAAM;MACvB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CAACJ,GAAG,CAACM,EAAE,CAAC,eAAe,CAAC,CAC1B,CAAC,EACDL,EAAE,CAAC,KAAK,EAAE;IACRa,GAAG,EAAE,kBAAkB;IACvBV,WAAW,EAAE;MAAEW,MAAM,EAAE;IAAQ;EACjC,CAAC,CAAC,CAEN,CAAC,CAEL,CAAC,CACF,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBjB,MAAM,CAACkB,aAAa,GAAG,IAAI;AAE3B,SAASlB,MAAM,EAAEiB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}