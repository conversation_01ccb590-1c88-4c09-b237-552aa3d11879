{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isNumber } from 'zrender/lib/core/util.js';\nexport default function categoryFilter(ecModel) {\n  var legendModels = ecModel.findComponents({\n    mainType: 'legend'\n  });\n  if (!legendModels || !legendModels.length) {\n    return;\n  }\n  ecModel.eachSeriesByType('graph', function (graphSeries) {\n    var categoriesData = graphSeries.getCategoriesData();\n    var graph = graphSeries.getGraph();\n    var data = graph.data;\n    var categoryNames = categoriesData.mapArray(categoriesData.getName);\n    data.filterSelf(function (idx) {\n      var model = data.getItemModel(idx);\n      var category = model.getShallow('category');\n      if (category != null) {\n        if (isNumber(category)) {\n          category = categoryNames[category];\n        }\n        // If in any legend component the status is not selected.\n        for (var i = 0; i < legendModels.length; i++) {\n          if (!legendModels[i].isSelected(category)) {\n            return false;\n          }\n        }\n      }\n      return true;\n    });\n  });\n}", "map": {"version": 3, "names": ["isNumber", "categoryFilter", "ecModel", "legend<PERSON><PERSON><PERSON>", "findComponents", "mainType", "length", "eachSeriesByType", "graphSeries", "categoriesData", "getCategoriesData", "graph", "getGraph", "data", "categoryNames", "mapArray", "getName", "filterSelf", "idx", "model", "getItemModel", "category", "getShallow", "i", "isSelected"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/chart/graph/categoryFilter.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isNumber } from 'zrender/lib/core/util.js';\nexport default function categoryFilter(ecModel) {\n  var legendModels = ecModel.findComponents({\n    mainType: 'legend'\n  });\n  if (!legendModels || !legendModels.length) {\n    return;\n  }\n  ecModel.eachSeriesByType('graph', function (graphSeries) {\n    var categoriesData = graphSeries.getCategoriesData();\n    var graph = graphSeries.getGraph();\n    var data = graph.data;\n    var categoryNames = categoriesData.mapArray(categoriesData.getName);\n    data.filterSelf(function (idx) {\n      var model = data.getItemModel(idx);\n      var category = model.getShallow('category');\n      if (category != null) {\n        if (isNumber(category)) {\n          category = categoryNames[category];\n        }\n        // If in any legend component the status is not selected.\n        for (var i = 0; i < legendModels.length; i++) {\n          if (!legendModels[i].isSelected(category)) {\n            return false;\n          }\n        }\n      }\n      return true;\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,eAAe,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC9C,IAAIC,YAAY,GAAGD,OAAO,CAACE,cAAc,CAAC;IACxCC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,IAAI,CAACF,YAAY,IAAI,CAACA,YAAY,CAACG,MAAM,EAAE;IACzC;EACF;EACAJ,OAAO,CAACK,gBAAgB,CAAC,OAAO,EAAE,UAAUC,WAAW,EAAE;IACvD,IAAIC,cAAc,GAAGD,WAAW,CAACE,iBAAiB,CAAC,CAAC;IACpD,IAAIC,KAAK,GAAGH,WAAW,CAACI,QAAQ,CAAC,CAAC;IAClC,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACrB,IAAIC,aAAa,GAAGL,cAAc,CAACM,QAAQ,CAACN,cAAc,CAACO,OAAO,CAAC;IACnEH,IAAI,CAACI,UAAU,CAAC,UAAUC,GAAG,EAAE;MAC7B,IAAIC,KAAK,GAAGN,IAAI,CAACO,YAAY,CAACF,GAAG,CAAC;MAClC,IAAIG,QAAQ,GAAGF,KAAK,CAACG,UAAU,CAAC,UAAU,CAAC;MAC3C,IAAID,QAAQ,IAAI,IAAI,EAAE;QACpB,IAAIrB,QAAQ,CAACqB,QAAQ,CAAC,EAAE;UACtBA,QAAQ,GAAGP,aAAa,CAACO,QAAQ,CAAC;QACpC;QACA;QACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,YAAY,CAACG,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC5C,IAAI,CAACpB,YAAY,CAACoB,CAAC,CAAC,CAACC,UAAU,CAACH,QAAQ,CAAC,EAAE;YACzC,OAAO,KAAK;UACd;QACF;MACF;MACA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}