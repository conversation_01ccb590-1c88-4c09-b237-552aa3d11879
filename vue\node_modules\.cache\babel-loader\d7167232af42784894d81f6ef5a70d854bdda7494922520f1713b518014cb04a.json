{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport LineDraw from '../helper/LineDraw.js';\nimport RoamController from '../../component/helper/RoamController.js';\nimport * as roamHelper from '../../component/helper/roamHelper.js';\nimport { onIrrelevantElement } from '../../component/helper/cursorHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport adjustEdge from './adjustEdge.js';\nimport { getNodeGlobalScale } from './graphHelper.js';\nimport ChartView from '../../view/Chart.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { simpleLayoutEdge } from './simpleLayoutHelper.js';\nimport { circularLayout, rotateNodeLabel } from './circularLayoutHelper.js';\nfunction isViewCoordSys(coordSys) {\n  return coordSys.type === 'view';\n}\nvar GraphView = /** @class */function (_super) {\n  __extends(GraphView, _super);\n  function GraphView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GraphView.type;\n    return _this;\n  }\n  GraphView.prototype.init = function (ecModel, api) {\n    var symbolDraw = new SymbolDraw();\n    var lineDraw = new LineDraw();\n    var group = this.group;\n    this._controller = new RoamController(api.getZr());\n    this._controllerHost = {\n      target: group\n    };\n    group.add(symbolDraw.group);\n    group.add(lineDraw.group);\n    this._symbolDraw = symbolDraw;\n    this._lineDraw = lineDraw;\n    this._firstRender = true;\n  };\n  GraphView.prototype.render = function (seriesModel, ecModel, api) {\n    var _this = this;\n    var coordSys = seriesModel.coordinateSystem;\n    this._model = seriesModel;\n    var symbolDraw = this._symbolDraw;\n    var lineDraw = this._lineDraw;\n    var group = this.group;\n    if (isViewCoordSys(coordSys)) {\n      var groupNewProp = {\n        x: coordSys.x,\n        y: coordSys.y,\n        scaleX: coordSys.scaleX,\n        scaleY: coordSys.scaleY\n      };\n      if (this._firstRender) {\n        group.attr(groupNewProp);\n      } else {\n        graphic.updateProps(group, groupNewProp, seriesModel);\n      }\n    }\n    // Fix edge contact point with node\n    adjustEdge(seriesModel.getGraph(), getNodeGlobalScale(seriesModel));\n    var data = seriesModel.getData();\n    symbolDraw.updateData(data);\n    var edgeData = seriesModel.getEdgeData();\n    // TODO: TYPE\n    lineDraw.updateData(edgeData);\n    this._updateNodeAndLinkScale();\n    this._updateController(seriesModel, ecModel, api);\n    clearTimeout(this._layoutTimeout);\n    var forceLayout = seriesModel.forceLayout;\n    var layoutAnimation = seriesModel.get(['force', 'layoutAnimation']);\n    if (forceLayout) {\n      this._startForceLayoutIteration(forceLayout, layoutAnimation);\n    }\n    var layout = seriesModel.get('layout');\n    data.graph.eachNode(function (node) {\n      var idx = node.dataIndex;\n      var el = node.getGraphicEl();\n      var itemModel = node.getModel();\n      if (!el) {\n        return;\n      }\n      // Update draggable\n      el.off('drag').off('dragend');\n      var draggable = itemModel.get('draggable');\n      if (draggable) {\n        el.on('drag', function (e) {\n          switch (layout) {\n            case 'force':\n              forceLayout.warmUp();\n              !_this._layouting && _this._startForceLayoutIteration(forceLayout, layoutAnimation);\n              forceLayout.setFixed(idx);\n              // Write position back to layout\n              data.setItemLayout(idx, [el.x, el.y]);\n              break;\n            case 'circular':\n              data.setItemLayout(idx, [el.x, el.y]);\n              // mark node fixed\n              node.setLayout({\n                fixed: true\n              }, true);\n              // recalculate circular layout\n              circularLayout(seriesModel, 'symbolSize', node, [e.offsetX, e.offsetY]);\n              _this.updateLayout(seriesModel);\n              break;\n            case 'none':\n            default:\n              data.setItemLayout(idx, [el.x, el.y]);\n              // update edge\n              simpleLayoutEdge(seriesModel.getGraph(), seriesModel);\n              _this.updateLayout(seriesModel);\n              break;\n          }\n        }).on('dragend', function () {\n          if (forceLayout) {\n            forceLayout.setUnfixed(idx);\n          }\n        });\n      }\n      el.setDraggable(draggable, !!itemModel.get('cursor'));\n      var focus = itemModel.get(['emphasis', 'focus']);\n      if (focus === 'adjacency') {\n        getECData(el).focus = node.getAdjacentDataIndices();\n      }\n    });\n    data.graph.eachEdge(function (edge) {\n      var el = edge.getGraphicEl();\n      var focus = edge.getModel().get(['emphasis', 'focus']);\n      if (!el) {\n        return;\n      }\n      if (focus === 'adjacency') {\n        getECData(el).focus = {\n          edge: [edge.dataIndex],\n          node: [edge.node1.dataIndex, edge.node2.dataIndex]\n        };\n      }\n    });\n    var circularRotateLabel = seriesModel.get('layout') === 'circular' && seriesModel.get(['circular', 'rotateLabel']);\n    var cx = data.getLayout('cx');\n    var cy = data.getLayout('cy');\n    data.graph.eachNode(function (node) {\n      rotateNodeLabel(node, circularRotateLabel, cx, cy);\n    });\n    this._firstRender = false;\n  };\n  GraphView.prototype.dispose = function () {\n    this.remove();\n    this._controller && this._controller.dispose();\n    this._controllerHost = null;\n  };\n  GraphView.prototype._startForceLayoutIteration = function (forceLayout, layoutAnimation) {\n    var self = this;\n    (function step() {\n      forceLayout.step(function (stopped) {\n        self.updateLayout(self._model);\n        (self._layouting = !stopped) && (layoutAnimation ? self._layoutTimeout = setTimeout(step, 16) : step());\n      });\n    })();\n  };\n  GraphView.prototype._updateController = function (seriesModel, ecModel, api) {\n    var _this = this;\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    var group = this.group;\n    controller.setPointerChecker(function (e, x, y) {\n      var rect = group.getBoundingRect();\n      rect.applyTransform(group.transform);\n      return rect.contain(x, y) && !onIrrelevantElement(e, api, seriesModel);\n    });\n    if (!isViewCoordSys(seriesModel.coordinateSystem)) {\n      controller.disable();\n      return;\n    }\n    controller.enable(seriesModel.get('roam'));\n    controllerHost.zoomLimit = seriesModel.get('scaleLimit');\n    controllerHost.zoom = seriesModel.coordinateSystem.getZoom();\n    controller.off('pan').off('zoom').on('pan', function (e) {\n      roamHelper.updateViewOnPan(controllerHost, e.dx, e.dy);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'graphRoam',\n        dx: e.dx,\n        dy: e.dy\n      });\n    }).on('zoom', function (e) {\n      roamHelper.updateViewOnZoom(controllerHost, e.scale, e.originX, e.originY);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'graphRoam',\n        zoom: e.scale,\n        originX: e.originX,\n        originY: e.originY\n      });\n      _this._updateNodeAndLinkScale();\n      adjustEdge(seriesModel.getGraph(), getNodeGlobalScale(seriesModel));\n      _this._lineDraw.updateLayout();\n      // Only update label layout on zoom\n      api.updateLabelLayout();\n    });\n  };\n  GraphView.prototype._updateNodeAndLinkScale = function () {\n    var seriesModel = this._model;\n    var data = seriesModel.getData();\n    var nodeScale = getNodeGlobalScale(seriesModel);\n    data.eachItemGraphicEl(function (el, idx) {\n      el && el.setSymbolScale(nodeScale);\n    });\n  };\n  GraphView.prototype.updateLayout = function (seriesModel) {\n    adjustEdge(seriesModel.getGraph(), getNodeGlobalScale(seriesModel));\n    this._symbolDraw.updateLayout();\n    this._lineDraw.updateLayout();\n  };\n  GraphView.prototype.remove = function () {\n    clearTimeout(this._layoutTimeout);\n    this._layouting = false;\n    this._layoutTimeout = null;\n    this._symbolDraw && this._symbolDraw.remove();\n    this._lineDraw && this._lineDraw.remove();\n  };\n  GraphView.type = 'graph';\n  return GraphView;\n}(ChartView);\nexport default GraphView;", "map": {"version": 3, "names": ["__extends", "SymbolDraw", "LineDraw", "RoamController", "roamHelper", "onIrrelevantElement", "graphic", "adjustEdge", "getNodeGlobalScale", "ChartView", "getECData", "simpleLayoutEdge", "circularLayout", "rotateNodeLabel", "isViewCoordSys", "coordSys", "type", "GraphView", "_super", "_this", "apply", "arguments", "prototype", "init", "ecModel", "api", "symbolDraw", "lineDraw", "group", "_controller", "getZr", "_controllerHost", "target", "add", "_symbolDraw", "_lineDraw", "_firstRender", "render", "seriesModel", "coordinateSystem", "_model", "groupNewProp", "x", "y", "scaleX", "scaleY", "attr", "updateProps", "getGraph", "data", "getData", "updateData", "edgeData", "getEdgeData", "_updateNodeAndLinkScale", "_updateController", "clearTimeout", "_layoutTimeout", "forceLayout", "layoutAnimation", "get", "_startForceLayoutIteration", "layout", "graph", "eachNode", "node", "idx", "dataIndex", "el", "getGraphicEl", "itemModel", "getModel", "off", "draggable", "on", "e", "warmUp", "_layouting", "setFixed", "setItemLayout", "setLayout", "fixed", "offsetX", "offsetY", "updateLayout", "setUnfixed", "setDraggable", "focus", "getAdjacentDataIndices", "eachEdge", "edge", "node1", "node2", "circularRotateLabel", "cx", "getLayout", "cy", "dispose", "remove", "self", "step", "stopped", "setTimeout", "controller", "controllerHost", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rect", "getBoundingRect", "applyTransform", "transform", "contain", "disable", "enable", "zoomLimit", "zoom", "getZoom", "updateViewOnPan", "dx", "dy", "dispatchAction", "seriesId", "id", "updateViewOnZoom", "scale", "originX", "originY", "updateLabelLayout", "nodeScale", "eachItemGraphicEl", "setSymbolScale"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/chart/graph/GraphView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport LineDraw from '../helper/LineDraw.js';\nimport RoamController from '../../component/helper/RoamController.js';\nimport * as roamHelper from '../../component/helper/roamHelper.js';\nimport { onIrrelevantElement } from '../../component/helper/cursorHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport adjustEdge from './adjustEdge.js';\nimport { getNodeGlobalScale } from './graphHelper.js';\nimport ChartView from '../../view/Chart.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { simpleLayoutEdge } from './simpleLayoutHelper.js';\nimport { circularLayout, rotateNodeLabel } from './circularLayoutHelper.js';\nfunction isViewCoordSys(coordSys) {\n  return coordSys.type === 'view';\n}\nvar GraphView = /** @class */function (_super) {\n  __extends(GraphView, _super);\n  function GraphView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GraphView.type;\n    return _this;\n  }\n  GraphView.prototype.init = function (ecModel, api) {\n    var symbolDraw = new SymbolDraw();\n    var lineDraw = new LineDraw();\n    var group = this.group;\n    this._controller = new RoamController(api.getZr());\n    this._controllerHost = {\n      target: group\n    };\n    group.add(symbolDraw.group);\n    group.add(lineDraw.group);\n    this._symbolDraw = symbolDraw;\n    this._lineDraw = lineDraw;\n    this._firstRender = true;\n  };\n  GraphView.prototype.render = function (seriesModel, ecModel, api) {\n    var _this = this;\n    var coordSys = seriesModel.coordinateSystem;\n    this._model = seriesModel;\n    var symbolDraw = this._symbolDraw;\n    var lineDraw = this._lineDraw;\n    var group = this.group;\n    if (isViewCoordSys(coordSys)) {\n      var groupNewProp = {\n        x: coordSys.x,\n        y: coordSys.y,\n        scaleX: coordSys.scaleX,\n        scaleY: coordSys.scaleY\n      };\n      if (this._firstRender) {\n        group.attr(groupNewProp);\n      } else {\n        graphic.updateProps(group, groupNewProp, seriesModel);\n      }\n    }\n    // Fix edge contact point with node\n    adjustEdge(seriesModel.getGraph(), getNodeGlobalScale(seriesModel));\n    var data = seriesModel.getData();\n    symbolDraw.updateData(data);\n    var edgeData = seriesModel.getEdgeData();\n    // TODO: TYPE\n    lineDraw.updateData(edgeData);\n    this._updateNodeAndLinkScale();\n    this._updateController(seriesModel, ecModel, api);\n    clearTimeout(this._layoutTimeout);\n    var forceLayout = seriesModel.forceLayout;\n    var layoutAnimation = seriesModel.get(['force', 'layoutAnimation']);\n    if (forceLayout) {\n      this._startForceLayoutIteration(forceLayout, layoutAnimation);\n    }\n    var layout = seriesModel.get('layout');\n    data.graph.eachNode(function (node) {\n      var idx = node.dataIndex;\n      var el = node.getGraphicEl();\n      var itemModel = node.getModel();\n      if (!el) {\n        return;\n      }\n      // Update draggable\n      el.off('drag').off('dragend');\n      var draggable = itemModel.get('draggable');\n      if (draggable) {\n        el.on('drag', function (e) {\n          switch (layout) {\n            case 'force':\n              forceLayout.warmUp();\n              !_this._layouting && _this._startForceLayoutIteration(forceLayout, layoutAnimation);\n              forceLayout.setFixed(idx);\n              // Write position back to layout\n              data.setItemLayout(idx, [el.x, el.y]);\n              break;\n            case 'circular':\n              data.setItemLayout(idx, [el.x, el.y]);\n              // mark node fixed\n              node.setLayout({\n                fixed: true\n              }, true);\n              // recalculate circular layout\n              circularLayout(seriesModel, 'symbolSize', node, [e.offsetX, e.offsetY]);\n              _this.updateLayout(seriesModel);\n              break;\n            case 'none':\n            default:\n              data.setItemLayout(idx, [el.x, el.y]);\n              // update edge\n              simpleLayoutEdge(seriesModel.getGraph(), seriesModel);\n              _this.updateLayout(seriesModel);\n              break;\n          }\n        }).on('dragend', function () {\n          if (forceLayout) {\n            forceLayout.setUnfixed(idx);\n          }\n        });\n      }\n      el.setDraggable(draggable, !!itemModel.get('cursor'));\n      var focus = itemModel.get(['emphasis', 'focus']);\n      if (focus === 'adjacency') {\n        getECData(el).focus = node.getAdjacentDataIndices();\n      }\n    });\n    data.graph.eachEdge(function (edge) {\n      var el = edge.getGraphicEl();\n      var focus = edge.getModel().get(['emphasis', 'focus']);\n      if (!el) {\n        return;\n      }\n      if (focus === 'adjacency') {\n        getECData(el).focus = {\n          edge: [edge.dataIndex],\n          node: [edge.node1.dataIndex, edge.node2.dataIndex]\n        };\n      }\n    });\n    var circularRotateLabel = seriesModel.get('layout') === 'circular' && seriesModel.get(['circular', 'rotateLabel']);\n    var cx = data.getLayout('cx');\n    var cy = data.getLayout('cy');\n    data.graph.eachNode(function (node) {\n      rotateNodeLabel(node, circularRotateLabel, cx, cy);\n    });\n    this._firstRender = false;\n  };\n  GraphView.prototype.dispose = function () {\n    this.remove();\n    this._controller && this._controller.dispose();\n    this._controllerHost = null;\n  };\n  GraphView.prototype._startForceLayoutIteration = function (forceLayout, layoutAnimation) {\n    var self = this;\n    (function step() {\n      forceLayout.step(function (stopped) {\n        self.updateLayout(self._model);\n        (self._layouting = !stopped) && (layoutAnimation ? self._layoutTimeout = setTimeout(step, 16) : step());\n      });\n    })();\n  };\n  GraphView.prototype._updateController = function (seriesModel, ecModel, api) {\n    var _this = this;\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    var group = this.group;\n    controller.setPointerChecker(function (e, x, y) {\n      var rect = group.getBoundingRect();\n      rect.applyTransform(group.transform);\n      return rect.contain(x, y) && !onIrrelevantElement(e, api, seriesModel);\n    });\n    if (!isViewCoordSys(seriesModel.coordinateSystem)) {\n      controller.disable();\n      return;\n    }\n    controller.enable(seriesModel.get('roam'));\n    controllerHost.zoomLimit = seriesModel.get('scaleLimit');\n    controllerHost.zoom = seriesModel.coordinateSystem.getZoom();\n    controller.off('pan').off('zoom').on('pan', function (e) {\n      roamHelper.updateViewOnPan(controllerHost, e.dx, e.dy);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'graphRoam',\n        dx: e.dx,\n        dy: e.dy\n      });\n    }).on('zoom', function (e) {\n      roamHelper.updateViewOnZoom(controllerHost, e.scale, e.originX, e.originY);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'graphRoam',\n        zoom: e.scale,\n        originX: e.originX,\n        originY: e.originY\n      });\n      _this._updateNodeAndLinkScale();\n      adjustEdge(seriesModel.getGraph(), getNodeGlobalScale(seriesModel));\n      _this._lineDraw.updateLayout();\n      // Only update label layout on zoom\n      api.updateLabelLayout();\n    });\n  };\n  GraphView.prototype._updateNodeAndLinkScale = function () {\n    var seriesModel = this._model;\n    var data = seriesModel.getData();\n    var nodeScale = getNodeGlobalScale(seriesModel);\n    data.eachItemGraphicEl(function (el, idx) {\n      el && el.setSymbolScale(nodeScale);\n    });\n  };\n  GraphView.prototype.updateLayout = function (seriesModel) {\n    adjustEdge(seriesModel.getGraph(), getNodeGlobalScale(seriesModel));\n    this._symbolDraw.updateLayout();\n    this._lineDraw.updateLayout();\n  };\n  GraphView.prototype.remove = function () {\n    clearTimeout(this._layoutTimeout);\n    this._layouting = false;\n    this._layoutTimeout = null;\n    this._symbolDraw && this._symbolDraw.remove();\n    this._lineDraw && this._lineDraw.remove();\n  };\n  GraphView.type = 'graph';\n  return GraphView;\n}(ChartView);\nexport default GraphView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,UAAU,MAAM,sCAAsC;AAClE,SAASC,mBAAmB,QAAQ,wCAAwC;AAC5E,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,kBAAkB,QAAQ,kBAAkB;AACrD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,cAAc,EAAEC,eAAe,QAAQ,2BAA2B;AAC3E,SAASC,cAAcA,CAACC,QAAQ,EAAE;EAChC,OAAOA,QAAQ,CAACC,IAAI,KAAK,MAAM;AACjC;AACA,IAAIC,SAAS,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC7ClB,SAAS,CAACiB,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAAA,EAAG;IACnB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACH,IAAI,GAAGC,SAAS,CAACD,IAAI;IAC3B,OAAOG,KAAK;EACd;EACAF,SAAS,CAACK,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IACjD,IAAIC,UAAU,GAAG,IAAIzB,UAAU,CAAC,CAAC;IACjC,IAAI0B,QAAQ,GAAG,IAAIzB,QAAQ,CAAC,CAAC;IAC7B,IAAI0B,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI,CAACC,WAAW,GAAG,IAAI1B,cAAc,CAACsB,GAAG,CAACK,KAAK,CAAC,CAAC,CAAC;IAClD,IAAI,CAACC,eAAe,GAAG;MACrBC,MAAM,EAAEJ;IACV,CAAC;IACDA,KAAK,CAACK,GAAG,CAACP,UAAU,CAACE,KAAK,CAAC;IAC3BA,KAAK,CAACK,GAAG,CAACN,QAAQ,CAACC,KAAK,CAAC;IACzB,IAAI,CAACM,WAAW,GAAGR,UAAU;IAC7B,IAAI,CAACS,SAAS,GAAGR,QAAQ;IACzB,IAAI,CAACS,YAAY,GAAG,IAAI;EAC1B,CAAC;EACDnB,SAAS,CAACK,SAAS,CAACe,MAAM,GAAG,UAAUC,WAAW,EAAEd,OAAO,EAAEC,GAAG,EAAE;IAChE,IAAIN,KAAK,GAAG,IAAI;IAChB,IAAIJ,QAAQ,GAAGuB,WAAW,CAACC,gBAAgB;IAC3C,IAAI,CAACC,MAAM,GAAGF,WAAW;IACzB,IAAIZ,UAAU,GAAG,IAAI,CAACQ,WAAW;IACjC,IAAIP,QAAQ,GAAG,IAAI,CAACQ,SAAS;IAC7B,IAAIP,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAId,cAAc,CAACC,QAAQ,CAAC,EAAE;MAC5B,IAAI0B,YAAY,GAAG;QACjBC,CAAC,EAAE3B,QAAQ,CAAC2B,CAAC;QACbC,CAAC,EAAE5B,QAAQ,CAAC4B,CAAC;QACbC,MAAM,EAAE7B,QAAQ,CAAC6B,MAAM;QACvBC,MAAM,EAAE9B,QAAQ,CAAC8B;MACnB,CAAC;MACD,IAAI,IAAI,CAACT,YAAY,EAAE;QACrBR,KAAK,CAACkB,IAAI,CAACL,YAAY,CAAC;MAC1B,CAAC,MAAM;QACLnC,OAAO,CAACyC,WAAW,CAACnB,KAAK,EAAEa,YAAY,EAAEH,WAAW,CAAC;MACvD;IACF;IACA;IACA/B,UAAU,CAAC+B,WAAW,CAACU,QAAQ,CAAC,CAAC,EAAExC,kBAAkB,CAAC8B,WAAW,CAAC,CAAC;IACnE,IAAIW,IAAI,GAAGX,WAAW,CAACY,OAAO,CAAC,CAAC;IAChCxB,UAAU,CAACyB,UAAU,CAACF,IAAI,CAAC;IAC3B,IAAIG,QAAQ,GAAGd,WAAW,CAACe,WAAW,CAAC,CAAC;IACxC;IACA1B,QAAQ,CAACwB,UAAU,CAACC,QAAQ,CAAC;IAC7B,IAAI,CAACE,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,iBAAiB,CAACjB,WAAW,EAAEd,OAAO,EAAEC,GAAG,CAAC;IACjD+B,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;IACjC,IAAIC,WAAW,GAAGpB,WAAW,CAACoB,WAAW;IACzC,IAAIC,eAAe,GAAGrB,WAAW,CAACsB,GAAG,CAAC,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;IACnE,IAAIF,WAAW,EAAE;MACf,IAAI,CAACG,0BAA0B,CAACH,WAAW,EAAEC,eAAe,CAAC;IAC/D;IACA,IAAIG,MAAM,GAAGxB,WAAW,CAACsB,GAAG,CAAC,QAAQ,CAAC;IACtCX,IAAI,CAACc,KAAK,CAACC,QAAQ,CAAC,UAAUC,IAAI,EAAE;MAClC,IAAIC,GAAG,GAAGD,IAAI,CAACE,SAAS;MACxB,IAAIC,EAAE,GAAGH,IAAI,CAACI,YAAY,CAAC,CAAC;MAC5B,IAAIC,SAAS,GAAGL,IAAI,CAACM,QAAQ,CAAC,CAAC;MAC/B,IAAI,CAACH,EAAE,EAAE;QACP;MACF;MACA;MACAA,EAAE,CAACI,GAAG,CAAC,MAAM,CAAC,CAACA,GAAG,CAAC,SAAS,CAAC;MAC7B,IAAIC,SAAS,GAAGH,SAAS,CAACV,GAAG,CAAC,WAAW,CAAC;MAC1C,IAAIa,SAAS,EAAE;QACbL,EAAE,CAACM,EAAE,CAAC,MAAM,EAAE,UAAUC,CAAC,EAAE;UACzB,QAAQb,MAAM;YACZ,KAAK,OAAO;cACVJ,WAAW,CAACkB,MAAM,CAAC,CAAC;cACpB,CAACzD,KAAK,CAAC0D,UAAU,IAAI1D,KAAK,CAAC0C,0BAA0B,CAACH,WAAW,EAAEC,eAAe,CAAC;cACnFD,WAAW,CAACoB,QAAQ,CAACZ,GAAG,CAAC;cACzB;cACAjB,IAAI,CAAC8B,aAAa,CAACb,GAAG,EAAE,CAACE,EAAE,CAAC1B,CAAC,EAAE0B,EAAE,CAACzB,CAAC,CAAC,CAAC;cACrC;YACF,KAAK,UAAU;cACbM,IAAI,CAAC8B,aAAa,CAACb,GAAG,EAAE,CAACE,EAAE,CAAC1B,CAAC,EAAE0B,EAAE,CAACzB,CAAC,CAAC,CAAC;cACrC;cACAsB,IAAI,CAACe,SAAS,CAAC;gBACbC,KAAK,EAAE;cACT,CAAC,EAAE,IAAI,CAAC;cACR;cACArE,cAAc,CAAC0B,WAAW,EAAE,YAAY,EAAE2B,IAAI,EAAE,CAACU,CAAC,CAACO,OAAO,EAAEP,CAAC,CAACQ,OAAO,CAAC,CAAC;cACvEhE,KAAK,CAACiE,YAAY,CAAC9C,WAAW,CAAC;cAC/B;YACF,KAAK,MAAM;YACX;cACEW,IAAI,CAAC8B,aAAa,CAACb,GAAG,EAAE,CAACE,EAAE,CAAC1B,CAAC,EAAE0B,EAAE,CAACzB,CAAC,CAAC,CAAC;cACrC;cACAhC,gBAAgB,CAAC2B,WAAW,CAACU,QAAQ,CAAC,CAAC,EAAEV,WAAW,CAAC;cACrDnB,KAAK,CAACiE,YAAY,CAAC9C,WAAW,CAAC;cAC/B;UACJ;QACF,CAAC,CAAC,CAACoC,EAAE,CAAC,SAAS,EAAE,YAAY;UAC3B,IAAIhB,WAAW,EAAE;YACfA,WAAW,CAAC2B,UAAU,CAACnB,GAAG,CAAC;UAC7B;QACF,CAAC,CAAC;MACJ;MACAE,EAAE,CAACkB,YAAY,CAACb,SAAS,EAAE,CAAC,CAACH,SAAS,CAACV,GAAG,CAAC,QAAQ,CAAC,CAAC;MACrD,IAAI2B,KAAK,GAAGjB,SAAS,CAACV,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;MAChD,IAAI2B,KAAK,KAAK,WAAW,EAAE;QACzB7E,SAAS,CAAC0D,EAAE,CAAC,CAACmB,KAAK,GAAGtB,IAAI,CAACuB,sBAAsB,CAAC,CAAC;MACrD;IACF,CAAC,CAAC;IACFvC,IAAI,CAACc,KAAK,CAAC0B,QAAQ,CAAC,UAAUC,IAAI,EAAE;MAClC,IAAItB,EAAE,GAAGsB,IAAI,CAACrB,YAAY,CAAC,CAAC;MAC5B,IAAIkB,KAAK,GAAGG,IAAI,CAACnB,QAAQ,CAAC,CAAC,CAACX,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;MACtD,IAAI,CAACQ,EAAE,EAAE;QACP;MACF;MACA,IAAImB,KAAK,KAAK,WAAW,EAAE;QACzB7E,SAAS,CAAC0D,EAAE,CAAC,CAACmB,KAAK,GAAG;UACpBG,IAAI,EAAE,CAACA,IAAI,CAACvB,SAAS,CAAC;UACtBF,IAAI,EAAE,CAACyB,IAAI,CAACC,KAAK,CAACxB,SAAS,EAAEuB,IAAI,CAACE,KAAK,CAACzB,SAAS;QACnD,CAAC;MACH;IACF,CAAC,CAAC;IACF,IAAI0B,mBAAmB,GAAGvD,WAAW,CAACsB,GAAG,CAAC,QAAQ,CAAC,KAAK,UAAU,IAAItB,WAAW,CAACsB,GAAG,CAAC,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;IAClH,IAAIkC,EAAE,GAAG7C,IAAI,CAAC8C,SAAS,CAAC,IAAI,CAAC;IAC7B,IAAIC,EAAE,GAAG/C,IAAI,CAAC8C,SAAS,CAAC,IAAI,CAAC;IAC7B9C,IAAI,CAACc,KAAK,CAACC,QAAQ,CAAC,UAAUC,IAAI,EAAE;MAClCpD,eAAe,CAACoD,IAAI,EAAE4B,mBAAmB,EAAEC,EAAE,EAAEE,EAAE,CAAC;IACpD,CAAC,CAAC;IACF,IAAI,CAAC5D,YAAY,GAAG,KAAK;EAC3B,CAAC;EACDnB,SAAS,CAACK,SAAS,CAAC2E,OAAO,GAAG,YAAY;IACxC,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,IAAI,CAACrE,WAAW,IAAI,IAAI,CAACA,WAAW,CAACoE,OAAO,CAAC,CAAC;IAC9C,IAAI,CAAClE,eAAe,GAAG,IAAI;EAC7B,CAAC;EACDd,SAAS,CAACK,SAAS,CAACuC,0BAA0B,GAAG,UAAUH,WAAW,EAAEC,eAAe,EAAE;IACvF,IAAIwC,IAAI,GAAG,IAAI;IACf,CAAC,SAASC,IAAIA,CAAA,EAAG;MACf1C,WAAW,CAAC0C,IAAI,CAAC,UAAUC,OAAO,EAAE;QAClCF,IAAI,CAACf,YAAY,CAACe,IAAI,CAAC3D,MAAM,CAAC;QAC9B,CAAC2D,IAAI,CAACtB,UAAU,GAAG,CAACwB,OAAO,MAAM1C,eAAe,GAAGwC,IAAI,CAAC1C,cAAc,GAAG6C,UAAU,CAACF,IAAI,EAAE,EAAE,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC;MACzG,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC;EACN,CAAC;EACDnF,SAAS,CAACK,SAAS,CAACiC,iBAAiB,GAAG,UAAUjB,WAAW,EAAEd,OAAO,EAAEC,GAAG,EAAE;IAC3E,IAAIN,KAAK,GAAG,IAAI;IAChB,IAAIoF,UAAU,GAAG,IAAI,CAAC1E,WAAW;IACjC,IAAI2E,cAAc,GAAG,IAAI,CAACzE,eAAe;IACzC,IAAIH,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB2E,UAAU,CAACE,iBAAiB,CAAC,UAAU9B,CAAC,EAAEjC,CAAC,EAAEC,CAAC,EAAE;MAC9C,IAAI+D,IAAI,GAAG9E,KAAK,CAAC+E,eAAe,CAAC,CAAC;MAClCD,IAAI,CAACE,cAAc,CAAChF,KAAK,CAACiF,SAAS,CAAC;MACpC,OAAOH,IAAI,CAACI,OAAO,CAACpE,CAAC,EAAEC,CAAC,CAAC,IAAI,CAACtC,mBAAmB,CAACsE,CAAC,EAAElD,GAAG,EAAEa,WAAW,CAAC;IACxE,CAAC,CAAC;IACF,IAAI,CAACxB,cAAc,CAACwB,WAAW,CAACC,gBAAgB,CAAC,EAAE;MACjDgE,UAAU,CAACQ,OAAO,CAAC,CAAC;MACpB;IACF;IACAR,UAAU,CAACS,MAAM,CAAC1E,WAAW,CAACsB,GAAG,CAAC,MAAM,CAAC,CAAC;IAC1C4C,cAAc,CAACS,SAAS,GAAG3E,WAAW,CAACsB,GAAG,CAAC,YAAY,CAAC;IACxD4C,cAAc,CAACU,IAAI,GAAG5E,WAAW,CAACC,gBAAgB,CAAC4E,OAAO,CAAC,CAAC;IAC5DZ,UAAU,CAAC/B,GAAG,CAAC,KAAK,CAAC,CAACA,GAAG,CAAC,MAAM,CAAC,CAACE,EAAE,CAAC,KAAK,EAAE,UAAUC,CAAC,EAAE;MACvDvE,UAAU,CAACgH,eAAe,CAACZ,cAAc,EAAE7B,CAAC,CAAC0C,EAAE,EAAE1C,CAAC,CAAC2C,EAAE,CAAC;MACtD7F,GAAG,CAAC8F,cAAc,CAAC;QACjBC,QAAQ,EAAElF,WAAW,CAACmF,EAAE;QACxBzG,IAAI,EAAE,WAAW;QACjBqG,EAAE,EAAE1C,CAAC,CAAC0C,EAAE;QACRC,EAAE,EAAE3C,CAAC,CAAC2C;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC5C,EAAE,CAAC,MAAM,EAAE,UAAUC,CAAC,EAAE;MACzBvE,UAAU,CAACsH,gBAAgB,CAAClB,cAAc,EAAE7B,CAAC,CAACgD,KAAK,EAAEhD,CAAC,CAACiD,OAAO,EAAEjD,CAAC,CAACkD,OAAO,CAAC;MAC1EpG,GAAG,CAAC8F,cAAc,CAAC;QACjBC,QAAQ,EAAElF,WAAW,CAACmF,EAAE;QACxBzG,IAAI,EAAE,WAAW;QACjBkG,IAAI,EAAEvC,CAAC,CAACgD,KAAK;QACbC,OAAO,EAAEjD,CAAC,CAACiD,OAAO;QAClBC,OAAO,EAAElD,CAAC,CAACkD;MACb,CAAC,CAAC;MACF1G,KAAK,CAACmC,uBAAuB,CAAC,CAAC;MAC/B/C,UAAU,CAAC+B,WAAW,CAACU,QAAQ,CAAC,CAAC,EAAExC,kBAAkB,CAAC8B,WAAW,CAAC,CAAC;MACnEnB,KAAK,CAACgB,SAAS,CAACiD,YAAY,CAAC,CAAC;MAC9B;MACA3D,GAAG,CAACqG,iBAAiB,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC;EACD7G,SAAS,CAACK,SAAS,CAACgC,uBAAuB,GAAG,YAAY;IACxD,IAAIhB,WAAW,GAAG,IAAI,CAACE,MAAM;IAC7B,IAAIS,IAAI,GAAGX,WAAW,CAACY,OAAO,CAAC,CAAC;IAChC,IAAI6E,SAAS,GAAGvH,kBAAkB,CAAC8B,WAAW,CAAC;IAC/CW,IAAI,CAAC+E,iBAAiB,CAAC,UAAU5D,EAAE,EAAEF,GAAG,EAAE;MACxCE,EAAE,IAAIA,EAAE,CAAC6D,cAAc,CAACF,SAAS,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;EACD9G,SAAS,CAACK,SAAS,CAAC8D,YAAY,GAAG,UAAU9C,WAAW,EAAE;IACxD/B,UAAU,CAAC+B,WAAW,CAACU,QAAQ,CAAC,CAAC,EAAExC,kBAAkB,CAAC8B,WAAW,CAAC,CAAC;IACnE,IAAI,CAACJ,WAAW,CAACkD,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACjD,SAAS,CAACiD,YAAY,CAAC,CAAC;EAC/B,CAAC;EACDnE,SAAS,CAACK,SAAS,CAAC4E,MAAM,GAAG,YAAY;IACvC1C,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;IACjC,IAAI,CAACoB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACpB,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACvB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACgE,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC/D,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC+D,MAAM,CAAC,CAAC;EAC3C,CAAC;EACDjF,SAAS,CAACD,IAAI,GAAG,OAAO;EACxB,OAAOC,SAAS;AAClB,CAAC,CAACR,SAAS,CAAC;AACZ,eAAeQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}