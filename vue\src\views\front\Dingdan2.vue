<template>
    <div class="cart-container">
        <!-- 购物车统计 -->
        <div class="content-section">
            <div class="cart-summary" v-if="filteredTableData.length > 0">
                <div class="summary-card">
                    <div class="summary-item">
                        <i class="el-icon-shopping-cart-2 summary-icon"></i>
                        <div class="summary-content">
                            <div class="summary-label">商品数量</div>
                            <div class="summary-value">{{ filteredTableData.length }} 件</div>
                        </div>
                    </div>
                    <div class="summary-item">
                        <i class="el-icon-money summary-icon"></i>
                        <div class="summary-content">
                            <div class="summary-label">总金额</div>
                            <div class="summary-value">¥{{ totalAmount }}</div>
                        </div>
                    </div>
                    <div class="summary-actions">
                        <el-button 
                            type="primary" 
                            size="large"
                            @click="payAllSelected"
                            :disabled="selectedItems.length === 0"
                            class="pay-all-btn">
                            <i class="el-icon-wallet"></i>
                            批量支付 ({{ selectedItems.length }})
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="operation-section">
                <el-button 
                    type="danger" 
                    size="medium"
                    @click="delBatch"
                    :disabled="!ids.length"
                    class="batch-delete-btn">
                    <i class="el-icon-delete"></i>
                    批量删除 ({{ ids.length }})
                </el-button>
            </div>

            <!-- 购物车列表 -->
            <div class="cart-list">
                <div v-if="filteredTableData.length === 0" class="empty-state">
                    <i class="el-icon-shopping-cart-full"></i>
                    <h3>购物车空空如也</h3>
                    <p>快去挑选您喜欢的商品吧</p>
                    <el-button type="primary" @click="$router.push('/front/home')" class="go-shopping-btn">
                        去购物
                    </el-button>
                </div>
                
                <div v-else class="cart-grid">
                    <div 
                        v-for="item in filteredTableData" 
                        :key="item.id"
                        class="cart-item"
                        :class="{ 'selected': selectedItems.includes(item.id) }"
                        @click="toggleSelection(item)">
                        
                        <div class="item-header">
                            <div class="item-info">
                                <div class="item-name">{{ item.foodName || '商品ID: ' + item.foodId }}</div>
                                <div class="item-time">{{ item.sfCreateTime }}</div>
                            </div>
                            <div class="item-status">
                                <el-tag 
                                    :type="item.status === '未付款' ? 'danger' : 'success'"
                                    size="medium"
                                    class="status-tag">
                                    {{ item.status }}
                                </el-tag>
                            </div>
                        </div>

                        <div class="item-content">
                            <div class="item-details">
                                <div class="detail-item">
                                    <i class="el-icon-user detail-icon"></i>
                                    <span class="detail-label">用户：</span>
                                    <span class="detail-value">{{ item.sfUserName }}</span>
                                </div>
                                <div class="detail-item" v-if="item.sfRemark">
                                    <i class="el-icon-chat-line-square detail-icon"></i>
                                    <span class="detail-label">备注：</span>
                                    <span class="detail-value">{{ item.sfRemark }}</span>
                                </div>
                                <div class="detail-item">
                                    <i class="el-icon-shopping-bag-2 detail-icon"></i>
                                    <span class="detail-label">数量：</span>
                                    <div class="quantity-controls">
                                        <el-button 
                                            size="mini" 
                                            icon="el-icon-minus" 
                                            @click.stop="updateQuantity(item, item.quantity - 1)"
                                            :disabled="item.quantity <= 1"
                                            class="quantity-btn">
                                        </el-button>
                                        <span class="quantity-value">{{ item.quantity }}</span>
                                        <el-button 
                                            size="mini" 
                                            icon="el-icon-plus" 
                                            @click.stop="updateQuantity(item, item.quantity + 1)"
                                            :disabled="item.quantity >= 99"
                                            class="quantity-btn">
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="item-price">
                                <div class="price-label">商品价格</div>
                                <div class="price-value">¥{{ item.sfTotalPrice }}</div>
                            </div>
                        </div>

                        <div class="item-actions">
                            <el-button
                                v-if="item.status === '未付款' && item.sfCartStatus === '已加入购物车'"
                                type="success"
                                size="small"
                                @click.stop="handlePay(item)"
                                class="action-btn pay-btn">
                                <i class="el-icon-wallet"></i>
                                立即支付
                            </el-button>
                            <el-button
                                type="danger"
                                size="small"
                                @click.stop="del(item.id)"
                                class="action-btn">
                                <i class="el-icon-delete"></i>
                                删除
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-section">
                <el-pagination
                    background
                    @current-change="handleCurrentChange"
                    :current-page="pageNum"
                    :page-size="pageSize"
                    layout="prev, pager, next"
                    :total="total"
                    class="custom-pagination">
                </el-pagination>
            </div>
        </div>

        <!-- 餐桌选择对话框 -->
        <el-dialog 
            title="选择餐桌" 
            :visible.sync="tableDialogVisible" 
            width="800px"
            :close-on-click-modal="false">
            <div class="table-selection-container">
                <div class="table-selection-header">
                    <div class="selection-info">
                        <p v-if="currentPayItem">
                            单个商品支付：{{ currentPayItem.foodName || '商品ID: ' + currentPayItem.foodId }}，
                            金额：<span class="total-price">¥{{ currentPayItem.sfTotalPrice }}</span>
                        </p>
                        <p v-else>
                            批量支付：共{{ selectedItems.length }}件商品，
                            总金额：<span class="total-price">¥{{ totalAmount }}</span>
                        </p>
                        <p v-if="selectedTable">已选择：{{ selectedTable.tableNumber }}号桌 ({{ selectedTable.seats }}人座，{{ selectedTable.area }})</p>
                    </div>
                </div>
                
                <!-- 餐桌选择组件 -->
                <table-select 
                    v-model="selectedTable" 
                    @table-selected="handleTableSelected">
                </table-select>
            </div>
            
            <template slot="footer">
                <span class="dialog-footer">
                    <el-button @click="tableDialogVisible = false">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="confirmCheckout"
                        :disabled="!selectedTable">
                        确认下单
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import TableSelect from '@/components/TableSelect.vue'

export default {
    name: "CartPage",
    components: {
        TableSelect
    },
    data() {
        return {
            tableData: [],  // 所有的数据
            filteredTableData: [], // 过滤后的数据
            pageNum: 1,   // 当前的页码
            pageSize: 10,  // 每页显示的个数
            total: 0,

            fromVisible: false,
            form: {},
            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
            rules: {
                sfUserName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
                sfUserId: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
                status: [{ required: true, message: '请选择订单状态', trigger: 'change' }],
                sfOrderNumber: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],
                sfCreateTime: [{ required: true, message: '请选择下单时间', trigger: 'change' }],
                sfTotalPrice: [{ required: true, message: '请输入订单价格', trigger: 'blur' }],
                sfCartStatus: [{ required: true, message: '请选择购物车状态', trigger: 'change' }],
            },
            ids: [],
            selectedItems: [],
            // 餐桌选择相关
            tableDialogVisible: false,
            selectedTable: null,
            currentPayItem: null, // 当前要支付的单个商品
            goodsPriceMap: {}, // 存储商品ID和价格的映射
            goodsNameMap: {}, // 存储商品ID和名称的映射
            submitting: false // 防重复提交
        }
    },
    computed: {
        totalAmount() {
            return this.selectedItems.reduce((total, itemId) => {
                const item = this.filteredTableData.find(item => item.id === itemId)
                return total + (item ? parseFloat(item.sfTotalPrice) : 0)
            }, 0).toFixed(2)
        }
    },
    created() {
        this.load(1) // 加载购物车数据
    },
    methods: {
        toggleSelection(item) {
            const index = this.selectedItems.indexOf(item.id)
            if (index > -1) {
                this.selectedItems.splice(index, 1)
            } else {
                this.selectedItems.push(item.id)
            }
            this.ids = this.selectedItems
        },

        // 批量支付选中的商品（购物车结算）
        payAllSelected() {
            if (this.selectedItems.length === 0) {
                this.$message.warning('请选择要支付的商品')
                return
            }
            
            // 打开餐桌选择对话框
            this.currentPayItem = null // 清除单个商品支付状态
            this.selectedTable = null
            this.tableDialogVisible = true
        },

        // 处理餐桌选择
        handleTableSelected(table) {
            this.selectedTable = table
        },

        // 确认下单（选择餐桌后）
        confirmCheckout() {
            if (!this.selectedTable) {
                this.$message.warning('请选择餐桌')
                return
            }

            // 防重复提交
            if (this.submitting) {
                this.$message.warning('正在处理中，请勿重复提交')
                return
            }

            // 判断是单个商品支付还是批量支付
            const isSinglePay = this.currentPayItem !== null
            const confirmMessage = isSinglePay 
                ? `确认要在${this.selectedTable.tableNumber}号桌支付该商品吗？金额：¥${this.currentPayItem.sfTotalPrice}`
                : `确认要在${this.selectedTable.tableNumber}号桌结算购物车吗？总金额：¥${this.totalAmount}`
            const confirmTitle = isSinglePay ? '单个商品支付确认' : '购物车结算确认'

            this.$confirm(confirmMessage, confirmTitle, {
                confirmButtonText: '确定支付',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.submitting = true
                
                // 准备请求参数
                const requestData = {
                    tableNumber: this.selectedTable.tableNumber
                }
                
                if (isSinglePay) {
                    // 单个商品支付
                    requestData.remark = `单个商品结算：${this.currentPayItem.foodName || '商品ID:' + this.currentPayItem.foodId}`
                } else {
                    // 批量支付
                    requestData.remark = `购物车批量结算，共${this.selectedItems.length}件商品`
                }

                // 购物车结算，会将所有购物车商品转为订单
                this.$request.post('/cart/checkout', requestData).then(res => {
                    if (res.code === '200') {
                        const successMessage = isSinglePay 
                            ? `商品支付成功，订单已生成！餐桌：${this.selectedTable.tableNumber}号`
                            : `购物车结算成功，订单已生成！餐桌：${this.selectedTable.tableNumber}号`
                        this.$message.success(successMessage)
                        
                        // 重置状态
                        this.selectedItems = []
                        this.ids = []
                        this.currentPayItem = null
                        this.tableDialogVisible = false
                        this.selectedTable = null
                        this.load(1) // 重新加载购物车
                    } else {
                        this.$message.error(res.msg || '支付失败')
                    }
                }).catch(err => {
                    console.error('支付失败:', err)
                    if (err.response && err.response.data && err.response.data.msg) {
                        this.$message.error(err.response.data.msg)
                    } else {
                        this.$message.error('支付失败，请重试')
                    }
                }).finally(() => {
                    this.submitting = false
                })
            }).catch(() => {
                this.$message.info('已取消支付')
            })
        },

        // 加载商品价格和名称
        loadGoodsPrices() {
            this.$request.get('/foods/selectAll').then(res => {
                if (res.code === '200') {
                    this.goodsPriceMap = {}
                    this.goodsNameMap = {}
                    res.data?.forEach(item => {
                        // 使用字符串和数字两种格式作为键，确保能匹配到
                        this.goodsPriceMap[item.id] = item.sfPrice
                        this.goodsPriceMap[item.id.toString()] = item.sfPrice
                        this.goodsNameMap[item.id] = item.name
                        this.goodsNameMap[item.id.toString()] = item.name
                    })
                    // 重新过滤数据以更新价格和名称
                    this.filterTableData()
                }
            }).catch(err => {
                this.$message.error('加载商品信息失败')
                console.error(err)
            })
        },

        // 处理购物车数据，购物车API已返回完整数据，无需过滤
        filterTableData() {
            this.filteredTableData = this.tableData.map(item => {
                // 购物车API返回的数据结构已包含商品详情和小计
                return {
                    ...item,
                    // 为了兼容现有模板，映射字段名
                    sfUserName: item.userId ? '当前用户' : '匿名用户',
                    sfProductIds: item.foodId,
                    sfTotalPrice: item.subtotal,
                    sfCreateTime: item.createTime,
                    status: '未付款', // 购物车商品默认未付款状态
                    sfCartStatus: '已加入购物车'
                }
            })
            this.total = this.filteredTableData.length
        },

        // 立即支付操作（购物车结算）
        handlePay(row) {
            // 设置当前要支付的商品，然后打开餐桌选择对话框
            this.currentPayItem = row
            this.selectedItems = [row.id] // 设置为当前商品
            this.selectedTable = null
            this.tableDialogVisible = true
        },

        handleAdd() {
            this.form = {
                status: '未付款',
                sfCartStatus: '已加入购物车',
                sfCreateTime: new Date(),
                sfTotalPrice: 0
            }
            this.fromVisible = true
        },

        handleEdit(row) {
            this.form = JSON.parse(JSON.stringify(row))
            // 如果是购物车订单，自动同步商品价格
            if (this.form.sfCartStatus === '已加入购物车' && this.goodsPriceMap[this.form.sfProductIds]) {
                this.form.sfTotalPrice = this.goodsPriceMap[this.form.sfProductIds]
            }
            this.fromVisible = true
        },

        // 更新商品数量
        updateQuantity(item, newQuantity) {
            if (newQuantity < 1 || newQuantity > 99) {
                return
            }
            
            this.$request.put('/cart/update', {
                foodId: item.foodId,
                quantity: newQuantity
            }).then(res => {
                if (res.code === '200') {
                    this.$message.success('数量更新成功')
                    this.load(this.pageNum) // 重新加载数据
                } else {
                    this.$message.error(res.msg || '更新失败')
                }
            }).catch(() => {
                this.$message.error('更新失败，请重试')
            })
        },

        save() {
            this.$refs.formRef.validate((valid) => {
                if (valid) {
                                    // 格式化下单时间
                if (this.form.sfCreateTime instanceof Date) {
                    this.form.sfCreateTime = this.formatDateTime(this.form.sfCreateTime)
                }

                    // 如果是购物车订单，确保价格与商品一致
                    if (this.form.sfCartStatus === '已加入购物车' && this.goodsPriceMap[this.form.sfProductIds]) {
                        this.form.sfTotalPrice = this.goodsPriceMap[this.form.sfProductIds]
                    }

                    this.$request({
                        url: this.form.id ? '/dingdan/update' : '/dingdan/add',
                        method: this.form.id ? 'PUT' : 'POST',
                        data: this.form
                    }).then(res => {
                        if (res.code === '200') {
                            this.$message.success('保存成功')
                            this.load(1)
                            this.fromVisible = false
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                }
            })
        },

        formatDateTime(date) {
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            const hours = String(date.getHours()).padStart(2, '0')
            const minutes = String(date.getMinutes()).padStart(2, '0')
            const seconds = String(date.getSeconds()).padStart(2, '0')

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
        },

        del(id) {
            // 根据购物车项ID找到对应的商品ID
            const item = this.filteredTableData.find(item => item.id === id)
            if (!item) {
                this.$message.error('商品不存在')
                return
            }
            
            this.$confirm('您确定要从购物车中移除该商品吗？', '确认删除', {type: "warning"}).then(response => {
                this.$request.delete('/cart/remove', {
                    params: { foodId: item.foodId }
                }).then(res => {
                    if (res.code === '200') {
                        this.$message.success('商品已从购物车中移除')
                        this.load(1)
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {})
        },

        handleSelectionChange(rows) {
            this.ids = rows.map(v => v.id)
        },

        delBatch() {
            if (!this.ids.length) {
                this.$message.warning('请选择数据')
                return
            }
            this.$confirm('您确定要清空购物车吗？', '确认删除', {type: "warning"}).then(response => {
                this.$request.delete('/cart/clear').then(res => {
                    if (res.code === '200') {
                        this.$message.success('购物车已清空')
                        this.selectedItems = []
                        this.ids = []
                        this.load(1)
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {})
        },

        load(pageNum) {
            if (pageNum) this.pageNum = pageNum
            this.$request.get('/cart/list').then(res => {
                if (res.code === '200') {
                    this.tableData = res.data || []
                    this.filterTableData()
                } else {
                    this.$message.error(res.msg)
                }
            }).catch(err => {
                this.$message.error('加载购物车数据失败')
                console.error(err)
            })
        },



        handleCurrentChange(pageNum) {
            this.load(pageNum)
        },
    }
}
</script>

<style scoped>
.cart-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    min-height: 100vh;
}

/* 内容区域 */
.content-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}



/* 购物车统计 */
.cart-summary {
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.summary-icon {
    width: 48px;
    height: 48px;
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.summary-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.summary-label {
    font-size: 14px;
    color: #64748b;
}

.summary-value {
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
}

.summary-actions {
    display: flex;
    gap: 12px;
}

.pay-all-btn {
    background: #10b981;
    border-color: #10b981;
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 16px;
}

.pay-all-btn:hover {
    background: #059669;
    border-color: #059669;
}

.pay-all-btn:disabled {
    background: #e5e7eb;
    border-color: #e5e7eb;
    color: #9ca3af;
}

/* 操作区域 */
.operation-section {
    margin-bottom: 30px;
}

.batch-delete-btn {
    background: #ef4444;
    border-color: #ef4444;
    border-radius: 8px;
    font-weight: 500;
}

.batch-delete-btn:hover {
    background: #dc2626;
    border-color: #dc2626;
}

.batch-delete-btn:disabled {
    background: #e5e7eb;
    border-color: #e5e7eb;
    color: #9ca3af;
}

/* 购物车列表 */
.cart-list {
    margin-bottom: 40px;
}

.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #64748b;
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 16px;
    color: #cbd5e1;
}

.empty-state h3 {
    font-size: 20px;
    margin-bottom: 8px;
    color: #475569;
}

.empty-state p {
    margin-bottom: 24px;
}

.go-shopping-btn {
    background: #3b82f6;
    border-color: #3b82f6;
    border-radius: 25px;
    padding: 12px 32px;
    font-weight: 600;
}

.go-shopping-btn:hover {
    background: #1e40af;
    border-color: #1e40af;
}

.cart-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
}

.cart-item {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.cart-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);
    border-color: #3b82f6;
}

.cart-item.selected {
    border-color: #3b82f6;
    background: #f0f9ff;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.item-info {
    flex: 1;
}

.item-name {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.item-time {
    font-size: 14px;
    color: #64748b;
}

.item-status {
    flex-shrink: 0;
}

.status-tag {
    font-weight: 500;
    border-radius: 12px;
    padding: 4px 12px;
}

.item-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.item-details {
    flex: 1;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-icon {
    width: 16px;
    color: #3b82f6;
    margin-right: 8px;
}

.detail-label {
    color: #64748b;
    margin-right: 8px;
}

.detail-value {
    color: #1e293b;
    font-weight: 500;
}

.item-price {
    text-align: right;
    flex-shrink: 0;
    margin-left: 20px;
}

.price-label {
    font-size: 12px;
    color: #64748b;
    margin-bottom: 4px;
}

.price-value {
    font-size: 20px;
    font-weight: 700;
    color: #10b981;
}

.item-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.action-btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
}

.pay-btn {
    background: #10b981;
    border-color: #10b981;
}

.pay-btn:hover {
    background: #059669;
    border-color: #059669;
}

/* 数量控制 */
.quantity-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.quantity-btn {
    width: 24px;
    height: 24px;
    padding: 0;
    border-radius: 50%;
    border: 1px solid #e5e7eb;
    background: white;
    color: #6b7280;
    transition: all 0.3s ease;
}

.quantity-btn:hover:not(:disabled) {
    border-color: #3b82f6;
    color: #3b82f6;
    background: #f0f9ff;
}

.quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quantity-value {
    font-weight: 600;
    color: #1f2937;
    min-width: 20px;
    text-align: center;
}

/* 分页 */
.pagination-section {
    display: flex;
    justify-content: center;
    padding: 20px 0;
}

.custom-pagination >>> .el-pager li {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin: 0 4px;
    transition: all 0.3s ease;
}

.custom-pagination >>> .el-pager li:hover {
    border-color: #3b82f6;
    color: #3b82f6;
}

.custom-pagination >>> .el-pager li.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .cart-grid {
        grid-template-columns: 1fr;
    }
    

    
    .summary-card {
        flex-direction: column;
        text-align: center;
    }
    
    .item-content {
        flex-direction: column;
    }
    
    .item-price {
        margin-left: 0;
        margin-top: 16px;
        text-align: left;
    }
}

/* 餐桌选择对话框样式 */
.table-selection-container {
    padding: 20px 0;
}

.table-selection-header {
    margin-bottom: 20px;
}

.selection-info p {
    margin: 8px 0;
    font-size: 16px;
}

.total-price {
    color: #e6a23c;
    font-weight: bold;
    font-size: 18px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>