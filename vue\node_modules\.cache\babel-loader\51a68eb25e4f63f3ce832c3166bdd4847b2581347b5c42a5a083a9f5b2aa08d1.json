{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  data() {\n    const validatePassword = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请确认密码'));\n      } else if (value !== this.user.newPassword) {\n        callback(new Error('确认密码错误'));\n      } else {\n        callback();\n      }\n    };\n    return {\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      dialogVisible: false,\n      rules: {\n        password: [{\n          required: true,\n          message: '请输入原始密码',\n          trigger: 'blur'\n        }],\n        newPassword: [{\n          required: true,\n          message: '请输入新密码',\n          trigger: 'blur'\n        }],\n        confirmPassword: [{\n          validator: validatePassword,\n          required: true,\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created() {},\n  methods: {\n    update() {\n      // 保存当前的用户信息到数据库\n      this.$request.put('/user/update', this.user).then(res => {\n        if (res.code === '200') {\n          // 成功更新\n          this.$message.success('保存成功');\n          // 更新浏览器缓存里的用户信息\n          localStorage.setItem('xm-user', JSON.stringify(this.user));\n\n          // 触发父级的数据更新\n          this.$emit('update:user');\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    handleAvatarSuccess(response, file, fileList) {\n      // 把user的头像属性换成上传的图片的链接\n      this.$set(this.user, 'avatar', response.data);\n    },\n    // 修改密码\n    updatePassword() {\n      this.dialogVisible = true;\n    },\n    save() {\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          this.$request.put('/updatePassword', this.user).then(res => {\n            if (res.code === '200') {\n              // 成功更新\n              this.$message.success('修改密码成功');\n              this.$router.push('/login');\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["data", "validatePassword", "rule", "value", "callback", "Error", "user", "newPassword", "JSON", "parse", "localStorage", "getItem", "dialogVisible", "rules", "password", "required", "message", "trigger", "confirmPassword", "validator", "created", "methods", "update", "$request", "put", "then", "res", "code", "$message", "success", "setItem", "stringify", "$emit", "error", "msg", "handleAvatarSuccess", "response", "file", "fileList", "$set", "updatePassword", "save", "$refs", "formRef", "validate", "valid", "$router", "push"], "sources": ["src/views/front/Person.vue"], "sourcesContent": ["<template>\r\n  <div class=\"main-content\">\r\n    <el-card style=\"width: 50%; margin: 30px auto\">\r\n      <div style=\"text-align: right; margin-bottom: 20px\">\r\n        <el-button type=\"primary\" @click=\"updatePassword\">修改密码</el-button>\r\n      </div>\r\n      <el-form :model=\"user\" label-width=\"80px\" style=\"padding-right: 20px\">\r\n        <div style=\"margin: 15px; text-align: center\">\r\n          <el-upload\r\n              class=\"avatar-uploader\"\r\n              :action=\"$baseUrl + '/files/upload'\"\r\n              :show-file-list=\"false\"\r\n              :on-success=\"handleAvatarSuccess\"\r\n          >\r\n            <img v-if=\"user.avatar\" :src=\"user.avatar\" class=\"avatar\" />\r\n            <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\r\n          </el-upload>\r\n        </div>\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"user.username\" placeholder=\"用户名\" disabled></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"昵称\" prop=\"name\">\r\n          <el-input v-model=\"user.name\" placeholder=\"昵称\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"电话\" prop=\"phone\">\r\n          <el-input v-model=\"user.phone\" placeholder=\"电话\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <el-input v-model=\"user.email\" placeholder=\"邮箱\"></el-input>\r\n        </el-form-item>\r\n        <div style=\"text-align: center; margin-bottom: 20px\">\r\n          <el-button type=\"primary\" @click=\"update\">保 存</el-button>\r\n        </div>\r\n      </el-form>\r\n    </el-card>\r\n    <el-dialog title=\"修改密码\" :visible.sync=\"dialogVisible\" width=\"30%\" :close-on-click-modal=\"false\" destroy-on-close>\r\n      <el-form :model=\"user\" label-width=\"80px\" style=\"padding-right: 20px\" :rules=\"rules\" ref=\"formRef\">\r\n        <el-form-item label=\"原始密码\" prop=\"password\">\r\n          <el-input show-password v-model=\"user.password\" placeholder=\"原始密码\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"新密码\" prop=\"newPassword\">\r\n          <el-input show-password v-model=\"user.newPassword\" placeholder=\"新密码\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\r\n          <el-input show-password v-model=\"user.confirmPassword\" placeholder=\"确认密码\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"fromVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    const validatePassword = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请确认密码'))\r\n      } else if (value !== this.user.newPassword) {\r\n        callback(new Error('确认密码错误'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n    return {\r\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n      dialogVisible: false,\r\n\r\n      rules: {\r\n        password: [\r\n          { required: true, message: '请输入原始密码', trigger: 'blur' },\r\n        ],\r\n        newPassword: [\r\n          { required: true, message: '请输入新密码', trigger: 'blur' },\r\n        ],\r\n        confirmPassword: [\r\n          { validator: validatePassword, required: true, trigger: 'blur' },\r\n        ],\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n\r\n  },\r\n  methods: {\r\n    update() {\r\n      // 保存当前的用户信息到数据库\r\n      this.$request.put('/user/update', this.user).then(res => {\r\n        if (res.code === '200') {\r\n          // 成功更新\r\n          this.$message.success('保存成功')\r\n          // 更新浏览器缓存里的用户信息\r\n          localStorage.setItem('xm-user', JSON.stringify(this.user))\r\n\r\n          // 触发父级的数据更新\r\n          this.$emit('update:user')\r\n        } else {\r\n          this.$message.error(res.msg)\r\n        }\r\n      })\r\n    },\r\n    handleAvatarSuccess(response, file, fileList) {\r\n      // 把user的头像属性换成上传的图片的链接\r\n      this.$set(this.user, 'avatar', response.data)\r\n    },\r\n    // 修改密码\r\n    updatePassword() {\r\n      this.dialogVisible = true\r\n    },\r\n    save() {\r\n      this.$refs.formRef.validate((valid) => {\r\n        if (valid) {\r\n          this.$request.put('/updatePassword', this.user).then(res => {\r\n            if (res.code === '200') {\r\n              // 成功更新\r\n              this.$message.success('修改密码成功')\r\n              this.$router.push('/login')\r\n            } else {\r\n              this.$message.error(res.msg)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/deep/.el-form-item__label {\r\n  font-weight: bold;\r\n}\r\n/deep/.el-upload {\r\n  border-radius: 50%;\r\n}\r\n/deep/.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  border-radius: 50%;\r\n}\r\n/deep/.avatar-uploader .el-upload:hover {\r\n  border-color: #409EFF;\r\n}\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 120px;\r\n  height: 120px;\r\n  line-height: 120px;\r\n  text-align: center;\r\n  border-radius: 50%;\r\n}\r\n.avatar {\r\n  width: 120px;\r\n  height: 120px;\r\n  display: block;\r\n  border-radius: 50%;\r\n}\r\n</style>"], "mappings": ";AAwDA;EACAA,KAAA;IACA,MAAAC,gBAAA,GAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAD,KAAA;QACAC,QAAA,KAAAC,KAAA;MACA,WAAAF,KAAA,UAAAG,IAAA,CAAAC,WAAA;QACAH,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IACA;MACAE,IAAA,EAAAE,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,aAAA;MAEAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,WAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,eAAA,GACA;UAAAC,SAAA,EAAAlB,gBAAA;UAAAc,QAAA;UAAAE,OAAA;QAAA;MAEA;IACA;EACA;EACAG,QAAA,GAEA;EACAC,OAAA;IACAC,OAAA;MACA;MACA,KAAAC,QAAA,CAAAC,GAAA,sBAAAlB,IAAA,EAAAmB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA;UACA,KAAAC,QAAA,CAAAC,OAAA;UACA;UACAnB,YAAA,CAAAoB,OAAA,YAAAtB,IAAA,CAAAuB,SAAA,MAAAzB,IAAA;;UAEA;UACA,KAAA0B,KAAA;QACA;UACA,KAAAJ,QAAA,CAAAK,KAAA,CAAAP,GAAA,CAAAQ,GAAA;QACA;MACA;IACA;IACAC,oBAAAC,QAAA,EAAAC,IAAA,EAAAC,QAAA;MACA;MACA,KAAAC,IAAA,MAAAjC,IAAA,YAAA8B,QAAA,CAAApC,IAAA;IACA;IACA;IACAwC,eAAA;MACA,KAAA5B,aAAA;IACA;IACA6B,KAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAtB,QAAA,CAAAC,GAAA,yBAAAlB,IAAA,EAAAmB,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA,KAAAiB,OAAA,CAAAC,IAAA;YACA;cACA,KAAAnB,QAAA,CAAAK,KAAA,CAAAP,GAAA,CAAAQ,GAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}