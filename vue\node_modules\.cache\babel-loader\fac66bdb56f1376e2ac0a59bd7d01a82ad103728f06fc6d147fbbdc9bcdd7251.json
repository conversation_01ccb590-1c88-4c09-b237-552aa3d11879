{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseAxisPointer from './BaseAxisPointer.js';\nimport * as viewHelper from './viewHelper.js';\nimport * as singleAxisHelper from '../../coord/single/singleAxisHelper.js';\nvar XY = ['x', 'y'];\nvar WH = ['width', 'height'];\nvar SingleAxisPointer = /** @class */function (_super) {\n  __extends(SingleAxisPointer, _super);\n  function SingleAxisPointer() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\r\n   * @override\r\n   */\n  SingleAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    var axis = axisModel.axis;\n    var coordSys = axis.coordinateSystem;\n    var otherExtent = getGlobalExtent(coordSys, 1 - getPointDimIndex(axis));\n    var pixelValue = coordSys.dataToPoint(value)[0];\n    var axisPointerType = axisPointerModel.get('type');\n    if (axisPointerType && axisPointerType !== 'none') {\n      var elStyle = viewHelper.buildElStyle(axisPointerModel);\n      var pointerOption = pointerShapeBuilder[axisPointerType](axis, pixelValue, otherExtent);\n      pointerOption.style = elStyle;\n      elOption.graphicKey = pointerOption.type;\n      elOption.pointer = pointerOption;\n    }\n    var layoutInfo = singleAxisHelper.layout(axisModel);\n    viewHelper.buildCartesianSingleLabelElOption(\n    // @ts-ignore\n    value, elOption, layoutInfo, axisModel, axisPointerModel, api);\n  };\n  /**\r\n   * @override\r\n   */\n  SingleAxisPointer.prototype.getHandleTransform = function (value, axisModel, axisPointerModel) {\n    var layoutInfo = singleAxisHelper.layout(axisModel, {\n      labelInside: false\n    });\n    // @ts-ignore\n    layoutInfo.labelMargin = axisPointerModel.get(['handle', 'margin']);\n    var position = viewHelper.getTransformedPosition(axisModel.axis, value, layoutInfo);\n    return {\n      x: position[0],\n      y: position[1],\n      rotation: layoutInfo.rotation + (layoutInfo.labelDirection < 0 ? Math.PI : 0)\n    };\n  };\n  /**\r\n   * @override\r\n   */\n  SingleAxisPointer.prototype.updateHandleTransform = function (transform, delta, axisModel, axisPointerModel) {\n    var axis = axisModel.axis;\n    var coordSys = axis.coordinateSystem;\n    var dimIndex = getPointDimIndex(axis);\n    var axisExtent = getGlobalExtent(coordSys, dimIndex);\n    var currPosition = [transform.x, transform.y];\n    currPosition[dimIndex] += delta[dimIndex];\n    currPosition[dimIndex] = Math.min(axisExtent[1], currPosition[dimIndex]);\n    currPosition[dimIndex] = Math.max(axisExtent[0], currPosition[dimIndex]);\n    var otherExtent = getGlobalExtent(coordSys, 1 - dimIndex);\n    var cursorOtherValue = (otherExtent[1] + otherExtent[0]) / 2;\n    var cursorPoint = [cursorOtherValue, cursorOtherValue];\n    cursorPoint[dimIndex] = currPosition[dimIndex];\n    return {\n      x: currPosition[0],\n      y: currPosition[1],\n      rotation: transform.rotation,\n      cursorPoint: cursorPoint,\n      tooltipOption: {\n        verticalAlign: 'middle'\n      }\n    };\n  };\n  return SingleAxisPointer;\n}(BaseAxisPointer);\nvar pointerShapeBuilder = {\n  line: function (axis, pixelValue, otherExtent) {\n    var targetShape = viewHelper.makeLineShape([pixelValue, otherExtent[0]], [pixelValue, otherExtent[1]], getPointDimIndex(axis));\n    return {\n      type: 'Line',\n      subPixelOptimize: true,\n      shape: targetShape\n    };\n  },\n  shadow: function (axis, pixelValue, otherExtent) {\n    var bandWidth = axis.getBandWidth();\n    var span = otherExtent[1] - otherExtent[0];\n    return {\n      type: 'Rect',\n      shape: viewHelper.makeRectShape([pixelValue - bandWidth / 2, otherExtent[0]], [bandWidth, span], getPointDimIndex(axis))\n    };\n  }\n};\nfunction getPointDimIndex(axis) {\n  return axis.isHorizontal() ? 0 : 1;\n}\nfunction getGlobalExtent(coordSys, dimIndex) {\n  var rect = coordSys.getRect();\n  return [rect[XY[dimIndex]], rect[XY[dimIndex]] + rect[WH[dimIndex]]];\n}\nexport default SingleAxisPointer;", "map": {"version": 3, "names": ["__extends", "BaseAxisPointer", "viewHelper", "singleAxisHelper", "XY", "WH", "SingleAxisPointer", "_super", "apply", "arguments", "prototype", "makeElOption", "elOption", "value", "axisModel", "axisPointerModel", "api", "axis", "coordSys", "coordinateSystem", "otherExtent", "getGlobalExtent", "getPointDimIndex", "pixelValue", "dataToPoint", "axisPointerType", "get", "elStyle", "buildElStyle", "pointerOption", "pointerShapeBuilder", "style", "<PERSON><PERSON><PERSON>", "type", "pointer", "layoutInfo", "layout", "buildCartesianSingleLabelElOption", "getHandleTransform", "labelInside", "labelMargin", "position", "getTransformedPosition", "x", "y", "rotation", "labelDirection", "Math", "PI", "updateHandleTransform", "transform", "delta", "dimIndex", "axisExtent", "currPosition", "min", "max", "cursor<PERSON><PERSON><PERSON><PERSON><PERSON>", "cursorPoint", "tooltipOption", "verticalAlign", "line", "targetShape", "makeLineShape", "subPixelOptimize", "shape", "shadow", "bandWidth", "getBandWidth", "span", "makeRectShape", "isHorizontal", "rect", "getRect"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/component/axisPointer/SingleAxisPointer.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseAxisPointer from './BaseAxisPointer.js';\nimport * as viewHelper from './viewHelper.js';\nimport * as singleAxisHelper from '../../coord/single/singleAxisHelper.js';\nvar XY = ['x', 'y'];\nvar WH = ['width', 'height'];\nvar SingleAxisPointer = /** @class */function (_super) {\n  __extends(SingleAxisPointer, _super);\n  function SingleAxisPointer() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\r\n   * @override\r\n   */\n  SingleAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    var axis = axisModel.axis;\n    var coordSys = axis.coordinateSystem;\n    var otherExtent = getGlobalExtent(coordSys, 1 - getPointDimIndex(axis));\n    var pixelValue = coordSys.dataToPoint(value)[0];\n    var axisPointerType = axisPointerModel.get('type');\n    if (axisPointerType && axisPointerType !== 'none') {\n      var elStyle = viewHelper.buildElStyle(axisPointerModel);\n      var pointerOption = pointerShapeBuilder[axisPointerType](axis, pixelValue, otherExtent);\n      pointerOption.style = elStyle;\n      elOption.graphicKey = pointerOption.type;\n      elOption.pointer = pointerOption;\n    }\n    var layoutInfo = singleAxisHelper.layout(axisModel);\n    viewHelper.buildCartesianSingleLabelElOption(\n    // @ts-ignore\n    value, elOption, layoutInfo, axisModel, axisPointerModel, api);\n  };\n  /**\r\n   * @override\r\n   */\n  SingleAxisPointer.prototype.getHandleTransform = function (value, axisModel, axisPointerModel) {\n    var layoutInfo = singleAxisHelper.layout(axisModel, {\n      labelInside: false\n    });\n    // @ts-ignore\n    layoutInfo.labelMargin = axisPointerModel.get(['handle', 'margin']);\n    var position = viewHelper.getTransformedPosition(axisModel.axis, value, layoutInfo);\n    return {\n      x: position[0],\n      y: position[1],\n      rotation: layoutInfo.rotation + (layoutInfo.labelDirection < 0 ? Math.PI : 0)\n    };\n  };\n  /**\r\n   * @override\r\n   */\n  SingleAxisPointer.prototype.updateHandleTransform = function (transform, delta, axisModel, axisPointerModel) {\n    var axis = axisModel.axis;\n    var coordSys = axis.coordinateSystem;\n    var dimIndex = getPointDimIndex(axis);\n    var axisExtent = getGlobalExtent(coordSys, dimIndex);\n    var currPosition = [transform.x, transform.y];\n    currPosition[dimIndex] += delta[dimIndex];\n    currPosition[dimIndex] = Math.min(axisExtent[1], currPosition[dimIndex]);\n    currPosition[dimIndex] = Math.max(axisExtent[0], currPosition[dimIndex]);\n    var otherExtent = getGlobalExtent(coordSys, 1 - dimIndex);\n    var cursorOtherValue = (otherExtent[1] + otherExtent[0]) / 2;\n    var cursorPoint = [cursorOtherValue, cursorOtherValue];\n    cursorPoint[dimIndex] = currPosition[dimIndex];\n    return {\n      x: currPosition[0],\n      y: currPosition[1],\n      rotation: transform.rotation,\n      cursorPoint: cursorPoint,\n      tooltipOption: {\n        verticalAlign: 'middle'\n      }\n    };\n  };\n  return SingleAxisPointer;\n}(BaseAxisPointer);\nvar pointerShapeBuilder = {\n  line: function (axis, pixelValue, otherExtent) {\n    var targetShape = viewHelper.makeLineShape([pixelValue, otherExtent[0]], [pixelValue, otherExtent[1]], getPointDimIndex(axis));\n    return {\n      type: 'Line',\n      subPixelOptimize: true,\n      shape: targetShape\n    };\n  },\n  shadow: function (axis, pixelValue, otherExtent) {\n    var bandWidth = axis.getBandWidth();\n    var span = otherExtent[1] - otherExtent[0];\n    return {\n      type: 'Rect',\n      shape: viewHelper.makeRectShape([pixelValue - bandWidth / 2, otherExtent[0]], [bandWidth, span], getPointDimIndex(axis))\n    };\n  }\n};\nfunction getPointDimIndex(axis) {\n  return axis.isHorizontal() ? 0 : 1;\n}\nfunction getGlobalExtent(coordSys, dimIndex) {\n  var rect = coordSys.getRect();\n  return [rect[XY[dimIndex]], rect[XY[dimIndex]] + rect[WH[dimIndex]]];\n}\nexport default SingleAxisPointer;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAO,KAAKC,UAAU,MAAM,iBAAiB;AAC7C,OAAO,KAAKC,gBAAgB,MAAM,wCAAwC;AAC1E,IAAIC,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AACnB,IAAIC,EAAE,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC5B,IAAIC,iBAAiB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACrDP,SAAS,CAACM,iBAAiB,EAAEC,MAAM,CAAC;EACpC,SAASD,iBAAiBA,CAAA,EAAG;IAC3B,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACA;AACF;AACA;EACEH,iBAAiB,CAACI,SAAS,CAACC,YAAY,GAAG,UAAUC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,GAAG,EAAE;IACtG,IAAIC,IAAI,GAAGH,SAAS,CAACG,IAAI;IACzB,IAAIC,QAAQ,GAAGD,IAAI,CAACE,gBAAgB;IACpC,IAAIC,WAAW,GAAGC,eAAe,CAACH,QAAQ,EAAE,CAAC,GAAGI,gBAAgB,CAACL,IAAI,CAAC,CAAC;IACvE,IAAIM,UAAU,GAAGL,QAAQ,CAACM,WAAW,CAACX,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/C,IAAIY,eAAe,GAAGV,gBAAgB,CAACW,GAAG,CAAC,MAAM,CAAC;IAClD,IAAID,eAAe,IAAIA,eAAe,KAAK,MAAM,EAAE;MACjD,IAAIE,OAAO,GAAGzB,UAAU,CAAC0B,YAAY,CAACb,gBAAgB,CAAC;MACvD,IAAIc,aAAa,GAAGC,mBAAmB,CAACL,eAAe,CAAC,CAACR,IAAI,EAAEM,UAAU,EAAEH,WAAW,CAAC;MACvFS,aAAa,CAACE,KAAK,GAAGJ,OAAO;MAC7Bf,QAAQ,CAACoB,UAAU,GAAGH,aAAa,CAACI,IAAI;MACxCrB,QAAQ,CAACsB,OAAO,GAAGL,aAAa;IAClC;IACA,IAAIM,UAAU,GAAGhC,gBAAgB,CAACiC,MAAM,CAACtB,SAAS,CAAC;IACnDZ,UAAU,CAACmC,iCAAiC;IAC5C;IACAxB,KAAK,EAAED,QAAQ,EAAEuB,UAAU,EAAErB,SAAS,EAAEC,gBAAgB,EAAEC,GAAG,CAAC;EAChE,CAAC;EACD;AACF;AACA;EACEV,iBAAiB,CAACI,SAAS,CAAC4B,kBAAkB,GAAG,UAAUzB,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,EAAE;IAC7F,IAAIoB,UAAU,GAAGhC,gBAAgB,CAACiC,MAAM,CAACtB,SAAS,EAAE;MAClDyB,WAAW,EAAE;IACf,CAAC,CAAC;IACF;IACAJ,UAAU,CAACK,WAAW,GAAGzB,gBAAgB,CAACW,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACnE,IAAIe,QAAQ,GAAGvC,UAAU,CAACwC,sBAAsB,CAAC5B,SAAS,CAACG,IAAI,EAAEJ,KAAK,EAAEsB,UAAU,CAAC;IACnF,OAAO;MACLQ,CAAC,EAAEF,QAAQ,CAAC,CAAC,CAAC;MACdG,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAC;MACdI,QAAQ,EAAEV,UAAU,CAACU,QAAQ,IAAIV,UAAU,CAACW,cAAc,GAAG,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;IAC9E,CAAC;EACH,CAAC;EACD;AACF;AACA;EACE1C,iBAAiB,CAACI,SAAS,CAACuC,qBAAqB,GAAG,UAAUC,SAAS,EAAEC,KAAK,EAAErC,SAAS,EAAEC,gBAAgB,EAAE;IAC3G,IAAIE,IAAI,GAAGH,SAAS,CAACG,IAAI;IACzB,IAAIC,QAAQ,GAAGD,IAAI,CAACE,gBAAgB;IACpC,IAAIiC,QAAQ,GAAG9B,gBAAgB,CAACL,IAAI,CAAC;IACrC,IAAIoC,UAAU,GAAGhC,eAAe,CAACH,QAAQ,EAAEkC,QAAQ,CAAC;IACpD,IAAIE,YAAY,GAAG,CAACJ,SAAS,CAACP,CAAC,EAAEO,SAAS,CAACN,CAAC,CAAC;IAC7CU,YAAY,CAACF,QAAQ,CAAC,IAAID,KAAK,CAACC,QAAQ,CAAC;IACzCE,YAAY,CAACF,QAAQ,CAAC,GAAGL,IAAI,CAACQ,GAAG,CAACF,UAAU,CAAC,CAAC,CAAC,EAAEC,YAAY,CAACF,QAAQ,CAAC,CAAC;IACxEE,YAAY,CAACF,QAAQ,CAAC,GAAGL,IAAI,CAACS,GAAG,CAACH,UAAU,CAAC,CAAC,CAAC,EAAEC,YAAY,CAACF,QAAQ,CAAC,CAAC;IACxE,IAAIhC,WAAW,GAAGC,eAAe,CAACH,QAAQ,EAAE,CAAC,GAAGkC,QAAQ,CAAC;IACzD,IAAIK,gBAAgB,GAAG,CAACrC,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5D,IAAIsC,WAAW,GAAG,CAACD,gBAAgB,EAAEA,gBAAgB,CAAC;IACtDC,WAAW,CAACN,QAAQ,CAAC,GAAGE,YAAY,CAACF,QAAQ,CAAC;IAC9C,OAAO;MACLT,CAAC,EAAEW,YAAY,CAAC,CAAC,CAAC;MAClBV,CAAC,EAAEU,YAAY,CAAC,CAAC,CAAC;MAClBT,QAAQ,EAAEK,SAAS,CAACL,QAAQ;MAC5Ba,WAAW,EAAEA,WAAW;MACxBC,aAAa,EAAE;QACbC,aAAa,EAAE;MACjB;IACF,CAAC;EACH,CAAC;EACD,OAAOtD,iBAAiB;AAC1B,CAAC,CAACL,eAAe,CAAC;AAClB,IAAI6B,mBAAmB,GAAG;EACxB+B,IAAI,EAAE,SAAAA,CAAU5C,IAAI,EAAEM,UAAU,EAAEH,WAAW,EAAE;IAC7C,IAAI0C,WAAW,GAAG5D,UAAU,CAAC6D,aAAa,CAAC,CAACxC,UAAU,EAAEH,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAACG,UAAU,EAAEH,WAAW,CAAC,CAAC,CAAC,CAAC,EAAEE,gBAAgB,CAACL,IAAI,CAAC,CAAC;IAC9H,OAAO;MACLgB,IAAI,EAAE,MAAM;MACZ+B,gBAAgB,EAAE,IAAI;MACtBC,KAAK,EAAEH;IACT,CAAC;EACH,CAAC;EACDI,MAAM,EAAE,SAAAA,CAAUjD,IAAI,EAAEM,UAAU,EAAEH,WAAW,EAAE;IAC/C,IAAI+C,SAAS,GAAGlD,IAAI,CAACmD,YAAY,CAAC,CAAC;IACnC,IAAIC,IAAI,GAAGjD,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;IAC1C,OAAO;MACLa,IAAI,EAAE,MAAM;MACZgC,KAAK,EAAE/D,UAAU,CAACoE,aAAa,CAAC,CAAC/C,UAAU,GAAG4C,SAAS,GAAG,CAAC,EAAE/C,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC+C,SAAS,EAAEE,IAAI,CAAC,EAAE/C,gBAAgB,CAACL,IAAI,CAAC;IACzH,CAAC;EACH;AACF,CAAC;AACD,SAASK,gBAAgBA,CAACL,IAAI,EAAE;EAC9B,OAAOA,IAAI,CAACsD,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AACpC;AACA,SAASlD,eAAeA,CAACH,QAAQ,EAAEkC,QAAQ,EAAE;EAC3C,IAAIoB,IAAI,GAAGtD,QAAQ,CAACuD,OAAO,CAAC,CAAC;EAC7B,OAAO,CAACD,IAAI,CAACpE,EAAE,CAACgD,QAAQ,CAAC,CAAC,EAAEoB,IAAI,CAACpE,EAAE,CAACgD,QAAQ,CAAC,CAAC,GAAGoB,IAAI,CAACnE,EAAE,CAAC+C,QAAQ,CAAC,CAAC,CAAC;AACtE;AACA,eAAe9C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}