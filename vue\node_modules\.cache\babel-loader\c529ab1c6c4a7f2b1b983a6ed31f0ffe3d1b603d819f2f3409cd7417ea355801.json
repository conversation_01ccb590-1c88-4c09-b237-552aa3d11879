{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: \"FrontLayout\",\n  data() {\n    return {\n      top: '',\n      notice: [],\n      user: JSON.parse(localStorage.getItem(\"xm-user\") || '{}'),\n      name: null\n    };\n  },\n  mounted() {\n    this.loadNotice();\n  },\n  methods: {\n    loadNotice() {\n      this.$request.get('/notice/selectAll').then(res => {\n        this.notice = res.data;\n        let i = 0;\n        if (this.notice && this.notice.length) {\n          this.top = this.notice[0].content;\n          setInterval(() => {\n            this.top = this.notice[i].content;\n            i++;\n            if (i === this.notice.length) {\n              i = 0;\n            }\n          }, 2500);\n        }\n      });\n    },\n    updateUser() {\n      this.user = JSON.parse(localStorage.getItem('xm-user') || '{}'); // 重新获取下用户的最新信息\n    },\n    navTo(url) {\n      location.href = url;\n    },\n    // 退出登录\n    logout() {\n      localStorage.removeItem(\"xm-user\");\n      this.$router.push(\"/login\");\n    },\n    search() {\n      let name = this.name ? this.name : '';\n      location.href = '/front/search?name=' + name;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "top", "notice", "user", "JSON", "parse", "localStorage", "getItem", "mounted", "loadNotice", "methods", "$request", "get", "then", "res", "i", "length", "content", "setInterval", "updateUser", "navTo", "url", "location", "href", "logout", "removeItem", "$router", "push", "search"], "sources": ["src/views/Front.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!--头部-->\r\n    <div class=\"front-header\">\r\n      <div class=\"front-header-left\" @click=\"navTo('/front/home')\">\r\n        <img src=\"@/assets/imgs/logo.png\" alt=\"\">\r\n        <div class=\"title\">点餐系统前台</div>\r\n      </div>\r\n      <div class=\"front-header-center\" style=\"text-align: right\">\r\n\r\n        <div class=\"front-header-nav\">\r\n          <el-menu :default-active=\"$route.path\" mode=\"horizontal\" router>\r\n            <el-menu-item index=\"/front/home\">系统首页</el-menu-item>\r\n            <el-menu-item index=\"/front/dingdan\">订单信息</el-menu-item>\r\n            <el-menu-item index=\"/front/dingdan2\">购物车</el-menu-item>\r\n              <el-menu-item index=\"/front/blogs\">系统讨论</el-menu-item>\r\n              <el-menu-item index=\"/front/myBlogs\">我要发帖</el-menu-item>\r\n              <el-menu-item index=\"/front/complaint\">填写点餐投诉</el-menu-item>\r\n              <el-menu-item index=\"/front/response\">点餐投诉反馈</el-menu-item>\r\n              <el-menu-item index=\"/front/leavemess\">咨询留言</el-menu-item>\r\n              <el-menu-item index=\"/front/replyLeavemess\">留言回复</el-menu-item>\r\n              <el-menu-item index=\"/front/freemovies\">点餐推荐</el-menu-item>\r\n              <el-menu-item index=\"/front/notice\">公告信息</el-menu-item>\r\n              <el-menu-item index=\"/front/person\">个人中心</el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n      <div class=\"front-header-right\">\r\n        <div v-if=\"!user.username\">\r\n          <el-button @click=\"$router.push('/login')\">登录</el-button>\r\n          <el-button @click=\"$router.push('/register')\">注册</el-button>\r\n        </div>\r\n        <div v-else>\r\n          <el-dropdown>\r\n            <div class=\"front-header-dropdown\">\r\n              <img @click=\"navTo('/front/person')\" :src=\"user.avatar\" alt=\"\">\r\n              <div style=\"margin-left: 10px\">\r\n                <span>{{ user.name }}</span><i class=\"el-icon-arrow-down\" style=\"margin-left: 5px\"></i>\r\n              </div>\r\n            </div>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n\r\n              <el-dropdown-item>\r\n                <div style=\"text-decoration: none\" @click=\"logout\">退出</div>\r\n              </el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!--主体-->\r\n    <div class=\"main-body\">\r\n      <router-view ref=\"child\" @update:user=\"updateUser\" />\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\n\r\nexport default {\r\n  name: \"FrontLayout\",\r\n\r\n  data () {\r\n    return {\r\n      top: '',\r\n      notice: [],\r\n      user: JSON.parse(localStorage.getItem(\"xm-user\") || '{}'),\r\n      name: null\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.loadNotice()\r\n  },\r\n\r\n  methods: {\r\n    loadNotice() {\r\n      this.$request.get('/notice/selectAll').then(res => {\r\n        this.notice = res.data\r\n        let i = 0\r\n        if (this.notice && this.notice.length) {\r\n          this.top = this.notice[0].content\r\n          setInterval(() => {\r\n            this.top = this.notice[i].content\r\n            i++\r\n            if (i === this.notice.length) {\r\n              i = 0\r\n            }\r\n          }, 2500)\r\n        }\r\n      })\r\n    },\r\n    updateUser() {\r\n      this.user = JSON.parse(localStorage.getItem('xm-user') || '{}')   // 重新获取下用户的最新信息\r\n    },\r\n    navTo(url) {\r\n      location.href = url\r\n    },\r\n    // 退出登录\r\n    logout() {\r\n      localStorage.removeItem(\"xm-user\");\r\n      this.$router.push(\"/login\");\r\n    },\r\n    search() {\r\n      let name = this.name ? this.name : ''\r\n      location.href = '/front/search?name=' + name\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n  @import \"@/assets/css/front.css\";\r\n</style>"], "mappings": ";AA4DA;EACAA,IAAA;EAEAC,KAAA;IACA;MACAC,GAAA;MACAC,MAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAR,IAAA;IACA;EACA;EAEAS,QAAA;IACA,KAAAC,UAAA;EACA;EAEAC,OAAA;IACAD,WAAA;MACA,KAAAE,QAAA,CAAAC,GAAA,sBAAAC,IAAA,CAAAC,GAAA;QACA,KAAAZ,MAAA,GAAAY,GAAA,CAAAd,IAAA;QACA,IAAAe,CAAA;QACA,SAAAb,MAAA,SAAAA,MAAA,CAAAc,MAAA;UACA,KAAAf,GAAA,QAAAC,MAAA,IAAAe,OAAA;UACAC,WAAA;YACA,KAAAjB,GAAA,QAAAC,MAAA,CAAAa,CAAA,EAAAE,OAAA;YACAF,CAAA;YACA,IAAAA,CAAA,UAAAb,MAAA,CAAAc,MAAA;cACAD,CAAA;YACA;UACA;QACA;MACA;IACA;IACAI,WAAA;MACA,KAAAhB,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA;IACAa,MAAAC,GAAA;MACAC,QAAA,CAAAC,IAAA,GAAAF,GAAA;IACA;IACA;IACAG,OAAA;MACAlB,YAAA,CAAAmB,UAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAC,OAAA;MACA,IAAA7B,IAAA,QAAAA,IAAA,QAAAA,IAAA;MACAuB,QAAA,CAAAC,IAAA,2BAAAxB,IAAA;IACA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}