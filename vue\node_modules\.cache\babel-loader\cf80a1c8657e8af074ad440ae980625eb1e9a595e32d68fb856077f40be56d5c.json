{"ast": null, "code": "export default {\n  name: 'SmsCode',\n  props: {\n    phoneNumber: {\n      type: String,\n      required: true\n    },\n    placeholder: {\n      type: String,\n      default: '请输入验证码'\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    value: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      verifyCode: this.value,\n      sending: false,\n      countdown: 0,\n      timer: null\n    };\n  },\n  computed: {\n    canSend() {\n      return this.phoneNumber && this.phoneNumber.length === 11 && this.countdown === 0;\n    },\n    buttonText() {\n      if (this.sending) {\n        return '发送中...';\n      }\n      if (this.countdown > 0) {\n        return `${this.countdown}秒后重试`;\n      }\n      return '发送验证码';\n    }\n  },\n  watch: {\n    value(newVal) {\n      this.verifyCode = newVal;\n    },\n    phoneNumber() {\n      // 手机号变化时重置状态\n      this.resetCountdown();\n    }\n  },\n  beforeDestroy() {\n    this.clearTimer();\n  },\n  methods: {\n    handleInput(value) {\n      // 只允许输入数字\n      const numericValue = value.replace(/[^0-9]/g, '');\n      this.verifyCode = numericValue;\n      this.$emit('input', numericValue);\n    },\n    async sendCode() {\n      if (!this.canSend) return;\n      if (!this.phoneNumber) {\n        this.$message.warning('请先输入手机号');\n        return;\n      }\n\n      // 简单的手机号格式验证\n      if (!/^1[3-9]\\d{9}$/.test(this.phoneNumber)) {\n        this.$message.warning('请输入正确的手机号格式');\n        return;\n      }\n      this.sending = true;\n      try {\n        const response = await this.$request.post('/sms/sendCode', null, {\n          params: {\n            phoneNumber: this.phoneNumber\n          }\n        });\n        if (response.code === '200') {\n          this.$message.success('验证码发送成功');\n          this.startCountdown(60); // 60秒倒计时\n          this.$emit('send-success', response.data);\n        } else {\n          this.$message.error(response.msg || '验证码发送失败');\n          this.$emit('send-error', response);\n        }\n      } catch (error) {\n        console.error('发送验证码失败:', error);\n        this.$message.error('验证码发送失败，请稍后重试');\n        this.$emit('send-error', error);\n      } finally {\n        this.sending = false;\n      }\n    },\n    startCountdown(seconds) {\n      this.countdown = seconds;\n      this.timer = setInterval(() => {\n        this.countdown--;\n        if (this.countdown <= 0) {\n          this.clearTimer();\n        }\n      }, 1000);\n    },\n    clearTimer() {\n      if (this.timer) {\n        clearInterval(this.timer);\n        this.timer = null;\n      }\n      this.countdown = 0;\n    },\n    resetCountdown() {\n      this.clearTimer();\n    },\n    // 验证验证码\n    async verifyCode() {\n      if (!this.verifyCode || !this.phoneNumber) {\n        return false;\n      }\n      try {\n        const response = await this.$request.post('/sms/verifyCode', null, {\n          params: {\n            phoneNumber: this.phoneNumber,\n            code: this.verifyCode\n          }\n        });\n        return response.code === '200';\n      } catch (error) {\n        console.error('验证码验证失败:', error);\n        return false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "phoneNumber", "type", "String", "required", "placeholder", "default", "disabled", "Boolean", "value", "data", "verifyCode", "sending", "countdown", "timer", "computed", "canSend", "length", "buttonText", "watch", "newVal", "resetCountdown", "<PERSON><PERSON><PERSON><PERSON>", "clearTimer", "methods", "handleInput", "numericValue", "replace", "$emit", "sendCode", "$message", "warning", "test", "response", "$request", "post", "params", "code", "success", "startCountdown", "error", "msg", "console", "seconds", "setInterval", "clearInterval"], "sources": ["src/components/SmsCode.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sms-code-container\">\r\n    <el-input\r\n      v-model=\"verifyCode\"\r\n      :placeholder=\"placeholder\"\r\n      :disabled=\"disabled\"\r\n      maxlength=\"6\"\r\n      @input=\"handleInput\"\r\n      class=\"code-input\">\r\n      <el-button\r\n        slot=\"append\"\r\n        :disabled=\"!canSend || sending\"\r\n        :loading=\"sending\"\r\n        @click=\"sendCode\"\r\n        class=\"send-btn\">\r\n        {{ buttonText }}\r\n      </el-button>\r\n    </el-input>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SmsCode',\r\n  props: {\r\n    phoneNumber: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '请输入验证码'\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      verifyCode: this.value,\r\n      sending: false,\r\n      countdown: 0,\r\n      timer: null\r\n    }\r\n  },\r\n  computed: {\r\n    canSend() {\r\n      return this.phoneNumber && this.phoneNumber.length === 11 && this.countdown === 0\r\n    },\r\n    buttonText() {\r\n      if (this.sending) {\r\n        return '发送中...'\r\n      }\r\n      if (this.countdown > 0) {\r\n        return `${this.countdown}秒后重试`\r\n      }\r\n      return '发送验证码'\r\n    }\r\n  },\r\n  watch: {\r\n    value(newVal) {\r\n      this.verifyCode = newVal\r\n    },\r\n    phoneNumber() {\r\n      // 手机号变化时重置状态\r\n      this.resetCountdown()\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    this.clearTimer()\r\n  },\r\n  methods: {\r\n    handleInput(value) {\r\n      // 只允许输入数字\r\n      const numericValue = value.replace(/[^0-9]/g, '')\r\n      this.verifyCode = numericValue\r\n      this.$emit('input', numericValue)\r\n    },\r\n    \r\n    async sendCode() {\r\n      if (!this.canSend) return\r\n      \r\n      if (!this.phoneNumber) {\r\n        this.$message.warning('请先输入手机号')\r\n        return\r\n      }\r\n      \r\n      // 简单的手机号格式验证\r\n      if (!/^1[3-9]\\d{9}$/.test(this.phoneNumber)) {\r\n        this.$message.warning('请输入正确的手机号格式')\r\n        return\r\n      }\r\n      \r\n      this.sending = true\r\n      \r\n      try {\r\n        const response = await this.$request.post('/sms/sendCode', null, {\r\n          params: {\r\n            phoneNumber: this.phoneNumber\r\n          }\r\n        })\r\n        \r\n        if (response.code === '200') {\r\n          this.$message.success('验证码发送成功')\r\n          this.startCountdown(60) // 60秒倒计时\r\n          this.$emit('send-success', response.data)\r\n        } else {\r\n          this.$message.error(response.msg || '验证码发送失败')\r\n          this.$emit('send-error', response)\r\n        }\r\n      } catch (error) {\r\n        console.error('发送验证码失败:', error)\r\n        this.$message.error('验证码发送失败，请稍后重试')\r\n        this.$emit('send-error', error)\r\n      } finally {\r\n        this.sending = false\r\n      }\r\n    },\r\n    \r\n    startCountdown(seconds) {\r\n      this.countdown = seconds\r\n      this.timer = setInterval(() => {\r\n        this.countdown--\r\n        if (this.countdown <= 0) {\r\n          this.clearTimer()\r\n        }\r\n      }, 1000)\r\n    },\r\n    \r\n    clearTimer() {\r\n      if (this.timer) {\r\n        clearInterval(this.timer)\r\n        this.timer = null\r\n      }\r\n      this.countdown = 0\r\n    },\r\n    \r\n    resetCountdown() {\r\n      this.clearTimer()\r\n    },\r\n    \r\n    // 验证验证码\r\n    async verifyCode() {\r\n      if (!this.verifyCode || !this.phoneNumber) {\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        const response = await this.$request.post('/sms/verifyCode', null, {\r\n          params: {\r\n            phoneNumber: this.phoneNumber,\r\n            code: this.verifyCode\r\n          }\r\n        })\r\n        \r\n        return response.code === '200'\r\n      } catch (error) {\r\n        console.error('验证码验证失败:', error)\r\n        return false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.sms-code-container {\r\n  width: 100%;\r\n}\r\n\r\n.code-input {\r\n  width: 100%;\r\n}\r\n\r\n.code-input >>> .el-input__inner {\r\n  border-radius: 4px 0 0 4px;\r\n}\r\n\r\n.send-btn {\r\n  width: 120px;\r\n  border-radius: 0 4px 4px 0;\r\n  border-left: none;\r\n  background-color: #409eff;\r\n  color: white;\r\n  font-size: 14px;\r\n}\r\n\r\n.send-btn:hover:not(:disabled) {\r\n  background-color: #66b1ff;\r\n}\r\n\r\n.send-btn:disabled {\r\n  background-color: #c0c4cc;\r\n  border-color: #c0c4cc;\r\n  color: #ffffff;\r\n  cursor: not-allowed;\r\n}\r\n</style> "], "mappings": "AAsBA;EACAA,IAAA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,WAAA;MACAH,IAAA,EAAAC,MAAA;MACAG,OAAA;IACA;IACAC,QAAA;MACAL,IAAA,EAAAM,OAAA;MACAF,OAAA;IACA;IACAG,KAAA;MACAP,IAAA,EAAAC,MAAA;MACAG,OAAA;IACA;EACA;EACAI,KAAA;IACA;MACAC,UAAA,OAAAF,KAAA;MACAG,OAAA;MACAC,SAAA;MACAC,KAAA;IACA;EACA;EACAC,QAAA;IACAC,QAAA;MACA,YAAAf,WAAA,SAAAA,WAAA,CAAAgB,MAAA,gBAAAJ,SAAA;IACA;IACAK,WAAA;MACA,SAAAN,OAAA;QACA;MACA;MACA,SAAAC,SAAA;QACA,eAAAA,SAAA;MACA;MACA;IACA;EACA;EACAM,KAAA;IACAV,MAAAW,MAAA;MACA,KAAAT,UAAA,GAAAS,MAAA;IACA;IACAnB,YAAA;MACA;MACA,KAAAoB,cAAA;IACA;EACA;EACAC,cAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACAC,YAAAhB,KAAA;MACA;MACA,MAAAiB,YAAA,GAAAjB,KAAA,CAAAkB,OAAA;MACA,KAAAhB,UAAA,GAAAe,YAAA;MACA,KAAAE,KAAA,UAAAF,YAAA;IACA;IAEA,MAAAG,SAAA;MACA,UAAAb,OAAA;MAEA,UAAAf,WAAA;QACA,KAAA6B,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,qBAAAC,IAAA,MAAA/B,WAAA;QACA,KAAA6B,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAnB,OAAA;MAEA;QACA,MAAAqB,QAAA,cAAAC,QAAA,CAAAC,IAAA;UACAC,MAAA;YACAnC,WAAA,OAAAA;UACA;QACA;QAEA,IAAAgC,QAAA,CAAAI,IAAA;UACA,KAAAP,QAAA,CAAAQ,OAAA;UACA,KAAAC,cAAA;UACA,KAAAX,KAAA,iBAAAK,QAAA,CAAAvB,IAAA;QACA;UACA,KAAAoB,QAAA,CAAAU,KAAA,CAAAP,QAAA,CAAAQ,GAAA;UACA,KAAAb,KAAA,eAAAK,QAAA;QACA;MACA,SAAAO,KAAA;QACAE,OAAA,CAAAF,KAAA,aAAAA,KAAA;QACA,KAAAV,QAAA,CAAAU,KAAA;QACA,KAAAZ,KAAA,eAAAY,KAAA;MACA;QACA,KAAA5B,OAAA;MACA;IACA;IAEA2B,eAAAI,OAAA;MACA,KAAA9B,SAAA,GAAA8B,OAAA;MACA,KAAA7B,KAAA,GAAA8B,WAAA;QACA,KAAA/B,SAAA;QACA,SAAAA,SAAA;UACA,KAAAU,UAAA;QACA;MACA;IACA;IAEAA,WAAA;MACA,SAAAT,KAAA;QACA+B,aAAA,MAAA/B,KAAA;QACA,KAAAA,KAAA;MACA;MACA,KAAAD,SAAA;IACA;IAEAQ,eAAA;MACA,KAAAE,UAAA;IACA;IAEA;IACA,MAAAZ,WAAA;MACA,UAAAA,UAAA,UAAAV,WAAA;QACA;MACA;MAEA;QACA,MAAAgC,QAAA,cAAAC,QAAA,CAAAC,IAAA;UACAC,MAAA;YACAnC,WAAA,OAAAA,WAAA;YACAoC,IAAA,OAAA1B;UACA;QACA;QAEA,OAAAsB,QAAA,CAAAI,IAAA;MACA,SAAAG,KAAA;QACAE,OAAA,CAAAF,KAAA,aAAAA,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}