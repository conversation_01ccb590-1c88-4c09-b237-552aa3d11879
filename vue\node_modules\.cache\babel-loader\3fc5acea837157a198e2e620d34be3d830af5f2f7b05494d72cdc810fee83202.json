{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nvar render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", [_c(\"div\", {\n    staticClass: \"front-header\"\n  }, [_c(\"div\", {\n    staticClass: \"front-header-left\",\n    on: {\n      click: function ($event) {\n        return _vm.navTo(\"/front/home\");\n      }\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/imgs/logo.png\"),\n      alt: \"\"\n    }\n  }), _c(\"div\", {\n    staticClass: \"title\"\n  }, [_vm._v(\"点餐系统前台\")])]), _c(\"div\", {\n    staticClass: \"front-header-center\",\n    staticStyle: {\n      \"text-align\": \"right\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"front-header-nav\"\n  }, [_c(\"el-menu\", {\n    attrs: {\n      \"default-active\": _vm.$route.path,\n      mode: \"horizontal\",\n      router: \"\"\n    }\n  }, [_c(\"el-menu-item\", {\n    attrs: {\n      index: \"/front/home\"\n    }\n  }, [_vm._v(\"系统首页\")]), _c(\"el-menu-item\", {\n    attrs: {\n      index: \"/front/dingdan\"\n    }\n  }, [_vm._v(\"订单信息\")]), _c(\"el-menu-item\", {\n    attrs: {\n      index: \"/front/dingdan2\"\n    }\n  }, [_vm._v(\"购物车\")]), _c(\"el-menu-item\", {\n    attrs: {\n      index: \"/front/blogs\"\n    }\n  }, [_vm._v(\"系统讨论\")]), _c(\"el-menu-item\", {\n    attrs: {\n      index: \"/front/freemovies\"\n    }\n  }, [_vm._v(\"点餐推荐\")]), _c(\"el-menu-item\", {\n    attrs: {\n      index: \"/front/notice\"\n    }\n  }, [_vm._v(\"公告信息\")])], 1)], 1)]), _c(\"div\", {\n    staticClass: \"front-header-right\"\n  }, [!_vm.user.username ? _c(\"div\", [_c(\"el-button\", {\n    on: {\n      click: function ($event) {\n        return _vm.$router.push(\"/login\");\n      }\n    }\n  }, [_vm._v(\"登录\")]), _c(\"el-button\", {\n    on: {\n      click: function ($event) {\n        return _vm.$router.push(\"/register\");\n      }\n    }\n  }, [_vm._v(\"注册\")])], 1) : _c(\"div\", [_c(\"el-dropdown\", [_c(\"div\", {\n    staticClass: \"front-header-dropdown\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: _vm.user.avatar,\n      alt: \"\"\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navTo(\"/front/person\");\n      }\n    }\n  }), _c(\"div\", {\n    staticStyle: {\n      \"margin-left\": \"10px\"\n    }\n  }, [_c(\"span\", [_vm._v(_vm._s(_vm.user.name))]), _c(\"i\", {\n    staticClass: \"el-icon-arrow-down\",\n    staticStyle: {\n      \"margin-left\": \"5px\"\n    }\n  })])]), _c(\"el-dropdown-menu\", {\n    attrs: {\n      slot: \"dropdown\"\n    },\n    slot: \"dropdown\"\n  }, [_c(\"el-dropdown-item\", [_c(\"div\", {\n    staticStyle: {\n      \"text-decoration\": \"none\"\n    },\n    on: {\n      click: function ($event) {\n        return _vm.navTo(\"/front/person\");\n      }\n    }\n  }, [_vm._v(\"个人中心\")])]), _c(\"el-dropdown-item\", [_c(\"div\", {\n    staticStyle: {\n      \"text-decoration\": \"none\"\n    },\n    on: {\n      click: _vm.logout\n    }\n  }, [_vm._v(\"退出登录\")])])], 1)], 1)], 1)])]), _c(\"div\", {\n    staticClass: \"main-body\"\n  }, [_c(\"router-view\", {\n    ref: \"child\",\n    on: {\n      \"update:user\": _vm.updateUser\n    }\n  })], 1)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "on", "click", "$event", "navTo", "attrs", "src", "require", "alt", "_v", "staticStyle", "$route", "path", "mode", "router", "index", "user", "username", "$router", "push", "avatar", "_s", "name", "slot", "logout", "ref", "updateUser", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/src/views/Front.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", [\n    _c(\"div\", { staticClass: \"front-header\" }, [\n      _c(\n        \"div\",\n        {\n          staticClass: \"front-header-left\",\n          on: {\n            click: function ($event) {\n              return _vm.navTo(\"/front/home\")\n            },\n          },\n        },\n        [\n          _c(\"img\", {\n            attrs: { src: require(\"@/assets/imgs/logo.png\"), alt: \"\" },\n          }),\n          _c(\"div\", { staticClass: \"title\" }, [_vm._v(\"点餐系统前台\")]),\n        ]\n      ),\n      _c(\n        \"div\",\n        {\n          staticClass: \"front-header-center\",\n          staticStyle: { \"text-align\": \"right\" },\n        },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"front-header-nav\" },\n            [\n              _c(\n                \"el-menu\",\n                {\n                  attrs: {\n                    \"default-active\": _vm.$route.path,\n                    mode: \"horizontal\",\n                    router: \"\",\n                  },\n                },\n                [\n                  _c(\"el-menu-item\", { attrs: { index: \"/front/home\" } }, [\n                    _vm._v(\"系统首页\"),\n                  ]),\n                  _c(\"el-menu-item\", { attrs: { index: \"/front/dingdan\" } }, [\n                    _vm._v(\"订单信息\"),\n                  ]),\n                  _c(\"el-menu-item\", { attrs: { index: \"/front/dingdan2\" } }, [\n                    _vm._v(\"购物车\"),\n                  ]),\n                  _c(\"el-menu-item\", { attrs: { index: \"/front/blogs\" } }, [\n                    _vm._v(\"系统讨论\"),\n                  ]),\n                  _c(\n                    \"el-menu-item\",\n                    { attrs: { index: \"/front/freemovies\" } },\n                    [_vm._v(\"点餐推荐\")]\n                  ),\n                  _c(\"el-menu-item\", { attrs: { index: \"/front/notice\" } }, [\n                    _vm._v(\"公告信息\"),\n                  ]),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ]\n      ),\n      _c(\"div\", { staticClass: \"front-header-right\" }, [\n        !_vm.user.username\n          ? _c(\n              \"div\",\n              [\n                _c(\n                  \"el-button\",\n                  {\n                    on: {\n                      click: function ($event) {\n                        return _vm.$router.push(\"/login\")\n                      },\n                    },\n                  },\n                  [_vm._v(\"登录\")]\n                ),\n                _c(\n                  \"el-button\",\n                  {\n                    on: {\n                      click: function ($event) {\n                        return _vm.$router.push(\"/register\")\n                      },\n                    },\n                  },\n                  [_vm._v(\"注册\")]\n                ),\n              ],\n              1\n            )\n          : _c(\n              \"div\",\n              [\n                _c(\n                  \"el-dropdown\",\n                  [\n                    _c(\"div\", { staticClass: \"front-header-dropdown\" }, [\n                      _c(\"img\", {\n                        attrs: { src: _vm.user.avatar, alt: \"\" },\n                        on: {\n                          click: function ($event) {\n                            return _vm.navTo(\"/front/person\")\n                          },\n                        },\n                      }),\n                      _c(\"div\", { staticStyle: { \"margin-left\": \"10px\" } }, [\n                        _c(\"span\", [_vm._v(_vm._s(_vm.user.name))]),\n                        _c(\"i\", {\n                          staticClass: \"el-icon-arrow-down\",\n                          staticStyle: { \"margin-left\": \"5px\" },\n                        }),\n                      ]),\n                    ]),\n                    _c(\n                      \"el-dropdown-menu\",\n                      { attrs: { slot: \"dropdown\" }, slot: \"dropdown\" },\n                      [\n                        _c(\"el-dropdown-item\", [\n                          _c(\n                            \"div\",\n                            {\n                              staticStyle: { \"text-decoration\": \"none\" },\n                              on: {\n                                click: function ($event) {\n                                  return _vm.navTo(\"/front/person\")\n                                },\n                              },\n                            },\n                            [_vm._v(\"个人中心\")]\n                          ),\n                        ]),\n                        _c(\"el-dropdown-item\", [\n                          _c(\n                            \"div\",\n                            {\n                              staticStyle: { \"text-decoration\": \"none\" },\n                              on: { click: _vm.logout },\n                            },\n                            [_vm._v(\"退出登录\")]\n                          ),\n                        ]),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n              ],\n              1\n            ),\n      ]),\n    ]),\n    _c(\n      \"div\",\n      { staticClass: \"main-body\" },\n      [\n        _c(\"router-view\", {\n          ref: \"child\",\n          on: { \"update:user\": _vm.updateUser },\n        }),\n      ],\n      1\n    ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE,CACfA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,mBAAmB;IAChCC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAON,GAAG,CAACO,KAAK,CAAC,aAAa,CAAC;MACjC;IACF;EACF,CAAC,EACD,CACEN,EAAE,CAAC,KAAK,EAAE;IACRO,KAAK,EAAE;MAAEC,GAAG,EAAEC,OAAO,CAAC,wBAAwB,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC3D,CAAC,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CAACH,GAAG,CAACY,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAE3D,CAAC,EACDX,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,qBAAqB;IAClCU,WAAW,EAAE;MAAE,YAAY,EAAE;IAAQ;EACvC,CAAC,EACD,CACEZ,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAmB,CAAC,EACnC,CACEF,EAAE,CACA,SAAS,EACT;IACEO,KAAK,EAAE;MACL,gBAAgB,EAAER,GAAG,CAACc,MAAM,CAACC,IAAI;MACjCC,IAAI,EAAE,YAAY;MAClBC,MAAM,EAAE;IACV;EACF,CAAC,EACD,CACEhB,EAAE,CAAC,cAAc,EAAE;IAAEO,KAAK,EAAE;MAAEU,KAAK,EAAE;IAAc;EAAE,CAAC,EAAE,CACtDlB,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFX,EAAE,CAAC,cAAc,EAAE;IAAEO,KAAK,EAAE;MAAEU,KAAK,EAAE;IAAiB;EAAE,CAAC,EAAE,CACzDlB,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFX,EAAE,CAAC,cAAc,EAAE;IAAEO,KAAK,EAAE;MAAEU,KAAK,EAAE;IAAkB;EAAE,CAAC,EAAE,CAC1DlB,GAAG,CAACY,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,EACFX,EAAE,CAAC,cAAc,EAAE;IAAEO,KAAK,EAAE;MAAEU,KAAK,EAAE;IAAe;EAAE,CAAC,EAAE,CACvDlB,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFX,EAAE,CACA,cAAc,EACd;IAAEO,KAAK,EAAE;MAAEU,KAAK,EAAE;IAAoB;EAAE,CAAC,EACzC,CAAClB,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDX,EAAE,CAAC,cAAc,EAAE;IAAEO,KAAK,EAAE;MAAEU,KAAK,EAAE;IAAgB;EAAE,CAAC,EAAE,CACxDlB,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC,EACDX,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CAC/C,CAACH,GAAG,CAACmB,IAAI,CAACC,QAAQ,GACdnB,EAAE,CACA,KAAK,EACL,CACEA,EAAE,CACA,WAAW,EACX;IACEG,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAON,GAAG,CAACqB,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;MACnC;IACF;EACF,CAAC,EACD,CAACtB,GAAG,CAACY,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDX,EAAE,CACA,WAAW,EACX;IACEG,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAON,GAAG,CAACqB,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;MACtC;IACF;EACF,CAAC,EACD,CAACtB,GAAG,CAACY,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,GACDX,EAAE,CACA,KAAK,EACL,CACEA,EAAE,CACA,aAAa,EACb,CACEA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,EAAE,CAClDF,EAAE,CAAC,KAAK,EAAE;IACRO,KAAK,EAAE;MAAEC,GAAG,EAAET,GAAG,CAACmB,IAAI,CAACI,MAAM;MAAEZ,GAAG,EAAE;IAAG,CAAC;IACxCP,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAON,GAAG,CAACO,KAAK,CAAC,eAAe,CAAC;MACnC;IACF;EACF,CAAC,CAAC,EACFN,EAAE,CAAC,KAAK,EAAE;IAAEY,WAAW,EAAE;MAAE,aAAa,EAAE;IAAO;EAAE,CAAC,EAAE,CACpDZ,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACmB,IAAI,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC,EAC3CxB,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE,oBAAoB;IACjCU,WAAW,EAAE;MAAE,aAAa,EAAE;IAAM;EACtC,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFZ,EAAE,CACA,kBAAkB,EAClB;IAAEO,KAAK,EAAE;MAAEkB,IAAI,EAAE;IAAW,CAAC;IAAEA,IAAI,EAAE;EAAW,CAAC,EACjD,CACEzB,EAAE,CAAC,kBAAkB,EAAE,CACrBA,EAAE,CACA,KAAK,EACL;IACEY,WAAW,EAAE;MAAE,iBAAiB,EAAE;IAAO,CAAC;IAC1CT,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAON,GAAG,CAACO,KAAK,CAAC,eAAe,CAAC;MACnC;IACF;EACF,CAAC,EACD,CAACP,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,CAAC,EACFX,EAAE,CAAC,kBAAkB,EAAE,CACrBA,EAAE,CACA,KAAK,EACL;IACEY,WAAW,EAAE;MAAE,iBAAiB,EAAE;IAAO,CAAC;IAC1CT,EAAE,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAAC2B;IAAO;EAC1B,CAAC,EACD,CAAC3B,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACN,CAAC,CACH,CAAC,EACFX,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,aAAa,EAAE;IAChB2B,GAAG,EAAE,OAAO;IACZxB,EAAE,EAAE;MAAE,aAAa,EAAEJ,GAAG,CAAC6B;IAAW;EACtC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB/B,MAAM,CAACgC,aAAa,GAAG,IAAI;AAE3B,SAAShC,MAAM,EAAE+B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}