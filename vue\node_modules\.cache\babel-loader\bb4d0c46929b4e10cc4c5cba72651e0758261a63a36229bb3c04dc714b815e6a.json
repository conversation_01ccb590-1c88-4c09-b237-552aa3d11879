{"ast": null, "code": "import \"core-js/modules/esnext.iterator.map.js\";\nexport default {\n  name: \"<PERSON><PERSON><PERSON>\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前的页码\n      pageSize: 10,\n      // 每页显示的个数\n      total: 0,\n      massage: null,\n      // 修改为按留言内容查询\n      fromVisible: false,\n      form: {},\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      rules: {},\n      ids: []\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    handleAdd() {\n      // 新增数据\n      this.form = {}; // 新增数据的时候清空数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    handleEdit(row) {\n      // 编辑数据\n      this.form = JSON.parse(JSON.stringify(row)); // 给form对象赋值  注意要深拷贝数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    save() {\n      // 保存按钮触发的逻辑  它会触发新增或者更新\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          this.$request({\n            url: this.form.id ? '/pinglun/update' : '/pinglun/add',\n            method: this.form.id ? 'PUT' : 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              // 表示成功保存\n              this.$message.success('保存成功');\n              this.load(1);\n              this.fromVisible = false;\n            } else {\n              this.$message.error(res.msg); // 弹出错误的信息\n            }\n          });\n        }\n      });\n    },\n    del(id) {\n      // 单个删除\n      this.$confirm('您确定删除吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/pinglun/delete/' + id).then(res => {\n          if (res.code === '200') {\n            // 表示操作成功\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg); // 弹出错误的信息\n          }\n        });\n      }).catch(() => {});\n    },\n    handleSelectionChange(rows) {\n      // 当前选中的所有的行数据\n      this.ids = rows.map(v => v.id);\n    },\n    delBatch() {\n      // 批量删除\n      if (!this.ids.length) {\n        this.$message.warning('请选择数据');\n        return;\n      }\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/pinglun/delete/batch', {\n          data: this.ids\n        }).then(res => {\n          if (res.code === '200') {\n            // 表示操作成功\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg); // 弹出错误的信息\n          }\n        });\n      }).catch(() => {});\n    },\n    load(pageNum) {\n      // 分页查询\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/pinglun/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          massage: this.massage // 修改为按留言内容查询\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list;\n          this.total = res.data?.total;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    reset() {\n      this.massage = null; // 修改为重置留言内容查询条件\n      this.load(1);\n    },\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "massage", "fromVisible", "form", "user", "JSON", "parse", "localStorage", "getItem", "rules", "ids", "created", "load", "methods", "handleAdd", "handleEdit", "row", "stringify", "save", "$refs", "formRef", "validate", "valid", "$request", "url", "id", "method", "then", "res", "code", "$message", "success", "error", "msg", "del", "$confirm", "type", "response", "delete", "catch", "handleSelectionChange", "rows", "map", "v", "delBatch", "length", "warning", "get", "params", "list", "reset", "handleCurrentChange"], "sources": ["src/views/manager/Pinglun.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"search\">\r\n            <el-input placeholder=\"请输入留言内容查询\" style=\"width: 200px\" v-model=\"massage\"></el-input>\r\n            <el-button type=\"info\" plain style=\"margin-left: 10px\" @click=\"load(1)\">查询</el-button>\r\n            <el-button type=\"warning\" plain style=\"margin-left: 10px\" @click=\"reset\">重置</el-button>\r\n        </div>\r\n\r\n        <div class=\"operation\">\r\n            <el-button type=\"primary\" plain @click=\"handleAdd\">新增</el-button>\r\n            <el-button type=\"danger\" plain @click=\"delBatch\">批量删除</el-button>\r\n        </div>\r\n\r\n        <div class=\"table\">\r\n            <el-table :data=\"tableData\" strip @selection-change=\"handleSelectionChange\">\r\n                <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n                <el-table-column prop=\"id\" label=\"序号\" width=\"70\" align=\"center\" sortable></el-table-column>\r\n                <el-table-column prop=\"massage\" label=\"留言内容\"></el-table-column>\r\n                <el-table-column prop=\"yonghuid\" label=\"用户id\"></el-table-column>\r\n                <el-table-column prop=\"yonghuname\" label=\"用户名字\"></el-table-column>\r\n                <el-table-column prop=\"crearatime\" label=\"创建时间\"></el-table-column>\r\n                <el-table-column prop=\"bokeid\" label=\"博客id\"></el-table-column>\r\n                <el-table-column label=\"操作\" align=\"center\" width=\"180\">\r\n                    <template v-slot=\"scope\">\r\n                        <el-button size=\"mini\" type=\"primary\" plain @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n                        <el-button size=\"mini\" type=\"danger\" plain @click=\"del(scope.row.id)\">删除</el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n\r\n            <div class=\"pagination\">\r\n                <el-pagination\r\n                    background\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageNum\"\r\n                    :page-sizes=\"[5, 10, 20]\"\r\n                    :page-size=\"pageSize\"\r\n                    layout=\"total, prev, pager, next\"\r\n                    :total=\"total\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <el-dialog title=\"评论表\" :visible.sync=\"fromVisible\" width=\"40%\" :close-on-click-modal=\"false\" destroy-on-close>\r\n            <el-form :model=\"form\" label-width=\"100px\" style=\"padding-right: 50px\" :rules=\"rules\" ref=\"formRef\">\r\n                <el-form-item label=\"留言内容\" prop=\"massage\">\r\n                    <el-input v-model=\"form.massage\" placeholder=\"留言内容\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"用户id\" prop=\"yonghuid\">\r\n                    <el-input v-model=\"form.yonghuid\" placeholder=\"用户id\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"用户名字\" prop=\"yonghuname\">\r\n                    <el-input v-model=\"form.yonghuname\" placeholder=\"用户名字\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"创建时间\" prop=\"crearatime\">\r\n                    <el-input v-model=\"form.crearatime\" placeholder=\"创建时间\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"博客id\" prop=\"bokeid\">\r\n                    <el-input v-model=\"form.bokeid\" placeholder=\"博客id\"></el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"fromVisible = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Pinglun\",\r\n    data() {\r\n        return {\r\n            tableData: [],  // 所有的数据\r\n            pageNum: 1,   // 当前的页码\r\n            pageSize: 10,  // 每页显示的个数\r\n            total: 0,\r\n            massage: null,  // 修改为按留言内容查询\r\n            fromVisible: false,\r\n            form: {},\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n            rules: {},\r\n            ids: []\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)\r\n    },\r\n    methods: {\r\n        handleAdd() {   // 新增数据\r\n            this.form = {}  // 新增数据的时候清空数据\r\n            this.fromVisible = true   // 打开弹窗\r\n        },\r\n        handleEdit(row) {   // 编辑数据\r\n            this.form = JSON.parse(JSON.stringify(row))  // 给form对象赋值  注意要深拷贝数据\r\n            this.fromVisible = true   // 打开弹窗\r\n        },\r\n        save() {   // 保存按钮触发的逻辑  它会触发新增或者更新\r\n            this.$refs.formRef.validate((valid) => {\r\n                if (valid) {\r\n                    this.$request({\r\n                        url: this.form.id ? '/pinglun/update' : '/pinglun/add',\r\n                        method: this.form.id ? 'PUT' : 'POST',\r\n                        data: this.form\r\n                    }).then(res => {\r\n                        if (res.code === '200') {  // 表示成功保存\r\n                            this.$message.success('保存成功')\r\n                            this.load(1)\r\n                            this.fromVisible = false\r\n                        } else {\r\n                            this.$message.error(res.msg)  // 弹出错误的信息\r\n                        }\r\n                    })\r\n                }\r\n            })\r\n        },\r\n        del(id) {   // 单个删除\r\n            this.$confirm('您确定删除吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n                this.$request.delete('/pinglun/delete/' + id).then(res => {\r\n                    if (res.code === '200') {   // 表示操作成功\r\n                        this.$message.success('操作成功')\r\n                        this.load(1)\r\n                    } else {\r\n                        this.$message.error(res.msg)  // 弹出错误的信息\r\n                    }\r\n                })\r\n            }).catch(() => {\r\n            })\r\n        },\r\n        handleSelectionChange(rows) {   // 当前选中的所有的行数据\r\n            this.ids = rows.map(v => v.id)\r\n        },\r\n        delBatch() {   // 批量删除\r\n            if (!this.ids.length) {\r\n                this.$message.warning('请选择数据')\r\n                return\r\n            }\r\n            this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n                this.$request.delete('/pinglun/delete/batch', {data: this.ids}).then(res => {\r\n                    if (res.code === '200') {   // 表示操作成功\r\n                        this.$message.success('操作成功')\r\n                        this.load(1)\r\n                    } else {\r\n                        this.$message.error(res.msg)  // 弹出错误的信息\r\n                    }\r\n                })\r\n            }).catch(() => {\r\n            })\r\n        },\r\n        load(pageNum) {  // 分页查询\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/pinglun/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n                    massage: this.massage,  // 修改为按留言内容查询\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list\r\n                    this.total = res.data?.total\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            })\r\n        },\r\n        reset() {\r\n            this.massage = null  // 修改为重置留言内容查询条件\r\n            this.load(1)\r\n        },\r\n        handleCurrentChange(pageNum) {\r\n            this.load(pageNum)\r\n        },\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.search {\r\n    margin-bottom: 20px;\r\n}\r\n.operation {\r\n    margin-bottom: 20px;\r\n}\r\n.table {\r\n    margin-bottom: 20px;\r\n}\r\n.pagination {\r\n    margin-top: 20px;\r\n    text-align: center;\r\n}\r\n</style>"], "mappings": ";AAsEA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MACAC,OAAA;MAAA;MACAC,WAAA;MACAC,IAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,KAAA;MACAC,GAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAC,UAAA;MAAA;MACA,KAAAX,IAAA;MACA,KAAAD,WAAA;IACA;IACAa,WAAAC,GAAA;MAAA;MACA,KAAAb,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAY,SAAA,CAAAD,GAAA;MACA,KAAAd,WAAA;IACA;IACAgB,KAAA;MAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,QAAA;YACAC,GAAA,OAAArB,IAAA,CAAAsB,EAAA;YACAC,MAAA,OAAAvB,IAAA,CAAAsB,EAAA;YACA7B,IAAA,OAAAO;UACA,GAAAwB,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cAAA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA,KAAAnB,IAAA;cACA,KAAAV,WAAA;YACA;cACA,KAAA4B,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;YACA;UACA;QACA;MACA;IACA;IACAC,IAAAT,EAAA;MAAA;MACA,KAAAU,QAAA;QAAAC,IAAA;MAAA,GAAAT,IAAA,CAAAU,QAAA;QACA,KAAAd,QAAA,CAAAe,MAAA,sBAAAb,EAAA,EAAAE,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAnB,IAAA;UACA;YACA,KAAAkB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAM,KAAA,QACA;IACA;IACAC,sBAAAC,IAAA;MAAA;MACA,KAAA/B,GAAA,GAAA+B,IAAA,CAAAC,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAlB,EAAA;IACA;IACAmB,SAAA;MAAA;MACA,UAAAlC,GAAA,CAAAmC,MAAA;QACA,KAAAf,QAAA,CAAAgB,OAAA;QACA;MACA;MACA,KAAAX,QAAA;QAAAC,IAAA;MAAA,GAAAT,IAAA,CAAAU,QAAA;QACA,KAAAd,QAAA,CAAAe,MAAA;UAAA1C,IAAA,OAAAc;QAAA,GAAAiB,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAnB,IAAA;UACA;YACA,KAAAkB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAM,KAAA,QACA;IACA;IACA3B,KAAAd,OAAA;MAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAyB,QAAA,CAAAwB,GAAA;QACAC,MAAA;UACAlD,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAE,OAAA,OAAAA,OAAA;QACA;MACA,GAAA0B,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAhC,SAAA,GAAA+B,GAAA,CAAAhC,IAAA,EAAAqD,IAAA;UACA,KAAAjD,KAAA,GAAA4B,GAAA,CAAAhC,IAAA,EAAAI,KAAA;QACA;UACA,KAAA8B,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IACAiB,MAAA;MACA,KAAAjD,OAAA;MACA,KAAAW,IAAA;IACA;IACAuC,oBAAArD,OAAA;MACA,KAAAc,IAAA,CAAAd,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}