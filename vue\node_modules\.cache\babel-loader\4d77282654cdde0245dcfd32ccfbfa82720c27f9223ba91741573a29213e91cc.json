{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../Axis.js';\nvar Axis2D = /** @class */function (_super) {\n  __extends(Axis2D, _super);\n  function Axis2D(dim, scale, coordExtent, axisType, position) {\n    var _this = _super.call(this, dim, scale, coordExtent) || this;\n    /**\r\n     * Index of axis, can be used as key\r\n     * Injected outside.\r\n     */\n    _this.index = 0;\n    _this.type = axisType || 'value';\n    _this.position = position || 'bottom';\n    return _this;\n  }\n  Axis2D.prototype.isHorizontal = function () {\n    var position = this.position;\n    return position === 'top' || position === 'bottom';\n  };\n  /**\r\n   * Each item cooresponds to this.getExtent(), which\r\n   * means globalExtent[0] may greater than globalExtent[1],\r\n   * unless `asc` is input.\r\n   *\r\n   * @param {boolean} [asc]\r\n   * @return {Array.<number>}\r\n   */\n  Axis2D.prototype.getGlobalExtent = function (asc) {\n    var ret = this.getExtent();\n    ret[0] = this.toGlobalCoord(ret[0]);\n    ret[1] = this.toGlobalCoord(ret[1]);\n    asc && ret[0] > ret[1] && ret.reverse();\n    return ret;\n  };\n  Axis2D.prototype.pointToData = function (point, clamp) {\n    return this.coordToData(this.toLocalCoord(point[this.dim === 'x' ? 0 : 1]), clamp);\n  };\n  /**\r\n   * Set ordinalSortInfo\r\n   * @param info new OrdinalSortInfo\r\n   */\n  Axis2D.prototype.setCategorySortInfo = function (info) {\n    if (this.type !== 'category') {\n      return false;\n    }\n    this.model.option.categorySortInfo = info;\n    this.scale.setSortInfo(info);\n  };\n  return Axis2D;\n}(Axis);\nexport default Axis2D;", "map": {"version": 3, "names": ["__extends", "Axis", "Axis2D", "_super", "dim", "scale", "coordExtent", "axisType", "position", "_this", "call", "index", "type", "prototype", "isHorizontal", "getGlobalExtent", "asc", "ret", "getExtent", "toGlobalCoord", "reverse", "pointToData", "point", "clamp", "coordToData", "toLocalCoord", "setCategorySortInfo", "info", "model", "option", "categorySortInfo", "setSortInfo"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/coord/cartesian/Axis2D.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../Axis.js';\nvar Axis2D = /** @class */function (_super) {\n  __extends(Axis2D, _super);\n  function Axis2D(dim, scale, coordExtent, axisType, position) {\n    var _this = _super.call(this, dim, scale, coordExtent) || this;\n    /**\r\n     * Index of axis, can be used as key\r\n     * Injected outside.\r\n     */\n    _this.index = 0;\n    _this.type = axisType || 'value';\n    _this.position = position || 'bottom';\n    return _this;\n  }\n  Axis2D.prototype.isHorizontal = function () {\n    var position = this.position;\n    return position === 'top' || position === 'bottom';\n  };\n  /**\r\n   * Each item cooresponds to this.getExtent(), which\r\n   * means globalExtent[0] may greater than globalExtent[1],\r\n   * unless `asc` is input.\r\n   *\r\n   * @param {boolean} [asc]\r\n   * @return {Array.<number>}\r\n   */\n  Axis2D.prototype.getGlobalExtent = function (asc) {\n    var ret = this.getExtent();\n    ret[0] = this.toGlobalCoord(ret[0]);\n    ret[1] = this.toGlobalCoord(ret[1]);\n    asc && ret[0] > ret[1] && ret.reverse();\n    return ret;\n  };\n  Axis2D.prototype.pointToData = function (point, clamp) {\n    return this.coordToData(this.toLocalCoord(point[this.dim === 'x' ? 0 : 1]), clamp);\n  };\n  /**\r\n   * Set ordinalSortInfo\r\n   * @param info new OrdinalSortInfo\r\n   */\n  Axis2D.prototype.setCategorySortInfo = function (info) {\n    if (this.type !== 'category') {\n      return false;\n    }\n    this.model.option.categorySortInfo = info;\n    this.scale.setSortInfo(info);\n  };\n  return Axis2D;\n}(Axis);\nexport default Axis2D;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,IAAIC,MAAM,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC1CH,SAAS,CAACE,MAAM,EAAEC,MAAM,CAAC;EACzB,SAASD,MAAMA,CAACE,GAAG,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IAC3D,IAAIC,KAAK,GAAGN,MAAM,CAACO,IAAI,CAAC,IAAI,EAAEN,GAAG,EAAEC,KAAK,EAAEC,WAAW,CAAC,IAAI,IAAI;IAC9D;AACJ;AACA;AACA;IACIG,KAAK,CAACE,KAAK,GAAG,CAAC;IACfF,KAAK,CAACG,IAAI,GAAGL,QAAQ,IAAI,OAAO;IAChCE,KAAK,CAACD,QAAQ,GAAGA,QAAQ,IAAI,QAAQ;IACrC,OAAOC,KAAK;EACd;EACAP,MAAM,CAACW,SAAS,CAACC,YAAY,GAAG,YAAY;IAC1C,IAAIN,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,OAAOA,QAAQ,KAAK,KAAK,IAAIA,QAAQ,KAAK,QAAQ;EACpD,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEN,MAAM,CAACW,SAAS,CAACE,eAAe,GAAG,UAAUC,GAAG,EAAE;IAChD,IAAIC,GAAG,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC1BD,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAACE,aAAa,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;IACnCA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAACE,aAAa,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;IACnCD,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,IAAIA,GAAG,CAACG,OAAO,CAAC,CAAC;IACvC,OAAOH,GAAG;EACZ,CAAC;EACDf,MAAM,CAACW,SAAS,CAACQ,WAAW,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACrD,OAAO,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,YAAY,CAACH,KAAK,CAAC,IAAI,CAAClB,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEmB,KAAK,CAAC;EACpF,CAAC;EACD;AACF;AACA;AACA;EACErB,MAAM,CAACW,SAAS,CAACa,mBAAmB,GAAG,UAAUC,IAAI,EAAE;IACrD,IAAI,IAAI,CAACf,IAAI,KAAK,UAAU,EAAE;MAC5B,OAAO,KAAK;IACd;IACA,IAAI,CAACgB,KAAK,CAACC,MAAM,CAACC,gBAAgB,GAAGH,IAAI;IACzC,IAAI,CAACtB,KAAK,CAAC0B,WAAW,CAACJ,IAAI,CAAC;EAC9B,CAAC;EACD,OAAOzB,MAAM;AACf,CAAC,CAACD,IAAI,CAAC;AACP,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}