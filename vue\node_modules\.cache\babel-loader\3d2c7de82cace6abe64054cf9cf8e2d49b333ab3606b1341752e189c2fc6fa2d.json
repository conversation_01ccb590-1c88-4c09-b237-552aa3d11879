{"ast": null, "code": "export default {\n  name: \"GoodsList\",\n  data() {\n    return {\n      tableData: [],\n      // 商品数据\n      pageNum: 1,\n      // 当前页码\n      pageSize: 12,\n      // 每页12条\n      total: 0,\n      // 总数\n      name: null,\n      // 搜索关键词\n      detailVisible: false,\n      // 详情弹窗显示\n      currentGoods: null,\n      // 当前查看的商品\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}') // 当前登录用户\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    load(pageNum) {\n      // 加载商品数据\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/foods/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          name: this.name\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list;\n          this.total = res.data?.total;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    showDetail(item) {\n      // 显示商品详情\n      this.currentGoods = item;\n      this.detailVisible = true;\n    },\n    // 加入购物车\n    addToCart(goodsId) {\n      this.$request.post('/dingdan/add', {\n        yonghuname: this.user.name || '匿名用户',\n        yonghuid: this.user.id || 0,\n        shangpinid: goodsId.toString(),\n        status: '未付款',\n        totalprice: '已加入购物车',\n        totalpricec: 0,\n        // 购物车状态下价格为0\n        createtime: new Date().toISOString()\n      }).then(res => {\n        if (res.code === '200') {\n          this.$message.success('商品已加入购物车！');\n        } else {\n          this.$message.error(res.msg || '操作失败');\n        }\n      }).catch(() => {\n        this.$message.error('操作失败，请重试');\n      });\n    },\n    // 立即购买\n    handleBuy(goodsId, price) {\n      this.$request.post('/dingdan/add', {\n        yonghuname: this.user.name || '匿名用户',\n        yonghuid: this.user.id || 0,\n        shangpinid: goodsId.toString(),\n        status: '已付款',\n        // 立即购买直接设为已付款状态\n        totalprice: '',\n        // 购物车状态为空\n        totalpricec: price,\n        // 商品价格放入订单价格字段\n        createtime: new Date().toISOString()\n      }).then(res => {\n        if (res.code === '200') {\n          this.$message.success('订单创建成功！');\n          this.detailVisible = false;\n        } else {\n          this.$message.error(res.msg || '下单失败');\n        }\n      }).catch(() => {\n        this.$message.error('下单失败，请重试');\n      });\n    },\n    handleCurrentChange(pageNum) {\n      // 分页变化\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "detailVisible", "currentGoods", "user", "JSON", "parse", "localStorage", "getItem", "created", "load", "methods", "$request", "get", "params", "then", "res", "code", "list", "$message", "error", "msg", "showDetail", "item", "addToCart", "goodsId", "post", "yong<PERSON><PERSON>", "yo<PERSON><PERSON><PERSON>", "id", "shang<PERSON>id", "toString", "status", "totalprice", "totalpricec", "createtime", "Date", "toISOString", "success", "catch", "handleBuy", "price", "handleCurrentChange"], "sources": ["src/views/front/Foods.vue"], "sourcesContent": ["<template>\r\n    <div class=\"taobao-container\">\r\n        <!-- 搜索栏 -->\r\n        <div class=\"search-bar\">\r\n            <el-input\r\n                placeholder=\"请输入商品名称搜索\"\r\n                v-model=\"name\"\r\n                class=\"search-input\"\r\n                clearable\r\n            >\r\n                <el-button\r\n                    slot=\"append\"\r\n                    icon=\"el-icon-search\"\r\n                    @click=\"load(1)\"\r\n                    style=\"background-color: #FF0036; color: white;\"\r\n                ></el-button>\r\n            </el-input>\r\n        </div>\r\n\r\n        <!-- 商品展示区 -->\r\n        <div class=\"goods-container\">\r\n            <div\r\n                class=\"goods-item\"\r\n                v-for=\"item in tableData\"\r\n                :key=\"item.id\"\r\n                @click=\"showDetail(item)\"\r\n            >\r\n                <div class=\"goods-img-container\">\r\n                    <el-image\r\n                        :src=\"item.img\"\r\n                        fit=\"cover\"\r\n                        class=\"goods-img\"\r\n                    ></el-image>\r\n                </div>\r\n                <div class=\"goods-info\">\r\n                    <div class=\"goods-title\">{{ item.name }}</div>\r\n                    <div class=\"goods-price\">¥{{ item.foodprice }}</div>\r\n\r\n                    <div class=\"goods-actions\">\r\n                        <el-button\r\n                            type=\"warning\"\r\n                            size=\"mini\"\r\n                            @click.stop=\"addToCart(item.id)\"\r\n                            class=\"cart-btn\"\r\n                        >\r\n                            <i class=\"el-icon-goods\"></i> 加购物车\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n            <el-pagination\r\n                background\r\n                @current-change=\"handleCurrentChange\"\r\n                :current-page=\"pageNum\"\r\n                :page-size=\"pageSize\"\r\n                layout=\"prev, pager, next\"\r\n                :total=\"total\"\r\n                :pager-count=\"5\"\r\n                prev-text=\"上一页\"\r\n                next-text=\"下一页\"\r\n            >\r\n            </el-pagination>\r\n        </div>\r\n\r\n        <!-- 商品详情弹窗 -->\r\n        <el-dialog\r\n            :visible.sync=\"detailVisible\"\r\n            width=\"60%\"\r\n            top=\"5vh\"\r\n            custom-class=\"goods-detail-dialog\"\r\n        >\r\n            <div class=\"detail-container\" v-if=\"currentGoods\">\r\n                <div class=\"detail-left\">\r\n                    <el-image\r\n                        :src=\"currentGoods.img\"\r\n                        fit=\"contain\"\r\n                        class=\"detail-img\"\r\n                    ></el-image>\r\n                </div>\r\n                <div class=\"detail-right\">\r\n                    <h2 class=\"detail-title\">{{ currentGoods.name }}</h2>\r\n                    <div class=\"detail-price\">\r\n                        <span class=\"price-symbol\">¥</span>\r\n                        <span class=\"price-number\">{{ currentGoods.foodprice }}</span>\r\n                    </div>\r\n                    <div class=\"detail-info\">\r\n                        <div class=\"info-item\">\r\n                            <span class=\"info-label\">商品类型:</span>\r\n                            <span class=\"info-value\">{{ currentGoods.foodtyope }}</span>\r\n                        </div>\r\n                        <div class=\"info-item\">\r\n                            <span class=\"info-label\">库存状态:</span>\r\n                            <span class=\"info-value\">{{ currentGoods.amount }}件</span>\r\n                        </div>\r\n                        <div class=\"info-item\">\r\n                            <span class=\"info-label\">上架状态:</span>\r\n                            <span class=\"info-value\">{{ currentGoods.fstatus }}</span>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"detail-desc\">\r\n                        <h3>商品描述</h3>\r\n                        <p>{{ currentGoods.fooddescription }}</p>\r\n                    </div>\r\n                    <div class=\"detail-actions\">\r\n                        <el-button\r\n                            type=\"warning\"\r\n                            class=\"cart-btn\"\r\n                            @click=\"addToCart(currentGoods.id)\"\r\n                        >\r\n                            <i class=\"el-icon-goods\"></i> 加入购物车\r\n                        </el-button>\r\n                        <el-button\r\n                            type=\"danger\"\r\n                            class=\"buy-btn\"\r\n                            @click=\"handleBuy(currentGoods.id, currentGoods.foodprice)\"\r\n                        >\r\n                            立即购买\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"GoodsList\",\r\n    data() {\r\n        return {\r\n            tableData: [],  // 商品数据\r\n            pageNum: 1,     // 当前页码\r\n            pageSize: 12,   // 每页12条\r\n            total: 0,       // 总数\r\n            name: null,     // 搜索关键词\r\n            detailVisible: false, // 详情弹窗显示\r\n            currentGoods: null,   // 当前查看的商品\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'), // 当前登录用户\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)\r\n    },\r\n    methods: {\r\n        load(pageNum) {  // 加载商品数据\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/foods/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n                    name: this.name,\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list\r\n                    this.total = res.data?.total\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            })\r\n        },\r\n        showDetail(item) {  // 显示商品详情\r\n            this.currentGoods = item\r\n            this.detailVisible = true\r\n        },\r\n        // 加入购物车\r\n        addToCart(goodsId) {\r\n            this.$request.post('/dingdan/add', {\r\n                yonghuname: this.user.name || '匿名用户',\r\n                yonghuid: this.user.id || 0,\r\n                shangpinid: goodsId.toString(),\r\n                status: '未付款',\r\n                totalprice: '已加入购物车',\r\n                totalpricec: 0, // 购物车状态下价格为0\r\n                createtime: new Date().toISOString()\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.$message.success('商品已加入购物车！')\r\n                } else {\r\n                    this.$message.error(res.msg || '操作失败')\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('操作失败，请重试')\r\n            })\r\n        },\r\n        // 立即购买\r\n        handleBuy(goodsId, price) {\r\n            this.$request.post('/dingdan/add', {\r\n                yonghuname: this.user.name || '匿名用户',\r\n                yonghuid: this.user.id || 0,\r\n                shangpinid: goodsId.toString(),\r\n                status: '已付款', // 立即购买直接设为已付款状态\r\n                totalprice: '', // 购物车状态为空\r\n                totalpricec: price, // 商品价格放入订单价格字段\r\n                createtime: new Date().toISOString()\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.$message.success('订单创建成功！')\r\n                    this.detailVisible = false\r\n                } else {\r\n                    this.$message.error(res.msg || '下单失败')\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('下单失败，请重试')\r\n            })\r\n        },\r\n        handleCurrentChange(pageNum) {  // 分页变化\r\n            this.load(pageNum)\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 样式部分保持不变 */\r\n.taobao-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 20px;\r\n}\r\n\r\n.search-bar {\r\n    margin-bottom: 30px;\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n\r\n.search-input {\r\n    width: 500px;\r\n}\r\n\r\n.search-input /deep/ .el-input-group__append {\r\n    background-color: #FF0036;\r\n    border-color: #FF0036;\r\n}\r\n\r\n.goods-container {\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    gap: 20px;\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.goods-item {\r\n    background: white;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    transition: transform 0.3s, box-shadow 0.3s;\r\n    cursor: pointer;\r\n}\r\n\r\n.goods-item:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.goods-img-container {\r\n    height: 200px;\r\n    overflow: hidden;\r\n}\r\n\r\n.goods-img {\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.goods-info {\r\n    padding: 15px;\r\n}\r\n\r\n.goods-title {\r\n    font-size: 14px;\r\n    color: #333;\r\n    margin-bottom: 10px;\r\n    height: 40px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n}\r\n\r\n.goods-price {\r\n    color: #FF0036;\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.goods-sales {\r\n    font-size: 12px;\r\n    color: #999;\r\n}\r\n\r\n.goods-actions {\r\n    margin-top: 10px;\r\n}\r\n\r\n.pagination-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 30px;\r\n}\r\n\r\n.detail-container {\r\n    display: flex;\r\n}\r\n\r\n.detail-left {\r\n    flex: 1;\r\n    padding: 20px;\r\n}\r\n\r\n.detail-right {\r\n    flex: 1;\r\n    padding: 20px;\r\n}\r\n\r\n.detail-img {\r\n    width: 100%;\r\n    height: 400px;\r\n}\r\n\r\n.detail-title {\r\n    font-size: 24px;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.detail-price {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.price-symbol {\r\n    color: #FF0036;\r\n    font-size: 20px;\r\n}\r\n\r\n.price-number {\r\n    color: #FF0036;\r\n    font-size: 28px;\r\n    font-weight: bold;\r\n}\r\n\r\n.detail-info {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.info-item {\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n}\r\n\r\n.info-label {\r\n    color: #999;\r\n    margin-right: 10px;\r\n}\r\n\r\n.info-value {\r\n    color: #333;\r\n}\r\n\r\n.detail-desc {\r\n    margin-top: 30px;\r\n    border-top: 1px solid #eee;\r\n    padding-top: 20px;\r\n}\r\n\r\n.detail-desc h3 {\r\n    font-size: 16px;\r\n    color: #333;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.detail-desc p {\r\n    font-size: 14px;\r\n    color: #666;\r\n    line-height: 1.6;\r\n}\r\n\r\n.detail-actions {\r\n    margin-top: 30px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n}\r\n\r\n.cart-btn {\r\n    background-color: #FF9500;\r\n    border-color: #FF9500;\r\n    color: white;\r\n    width: 48%;\r\n    height: 50px;\r\n    font-size: 18px;\r\n}\r\n\r\n.cart-btn:hover {\r\n    background-color: #FFAA33;\r\n    border-color: #FFAA33;\r\n}\r\n\r\n.buy-btn {\r\n    width: 48%;\r\n    height: 50px;\r\n    font-size: 18px;\r\n    background: linear-gradient(to right, #FF0036, #FF0036);\r\n    border: none;\r\n}\r\n\r\n/deep/ .goods-detail-dialog {\r\n    border-radius: 8px;\r\n}\r\n\r\n/deep/ .goods-detail-dialog .el-dialog__header {\r\n    border-bottom: 1px solid #eee;\r\n}\r\n\r\n/deep/ .goods-detail-dialog .el-dialog__body {\r\n    padding: 0;\r\n}\r\n</style>"], "mappings": "AAkIA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAAA;MACAL,IAAA;MAAA;MACAM,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,KAAAX,OAAA;MAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAa,QAAA,CAAAC,GAAA;QACAC,MAAA;UACAf,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAJ,IAAA,OAAAA;QACA;MACA,GAAAmB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAnB,SAAA,GAAAkB,GAAA,CAAAnB,IAAA,EAAAqB,IAAA;UACA,KAAAjB,KAAA,GAAAe,GAAA,CAAAnB,IAAA,EAAAI,KAAA;QACA;UACA,KAAAkB,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IACAC,WAAAC,IAAA;MAAA;MACA,KAAApB,YAAA,GAAAoB,IAAA;MACA,KAAArB,aAAA;IACA;IACA;IACAsB,UAAAC,OAAA;MACA,KAAAb,QAAA,CAAAc,IAAA;QACAC,UAAA,OAAAvB,IAAA,CAAAR,IAAA;QACAgC,QAAA,OAAAxB,IAAA,CAAAyB,EAAA;QACAC,UAAA,EAAAL,OAAA,CAAAM,QAAA;QACAC,MAAA;QACAC,UAAA;QACAC,WAAA;QAAA;QACAC,UAAA,MAAAC,IAAA,GAAAC,WAAA;MACA,GAAAtB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAE,QAAA,CAAAmB,OAAA;QACA;UACA,KAAAnB,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAkB,KAAA;QACA,KAAApB,QAAA,CAAAC,KAAA;MACA;IACA;IACA;IACAoB,UAAAf,OAAA,EAAAgB,KAAA;MACA,KAAA7B,QAAA,CAAAc,IAAA;QACAC,UAAA,OAAAvB,IAAA,CAAAR,IAAA;QACAgC,QAAA,OAAAxB,IAAA,CAAAyB,EAAA;QACAC,UAAA,EAAAL,OAAA,CAAAM,QAAA;QACAC,MAAA;QAAA;QACAC,UAAA;QAAA;QACAC,WAAA,EAAAO,KAAA;QAAA;QACAN,UAAA,MAAAC,IAAA,GAAAC,WAAA;MACA,GAAAtB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAE,QAAA,CAAAmB,OAAA;UACA,KAAApC,aAAA;QACA;UACA,KAAAiB,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAkB,KAAA;QACA,KAAApB,QAAA,CAAAC,KAAA;MACA;IACA;IACAsB,oBAAA3C,OAAA;MAAA;MACA,KAAAW,IAAA,CAAAX,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}