{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { devicePixelRatio } from '../config.js';\nimport * as util from '../core/util.js';\nimport Layer from './Layer.js';\nimport requestAnimationFrame from '../animation/requestAnimationFrame.js';\nimport env from '../core/env.js';\nimport { brush, brushSingle } from './graphic.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { getSize } from './helper.js';\nvar HOVER_LAYER_ZLEVEL = 1e5;\nvar CANVAS_ZLEVEL = 314159;\nvar EL_AFTER_INCREMENTAL_INC = 0.01;\nvar INCREMENTAL_INC = 0.001;\nfunction isLayerValid(layer) {\n  if (!layer) {\n    return false;\n  }\n  if (layer.__builtin__) {\n    return true;\n  }\n  if (typeof layer.resize !== 'function' || typeof layer.refresh !== 'function') {\n    return false;\n  }\n  return true;\n}\nfunction createRoot(width, height) {\n  var domRoot = document.createElement('div');\n  domRoot.style.cssText = ['position:relative', 'width:' + width + 'px', 'height:' + height + 'px', 'padding:0', 'margin:0', 'border-width:0'].join(';') + ';';\n  return domRoot;\n}\nvar CanvasPainter = function () {\n  function CanvasPainter(root, storage, opts, id) {\n    this.type = 'canvas';\n    this._zlevelList = [];\n    this._prevDisplayList = [];\n    this._layers = {};\n    this._layerConfig = {};\n    this._needsManuallyCompositing = false;\n    this.type = 'canvas';\n    var singleCanvas = !root.nodeName || root.nodeName.toUpperCase() === 'CANVAS';\n    this._opts = opts = util.extend({}, opts || {});\n    this.dpr = opts.devicePixelRatio || devicePixelRatio;\n    this._singleCanvas = singleCanvas;\n    this.root = root;\n    var rootStyle = root.style;\n    if (rootStyle) {\n      util.disableUserSelect(root);\n      root.innerHTML = '';\n    }\n    this.storage = storage;\n    var zlevelList = this._zlevelList;\n    this._prevDisplayList = [];\n    var layers = this._layers;\n    if (!singleCanvas) {\n      this._width = getSize(root, 0, opts);\n      this._height = getSize(root, 1, opts);\n      var domRoot = this._domRoot = createRoot(this._width, this._height);\n      root.appendChild(domRoot);\n    } else {\n      var rootCanvas = root;\n      var width = rootCanvas.width;\n      var height = rootCanvas.height;\n      if (opts.width != null) {\n        width = opts.width;\n      }\n      if (opts.height != null) {\n        height = opts.height;\n      }\n      this.dpr = opts.devicePixelRatio || 1;\n      rootCanvas.width = width * this.dpr;\n      rootCanvas.height = height * this.dpr;\n      this._width = width;\n      this._height = height;\n      var mainLayer = new Layer(rootCanvas, this, this.dpr);\n      mainLayer.__builtin__ = true;\n      mainLayer.initContext();\n      layers[CANVAS_ZLEVEL] = mainLayer;\n      mainLayer.zlevel = CANVAS_ZLEVEL;\n      zlevelList.push(CANVAS_ZLEVEL);\n      this._domRoot = root;\n    }\n  }\n  CanvasPainter.prototype.getType = function () {\n    return 'canvas';\n  };\n  CanvasPainter.prototype.isSingleCanvas = function () {\n    return this._singleCanvas;\n  };\n  CanvasPainter.prototype.getViewportRoot = function () {\n    return this._domRoot;\n  };\n  CanvasPainter.prototype.getViewportRootOffset = function () {\n    var viewportRoot = this.getViewportRoot();\n    if (viewportRoot) {\n      return {\n        offsetLeft: viewportRoot.offsetLeft || 0,\n        offsetTop: viewportRoot.offsetTop || 0\n      };\n    }\n  };\n  CanvasPainter.prototype.refresh = function (paintAll) {\n    var list = this.storage.getDisplayList(true);\n    var prevList = this._prevDisplayList;\n    var zlevelList = this._zlevelList;\n    this._redrawId = Math.random();\n    this._paintList(list, prevList, paintAll, this._redrawId);\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      var layer = this._layers[z];\n      if (!layer.__builtin__ && layer.refresh) {\n        var clearColor = i === 0 ? this._backgroundColor : null;\n        layer.refresh(clearColor);\n      }\n    }\n    if (this._opts.useDirtyRect) {\n      this._prevDisplayList = list.slice();\n    }\n    return this;\n  };\n  CanvasPainter.prototype.refreshHover = function () {\n    this._paintHoverList(this.storage.getDisplayList(false));\n  };\n  CanvasPainter.prototype._paintHoverList = function (list) {\n    var len = list.length;\n    var hoverLayer = this._hoverlayer;\n    hoverLayer && hoverLayer.clear();\n    if (!len) {\n      return;\n    }\n    var scope = {\n      inHover: true,\n      viewWidth: this._width,\n      viewHeight: this._height\n    };\n    var ctx;\n    for (var i = 0; i < len; i++) {\n      var el = list[i];\n      if (el.__inHover) {\n        if (!hoverLayer) {\n          hoverLayer = this._hoverlayer = this.getLayer(HOVER_LAYER_ZLEVEL);\n        }\n        if (!ctx) {\n          ctx = hoverLayer.ctx;\n          ctx.save();\n        }\n        brush(ctx, el, scope, i === len - 1);\n      }\n    }\n    if (ctx) {\n      ctx.restore();\n    }\n  };\n  CanvasPainter.prototype.getHoverLayer = function () {\n    return this.getLayer(HOVER_LAYER_ZLEVEL);\n  };\n  CanvasPainter.prototype.paintOne = function (ctx, el) {\n    brushSingle(ctx, el);\n  };\n  CanvasPainter.prototype._paintList = function (list, prevList, paintAll, redrawId) {\n    if (this._redrawId !== redrawId) {\n      return;\n    }\n    paintAll = paintAll || false;\n    this._updateLayerStatus(list);\n    var _a = this._doPaintList(list, prevList, paintAll),\n      finished = _a.finished,\n      needsRefreshHover = _a.needsRefreshHover;\n    if (this._needsManuallyCompositing) {\n      this._compositeManually();\n    }\n    if (needsRefreshHover) {\n      this._paintHoverList(list);\n    }\n    if (!finished) {\n      var self_1 = this;\n      requestAnimationFrame(function () {\n        self_1._paintList(list, prevList, paintAll, redrawId);\n      });\n    } else {\n      this.eachLayer(function (layer) {\n        layer.afterBrush && layer.afterBrush();\n      });\n    }\n  };\n  CanvasPainter.prototype._compositeManually = function () {\n    var ctx = this.getLayer(CANVAS_ZLEVEL).ctx;\n    var width = this._domRoot.width;\n    var height = this._domRoot.height;\n    ctx.clearRect(0, 0, width, height);\n    this.eachBuiltinLayer(function (layer) {\n      if (layer.virtual) {\n        ctx.drawImage(layer.dom, 0, 0, width, height);\n      }\n    });\n  };\n  CanvasPainter.prototype._doPaintList = function (list, prevList, paintAll) {\n    var _this = this;\n    var layerList = [];\n    var useDirtyRect = this._opts.useDirtyRect;\n    for (var zi = 0; zi < this._zlevelList.length; zi++) {\n      var zlevel = this._zlevelList[zi];\n      var layer = this._layers[zlevel];\n      if (layer.__builtin__ && layer !== this._hoverlayer && (layer.__dirty || paintAll)) {\n        layerList.push(layer);\n      }\n    }\n    var finished = true;\n    var needsRefreshHover = false;\n    var _loop_1 = function (k) {\n      var layer = layerList[k];\n      var ctx = layer.ctx;\n      var repaintRects = useDirtyRect && layer.createRepaintRects(list, prevList, this_1._width, this_1._height);\n      var start = paintAll ? layer.__startIndex : layer.__drawIndex;\n      var useTimer = !paintAll && layer.incremental && Date.now;\n      var startTime = useTimer && Date.now();\n      var clearColor = layer.zlevel === this_1._zlevelList[0] ? this_1._backgroundColor : null;\n      if (layer.__startIndex === layer.__endIndex) {\n        layer.clear(false, clearColor, repaintRects);\n      } else if (start === layer.__startIndex) {\n        var firstEl = list[start];\n        if (!firstEl.incremental || !firstEl.notClear || paintAll) {\n          layer.clear(false, clearColor, repaintRects);\n        }\n      }\n      if (start === -1) {\n        console.error('For some unknown reason. drawIndex is -1');\n        start = layer.__startIndex;\n      }\n      var i;\n      var repaint = function (repaintRect) {\n        var scope = {\n          inHover: false,\n          allClipped: false,\n          prevEl: null,\n          viewWidth: _this._width,\n          viewHeight: _this._height\n        };\n        for (i = start; i < layer.__endIndex; i++) {\n          var el = list[i];\n          if (el.__inHover) {\n            needsRefreshHover = true;\n          }\n          _this._doPaintEl(el, layer, useDirtyRect, repaintRect, scope, i === layer.__endIndex - 1);\n          if (useTimer) {\n            var dTime = Date.now() - startTime;\n            if (dTime > 15) {\n              break;\n            }\n          }\n        }\n        if (scope.prevElClipPaths) {\n          ctx.restore();\n        }\n      };\n      if (repaintRects) {\n        if (repaintRects.length === 0) {\n          i = layer.__endIndex;\n        } else {\n          var dpr = this_1.dpr;\n          for (var r = 0; r < repaintRects.length; ++r) {\n            var rect = repaintRects[r];\n            ctx.save();\n            ctx.beginPath();\n            ctx.rect(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n            ctx.clip();\n            repaint(rect);\n            ctx.restore();\n          }\n        }\n      } else {\n        ctx.save();\n        repaint();\n        ctx.restore();\n      }\n      layer.__drawIndex = i;\n      if (layer.__drawIndex < layer.__endIndex) {\n        finished = false;\n      }\n    };\n    var this_1 = this;\n    for (var k = 0; k < layerList.length; k++) {\n      _loop_1(k);\n    }\n    if (env.wxa) {\n      util.each(this._layers, function (layer) {\n        if (layer && layer.ctx && layer.ctx.draw) {\n          layer.ctx.draw();\n        }\n      });\n    }\n    return {\n      finished: finished,\n      needsRefreshHover: needsRefreshHover\n    };\n  };\n  CanvasPainter.prototype._doPaintEl = function (el, currentLayer, useDirtyRect, repaintRect, scope, isLast) {\n    var ctx = currentLayer.ctx;\n    if (useDirtyRect) {\n      var paintRect = el.getPaintRect();\n      if (!repaintRect || paintRect && paintRect.intersect(repaintRect)) {\n        brush(ctx, el, scope, isLast);\n        el.setPrevPaintRect(paintRect);\n      }\n    } else {\n      brush(ctx, el, scope, isLast);\n    }\n  };\n  CanvasPainter.prototype.getLayer = function (zlevel, virtual) {\n    if (this._singleCanvas && !this._needsManuallyCompositing) {\n      zlevel = CANVAS_ZLEVEL;\n    }\n    var layer = this._layers[zlevel];\n    if (!layer) {\n      layer = new Layer('zr_' + zlevel, this, this.dpr);\n      layer.zlevel = zlevel;\n      layer.__builtin__ = true;\n      if (this._layerConfig[zlevel]) {\n        util.merge(layer, this._layerConfig[zlevel], true);\n      } else if (this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC]) {\n        util.merge(layer, this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC], true);\n      }\n      if (virtual) {\n        layer.virtual = virtual;\n      }\n      this.insertLayer(zlevel, layer);\n      layer.initContext();\n    }\n    return layer;\n  };\n  CanvasPainter.prototype.insertLayer = function (zlevel, layer) {\n    var layersMap = this._layers;\n    var zlevelList = this._zlevelList;\n    var len = zlevelList.length;\n    var domRoot = this._domRoot;\n    var prevLayer = null;\n    var i = -1;\n    if (layersMap[zlevel]) {\n      if (process.env.NODE_ENV !== 'production') {\n        util.logError('ZLevel ' + zlevel + ' has been used already');\n      }\n      return;\n    }\n    if (!isLayerValid(layer)) {\n      if (process.env.NODE_ENV !== 'production') {\n        util.logError('Layer of zlevel ' + zlevel + ' is not valid');\n      }\n      return;\n    }\n    if (len > 0 && zlevel > zlevelList[0]) {\n      for (i = 0; i < len - 1; i++) {\n        if (zlevelList[i] < zlevel && zlevelList[i + 1] > zlevel) {\n          break;\n        }\n      }\n      prevLayer = layersMap[zlevelList[i]];\n    }\n    zlevelList.splice(i + 1, 0, zlevel);\n    layersMap[zlevel] = layer;\n    if (!layer.virtual) {\n      if (prevLayer) {\n        var prevDom = prevLayer.dom;\n        if (prevDom.nextSibling) {\n          domRoot.insertBefore(layer.dom, prevDom.nextSibling);\n        } else {\n          domRoot.appendChild(layer.dom);\n        }\n      } else {\n        if (domRoot.firstChild) {\n          domRoot.insertBefore(layer.dom, domRoot.firstChild);\n        } else {\n          domRoot.appendChild(layer.dom);\n        }\n      }\n    }\n    layer.painter || (layer.painter = this);\n  };\n  CanvasPainter.prototype.eachLayer = function (cb, context) {\n    var zlevelList = this._zlevelList;\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      cb.call(context, this._layers[z], z);\n    }\n  };\n  CanvasPainter.prototype.eachBuiltinLayer = function (cb, context) {\n    var zlevelList = this._zlevelList;\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      var layer = this._layers[z];\n      if (layer.__builtin__) {\n        cb.call(context, layer, z);\n      }\n    }\n  };\n  CanvasPainter.prototype.eachOtherLayer = function (cb, context) {\n    var zlevelList = this._zlevelList;\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      var layer = this._layers[z];\n      if (!layer.__builtin__) {\n        cb.call(context, layer, z);\n      }\n    }\n  };\n  CanvasPainter.prototype.getLayers = function () {\n    return this._layers;\n  };\n  CanvasPainter.prototype._updateLayerStatus = function (list) {\n    this.eachBuiltinLayer(function (layer, z) {\n      layer.__dirty = layer.__used = false;\n    });\n    function updatePrevLayer(idx) {\n      if (prevLayer) {\n        if (prevLayer.__endIndex !== idx) {\n          prevLayer.__dirty = true;\n        }\n        prevLayer.__endIndex = idx;\n      }\n    }\n    if (this._singleCanvas) {\n      for (var i_1 = 1; i_1 < list.length; i_1++) {\n        var el = list[i_1];\n        if (el.zlevel !== list[i_1 - 1].zlevel || el.incremental) {\n          this._needsManuallyCompositing = true;\n          break;\n        }\n      }\n    }\n    var prevLayer = null;\n    var incrementalLayerCount = 0;\n    var prevZlevel;\n    var i;\n    for (i = 0; i < list.length; i++) {\n      var el = list[i];\n      var zlevel = el.zlevel;\n      var layer = void 0;\n      if (prevZlevel !== zlevel) {\n        prevZlevel = zlevel;\n        incrementalLayerCount = 0;\n      }\n      if (el.incremental) {\n        layer = this.getLayer(zlevel + INCREMENTAL_INC, this._needsManuallyCompositing);\n        layer.incremental = true;\n        incrementalLayerCount = 1;\n      } else {\n        layer = this.getLayer(zlevel + (incrementalLayerCount > 0 ? EL_AFTER_INCREMENTAL_INC : 0), this._needsManuallyCompositing);\n      }\n      if (!layer.__builtin__) {\n        util.logError('ZLevel ' + zlevel + ' has been used by unkown layer ' + layer.id);\n      }\n      if (layer !== prevLayer) {\n        layer.__used = true;\n        if (layer.__startIndex !== i) {\n          layer.__dirty = true;\n        }\n        layer.__startIndex = i;\n        if (!layer.incremental) {\n          layer.__drawIndex = i;\n        } else {\n          layer.__drawIndex = -1;\n        }\n        updatePrevLayer(i);\n        prevLayer = layer;\n      }\n      if (el.__dirty & REDRAW_BIT && !el.__inHover) {\n        layer.__dirty = true;\n        if (layer.incremental && layer.__drawIndex < 0) {\n          layer.__drawIndex = i;\n        }\n      }\n    }\n    updatePrevLayer(i);\n    this.eachBuiltinLayer(function (layer, z) {\n      if (!layer.__used && layer.getElementCount() > 0) {\n        layer.__dirty = true;\n        layer.__startIndex = layer.__endIndex = layer.__drawIndex = 0;\n      }\n      if (layer.__dirty && layer.__drawIndex < 0) {\n        layer.__drawIndex = layer.__startIndex;\n      }\n    });\n  };\n  CanvasPainter.prototype.clear = function () {\n    this.eachBuiltinLayer(this._clearLayer);\n    return this;\n  };\n  CanvasPainter.prototype._clearLayer = function (layer) {\n    layer.clear();\n  };\n  CanvasPainter.prototype.setBackgroundColor = function (backgroundColor) {\n    this._backgroundColor = backgroundColor;\n    util.each(this._layers, function (layer) {\n      layer.setUnpainted();\n    });\n  };\n  CanvasPainter.prototype.configLayer = function (zlevel, config) {\n    if (config) {\n      var layerConfig = this._layerConfig;\n      if (!layerConfig[zlevel]) {\n        layerConfig[zlevel] = config;\n      } else {\n        util.merge(layerConfig[zlevel], config, true);\n      }\n      for (var i = 0; i < this._zlevelList.length; i++) {\n        var _zlevel = this._zlevelList[i];\n        if (_zlevel === zlevel || _zlevel === zlevel + EL_AFTER_INCREMENTAL_INC) {\n          var layer = this._layers[_zlevel];\n          util.merge(layer, layerConfig[zlevel], true);\n        }\n      }\n    }\n  };\n  CanvasPainter.prototype.delLayer = function (zlevel) {\n    var layers = this._layers;\n    var zlevelList = this._zlevelList;\n    var layer = layers[zlevel];\n    if (!layer) {\n      return;\n    }\n    layer.dom.parentNode.removeChild(layer.dom);\n    delete layers[zlevel];\n    zlevelList.splice(util.indexOf(zlevelList, zlevel), 1);\n  };\n  CanvasPainter.prototype.resize = function (width, height) {\n    if (!this._domRoot.style) {\n      if (width == null || height == null) {\n        return;\n      }\n      this._width = width;\n      this._height = height;\n      this.getLayer(CANVAS_ZLEVEL).resize(width, height);\n    } else {\n      var domRoot = this._domRoot;\n      domRoot.style.display = 'none';\n      var opts = this._opts;\n      var root = this.root;\n      width != null && (opts.width = width);\n      height != null && (opts.height = height);\n      width = getSize(root, 0, opts);\n      height = getSize(root, 1, opts);\n      domRoot.style.display = '';\n      if (this._width !== width || height !== this._height) {\n        domRoot.style.width = width + 'px';\n        domRoot.style.height = height + 'px';\n        for (var id in this._layers) {\n          if (this._layers.hasOwnProperty(id)) {\n            this._layers[id].resize(width, height);\n          }\n        }\n        this.refresh(true);\n      }\n      this._width = width;\n      this._height = height;\n    }\n    return this;\n  };\n  CanvasPainter.prototype.clearLayer = function (zlevel) {\n    var layer = this._layers[zlevel];\n    if (layer) {\n      layer.clear();\n    }\n  };\n  CanvasPainter.prototype.dispose = function () {\n    this.root.innerHTML = '';\n    this.root = this.storage = this._domRoot = this._layers = null;\n  };\n  CanvasPainter.prototype.getRenderedCanvas = function (opts) {\n    opts = opts || {};\n    if (this._singleCanvas && !this._compositeManually) {\n      return this._layers[CANVAS_ZLEVEL].dom;\n    }\n    var imageLayer = new Layer('image', this, opts.pixelRatio || this.dpr);\n    imageLayer.initContext();\n    imageLayer.clear(false, opts.backgroundColor || this._backgroundColor);\n    var ctx = imageLayer.ctx;\n    if (opts.pixelRatio <= this.dpr) {\n      this.refresh();\n      var width_1 = imageLayer.dom.width;\n      var height_1 = imageLayer.dom.height;\n      this.eachLayer(function (layer) {\n        if (layer.__builtin__) {\n          ctx.drawImage(layer.dom, 0, 0, width_1, height_1);\n        } else if (layer.renderToCanvas) {\n          ctx.save();\n          layer.renderToCanvas(ctx);\n          ctx.restore();\n        }\n      });\n    } else {\n      var scope = {\n        inHover: false,\n        viewWidth: this._width,\n        viewHeight: this._height\n      };\n      var displayList = this.storage.getDisplayList(true);\n      for (var i = 0, len = displayList.length; i < len; i++) {\n        var el = displayList[i];\n        brush(ctx, el, scope, i === len - 1);\n      }\n    }\n    return imageLayer.dom;\n  };\n  CanvasPainter.prototype.getWidth = function () {\n    return this._width;\n  };\n  CanvasPainter.prototype.getHeight = function () {\n    return this._height;\n  };\n  return CanvasPainter;\n}();\nexport default CanvasPainter;\n;", "map": {"version": 3, "names": ["devicePixelRatio", "util", "Layer", "requestAnimationFrame", "env", "brush", "brushSingle", "REDRAW_BIT", "getSize", "HOVER_LAYER_ZLEVEL", "CANVAS_ZLEVEL", "EL_AFTER_INCREMENTAL_INC", "INCREMENTAL_INC", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "layer", "__builtin__", "resize", "refresh", "createRoot", "width", "height", "domRoot", "document", "createElement", "style", "cssText", "join", "CanvasPainter", "root", "storage", "opts", "id", "type", "_zlevelList", "_prevDisplayList", "_layers", "_layerConfig", "_needsManuallyCompositing", "singleCanvas", "nodeName", "toUpperCase", "_opts", "extend", "dpr", "_singleCanvas", "rootStyle", "disableUserSelect", "innerHTML", "zlevelList", "layers", "_width", "_height", "_domRoot", "append<PERSON><PERSON><PERSON>", "rootCanvas", "<PERSON><PERSON>ayer", "initContext", "zlevel", "push", "prototype", "getType", "isSingleCanvas", "getViewportRoot", "getViewportRootOffset", "viewportRoot", "offsetLeft", "offsetTop", "paintAll", "list", "getDisplayList", "prevList", "_redrawId", "Math", "random", "_paintList", "i", "length", "z", "clearColor", "_backgroundColor", "useDirtyRect", "slice", "refreshHover", "_paintHoverList", "len", "hoverLayer", "_hoverlayer", "clear", "scope", "inHover", "viewWidth", "viewHeight", "ctx", "el", "__inHover", "<PERSON><PERSON><PERSON><PERSON>", "save", "restore", "get<PERSON>over<PERSON><PERSON>er", "paintOne", "redrawId", "_updateLayerStatus", "_a", "_doPaintList", "finished", "needsRefreshHover", "_compositeManually", "self_1", "eachLayer", "afterBrush", "clearRect", "eachBuiltin<PERSON>ayer", "virtual", "drawImage", "dom", "_this", "layerList", "zi", "__dirty", "_loop_1", "k", "repaintRects", "createRepaintRects", "this_1", "start", "__startIndex", "__drawIndex", "useTimer", "incremental", "Date", "now", "startTime", "__endIndex", "firstEl", "notClear", "console", "error", "repaint", "repaintRect", "allClipped", "prevEl", "_doPaintEl", "dTime", "prevElClipPaths", "r", "rect", "beginPath", "x", "y", "clip", "wxa", "each", "draw", "<PERSON><PERSON><PERSON><PERSON>", "isLast", "paintRect", "getPaintRect", "intersect", "setPrevPaintRect", "merge", "insertLayer", "layersMap", "prevLayer", "process", "NODE_ENV", "logError", "splice", "prevDom", "nextS<PERSON>ling", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "painter", "cb", "context", "call", "eachOther<PERSON>ayer", "getLayers", "__used", "updatePrevLayer", "idx", "i_1", "incrementalLayerCount", "prevZlevel", "getElementCount", "_clearLayer", "setBackgroundColor", "backgroundColor", "setUnpainted", "config<PERSON><PERSON>er", "config", "layerConfig", "_zlevel", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "display", "hasOwnProperty", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imageLayer", "pixelRatio", "width_1", "height_1", "renderToCanvas", "displayList", "getWidth", "getHeight"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/zrender/lib/canvas/Painter.js"], "sourcesContent": ["import { devicePixelRatio } from '../config.js';\nimport * as util from '../core/util.js';\nimport Layer from './Layer.js';\nimport requestAnimationFrame from '../animation/requestAnimationFrame.js';\nimport env from '../core/env.js';\nimport { brush, brushSingle } from './graphic.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { getSize } from './helper.js';\nvar HOVER_LAYER_ZLEVEL = 1e5;\nvar CANVAS_ZLEVEL = 314159;\nvar EL_AFTER_INCREMENTAL_INC = 0.01;\nvar INCREMENTAL_INC = 0.001;\nfunction isLayerValid(layer) {\n    if (!layer) {\n        return false;\n    }\n    if (layer.__builtin__) {\n        return true;\n    }\n    if (typeof (layer.resize) !== 'function'\n        || typeof (layer.refresh) !== 'function') {\n        return false;\n    }\n    return true;\n}\nfunction createRoot(width, height) {\n    var domRoot = document.createElement('div');\n    domRoot.style.cssText = [\n        'position:relative',\n        'width:' + width + 'px',\n        'height:' + height + 'px',\n        'padding:0',\n        'margin:0',\n        'border-width:0'\n    ].join(';') + ';';\n    return domRoot;\n}\nvar CanvasPainter = (function () {\n    function CanvasPainter(root, storage, opts, id) {\n        this.type = 'canvas';\n        this._zlevelList = [];\n        this._prevDisplayList = [];\n        this._layers = {};\n        this._layerConfig = {};\n        this._needsManuallyCompositing = false;\n        this.type = 'canvas';\n        var singleCanvas = !root.nodeName\n            || root.nodeName.toUpperCase() === 'CANVAS';\n        this._opts = opts = util.extend({}, opts || {});\n        this.dpr = opts.devicePixelRatio || devicePixelRatio;\n        this._singleCanvas = singleCanvas;\n        this.root = root;\n        var rootStyle = root.style;\n        if (rootStyle) {\n            util.disableUserSelect(root);\n            root.innerHTML = '';\n        }\n        this.storage = storage;\n        var zlevelList = this._zlevelList;\n        this._prevDisplayList = [];\n        var layers = this._layers;\n        if (!singleCanvas) {\n            this._width = getSize(root, 0, opts);\n            this._height = getSize(root, 1, opts);\n            var domRoot = this._domRoot = createRoot(this._width, this._height);\n            root.appendChild(domRoot);\n        }\n        else {\n            var rootCanvas = root;\n            var width = rootCanvas.width;\n            var height = rootCanvas.height;\n            if (opts.width != null) {\n                width = opts.width;\n            }\n            if (opts.height != null) {\n                height = opts.height;\n            }\n            this.dpr = opts.devicePixelRatio || 1;\n            rootCanvas.width = width * this.dpr;\n            rootCanvas.height = height * this.dpr;\n            this._width = width;\n            this._height = height;\n            var mainLayer = new Layer(rootCanvas, this, this.dpr);\n            mainLayer.__builtin__ = true;\n            mainLayer.initContext();\n            layers[CANVAS_ZLEVEL] = mainLayer;\n            mainLayer.zlevel = CANVAS_ZLEVEL;\n            zlevelList.push(CANVAS_ZLEVEL);\n            this._domRoot = root;\n        }\n    }\n    CanvasPainter.prototype.getType = function () {\n        return 'canvas';\n    };\n    CanvasPainter.prototype.isSingleCanvas = function () {\n        return this._singleCanvas;\n    };\n    CanvasPainter.prototype.getViewportRoot = function () {\n        return this._domRoot;\n    };\n    CanvasPainter.prototype.getViewportRootOffset = function () {\n        var viewportRoot = this.getViewportRoot();\n        if (viewportRoot) {\n            return {\n                offsetLeft: viewportRoot.offsetLeft || 0,\n                offsetTop: viewportRoot.offsetTop || 0\n            };\n        }\n    };\n    CanvasPainter.prototype.refresh = function (paintAll) {\n        var list = this.storage.getDisplayList(true);\n        var prevList = this._prevDisplayList;\n        var zlevelList = this._zlevelList;\n        this._redrawId = Math.random();\n        this._paintList(list, prevList, paintAll, this._redrawId);\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            var layer = this._layers[z];\n            if (!layer.__builtin__ && layer.refresh) {\n                var clearColor = i === 0 ? this._backgroundColor : null;\n                layer.refresh(clearColor);\n            }\n        }\n        if (this._opts.useDirtyRect) {\n            this._prevDisplayList = list.slice();\n        }\n        return this;\n    };\n    CanvasPainter.prototype.refreshHover = function () {\n        this._paintHoverList(this.storage.getDisplayList(false));\n    };\n    CanvasPainter.prototype._paintHoverList = function (list) {\n        var len = list.length;\n        var hoverLayer = this._hoverlayer;\n        hoverLayer && hoverLayer.clear();\n        if (!len) {\n            return;\n        }\n        var scope = {\n            inHover: true,\n            viewWidth: this._width,\n            viewHeight: this._height\n        };\n        var ctx;\n        for (var i = 0; i < len; i++) {\n            var el = list[i];\n            if (el.__inHover) {\n                if (!hoverLayer) {\n                    hoverLayer = this._hoverlayer = this.getLayer(HOVER_LAYER_ZLEVEL);\n                }\n                if (!ctx) {\n                    ctx = hoverLayer.ctx;\n                    ctx.save();\n                }\n                brush(ctx, el, scope, i === len - 1);\n            }\n        }\n        if (ctx) {\n            ctx.restore();\n        }\n    };\n    CanvasPainter.prototype.getHoverLayer = function () {\n        return this.getLayer(HOVER_LAYER_ZLEVEL);\n    };\n    CanvasPainter.prototype.paintOne = function (ctx, el) {\n        brushSingle(ctx, el);\n    };\n    CanvasPainter.prototype._paintList = function (list, prevList, paintAll, redrawId) {\n        if (this._redrawId !== redrawId) {\n            return;\n        }\n        paintAll = paintAll || false;\n        this._updateLayerStatus(list);\n        var _a = this._doPaintList(list, prevList, paintAll), finished = _a.finished, needsRefreshHover = _a.needsRefreshHover;\n        if (this._needsManuallyCompositing) {\n            this._compositeManually();\n        }\n        if (needsRefreshHover) {\n            this._paintHoverList(list);\n        }\n        if (!finished) {\n            var self_1 = this;\n            requestAnimationFrame(function () {\n                self_1._paintList(list, prevList, paintAll, redrawId);\n            });\n        }\n        else {\n            this.eachLayer(function (layer) {\n                layer.afterBrush && layer.afterBrush();\n            });\n        }\n    };\n    CanvasPainter.prototype._compositeManually = function () {\n        var ctx = this.getLayer(CANVAS_ZLEVEL).ctx;\n        var width = this._domRoot.width;\n        var height = this._domRoot.height;\n        ctx.clearRect(0, 0, width, height);\n        this.eachBuiltinLayer(function (layer) {\n            if (layer.virtual) {\n                ctx.drawImage(layer.dom, 0, 0, width, height);\n            }\n        });\n    };\n    CanvasPainter.prototype._doPaintList = function (list, prevList, paintAll) {\n        var _this = this;\n        var layerList = [];\n        var useDirtyRect = this._opts.useDirtyRect;\n        for (var zi = 0; zi < this._zlevelList.length; zi++) {\n            var zlevel = this._zlevelList[zi];\n            var layer = this._layers[zlevel];\n            if (layer.__builtin__\n                && layer !== this._hoverlayer\n                && (layer.__dirty || paintAll)) {\n                layerList.push(layer);\n            }\n        }\n        var finished = true;\n        var needsRefreshHover = false;\n        var _loop_1 = function (k) {\n            var layer = layerList[k];\n            var ctx = layer.ctx;\n            var repaintRects = useDirtyRect\n                && layer.createRepaintRects(list, prevList, this_1._width, this_1._height);\n            var start = paintAll ? layer.__startIndex : layer.__drawIndex;\n            var useTimer = !paintAll && layer.incremental && Date.now;\n            var startTime = useTimer && Date.now();\n            var clearColor = layer.zlevel === this_1._zlevelList[0]\n                ? this_1._backgroundColor : null;\n            if (layer.__startIndex === layer.__endIndex) {\n                layer.clear(false, clearColor, repaintRects);\n            }\n            else if (start === layer.__startIndex) {\n                var firstEl = list[start];\n                if (!firstEl.incremental || !firstEl.notClear || paintAll) {\n                    layer.clear(false, clearColor, repaintRects);\n                }\n            }\n            if (start === -1) {\n                console.error('For some unknown reason. drawIndex is -1');\n                start = layer.__startIndex;\n            }\n            var i;\n            var repaint = function (repaintRect) {\n                var scope = {\n                    inHover: false,\n                    allClipped: false,\n                    prevEl: null,\n                    viewWidth: _this._width,\n                    viewHeight: _this._height\n                };\n                for (i = start; i < layer.__endIndex; i++) {\n                    var el = list[i];\n                    if (el.__inHover) {\n                        needsRefreshHover = true;\n                    }\n                    _this._doPaintEl(el, layer, useDirtyRect, repaintRect, scope, i === layer.__endIndex - 1);\n                    if (useTimer) {\n                        var dTime = Date.now() - startTime;\n                        if (dTime > 15) {\n                            break;\n                        }\n                    }\n                }\n                if (scope.prevElClipPaths) {\n                    ctx.restore();\n                }\n            };\n            if (repaintRects) {\n                if (repaintRects.length === 0) {\n                    i = layer.__endIndex;\n                }\n                else {\n                    var dpr = this_1.dpr;\n                    for (var r = 0; r < repaintRects.length; ++r) {\n                        var rect = repaintRects[r];\n                        ctx.save();\n                        ctx.beginPath();\n                        ctx.rect(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n                        ctx.clip();\n                        repaint(rect);\n                        ctx.restore();\n                    }\n                }\n            }\n            else {\n                ctx.save();\n                repaint();\n                ctx.restore();\n            }\n            layer.__drawIndex = i;\n            if (layer.__drawIndex < layer.__endIndex) {\n                finished = false;\n            }\n        };\n        var this_1 = this;\n        for (var k = 0; k < layerList.length; k++) {\n            _loop_1(k);\n        }\n        if (env.wxa) {\n            util.each(this._layers, function (layer) {\n                if (layer && layer.ctx && layer.ctx.draw) {\n                    layer.ctx.draw();\n                }\n            });\n        }\n        return {\n            finished: finished,\n            needsRefreshHover: needsRefreshHover\n        };\n    };\n    CanvasPainter.prototype._doPaintEl = function (el, currentLayer, useDirtyRect, repaintRect, scope, isLast) {\n        var ctx = currentLayer.ctx;\n        if (useDirtyRect) {\n            var paintRect = el.getPaintRect();\n            if (!repaintRect || paintRect && paintRect.intersect(repaintRect)) {\n                brush(ctx, el, scope, isLast);\n                el.setPrevPaintRect(paintRect);\n            }\n        }\n        else {\n            brush(ctx, el, scope, isLast);\n        }\n    };\n    CanvasPainter.prototype.getLayer = function (zlevel, virtual) {\n        if (this._singleCanvas && !this._needsManuallyCompositing) {\n            zlevel = CANVAS_ZLEVEL;\n        }\n        var layer = this._layers[zlevel];\n        if (!layer) {\n            layer = new Layer('zr_' + zlevel, this, this.dpr);\n            layer.zlevel = zlevel;\n            layer.__builtin__ = true;\n            if (this._layerConfig[zlevel]) {\n                util.merge(layer, this._layerConfig[zlevel], true);\n            }\n            else if (this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC]) {\n                util.merge(layer, this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC], true);\n            }\n            if (virtual) {\n                layer.virtual = virtual;\n            }\n            this.insertLayer(zlevel, layer);\n            layer.initContext();\n        }\n        return layer;\n    };\n    CanvasPainter.prototype.insertLayer = function (zlevel, layer) {\n        var layersMap = this._layers;\n        var zlevelList = this._zlevelList;\n        var len = zlevelList.length;\n        var domRoot = this._domRoot;\n        var prevLayer = null;\n        var i = -1;\n        if (layersMap[zlevel]) {\n            if (process.env.NODE_ENV !== 'production') {\n                util.logError('ZLevel ' + zlevel + ' has been used already');\n            }\n            return;\n        }\n        if (!isLayerValid(layer)) {\n            if (process.env.NODE_ENV !== 'production') {\n                util.logError('Layer of zlevel ' + zlevel + ' is not valid');\n            }\n            return;\n        }\n        if (len > 0 && zlevel > zlevelList[0]) {\n            for (i = 0; i < len - 1; i++) {\n                if (zlevelList[i] < zlevel\n                    && zlevelList[i + 1] > zlevel) {\n                    break;\n                }\n            }\n            prevLayer = layersMap[zlevelList[i]];\n        }\n        zlevelList.splice(i + 1, 0, zlevel);\n        layersMap[zlevel] = layer;\n        if (!layer.virtual) {\n            if (prevLayer) {\n                var prevDom = prevLayer.dom;\n                if (prevDom.nextSibling) {\n                    domRoot.insertBefore(layer.dom, prevDom.nextSibling);\n                }\n                else {\n                    domRoot.appendChild(layer.dom);\n                }\n            }\n            else {\n                if (domRoot.firstChild) {\n                    domRoot.insertBefore(layer.dom, domRoot.firstChild);\n                }\n                else {\n                    domRoot.appendChild(layer.dom);\n                }\n            }\n        }\n        layer.painter || (layer.painter = this);\n    };\n    CanvasPainter.prototype.eachLayer = function (cb, context) {\n        var zlevelList = this._zlevelList;\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            cb.call(context, this._layers[z], z);\n        }\n    };\n    CanvasPainter.prototype.eachBuiltinLayer = function (cb, context) {\n        var zlevelList = this._zlevelList;\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            var layer = this._layers[z];\n            if (layer.__builtin__) {\n                cb.call(context, layer, z);\n            }\n        }\n    };\n    CanvasPainter.prototype.eachOtherLayer = function (cb, context) {\n        var zlevelList = this._zlevelList;\n        for (var i = 0; i < zlevelList.length; i++) {\n            var z = zlevelList[i];\n            var layer = this._layers[z];\n            if (!layer.__builtin__) {\n                cb.call(context, layer, z);\n            }\n        }\n    };\n    CanvasPainter.prototype.getLayers = function () {\n        return this._layers;\n    };\n    CanvasPainter.prototype._updateLayerStatus = function (list) {\n        this.eachBuiltinLayer(function (layer, z) {\n            layer.__dirty = layer.__used = false;\n        });\n        function updatePrevLayer(idx) {\n            if (prevLayer) {\n                if (prevLayer.__endIndex !== idx) {\n                    prevLayer.__dirty = true;\n                }\n                prevLayer.__endIndex = idx;\n            }\n        }\n        if (this._singleCanvas) {\n            for (var i_1 = 1; i_1 < list.length; i_1++) {\n                var el = list[i_1];\n                if (el.zlevel !== list[i_1 - 1].zlevel || el.incremental) {\n                    this._needsManuallyCompositing = true;\n                    break;\n                }\n            }\n        }\n        var prevLayer = null;\n        var incrementalLayerCount = 0;\n        var prevZlevel;\n        var i;\n        for (i = 0; i < list.length; i++) {\n            var el = list[i];\n            var zlevel = el.zlevel;\n            var layer = void 0;\n            if (prevZlevel !== zlevel) {\n                prevZlevel = zlevel;\n                incrementalLayerCount = 0;\n            }\n            if (el.incremental) {\n                layer = this.getLayer(zlevel + INCREMENTAL_INC, this._needsManuallyCompositing);\n                layer.incremental = true;\n                incrementalLayerCount = 1;\n            }\n            else {\n                layer = this.getLayer(zlevel + (incrementalLayerCount > 0 ? EL_AFTER_INCREMENTAL_INC : 0), this._needsManuallyCompositing);\n            }\n            if (!layer.__builtin__) {\n                util.logError('ZLevel ' + zlevel + ' has been used by unkown layer ' + layer.id);\n            }\n            if (layer !== prevLayer) {\n                layer.__used = true;\n                if (layer.__startIndex !== i) {\n                    layer.__dirty = true;\n                }\n                layer.__startIndex = i;\n                if (!layer.incremental) {\n                    layer.__drawIndex = i;\n                }\n                else {\n                    layer.__drawIndex = -1;\n                }\n                updatePrevLayer(i);\n                prevLayer = layer;\n            }\n            if ((el.__dirty & REDRAW_BIT) && !el.__inHover) {\n                layer.__dirty = true;\n                if (layer.incremental && layer.__drawIndex < 0) {\n                    layer.__drawIndex = i;\n                }\n            }\n        }\n        updatePrevLayer(i);\n        this.eachBuiltinLayer(function (layer, z) {\n            if (!layer.__used && layer.getElementCount() > 0) {\n                layer.__dirty = true;\n                layer.__startIndex = layer.__endIndex = layer.__drawIndex = 0;\n            }\n            if (layer.__dirty && layer.__drawIndex < 0) {\n                layer.__drawIndex = layer.__startIndex;\n            }\n        });\n    };\n    CanvasPainter.prototype.clear = function () {\n        this.eachBuiltinLayer(this._clearLayer);\n        return this;\n    };\n    CanvasPainter.prototype._clearLayer = function (layer) {\n        layer.clear();\n    };\n    CanvasPainter.prototype.setBackgroundColor = function (backgroundColor) {\n        this._backgroundColor = backgroundColor;\n        util.each(this._layers, function (layer) {\n            layer.setUnpainted();\n        });\n    };\n    CanvasPainter.prototype.configLayer = function (zlevel, config) {\n        if (config) {\n            var layerConfig = this._layerConfig;\n            if (!layerConfig[zlevel]) {\n                layerConfig[zlevel] = config;\n            }\n            else {\n                util.merge(layerConfig[zlevel], config, true);\n            }\n            for (var i = 0; i < this._zlevelList.length; i++) {\n                var _zlevel = this._zlevelList[i];\n                if (_zlevel === zlevel || _zlevel === zlevel + EL_AFTER_INCREMENTAL_INC) {\n                    var layer = this._layers[_zlevel];\n                    util.merge(layer, layerConfig[zlevel], true);\n                }\n            }\n        }\n    };\n    CanvasPainter.prototype.delLayer = function (zlevel) {\n        var layers = this._layers;\n        var zlevelList = this._zlevelList;\n        var layer = layers[zlevel];\n        if (!layer) {\n            return;\n        }\n        layer.dom.parentNode.removeChild(layer.dom);\n        delete layers[zlevel];\n        zlevelList.splice(util.indexOf(zlevelList, zlevel), 1);\n    };\n    CanvasPainter.prototype.resize = function (width, height) {\n        if (!this._domRoot.style) {\n            if (width == null || height == null) {\n                return;\n            }\n            this._width = width;\n            this._height = height;\n            this.getLayer(CANVAS_ZLEVEL).resize(width, height);\n        }\n        else {\n            var domRoot = this._domRoot;\n            domRoot.style.display = 'none';\n            var opts = this._opts;\n            var root = this.root;\n            width != null && (opts.width = width);\n            height != null && (opts.height = height);\n            width = getSize(root, 0, opts);\n            height = getSize(root, 1, opts);\n            domRoot.style.display = '';\n            if (this._width !== width || height !== this._height) {\n                domRoot.style.width = width + 'px';\n                domRoot.style.height = height + 'px';\n                for (var id in this._layers) {\n                    if (this._layers.hasOwnProperty(id)) {\n                        this._layers[id].resize(width, height);\n                    }\n                }\n                this.refresh(true);\n            }\n            this._width = width;\n            this._height = height;\n        }\n        return this;\n    };\n    CanvasPainter.prototype.clearLayer = function (zlevel) {\n        var layer = this._layers[zlevel];\n        if (layer) {\n            layer.clear();\n        }\n    };\n    CanvasPainter.prototype.dispose = function () {\n        this.root.innerHTML = '';\n        this.root =\n            this.storage =\n                this._domRoot =\n                    this._layers = null;\n    };\n    CanvasPainter.prototype.getRenderedCanvas = function (opts) {\n        opts = opts || {};\n        if (this._singleCanvas && !this._compositeManually) {\n            return this._layers[CANVAS_ZLEVEL].dom;\n        }\n        var imageLayer = new Layer('image', this, opts.pixelRatio || this.dpr);\n        imageLayer.initContext();\n        imageLayer.clear(false, opts.backgroundColor || this._backgroundColor);\n        var ctx = imageLayer.ctx;\n        if (opts.pixelRatio <= this.dpr) {\n            this.refresh();\n            var width_1 = imageLayer.dom.width;\n            var height_1 = imageLayer.dom.height;\n            this.eachLayer(function (layer) {\n                if (layer.__builtin__) {\n                    ctx.drawImage(layer.dom, 0, 0, width_1, height_1);\n                }\n                else if (layer.renderToCanvas) {\n                    ctx.save();\n                    layer.renderToCanvas(ctx);\n                    ctx.restore();\n                }\n            });\n        }\n        else {\n            var scope = {\n                inHover: false,\n                viewWidth: this._width,\n                viewHeight: this._height\n            };\n            var displayList = this.storage.getDisplayList(true);\n            for (var i = 0, len = displayList.length; i < len; i++) {\n                var el = displayList[i];\n                brush(ctx, el, scope, i === len - 1);\n            }\n        }\n        return imageLayer.dom;\n    };\n    CanvasPainter.prototype.getWidth = function () {\n        return this._width;\n    };\n    CanvasPainter.prototype.getHeight = function () {\n        return this._height;\n    };\n    return CanvasPainter;\n}());\nexport default CanvasPainter;\n;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,cAAc;AAC/C,OAAO,KAAKC,IAAI,MAAM,iBAAiB;AACvC,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,KAAK,EAAEC,WAAW,QAAQ,cAAc;AACjD,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,OAAO,QAAQ,aAAa;AACrC,IAAIC,kBAAkB,GAAG,GAAG;AAC5B,IAAIC,aAAa,GAAG,MAAM;AAC1B,IAAIC,wBAAwB,GAAG,IAAI;AACnC,IAAIC,eAAe,GAAG,KAAK;AAC3B,SAASC,YAAYA,CAACC,KAAK,EAAE;EACzB,IAAI,CAACA,KAAK,EAAE;IACR,OAAO,KAAK;EAChB;EACA,IAAIA,KAAK,CAACC,WAAW,EAAE;IACnB,OAAO,IAAI;EACf;EACA,IAAI,OAAQD,KAAK,CAACE,MAAO,KAAK,UAAU,IACjC,OAAQF,KAAK,CAACG,OAAQ,KAAK,UAAU,EAAE;IAC1C,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,SAASC,UAAUA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC/B,IAAIC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC3CF,OAAO,CAACG,KAAK,CAACC,OAAO,GAAG,CACpB,mBAAmB,EACnB,QAAQ,GAAGN,KAAK,GAAG,IAAI,EACvB,SAAS,GAAGC,MAAM,GAAG,IAAI,EACzB,WAAW,EACX,UAAU,EACV,gBAAgB,CACnB,CAACM,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EACjB,OAAOL,OAAO;AAClB;AACA,IAAIM,aAAa,GAAI,YAAY;EAC7B,SAASA,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAEC,IAAI,EAAEC,EAAE,EAAE;IAC5C,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,yBAAyB,GAAG,KAAK;IACtC,IAAI,CAACL,IAAI,GAAG,QAAQ;IACpB,IAAIM,YAAY,GAAG,CAACV,IAAI,CAACW,QAAQ,IAC1BX,IAAI,CAACW,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ;IAC/C,IAAI,CAACC,KAAK,GAAGX,IAAI,GAAG7B,IAAI,CAACyC,MAAM,CAAC,CAAC,CAAC,EAAEZ,IAAI,IAAI,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACa,GAAG,GAAGb,IAAI,CAAC9B,gBAAgB,IAAIA,gBAAgB;IACpD,IAAI,CAAC4C,aAAa,GAAGN,YAAY;IACjC,IAAI,CAACV,IAAI,GAAGA,IAAI;IAChB,IAAIiB,SAAS,GAAGjB,IAAI,CAACJ,KAAK;IAC1B,IAAIqB,SAAS,EAAE;MACX5C,IAAI,CAAC6C,iBAAiB,CAAClB,IAAI,CAAC;MAC5BA,IAAI,CAACmB,SAAS,GAAG,EAAE;IACvB;IACA,IAAI,CAAClB,OAAO,GAAGA,OAAO;IACtB,IAAImB,UAAU,GAAG,IAAI,CAACf,WAAW;IACjC,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAIe,MAAM,GAAG,IAAI,CAACd,OAAO;IACzB,IAAI,CAACG,YAAY,EAAE;MACf,IAAI,CAACY,MAAM,GAAG1C,OAAO,CAACoB,IAAI,EAAE,CAAC,EAAEE,IAAI,CAAC;MACpC,IAAI,CAACqB,OAAO,GAAG3C,OAAO,CAACoB,IAAI,EAAE,CAAC,EAAEE,IAAI,CAAC;MACrC,IAAIT,OAAO,GAAG,IAAI,CAAC+B,QAAQ,GAAGlC,UAAU,CAAC,IAAI,CAACgC,MAAM,EAAE,IAAI,CAACC,OAAO,CAAC;MACnEvB,IAAI,CAACyB,WAAW,CAAChC,OAAO,CAAC;IAC7B,CAAC,MACI;MACD,IAAIiC,UAAU,GAAG1B,IAAI;MACrB,IAAIT,KAAK,GAAGmC,UAAU,CAACnC,KAAK;MAC5B,IAAIC,MAAM,GAAGkC,UAAU,CAAClC,MAAM;MAC9B,IAAIU,IAAI,CAACX,KAAK,IAAI,IAAI,EAAE;QACpBA,KAAK,GAAGW,IAAI,CAACX,KAAK;MACtB;MACA,IAAIW,IAAI,CAACV,MAAM,IAAI,IAAI,EAAE;QACrBA,MAAM,GAAGU,IAAI,CAACV,MAAM;MACxB;MACA,IAAI,CAACuB,GAAG,GAAGb,IAAI,CAAC9B,gBAAgB,IAAI,CAAC;MACrCsD,UAAU,CAACnC,KAAK,GAAGA,KAAK,GAAG,IAAI,CAACwB,GAAG;MACnCW,UAAU,CAAClC,MAAM,GAAGA,MAAM,GAAG,IAAI,CAACuB,GAAG;MACrC,IAAI,CAACO,MAAM,GAAG/B,KAAK;MACnB,IAAI,CAACgC,OAAO,GAAG/B,MAAM;MACrB,IAAImC,SAAS,GAAG,IAAIrD,KAAK,CAACoD,UAAU,EAAE,IAAI,EAAE,IAAI,CAACX,GAAG,CAAC;MACrDY,SAAS,CAACxC,WAAW,GAAG,IAAI;MAC5BwC,SAAS,CAACC,WAAW,CAAC,CAAC;MACvBP,MAAM,CAACvC,aAAa,CAAC,GAAG6C,SAAS;MACjCA,SAAS,CAACE,MAAM,GAAG/C,aAAa;MAChCsC,UAAU,CAACU,IAAI,CAAChD,aAAa,CAAC;MAC9B,IAAI,CAAC0C,QAAQ,GAAGxB,IAAI;IACxB;EACJ;EACAD,aAAa,CAACgC,SAAS,CAACC,OAAO,GAAG,YAAY;IAC1C,OAAO,QAAQ;EACnB,CAAC;EACDjC,aAAa,CAACgC,SAAS,CAACE,cAAc,GAAG,YAAY;IACjD,OAAO,IAAI,CAACjB,aAAa;EAC7B,CAAC;EACDjB,aAAa,CAACgC,SAAS,CAACG,eAAe,GAAG,YAAY;IAClD,OAAO,IAAI,CAACV,QAAQ;EACxB,CAAC;EACDzB,aAAa,CAACgC,SAAS,CAACI,qBAAqB,GAAG,YAAY;IACxD,IAAIC,YAAY,GAAG,IAAI,CAACF,eAAe,CAAC,CAAC;IACzC,IAAIE,YAAY,EAAE;MACd,OAAO;QACHC,UAAU,EAAED,YAAY,CAACC,UAAU,IAAI,CAAC;QACxCC,SAAS,EAAEF,YAAY,CAACE,SAAS,IAAI;MACzC,CAAC;IACL;EACJ,CAAC;EACDvC,aAAa,CAACgC,SAAS,CAAC1C,OAAO,GAAG,UAAUkD,QAAQ,EAAE;IAClD,IAAIC,IAAI,GAAG,IAAI,CAACvC,OAAO,CAACwC,cAAc,CAAC,IAAI,CAAC;IAC5C,IAAIC,QAAQ,GAAG,IAAI,CAACpC,gBAAgB;IACpC,IAAIc,UAAU,GAAG,IAAI,CAACf,WAAW;IACjC,IAAI,CAACsC,SAAS,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;IAC9B,IAAI,CAACC,UAAU,CAACN,IAAI,EAAEE,QAAQ,EAAEH,QAAQ,EAAE,IAAI,CAACI,SAAS,CAAC;IACzD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,UAAU,CAAC4B,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIE,CAAC,GAAG7B,UAAU,CAAC2B,CAAC,CAAC;MACrB,IAAI7D,KAAK,GAAG,IAAI,CAACqB,OAAO,CAAC0C,CAAC,CAAC;MAC3B,IAAI,CAAC/D,KAAK,CAACC,WAAW,IAAID,KAAK,CAACG,OAAO,EAAE;QACrC,IAAI6D,UAAU,GAAGH,CAAC,KAAK,CAAC,GAAG,IAAI,CAACI,gBAAgB,GAAG,IAAI;QACvDjE,KAAK,CAACG,OAAO,CAAC6D,UAAU,CAAC;MAC7B;IACJ;IACA,IAAI,IAAI,CAACrC,KAAK,CAACuC,YAAY,EAAE;MACzB,IAAI,CAAC9C,gBAAgB,GAAGkC,IAAI,CAACa,KAAK,CAAC,CAAC;IACxC;IACA,OAAO,IAAI;EACf,CAAC;EACDtD,aAAa,CAACgC,SAAS,CAACuB,YAAY,GAAG,YAAY;IAC/C,IAAI,CAACC,eAAe,CAAC,IAAI,CAACtD,OAAO,CAACwC,cAAc,CAAC,KAAK,CAAC,CAAC;EAC5D,CAAC;EACD1C,aAAa,CAACgC,SAAS,CAACwB,eAAe,GAAG,UAAUf,IAAI,EAAE;IACtD,IAAIgB,GAAG,GAAGhB,IAAI,CAACQ,MAAM;IACrB,IAAIS,UAAU,GAAG,IAAI,CAACC,WAAW;IACjCD,UAAU,IAAIA,UAAU,CAACE,KAAK,CAAC,CAAC;IAChC,IAAI,CAACH,GAAG,EAAE;MACN;IACJ;IACA,IAAII,KAAK,GAAG;MACRC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAI,CAACxC,MAAM;MACtByC,UAAU,EAAE,IAAI,CAACxC;IACrB,CAAC;IACD,IAAIyC,GAAG;IACP,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,GAAG,EAAET,CAAC,EAAE,EAAE;MAC1B,IAAIkB,EAAE,GAAGzB,IAAI,CAACO,CAAC,CAAC;MAChB,IAAIkB,EAAE,CAACC,SAAS,EAAE;QACd,IAAI,CAACT,UAAU,EAAE;UACbA,UAAU,GAAG,IAAI,CAACC,WAAW,GAAG,IAAI,CAACS,QAAQ,CAACtF,kBAAkB,CAAC;QACrE;QACA,IAAI,CAACmF,GAAG,EAAE;UACNA,GAAG,GAAGP,UAAU,CAACO,GAAG;UACpBA,GAAG,CAACI,IAAI,CAAC,CAAC;QACd;QACA3F,KAAK,CAACuF,GAAG,EAAEC,EAAE,EAAEL,KAAK,EAAEb,CAAC,KAAKS,GAAG,GAAG,CAAC,CAAC;MACxC;IACJ;IACA,IAAIQ,GAAG,EAAE;MACLA,GAAG,CAACK,OAAO,CAAC,CAAC;IACjB;EACJ,CAAC;EACDtE,aAAa,CAACgC,SAAS,CAACuC,aAAa,GAAG,YAAY;IAChD,OAAO,IAAI,CAACH,QAAQ,CAACtF,kBAAkB,CAAC;EAC5C,CAAC;EACDkB,aAAa,CAACgC,SAAS,CAACwC,QAAQ,GAAG,UAAUP,GAAG,EAAEC,EAAE,EAAE;IAClDvF,WAAW,CAACsF,GAAG,EAAEC,EAAE,CAAC;EACxB,CAAC;EACDlE,aAAa,CAACgC,SAAS,CAACe,UAAU,GAAG,UAAUN,IAAI,EAAEE,QAAQ,EAAEH,QAAQ,EAAEiC,QAAQ,EAAE;IAC/E,IAAI,IAAI,CAAC7B,SAAS,KAAK6B,QAAQ,EAAE;MAC7B;IACJ;IACAjC,QAAQ,GAAGA,QAAQ,IAAI,KAAK;IAC5B,IAAI,CAACkC,kBAAkB,CAACjC,IAAI,CAAC;IAC7B,IAAIkC,EAAE,GAAG,IAAI,CAACC,YAAY,CAACnC,IAAI,EAAEE,QAAQ,EAAEH,QAAQ,CAAC;MAAEqC,QAAQ,GAAGF,EAAE,CAACE,QAAQ;MAAEC,iBAAiB,GAAGH,EAAE,CAACG,iBAAiB;IACtH,IAAI,IAAI,CAACpE,yBAAyB,EAAE;MAChC,IAAI,CAACqE,kBAAkB,CAAC,CAAC;IAC7B;IACA,IAAID,iBAAiB,EAAE;MACnB,IAAI,CAACtB,eAAe,CAACf,IAAI,CAAC;IAC9B;IACA,IAAI,CAACoC,QAAQ,EAAE;MACX,IAAIG,MAAM,GAAG,IAAI;MACjBxG,qBAAqB,CAAC,YAAY;QAC9BwG,MAAM,CAACjC,UAAU,CAACN,IAAI,EAAEE,QAAQ,EAAEH,QAAQ,EAAEiC,QAAQ,CAAC;MACzD,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACQ,SAAS,CAAC,UAAU9F,KAAK,EAAE;QAC5BA,KAAK,CAAC+F,UAAU,IAAI/F,KAAK,CAAC+F,UAAU,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;EACJ,CAAC;EACDlF,aAAa,CAACgC,SAAS,CAAC+C,kBAAkB,GAAG,YAAY;IACrD,IAAId,GAAG,GAAG,IAAI,CAACG,QAAQ,CAACrF,aAAa,CAAC,CAACkF,GAAG;IAC1C,IAAIzE,KAAK,GAAG,IAAI,CAACiC,QAAQ,CAACjC,KAAK;IAC/B,IAAIC,MAAM,GAAG,IAAI,CAACgC,QAAQ,CAAChC,MAAM;IACjCwE,GAAG,CAACkB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE3F,KAAK,EAAEC,MAAM,CAAC;IAClC,IAAI,CAAC2F,gBAAgB,CAAC,UAAUjG,KAAK,EAAE;MACnC,IAAIA,KAAK,CAACkG,OAAO,EAAE;QACfpB,GAAG,CAACqB,SAAS,CAACnG,KAAK,CAACoG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE/F,KAAK,EAAEC,MAAM,CAAC;MACjD;IACJ,CAAC,CAAC;EACN,CAAC;EACDO,aAAa,CAACgC,SAAS,CAAC4C,YAAY,GAAG,UAAUnC,IAAI,EAAEE,QAAQ,EAAEH,QAAQ,EAAE;IACvE,IAAIgD,KAAK,GAAG,IAAI;IAChB,IAAIC,SAAS,GAAG,EAAE;IAClB,IAAIpC,YAAY,GAAG,IAAI,CAACvC,KAAK,CAACuC,YAAY;IAC1C,KAAK,IAAIqC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,IAAI,CAACpF,WAAW,CAAC2C,MAAM,EAAEyC,EAAE,EAAE,EAAE;MACjD,IAAI5D,MAAM,GAAG,IAAI,CAACxB,WAAW,CAACoF,EAAE,CAAC;MACjC,IAAIvG,KAAK,GAAG,IAAI,CAACqB,OAAO,CAACsB,MAAM,CAAC;MAChC,IAAI3C,KAAK,CAACC,WAAW,IACdD,KAAK,KAAK,IAAI,CAACwE,WAAW,KACzBxE,KAAK,CAACwG,OAAO,IAAInD,QAAQ,CAAC,EAAE;QAChCiD,SAAS,CAAC1D,IAAI,CAAC5C,KAAK,CAAC;MACzB;IACJ;IACA,IAAI0F,QAAQ,GAAG,IAAI;IACnB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIc,OAAO,GAAG,SAAAA,CAAUC,CAAC,EAAE;MACvB,IAAI1G,KAAK,GAAGsG,SAAS,CAACI,CAAC,CAAC;MACxB,IAAI5B,GAAG,GAAG9E,KAAK,CAAC8E,GAAG;MACnB,IAAI6B,YAAY,GAAGzC,YAAY,IACxBlE,KAAK,CAAC4G,kBAAkB,CAACtD,IAAI,EAAEE,QAAQ,EAAEqD,MAAM,CAACzE,MAAM,EAAEyE,MAAM,CAACxE,OAAO,CAAC;MAC9E,IAAIyE,KAAK,GAAGzD,QAAQ,GAAGrD,KAAK,CAAC+G,YAAY,GAAG/G,KAAK,CAACgH,WAAW;MAC7D,IAAIC,QAAQ,GAAG,CAAC5D,QAAQ,IAAIrD,KAAK,CAACkH,WAAW,IAAIC,IAAI,CAACC,GAAG;MACzD,IAAIC,SAAS,GAAGJ,QAAQ,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC;MACtC,IAAIpD,UAAU,GAAGhE,KAAK,CAAC2C,MAAM,KAAKkE,MAAM,CAAC1F,WAAW,CAAC,CAAC,CAAC,GACjD0F,MAAM,CAAC5C,gBAAgB,GAAG,IAAI;MACpC,IAAIjE,KAAK,CAAC+G,YAAY,KAAK/G,KAAK,CAACsH,UAAU,EAAE;QACzCtH,KAAK,CAACyE,KAAK,CAAC,KAAK,EAAET,UAAU,EAAE2C,YAAY,CAAC;MAChD,CAAC,MACI,IAAIG,KAAK,KAAK9G,KAAK,CAAC+G,YAAY,EAAE;QACnC,IAAIQ,OAAO,GAAGjE,IAAI,CAACwD,KAAK,CAAC;QACzB,IAAI,CAACS,OAAO,CAACL,WAAW,IAAI,CAACK,OAAO,CAACC,QAAQ,IAAInE,QAAQ,EAAE;UACvDrD,KAAK,CAACyE,KAAK,CAAC,KAAK,EAAET,UAAU,EAAE2C,YAAY,CAAC;QAChD;MACJ;MACA,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;QACdW,OAAO,CAACC,KAAK,CAAC,0CAA0C,CAAC;QACzDZ,KAAK,GAAG9G,KAAK,CAAC+G,YAAY;MAC9B;MACA,IAAIlD,CAAC;MACL,IAAI8D,OAAO,GAAG,SAAAA,CAAUC,WAAW,EAAE;QACjC,IAAIlD,KAAK,GAAG;UACRC,OAAO,EAAE,KAAK;UACdkD,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE,IAAI;UACZlD,SAAS,EAAEyB,KAAK,CAACjE,MAAM;UACvByC,UAAU,EAAEwB,KAAK,CAAChE;QACtB,CAAC;QACD,KAAKwB,CAAC,GAAGiD,KAAK,EAAEjD,CAAC,GAAG7D,KAAK,CAACsH,UAAU,EAAEzD,CAAC,EAAE,EAAE;UACvC,IAAIkB,EAAE,GAAGzB,IAAI,CAACO,CAAC,CAAC;UAChB,IAAIkB,EAAE,CAACC,SAAS,EAAE;YACdW,iBAAiB,GAAG,IAAI;UAC5B;UACAU,KAAK,CAAC0B,UAAU,CAAChD,EAAE,EAAE/E,KAAK,EAAEkE,YAAY,EAAE0D,WAAW,EAAElD,KAAK,EAAEb,CAAC,KAAK7D,KAAK,CAACsH,UAAU,GAAG,CAAC,CAAC;UACzF,IAAIL,QAAQ,EAAE;YACV,IAAIe,KAAK,GAAGb,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,SAAS;YAClC,IAAIW,KAAK,GAAG,EAAE,EAAE;cACZ;YACJ;UACJ;QACJ;QACA,IAAItD,KAAK,CAACuD,eAAe,EAAE;UACvBnD,GAAG,CAACK,OAAO,CAAC,CAAC;QACjB;MACJ,CAAC;MACD,IAAIwB,YAAY,EAAE;QACd,IAAIA,YAAY,CAAC7C,MAAM,KAAK,CAAC,EAAE;UAC3BD,CAAC,GAAG7D,KAAK,CAACsH,UAAU;QACxB,CAAC,MACI;UACD,IAAIzF,GAAG,GAAGgF,MAAM,CAAChF,GAAG;UACpB,KAAK,IAAIqG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,YAAY,CAAC7C,MAAM,EAAE,EAAEoE,CAAC,EAAE;YAC1C,IAAIC,IAAI,GAAGxB,YAAY,CAACuB,CAAC,CAAC;YAC1BpD,GAAG,CAACI,IAAI,CAAC,CAAC;YACVJ,GAAG,CAACsD,SAAS,CAAC,CAAC;YACftD,GAAG,CAACqD,IAAI,CAACA,IAAI,CAACE,CAAC,GAAGxG,GAAG,EAAEsG,IAAI,CAACG,CAAC,GAAGzG,GAAG,EAAEsG,IAAI,CAAC9H,KAAK,GAAGwB,GAAG,EAAEsG,IAAI,CAAC7H,MAAM,GAAGuB,GAAG,CAAC;YACzEiD,GAAG,CAACyD,IAAI,CAAC,CAAC;YACVZ,OAAO,CAACQ,IAAI,CAAC;YACbrD,GAAG,CAACK,OAAO,CAAC,CAAC;UACjB;QACJ;MACJ,CAAC,MACI;QACDL,GAAG,CAACI,IAAI,CAAC,CAAC;QACVyC,OAAO,CAAC,CAAC;QACT7C,GAAG,CAACK,OAAO,CAAC,CAAC;MACjB;MACAnF,KAAK,CAACgH,WAAW,GAAGnD,CAAC;MACrB,IAAI7D,KAAK,CAACgH,WAAW,GAAGhH,KAAK,CAACsH,UAAU,EAAE;QACtC5B,QAAQ,GAAG,KAAK;MACpB;IACJ,CAAC;IACD,IAAImB,MAAM,GAAG,IAAI;IACjB,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,CAACxC,MAAM,EAAE4C,CAAC,EAAE,EAAE;MACvCD,OAAO,CAACC,CAAC,CAAC;IACd;IACA,IAAIpH,GAAG,CAACkJ,GAAG,EAAE;MACTrJ,IAAI,CAACsJ,IAAI,CAAC,IAAI,CAACpH,OAAO,EAAE,UAAUrB,KAAK,EAAE;QACrC,IAAIA,KAAK,IAAIA,KAAK,CAAC8E,GAAG,IAAI9E,KAAK,CAAC8E,GAAG,CAAC4D,IAAI,EAAE;UACtC1I,KAAK,CAAC8E,GAAG,CAAC4D,IAAI,CAAC,CAAC;QACpB;MACJ,CAAC,CAAC;IACN;IACA,OAAO;MACHhD,QAAQ,EAAEA,QAAQ;MAClBC,iBAAiB,EAAEA;IACvB,CAAC;EACL,CAAC;EACD9E,aAAa,CAACgC,SAAS,CAACkF,UAAU,GAAG,UAAUhD,EAAE,EAAE4D,YAAY,EAAEzE,YAAY,EAAE0D,WAAW,EAAElD,KAAK,EAAEkE,MAAM,EAAE;IACvG,IAAI9D,GAAG,GAAG6D,YAAY,CAAC7D,GAAG;IAC1B,IAAIZ,YAAY,EAAE;MACd,IAAI2E,SAAS,GAAG9D,EAAE,CAAC+D,YAAY,CAAC,CAAC;MACjC,IAAI,CAAClB,WAAW,IAAIiB,SAAS,IAAIA,SAAS,CAACE,SAAS,CAACnB,WAAW,CAAC,EAAE;QAC/DrI,KAAK,CAACuF,GAAG,EAAEC,EAAE,EAAEL,KAAK,EAAEkE,MAAM,CAAC;QAC7B7D,EAAE,CAACiE,gBAAgB,CAACH,SAAS,CAAC;MAClC;IACJ,CAAC,MACI;MACDtJ,KAAK,CAACuF,GAAG,EAAEC,EAAE,EAAEL,KAAK,EAAEkE,MAAM,CAAC;IACjC;EACJ,CAAC;EACD/H,aAAa,CAACgC,SAAS,CAACoC,QAAQ,GAAG,UAAUtC,MAAM,EAAEuD,OAAO,EAAE;IAC1D,IAAI,IAAI,CAACpE,aAAa,IAAI,CAAC,IAAI,CAACP,yBAAyB,EAAE;MACvDoB,MAAM,GAAG/C,aAAa;IAC1B;IACA,IAAII,KAAK,GAAG,IAAI,CAACqB,OAAO,CAACsB,MAAM,CAAC;IAChC,IAAI,CAAC3C,KAAK,EAAE;MACRA,KAAK,GAAG,IAAIZ,KAAK,CAAC,KAAK,GAAGuD,MAAM,EAAE,IAAI,EAAE,IAAI,CAACd,GAAG,CAAC;MACjD7B,KAAK,CAAC2C,MAAM,GAAGA,MAAM;MACrB3C,KAAK,CAACC,WAAW,GAAG,IAAI;MACxB,IAAI,IAAI,CAACqB,YAAY,CAACqB,MAAM,CAAC,EAAE;QAC3BxD,IAAI,CAAC8J,KAAK,CAACjJ,KAAK,EAAE,IAAI,CAACsB,YAAY,CAACqB,MAAM,CAAC,EAAE,IAAI,CAAC;MACtD,CAAC,MACI,IAAI,IAAI,CAACrB,YAAY,CAACqB,MAAM,GAAG9C,wBAAwB,CAAC,EAAE;QAC3DV,IAAI,CAAC8J,KAAK,CAACjJ,KAAK,EAAE,IAAI,CAACsB,YAAY,CAACqB,MAAM,GAAG9C,wBAAwB,CAAC,EAAE,IAAI,CAAC;MACjF;MACA,IAAIqG,OAAO,EAAE;QACTlG,KAAK,CAACkG,OAAO,GAAGA,OAAO;MAC3B;MACA,IAAI,CAACgD,WAAW,CAACvG,MAAM,EAAE3C,KAAK,CAAC;MAC/BA,KAAK,CAAC0C,WAAW,CAAC,CAAC;IACvB;IACA,OAAO1C,KAAK;EAChB,CAAC;EACDa,aAAa,CAACgC,SAAS,CAACqG,WAAW,GAAG,UAAUvG,MAAM,EAAE3C,KAAK,EAAE;IAC3D,IAAImJ,SAAS,GAAG,IAAI,CAAC9H,OAAO;IAC5B,IAAIa,UAAU,GAAG,IAAI,CAACf,WAAW;IACjC,IAAImD,GAAG,GAAGpC,UAAU,CAAC4B,MAAM;IAC3B,IAAIvD,OAAO,GAAG,IAAI,CAAC+B,QAAQ;IAC3B,IAAI8G,SAAS,GAAG,IAAI;IACpB,IAAIvF,CAAC,GAAG,CAAC,CAAC;IACV,IAAIsF,SAAS,CAACxG,MAAM,CAAC,EAAE;MACnB,IAAI0G,OAAO,CAAC/J,GAAG,CAACgK,QAAQ,KAAK,YAAY,EAAE;QACvCnK,IAAI,CAACoK,QAAQ,CAAC,SAAS,GAAG5G,MAAM,GAAG,wBAAwB,CAAC;MAChE;MACA;IACJ;IACA,IAAI,CAAC5C,YAAY,CAACC,KAAK,CAAC,EAAE;MACtB,IAAIqJ,OAAO,CAAC/J,GAAG,CAACgK,QAAQ,KAAK,YAAY,EAAE;QACvCnK,IAAI,CAACoK,QAAQ,CAAC,kBAAkB,GAAG5G,MAAM,GAAG,eAAe,CAAC;MAChE;MACA;IACJ;IACA,IAAI2B,GAAG,GAAG,CAAC,IAAI3B,MAAM,GAAGT,UAAU,CAAC,CAAC,CAAC,EAAE;MACnC,KAAK2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,GAAG,GAAG,CAAC,EAAET,CAAC,EAAE,EAAE;QAC1B,IAAI3B,UAAU,CAAC2B,CAAC,CAAC,GAAGlB,MAAM,IACnBT,UAAU,CAAC2B,CAAC,GAAG,CAAC,CAAC,GAAGlB,MAAM,EAAE;UAC/B;QACJ;MACJ;MACAyG,SAAS,GAAGD,SAAS,CAACjH,UAAU,CAAC2B,CAAC,CAAC,CAAC;IACxC;IACA3B,UAAU,CAACsH,MAAM,CAAC3F,CAAC,GAAG,CAAC,EAAE,CAAC,EAAElB,MAAM,CAAC;IACnCwG,SAAS,CAACxG,MAAM,CAAC,GAAG3C,KAAK;IACzB,IAAI,CAACA,KAAK,CAACkG,OAAO,EAAE;MAChB,IAAIkD,SAAS,EAAE;QACX,IAAIK,OAAO,GAAGL,SAAS,CAAChD,GAAG;QAC3B,IAAIqD,OAAO,CAACC,WAAW,EAAE;UACrBnJ,OAAO,CAACoJ,YAAY,CAAC3J,KAAK,CAACoG,GAAG,EAAEqD,OAAO,CAACC,WAAW,CAAC;QACxD,CAAC,MACI;UACDnJ,OAAO,CAACgC,WAAW,CAACvC,KAAK,CAACoG,GAAG,CAAC;QAClC;MACJ,CAAC,MACI;QACD,IAAI7F,OAAO,CAACqJ,UAAU,EAAE;UACpBrJ,OAAO,CAACoJ,YAAY,CAAC3J,KAAK,CAACoG,GAAG,EAAE7F,OAAO,CAACqJ,UAAU,CAAC;QACvD,CAAC,MACI;UACDrJ,OAAO,CAACgC,WAAW,CAACvC,KAAK,CAACoG,GAAG,CAAC;QAClC;MACJ;IACJ;IACApG,KAAK,CAAC6J,OAAO,KAAK7J,KAAK,CAAC6J,OAAO,GAAG,IAAI,CAAC;EAC3C,CAAC;EACDhJ,aAAa,CAACgC,SAAS,CAACiD,SAAS,GAAG,UAAUgE,EAAE,EAAEC,OAAO,EAAE;IACvD,IAAI7H,UAAU,GAAG,IAAI,CAACf,WAAW;IACjC,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,UAAU,CAAC4B,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIE,CAAC,GAAG7B,UAAU,CAAC2B,CAAC,CAAC;MACrBiG,EAAE,CAACE,IAAI,CAACD,OAAO,EAAE,IAAI,CAAC1I,OAAO,CAAC0C,CAAC,CAAC,EAAEA,CAAC,CAAC;IACxC;EACJ,CAAC;EACDlD,aAAa,CAACgC,SAAS,CAACoD,gBAAgB,GAAG,UAAU6D,EAAE,EAAEC,OAAO,EAAE;IAC9D,IAAI7H,UAAU,GAAG,IAAI,CAACf,WAAW;IACjC,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,UAAU,CAAC4B,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIE,CAAC,GAAG7B,UAAU,CAAC2B,CAAC,CAAC;MACrB,IAAI7D,KAAK,GAAG,IAAI,CAACqB,OAAO,CAAC0C,CAAC,CAAC;MAC3B,IAAI/D,KAAK,CAACC,WAAW,EAAE;QACnB6J,EAAE,CAACE,IAAI,CAACD,OAAO,EAAE/J,KAAK,EAAE+D,CAAC,CAAC;MAC9B;IACJ;EACJ,CAAC;EACDlD,aAAa,CAACgC,SAAS,CAACoH,cAAc,GAAG,UAAUH,EAAE,EAAEC,OAAO,EAAE;IAC5D,IAAI7H,UAAU,GAAG,IAAI,CAACf,WAAW;IACjC,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,UAAU,CAAC4B,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIE,CAAC,GAAG7B,UAAU,CAAC2B,CAAC,CAAC;MACrB,IAAI7D,KAAK,GAAG,IAAI,CAACqB,OAAO,CAAC0C,CAAC,CAAC;MAC3B,IAAI,CAAC/D,KAAK,CAACC,WAAW,EAAE;QACpB6J,EAAE,CAACE,IAAI,CAACD,OAAO,EAAE/J,KAAK,EAAE+D,CAAC,CAAC;MAC9B;IACJ;EACJ,CAAC;EACDlD,aAAa,CAACgC,SAAS,CAACqH,SAAS,GAAG,YAAY;IAC5C,OAAO,IAAI,CAAC7I,OAAO;EACvB,CAAC;EACDR,aAAa,CAACgC,SAAS,CAAC0C,kBAAkB,GAAG,UAAUjC,IAAI,EAAE;IACzD,IAAI,CAAC2C,gBAAgB,CAAC,UAAUjG,KAAK,EAAE+D,CAAC,EAAE;MACtC/D,KAAK,CAACwG,OAAO,GAAGxG,KAAK,CAACmK,MAAM,GAAG,KAAK;IACxC,CAAC,CAAC;IACF,SAASC,eAAeA,CAACC,GAAG,EAAE;MAC1B,IAAIjB,SAAS,EAAE;QACX,IAAIA,SAAS,CAAC9B,UAAU,KAAK+C,GAAG,EAAE;UAC9BjB,SAAS,CAAC5C,OAAO,GAAG,IAAI;QAC5B;QACA4C,SAAS,CAAC9B,UAAU,GAAG+C,GAAG;MAC9B;IACJ;IACA,IAAI,IAAI,CAACvI,aAAa,EAAE;MACpB,KAAK,IAAIwI,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGhH,IAAI,CAACQ,MAAM,EAAEwG,GAAG,EAAE,EAAE;QACxC,IAAIvF,EAAE,GAAGzB,IAAI,CAACgH,GAAG,CAAC;QAClB,IAAIvF,EAAE,CAACpC,MAAM,KAAKW,IAAI,CAACgH,GAAG,GAAG,CAAC,CAAC,CAAC3H,MAAM,IAAIoC,EAAE,CAACmC,WAAW,EAAE;UACtD,IAAI,CAAC3F,yBAAyB,GAAG,IAAI;UACrC;QACJ;MACJ;IACJ;IACA,IAAI6H,SAAS,GAAG,IAAI;IACpB,IAAImB,qBAAqB,GAAG,CAAC;IAC7B,IAAIC,UAAU;IACd,IAAI3G,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9B,IAAIkB,EAAE,GAAGzB,IAAI,CAACO,CAAC,CAAC;MAChB,IAAIlB,MAAM,GAAGoC,EAAE,CAACpC,MAAM;MACtB,IAAI3C,KAAK,GAAG,KAAK,CAAC;MAClB,IAAIwK,UAAU,KAAK7H,MAAM,EAAE;QACvB6H,UAAU,GAAG7H,MAAM;QACnB4H,qBAAqB,GAAG,CAAC;MAC7B;MACA,IAAIxF,EAAE,CAACmC,WAAW,EAAE;QAChBlH,KAAK,GAAG,IAAI,CAACiF,QAAQ,CAACtC,MAAM,GAAG7C,eAAe,EAAE,IAAI,CAACyB,yBAAyB,CAAC;QAC/EvB,KAAK,CAACkH,WAAW,GAAG,IAAI;QACxBqD,qBAAqB,GAAG,CAAC;MAC7B,CAAC,MACI;QACDvK,KAAK,GAAG,IAAI,CAACiF,QAAQ,CAACtC,MAAM,IAAI4H,qBAAqB,GAAG,CAAC,GAAG1K,wBAAwB,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC0B,yBAAyB,CAAC;MAC9H;MACA,IAAI,CAACvB,KAAK,CAACC,WAAW,EAAE;QACpBd,IAAI,CAACoK,QAAQ,CAAC,SAAS,GAAG5G,MAAM,GAAG,iCAAiC,GAAG3C,KAAK,CAACiB,EAAE,CAAC;MACpF;MACA,IAAIjB,KAAK,KAAKoJ,SAAS,EAAE;QACrBpJ,KAAK,CAACmK,MAAM,GAAG,IAAI;QACnB,IAAInK,KAAK,CAAC+G,YAAY,KAAKlD,CAAC,EAAE;UAC1B7D,KAAK,CAACwG,OAAO,GAAG,IAAI;QACxB;QACAxG,KAAK,CAAC+G,YAAY,GAAGlD,CAAC;QACtB,IAAI,CAAC7D,KAAK,CAACkH,WAAW,EAAE;UACpBlH,KAAK,CAACgH,WAAW,GAAGnD,CAAC;QACzB,CAAC,MACI;UACD7D,KAAK,CAACgH,WAAW,GAAG,CAAC,CAAC;QAC1B;QACAoD,eAAe,CAACvG,CAAC,CAAC;QAClBuF,SAAS,GAAGpJ,KAAK;MACrB;MACA,IAAK+E,EAAE,CAACyB,OAAO,GAAG/G,UAAU,IAAK,CAACsF,EAAE,CAACC,SAAS,EAAE;QAC5ChF,KAAK,CAACwG,OAAO,GAAG,IAAI;QACpB,IAAIxG,KAAK,CAACkH,WAAW,IAAIlH,KAAK,CAACgH,WAAW,GAAG,CAAC,EAAE;UAC5ChH,KAAK,CAACgH,WAAW,GAAGnD,CAAC;QACzB;MACJ;IACJ;IACAuG,eAAe,CAACvG,CAAC,CAAC;IAClB,IAAI,CAACoC,gBAAgB,CAAC,UAAUjG,KAAK,EAAE+D,CAAC,EAAE;MACtC,IAAI,CAAC/D,KAAK,CAACmK,MAAM,IAAInK,KAAK,CAACyK,eAAe,CAAC,CAAC,GAAG,CAAC,EAAE;QAC9CzK,KAAK,CAACwG,OAAO,GAAG,IAAI;QACpBxG,KAAK,CAAC+G,YAAY,GAAG/G,KAAK,CAACsH,UAAU,GAAGtH,KAAK,CAACgH,WAAW,GAAG,CAAC;MACjE;MACA,IAAIhH,KAAK,CAACwG,OAAO,IAAIxG,KAAK,CAACgH,WAAW,GAAG,CAAC,EAAE;QACxChH,KAAK,CAACgH,WAAW,GAAGhH,KAAK,CAAC+G,YAAY;MAC1C;IACJ,CAAC,CAAC;EACN,CAAC;EACDlG,aAAa,CAACgC,SAAS,CAAC4B,KAAK,GAAG,YAAY;IACxC,IAAI,CAACwB,gBAAgB,CAAC,IAAI,CAACyE,WAAW,CAAC;IACvC,OAAO,IAAI;EACf,CAAC;EACD7J,aAAa,CAACgC,SAAS,CAAC6H,WAAW,GAAG,UAAU1K,KAAK,EAAE;IACnDA,KAAK,CAACyE,KAAK,CAAC,CAAC;EACjB,CAAC;EACD5D,aAAa,CAACgC,SAAS,CAAC8H,kBAAkB,GAAG,UAAUC,eAAe,EAAE;IACpE,IAAI,CAAC3G,gBAAgB,GAAG2G,eAAe;IACvCzL,IAAI,CAACsJ,IAAI,CAAC,IAAI,CAACpH,OAAO,EAAE,UAAUrB,KAAK,EAAE;MACrCA,KAAK,CAAC6K,YAAY,CAAC,CAAC;IACxB,CAAC,CAAC;EACN,CAAC;EACDhK,aAAa,CAACgC,SAAS,CAACiI,WAAW,GAAG,UAAUnI,MAAM,EAAEoI,MAAM,EAAE;IAC5D,IAAIA,MAAM,EAAE;MACR,IAAIC,WAAW,GAAG,IAAI,CAAC1J,YAAY;MACnC,IAAI,CAAC0J,WAAW,CAACrI,MAAM,CAAC,EAAE;QACtBqI,WAAW,CAACrI,MAAM,CAAC,GAAGoI,MAAM;MAChC,CAAC,MACI;QACD5L,IAAI,CAAC8J,KAAK,CAAC+B,WAAW,CAACrI,MAAM,CAAC,EAAEoI,MAAM,EAAE,IAAI,CAAC;MACjD;MACA,KAAK,IAAIlH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC1C,WAAW,CAAC2C,MAAM,EAAED,CAAC,EAAE,EAAE;QAC9C,IAAIoH,OAAO,GAAG,IAAI,CAAC9J,WAAW,CAAC0C,CAAC,CAAC;QACjC,IAAIoH,OAAO,KAAKtI,MAAM,IAAIsI,OAAO,KAAKtI,MAAM,GAAG9C,wBAAwB,EAAE;UACrE,IAAIG,KAAK,GAAG,IAAI,CAACqB,OAAO,CAAC4J,OAAO,CAAC;UACjC9L,IAAI,CAAC8J,KAAK,CAACjJ,KAAK,EAAEgL,WAAW,CAACrI,MAAM,CAAC,EAAE,IAAI,CAAC;QAChD;MACJ;IACJ;EACJ,CAAC;EACD9B,aAAa,CAACgC,SAAS,CAACqI,QAAQ,GAAG,UAAUvI,MAAM,EAAE;IACjD,IAAIR,MAAM,GAAG,IAAI,CAACd,OAAO;IACzB,IAAIa,UAAU,GAAG,IAAI,CAACf,WAAW;IACjC,IAAInB,KAAK,GAAGmC,MAAM,CAACQ,MAAM,CAAC;IAC1B,IAAI,CAAC3C,KAAK,EAAE;MACR;IACJ;IACAA,KAAK,CAACoG,GAAG,CAAC+E,UAAU,CAACC,WAAW,CAACpL,KAAK,CAACoG,GAAG,CAAC;IAC3C,OAAOjE,MAAM,CAACQ,MAAM,CAAC;IACrBT,UAAU,CAACsH,MAAM,CAACrK,IAAI,CAACkM,OAAO,CAACnJ,UAAU,EAAES,MAAM,CAAC,EAAE,CAAC,CAAC;EAC1D,CAAC;EACD9B,aAAa,CAACgC,SAAS,CAAC3C,MAAM,GAAG,UAAUG,KAAK,EAAEC,MAAM,EAAE;IACtD,IAAI,CAAC,IAAI,CAACgC,QAAQ,CAAC5B,KAAK,EAAE;MACtB,IAAIL,KAAK,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE;QACjC;MACJ;MACA,IAAI,CAAC8B,MAAM,GAAG/B,KAAK;MACnB,IAAI,CAACgC,OAAO,GAAG/B,MAAM;MACrB,IAAI,CAAC2E,QAAQ,CAACrF,aAAa,CAAC,CAACM,MAAM,CAACG,KAAK,EAAEC,MAAM,CAAC;IACtD,CAAC,MACI;MACD,IAAIC,OAAO,GAAG,IAAI,CAAC+B,QAAQ;MAC3B/B,OAAO,CAACG,KAAK,CAAC4K,OAAO,GAAG,MAAM;MAC9B,IAAItK,IAAI,GAAG,IAAI,CAACW,KAAK;MACrB,IAAIb,IAAI,GAAG,IAAI,CAACA,IAAI;MACpBT,KAAK,IAAI,IAAI,KAAKW,IAAI,CAACX,KAAK,GAAGA,KAAK,CAAC;MACrCC,MAAM,IAAI,IAAI,KAAKU,IAAI,CAACV,MAAM,GAAGA,MAAM,CAAC;MACxCD,KAAK,GAAGX,OAAO,CAACoB,IAAI,EAAE,CAAC,EAAEE,IAAI,CAAC;MAC9BV,MAAM,GAAGZ,OAAO,CAACoB,IAAI,EAAE,CAAC,EAAEE,IAAI,CAAC;MAC/BT,OAAO,CAACG,KAAK,CAAC4K,OAAO,GAAG,EAAE;MAC1B,IAAI,IAAI,CAAClJ,MAAM,KAAK/B,KAAK,IAAIC,MAAM,KAAK,IAAI,CAAC+B,OAAO,EAAE;QAClD9B,OAAO,CAACG,KAAK,CAACL,KAAK,GAAGA,KAAK,GAAG,IAAI;QAClCE,OAAO,CAACG,KAAK,CAACJ,MAAM,GAAGA,MAAM,GAAG,IAAI;QACpC,KAAK,IAAIW,EAAE,IAAI,IAAI,CAACI,OAAO,EAAE;UACzB,IAAI,IAAI,CAACA,OAAO,CAACkK,cAAc,CAACtK,EAAE,CAAC,EAAE;YACjC,IAAI,CAACI,OAAO,CAACJ,EAAE,CAAC,CAACf,MAAM,CAACG,KAAK,EAAEC,MAAM,CAAC;UAC1C;QACJ;QACA,IAAI,CAACH,OAAO,CAAC,IAAI,CAAC;MACtB;MACA,IAAI,CAACiC,MAAM,GAAG/B,KAAK;MACnB,IAAI,CAACgC,OAAO,GAAG/B,MAAM;IACzB;IACA,OAAO,IAAI;EACf,CAAC;EACDO,aAAa,CAACgC,SAAS,CAAC2I,UAAU,GAAG,UAAU7I,MAAM,EAAE;IACnD,IAAI3C,KAAK,GAAG,IAAI,CAACqB,OAAO,CAACsB,MAAM,CAAC;IAChC,IAAI3C,KAAK,EAAE;MACPA,KAAK,CAACyE,KAAK,CAAC,CAAC;IACjB;EACJ,CAAC;EACD5D,aAAa,CAACgC,SAAS,CAAC4I,OAAO,GAAG,YAAY;IAC1C,IAAI,CAAC3K,IAAI,CAACmB,SAAS,GAAG,EAAE;IACxB,IAAI,CAACnB,IAAI,GACL,IAAI,CAACC,OAAO,GACR,IAAI,CAACuB,QAAQ,GACT,IAAI,CAACjB,OAAO,GAAG,IAAI;EACnC,CAAC;EACDR,aAAa,CAACgC,SAAS,CAAC6I,iBAAiB,GAAG,UAAU1K,IAAI,EAAE;IACxDA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,IAAI,IAAI,CAACc,aAAa,IAAI,CAAC,IAAI,CAAC8D,kBAAkB,EAAE;MAChD,OAAO,IAAI,CAACvE,OAAO,CAACzB,aAAa,CAAC,CAACwG,GAAG;IAC1C;IACA,IAAIuF,UAAU,GAAG,IAAIvM,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE4B,IAAI,CAAC4K,UAAU,IAAI,IAAI,CAAC/J,GAAG,CAAC;IACtE8J,UAAU,CAACjJ,WAAW,CAAC,CAAC;IACxBiJ,UAAU,CAAClH,KAAK,CAAC,KAAK,EAAEzD,IAAI,CAAC4J,eAAe,IAAI,IAAI,CAAC3G,gBAAgB,CAAC;IACtE,IAAIa,GAAG,GAAG6G,UAAU,CAAC7G,GAAG;IACxB,IAAI9D,IAAI,CAAC4K,UAAU,IAAI,IAAI,CAAC/J,GAAG,EAAE;MAC7B,IAAI,CAAC1B,OAAO,CAAC,CAAC;MACd,IAAI0L,OAAO,GAAGF,UAAU,CAACvF,GAAG,CAAC/F,KAAK;MAClC,IAAIyL,QAAQ,GAAGH,UAAU,CAACvF,GAAG,CAAC9F,MAAM;MACpC,IAAI,CAACwF,SAAS,CAAC,UAAU9F,KAAK,EAAE;QAC5B,IAAIA,KAAK,CAACC,WAAW,EAAE;UACnB6E,GAAG,CAACqB,SAAS,CAACnG,KAAK,CAACoG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEyF,OAAO,EAAEC,QAAQ,CAAC;QACrD,CAAC,MACI,IAAI9L,KAAK,CAAC+L,cAAc,EAAE;UAC3BjH,GAAG,CAACI,IAAI,CAAC,CAAC;UACVlF,KAAK,CAAC+L,cAAc,CAACjH,GAAG,CAAC;UACzBA,GAAG,CAACK,OAAO,CAAC,CAAC;QACjB;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAIT,KAAK,GAAG;QACRC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,IAAI,CAACxC,MAAM;QACtByC,UAAU,EAAE,IAAI,CAACxC;MACrB,CAAC;MACD,IAAI2J,WAAW,GAAG,IAAI,CAACjL,OAAO,CAACwC,cAAc,CAAC,IAAI,CAAC;MACnD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAES,GAAG,GAAG0H,WAAW,CAAClI,MAAM,EAAED,CAAC,GAAGS,GAAG,EAAET,CAAC,EAAE,EAAE;QACpD,IAAIkB,EAAE,GAAGiH,WAAW,CAACnI,CAAC,CAAC;QACvBtE,KAAK,CAACuF,GAAG,EAAEC,EAAE,EAAEL,KAAK,EAAEb,CAAC,KAAKS,GAAG,GAAG,CAAC,CAAC;MACxC;IACJ;IACA,OAAOqH,UAAU,CAACvF,GAAG;EACzB,CAAC;EACDvF,aAAa,CAACgC,SAAS,CAACoJ,QAAQ,GAAG,YAAY;IAC3C,OAAO,IAAI,CAAC7J,MAAM;EACtB,CAAC;EACDvB,aAAa,CAACgC,SAAS,CAACqJ,SAAS,GAAG,YAAY;IAC5C,OAAO,IAAI,CAAC7J,OAAO;EACvB,CAAC;EACD,OAAOxB,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,eAAeA,aAAa;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}