{"ast": null, "code": "export default {\n  name: \"Auth\",\n  data() {\n    return {};\n  },\n  created() {},\n  methods: {}\n};", "map": {"version": 3, "names": ["name", "data", "created", "methods"], "sources": ["src/views/manager/403.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div style=\"height: calc(100vh - 80px); overflow: hidden; display: flex; align-items: center; justify-content: center\">\r\n      <div style=\"font-size: 40px\">无权访问 <router-link to=\"/\">返回首页</router-link></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Auth\",\r\n  data() {\r\n    return {}\r\n  },\r\n  created() {\r\n\r\n  },\r\n  methods: {}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>"], "mappings": "AASA;EACAA,IAAA;EACAC,KAAA;IACA;EACA;EACAC,QAAA,GAEA;EACAC,OAAA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}