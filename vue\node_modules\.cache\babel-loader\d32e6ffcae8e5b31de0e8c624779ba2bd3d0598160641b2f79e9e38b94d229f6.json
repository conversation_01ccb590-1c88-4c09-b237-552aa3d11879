{"ast": null, "code": "import \"core-js/modules/esnext.iterator.map.js\";\nexport default {\n  name: \"Freemovies\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前页码\n      pageSize: 10,\n      // 每页显示个数\n      total: 0,\n      // 总记录数\n      name: null,\n      // 搜索关键字\n      fromVisible: false,\n      form: {},\n      // 弹窗表单数据\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      rules: {\n        // 可根据需要增加表单校验\n      },\n      ids: [],\n      // 控制视频播放的Dialog\n      videoVisible: false,\n      // 是否显示播放视频的弹窗\n      currentVideoUrl: null // 当前要播放的视频地址\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    // 新增\n    handleAdd() {\n      this.form = {};\n      this.fromVisible = true;\n    },\n    // 编辑\n    handleEdit(row) {\n      this.form = JSON.parse(JSON.stringify(row));\n      this.fromVisible = true;\n    },\n    // 图片上传成功回调\n    handleImgSuccess(res) {\n      // 假设后端返回 { code: '200', data: 'http://xxx/xxx.jpg' }\n      if (res.data) {\n        this.form.sfCoverImage = res.data;\n      } else {\n        this.$message.error('图片上传失败，后端未返回有效地址');\n      }\n    },\n    // 视频上传成功回调\n    handleVideoSuccess(res) {\n      // 假设后端返回 { code: '200', data: 'http://xxx/xxx.mp4' }\n      if (res.data) {\n        this.form.sfVideoUrl = res.data;\n      } else {\n        this.$message.error('视频上传失败，后端未返回有效地址');\n      }\n    },\n    // 点击\"播放视频\"按钮\n    playVideo(url) {\n      console.log('准备播放视频地址: ', url);\n      this.currentVideoUrl = url;\n      this.videoVisible = true;\n    },\n    // 视频弹窗关闭\n    handleVideoClose() {\n      // 关闭时，暂停并清空 src\n      this.currentVideoUrl = null;\n    },\n    // 保存（新增 / 更新）\n    save() {\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          const url = this.form.id ? '/freemovies/update' : '/freemovies/add';\n          const method = this.form.id ? 'PUT' : 'POST';\n          this.$request({\n            url,\n            method,\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              this.$message.success('保存成功');\n              this.load(1);\n              this.fromVisible = false;\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    },\n    // 删除单条记录\n    del(id) {\n      this.$confirm('您确定删除吗？', '确认删除', {\n        type: \"warning\"\n      }).then(_ => {\n        this.$request.delete('/freemovies/delete/' + id).then(res => {\n          if (res.code === '200') {\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {});\n    },\n    // 批量删除\n    handleSelectionChange(rows) {\n      this.ids = rows.map(v => v.id);\n    },\n    delBatch() {\n      if (!this.ids.length) {\n        this.$message.warning('请选择数据');\n        return;\n      }\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {\n        type: \"warning\"\n      }).then(_ => {\n        this.$request.delete('/freemovies/delete/batch', {\n          data: this.ids\n        }).then(res => {\n          if (res.code === '200') {\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {});\n    },\n    // 分页查询\n    load(pageNum) {\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/freemovies/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          name: this.name\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list;\n          this.total = res.data?.total;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    // 重置\n    reset() {\n      this.name = null;\n      this.load(1);\n    },\n    // 分页组件切换页码\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "fromVisible", "form", "user", "JSON", "parse", "localStorage", "getItem", "rules", "ids", "videoVisible", "currentVideoUrl", "created", "load", "methods", "handleAdd", "handleEdit", "row", "stringify", "handleImgSuccess", "res", "sfCoverImage", "$message", "error", "handleVideoSuccess", "sfVideoUrl", "playVideo", "url", "console", "log", "handleVideoClose", "save", "$refs", "formRef", "validate", "valid", "id", "method", "$request", "then", "code", "success", "msg", "del", "$confirm", "type", "_", "delete", "catch", "handleSelectionChange", "rows", "map", "v", "delBatch", "length", "warning", "get", "params", "list", "reset", "handleCurrentChange"], "sources": ["src/views/manager/Freemovies.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <!-- 搜索区域 -->\r\n        <div class=\"search\">\r\n            <el-input\r\n                placeholder=\"请输入关键字查询\"\r\n                style=\"width: 200px\"\r\n                v-model=\"name\"\r\n            ></el-input>\r\n            <el-button\r\n                type=\"info\"\r\n                plain\r\n                style=\"margin-left: 10px\"\r\n                @click=\"load(1)\"\r\n            >\r\n                查询\r\n            </el-button>\r\n            <el-button\r\n                type=\"warning\"\r\n                plain\r\n                style=\"margin-left: 10px\"\r\n                @click=\"reset\"\r\n            >\r\n                重置\r\n            </el-button>\r\n        </div>\r\n\r\n        <!-- 操作按钮 -->\r\n        <div class=\"operation\">\r\n            <el-button type=\"primary\" plain @click=\"handleAdd\">新增</el-button>\r\n            <el-button type=\"danger\" plain @click=\"delBatch\">批量删除</el-button>\r\n        </div>\r\n\r\n        <!-- 表格展示 -->\r\n        <div class=\"table\">\r\n            <el-table\r\n                :data=\"tableData\"\r\n                stripe\r\n                @selection-change=\"handleSelectionChange\"\r\n            >\r\n                <!-- 多选框列 -->\r\n                <el-table-column\r\n                    type=\"selection\"\r\n                    width=\"55\"\r\n                    align=\"center\"\r\n                ></el-table-column>\r\n\r\n                <!-- 课程ID -->\r\n                <el-table-column\r\n                    prop=\"id\"\r\n                    label=\"序号\"\r\n                    width=\"70\"\r\n                    align=\"center\"\r\n                    sortable\r\n                ></el-table-column>\r\n\r\n                <!-- 课程封面 -->\r\n                <el-table-column prop=\"sfCoverImage\" label=\"课程封面\" width=\"150\">\r\n                    <template v-slot=\"scope\">\r\n                        <!-- 当有封面地址时进行图片回显预览 -->\r\n                        <el-image\r\n                            v-if=\"scope.row.sfCoverImage\"\r\n                            :src=\"scope.row.sfCoverImage\"\r\n                            style=\"width: 60px; height: 40px; border-radius: 10px\"\r\n                            :preview-src-list=\"[scope.row.sfCoverImage]\"\r\n                        />\r\n                    </template>\r\n                </el-table-column>\r\n\r\n                <!-- 课程名称 -->\r\n                <el-table-column prop=\"name\" label=\"课程名称\"></el-table-column>\r\n                <!-- 课程介绍 -->\r\n                <el-table-column prop=\"content\" label=\"课程介绍\"></el-table-column>\r\n                <!-- 课程类型 -->\r\n                <el-table-column prop=\"sfCategory\" label=\"课程类型\"></el-table-column>\r\n                <!-- 课程视频 -->\r\n                <el-table-column prop=\"sfVideoUrl\" label=\"课程视频\">\r\n                    <template v-slot=\"scope\">\r\n                        <el-button\r\n                            v-if=\"scope.row.sfVideoUrl\"\r\n                            size=\"mini\"\r\n                            type=\"primary\"\r\n                            @click=\"playVideo(scope.row.sfVideoUrl)\"\r\n                        >\r\n                            播放视频\r\n                        </el-button>\r\n                    </template>\r\n                </el-table-column>\r\n                <!-- 发布时间 -->\r\n                <el-table-column prop=\"time\" label=\"发布时间\"></el-table-column>\r\n                <!-- 用户id -->\r\n                <el-table-column prop=\"sfAuthorId\" label=\"用户id\"></el-table-column>\r\n                <!-- 用户名字 -->\r\n                <el-table-column prop=\"sfAuthorName\" label=\"用户名字\"></el-table-column>\r\n\r\n                <!-- 操作列 -->\r\n                <el-table-column label=\"操作\" align=\"center\" width=\"180\">\r\n                    <template v-slot=\"scope\">\r\n                        <el-button\r\n                            size=\"mini\"\r\n                            type=\"primary\"\r\n                            plain\r\n                            @click=\"handleEdit(scope.row)\"\r\n                        >\r\n                            编辑\r\n                        </el-button>\r\n                        <el-button\r\n                            size=\"mini\"\r\n                            type=\"danger\"\r\n                            plain\r\n                            @click=\"del(scope.row.id)\"\r\n                        >\r\n                            删除\r\n                        </el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 分页 -->\r\n            <div class=\"pagination\">\r\n                <el-pagination\r\n                    background\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageNum\"\r\n                    :page-sizes=\"[5, 10, 20]\"\r\n                    :page-size=\"pageSize\"\r\n                    layout=\"total, prev, pager, next\"\r\n                    :total=\"total\"\r\n                >\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 新增 / 编辑弹窗 -->\r\n        <el-dialog\r\n            title=\"免费课程\"\r\n            :visible.sync=\"fromVisible\"\r\n            width=\"40%\"\r\n            :close-on-click-modal=\"false\"\r\n            destroy-on-close\r\n        >\r\n            <el-form\r\n                :model=\"form\"\r\n                label-width=\"100px\"\r\n                style=\"padding-right: 50px\"\r\n                :rules=\"rules\"\r\n                ref=\"formRef\"\r\n            >\r\n                <!-- 课程封面 - 图片上传 -->\r\n                <el-form-item label=\"课程封面\" prop=\"sfCoverImage\">\r\n                    <el-upload\r\n                        class=\"avatar-uploader\"\r\n                        :action=\"$baseUrl + '/files/upload'\"\r\n                        :headers=\"{ token: user.token }\"\r\n                        list-type=\"picture\"\r\n                        :on-success=\"handleImgSuccess\"\r\n                        accept=\"image/*\"\r\n                    >\r\n                        <el-button type=\"primary\">上传图片</el-button>\r\n                    </el-upload>\r\n                    <!-- 回显已上传的图片 -->\r\n                    <el-image\r\n                        v-if=\"form.sfCoverImage\"\r\n                        :src=\"form.sfCoverImage\"\r\n                        style=\"width: 60px; height: 40px; border-radius: 10px; margin-top: 5px;\"\r\n                        :preview-src-list=\"[form.sfCoverImage]\"\r\n                    />\r\n                </el-form-item>\r\n\r\n                <!-- 课程名称 -->\r\n                <el-form-item label=\"课程名称\" prop=\"name\">\r\n                    <el-input v-model=\"form.name\" placeholder=\"课程名称\"></el-input>\r\n                </el-form-item>\r\n\r\n                <!-- 课程介绍 -->\r\n                <el-form-item label=\"课程介绍\" prop=\"content\">\r\n                    <el-input v-model=\"form.content\" placeholder=\"课程介绍\"></el-input>\r\n                </el-form-item>\r\n\r\n                <!-- 课程类型 -->\r\n                <el-form-item label=\"课程类型\" prop=\"sfCategory\">\r\n                    <el-input v-model=\"form.sfCategory\" placeholder=\"课程类型\"></el-input>\r\n                </el-form-item>\r\n\r\n                <!-- 课程视频 - 上传组件 -->\r\n                <el-form-item label=\"课程视频\" prop=\"sfVideoUrl\">\r\n                    <el-upload\r\n                        class=\"video-uploader\"\r\n                        :action=\"$baseUrl + '/files/upload'\"\r\n                        :headers=\"{ token: user.token }\"\r\n                        :on-success=\"handleVideoSuccess\"\r\n                        accept=\"video/*\"\r\n                        :limit=\"1\"\r\n                        list-type=\"text\"\r\n                    >\r\n                        <el-button type=\"primary\">点击上传视频</el-button>\r\n                    </el-upload>\r\n                    <!-- 回显已上传的视频 -->\r\n                    <div v-if=\"form.sfVideoUrl\" style=\"margin-top: 5px;\">\r\n                        已上传视频，点击播放：\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            size=\"mini\"\r\n                            @click=\"playVideo(form.sfVideoUrl)\"\r\n                        >\r\n                            播放视频\r\n                        </el-button>\r\n                    </div>\r\n                </el-form-item>\r\n            </el-form>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"fromVisible = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 播放视频的弹窗 -->\r\n        <el-dialog\r\n            title=\"播放视频\"\r\n            :visible.sync=\"videoVisible\"\r\n            width=\"60%\"\r\n            @close=\"handleVideoClose\"\r\n            append-to-body\r\n        >\r\n            <!-- v-if 避免 src 为空时报错 -->\r\n            <video\r\n                v-if=\"currentVideoUrl\"\r\n                :src=\"currentVideoUrl\"\r\n                controls\r\n                autoplay\r\n                style=\"width: 100%; height: auto;\"\r\n            ></video>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Freemovies\",\r\n    data() {\r\n        return {\r\n            tableData: [],    // 所有的数据\r\n            pageNum: 1,       // 当前页码\r\n            pageSize: 10,     // 每页显示个数\r\n            total: 0,         // 总记录数\r\n            name: null,       // 搜索关键字\r\n            fromVisible: false,\r\n            form: {},         // 弹窗表单数据\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n            rules: {\r\n                // 可根据需要增加表单校验\r\n            },\r\n            ids: [],\r\n            // 控制视频播放的Dialog\r\n            videoVisible: false,  // 是否显示播放视频的弹窗\r\n            currentVideoUrl: null // 当前要播放的视频地址\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)\r\n    },\r\n    methods: {\r\n        // 新增\r\n        handleAdd() {\r\n            this.form = {}\r\n            this.fromVisible = true\r\n        },\r\n        // 编辑\r\n        handleEdit(row) {\r\n            this.form = JSON.parse(JSON.stringify(row))\r\n            this.fromVisible = true\r\n        },\r\n\r\n        // 图片上传成功回调\r\n        handleImgSuccess(res) {\r\n            // 假设后端返回 { code: '200', data: 'http://xxx/xxx.jpg' }\r\n            if (res.data) {\r\n                this.form.sfCoverImage = res.data\r\n            } else {\r\n                this.$message.error('图片上传失败，后端未返回有效地址')\r\n            }\r\n        },\r\n\r\n        // 视频上传成功回调\r\n        handleVideoSuccess(res) {\r\n            // 假设后端返回 { code: '200', data: 'http://xxx/xxx.mp4' }\r\n            if (res.data) {\r\n                this.form.sfVideoUrl = res.data\r\n            } else {\r\n                this.$message.error('视频上传失败，后端未返回有效地址')\r\n            }\r\n        },\r\n\r\n        // 点击\"播放视频\"按钮\r\n        playVideo(url) {\r\n            console.log('准备播放视频地址: ', url)\r\n            this.currentVideoUrl = url\r\n            this.videoVisible = true\r\n        },\r\n\r\n        // 视频弹窗关闭\r\n        handleVideoClose() {\r\n            // 关闭时，暂停并清空 src\r\n            this.currentVideoUrl = null\r\n        },\r\n\r\n        // 保存（新增 / 更新）\r\n        save() {\r\n            this.$refs.formRef.validate((valid) => {\r\n                if (valid) {\r\n                    const url = this.form.id ? '/freemovies/update' : '/freemovies/add'\r\n                    const method = this.form.id ? 'PUT' : 'POST'\r\n                    this.$request({ url, method, data: this.form }).then(res => {\r\n                        if (res.code === '200') {\r\n                            this.$message.success('保存成功')\r\n                            this.load(1)\r\n                            this.fromVisible = false\r\n                        } else {\r\n                            this.$message.error(res.msg)\r\n                        }\r\n                    })\r\n                }\r\n            })\r\n        },\r\n\r\n        // 删除单条记录\r\n        del(id) {\r\n            this.$confirm('您确定删除吗？', '确认删除', { type: \"warning\" })\r\n                .then(_ => {\r\n                    this.$request.delete('/freemovies/delete/' + id).then(res => {\r\n                        if (res.code === '200') {\r\n                            this.$message.success('操作成功')\r\n                            this.load(1)\r\n                        } else {\r\n                            this.$message.error(res.msg)\r\n                        }\r\n                    })\r\n                })\r\n                .catch(() => {})\r\n        },\r\n\r\n        // 批量删除\r\n        handleSelectionChange(rows) {\r\n            this.ids = rows.map(v => v.id)\r\n        },\r\n        delBatch() {\r\n            if (!this.ids.length) {\r\n                this.$message.warning('请选择数据')\r\n                return\r\n            }\r\n            this.$confirm('您确定批量删除这些数据吗？', '确认删除', { type: \"warning\" })\r\n                .then(_ => {\r\n                    this.$request.delete('/freemovies/delete/batch', { data: this.ids }).then(res => {\r\n                        if (res.code === '200') {\r\n                            this.$message.success('操作成功')\r\n                            this.load(1)\r\n                        } else {\r\n                            this.$message.error(res.msg)\r\n                        }\r\n                    })\r\n                })\r\n                .catch(() => {})\r\n        },\r\n\r\n        // 分页查询\r\n        load(pageNum) {\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/freemovies/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n                    name: this.name\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list\r\n                    this.total = res.data?.total\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            })\r\n        },\r\n\r\n        // 重置\r\n        reset() {\r\n            this.name = null\r\n            this.load(1)\r\n        },\r\n\r\n        // 分页组件切换页码\r\n        handleCurrentChange(pageNum) {\r\n            this.load(pageNum)\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.search {\r\n    margin-bottom: 20px;\r\n}\r\n.operation {\r\n    margin-bottom: 20px;\r\n}\r\n.table {\r\n    margin-bottom: 20px;\r\n}\r\n.pagination {\r\n    margin-top: 20px;\r\n    text-align: right;\r\n}\r\n</style>\r\n"], "mappings": ";AA8OA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAAA;MACAL,IAAA;MAAA;MACAM,WAAA;MACAC,IAAA;MAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,KAAA;QACA;MAAA,CACA;MACAC,GAAA;MACA;MACAC,YAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACA;IACAC,UAAA;MACA,KAAAb,IAAA;MACA,KAAAD,WAAA;IACA;IACA;IACAe,WAAAC,GAAA;MACA,KAAAf,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAc,SAAA,CAAAD,GAAA;MACA,KAAAhB,WAAA;IACA;IAEA;IACAkB,iBAAAC,GAAA;MACA;MACA,IAAAA,GAAA,CAAAxB,IAAA;QACA,KAAAM,IAAA,CAAAmB,YAAA,GAAAD,GAAA,CAAAxB,IAAA;MACA;QACA,KAAA0B,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAC,mBAAAJ,GAAA;MACA;MACA,IAAAA,GAAA,CAAAxB,IAAA;QACA,KAAAM,IAAA,CAAAuB,UAAA,GAAAL,GAAA,CAAAxB,IAAA;MACA;QACA,KAAA0B,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAG,UAAAC,GAAA;MACAC,OAAA,CAAAC,GAAA,eAAAF,GAAA;MACA,KAAAhB,eAAA,GAAAgB,GAAA;MACA,KAAAjB,YAAA;IACA;IAEA;IACAoB,iBAAA;MACA;MACA,KAAAnB,eAAA;IACA;IAEA;IACAoB,KAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,MAAAR,GAAA,QAAAzB,IAAA,CAAAkC,EAAA;UACA,MAAAC,MAAA,QAAAnC,IAAA,CAAAkC,EAAA;UACA,KAAAE,QAAA;YAAAX,GAAA;YAAAU,MAAA;YAAAzC,IAAA,OAAAM;UAAA,GAAAqC,IAAA,CAAAnB,GAAA;YACA,IAAAA,GAAA,CAAAoB,IAAA;cACA,KAAAlB,QAAA,CAAAmB,OAAA;cACA,KAAA5B,IAAA;cACA,KAAAZ,WAAA;YACA;cACA,KAAAqB,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAsB,GAAA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC,IAAAP,EAAA;MACA,KAAAQ,QAAA;QAAAC,IAAA;MAAA,GACAN,IAAA,CAAAO,CAAA;QACA,KAAAR,QAAA,CAAAS,MAAA,yBAAAX,EAAA,EAAAG,IAAA,CAAAnB,GAAA;UACA,IAAAA,GAAA,CAAAoB,IAAA;YACA,KAAAlB,QAAA,CAAAmB,OAAA;YACA,KAAA5B,IAAA;UACA;YACA,KAAAS,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAsB,GAAA;UACA;QACA;MACA,GACAM,KAAA;IACA;IAEA;IACAC,sBAAAC,IAAA;MACA,KAAAzC,GAAA,GAAAyC,IAAA,CAAAC,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAhB,EAAA;IACA;IACAiB,SAAA;MACA,UAAA5C,GAAA,CAAA6C,MAAA;QACA,KAAAhC,QAAA,CAAAiC,OAAA;QACA;MACA;MACA,KAAAX,QAAA;QAAAC,IAAA;MAAA,GACAN,IAAA,CAAAO,CAAA;QACA,KAAAR,QAAA,CAAAS,MAAA;UAAAnD,IAAA,OAAAa;QAAA,GAAA8B,IAAA,CAAAnB,GAAA;UACA,IAAAA,GAAA,CAAAoB,IAAA;YACA,KAAAlB,QAAA,CAAAmB,OAAA;YACA,KAAA5B,IAAA;UACA;YACA,KAAAS,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAsB,GAAA;UACA;QACA;MACA,GACAM,KAAA;IACA;IAEA;IACAnC,KAAAf,OAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAwC,QAAA,CAAAkB,GAAA;QACAC,MAAA;UACA3D,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAJ,IAAA,OAAAA;QACA;MACA,GAAA4C,IAAA,CAAAnB,GAAA;QACA,IAAAA,GAAA,CAAAoB,IAAA;UACA,KAAA3C,SAAA,GAAAuB,GAAA,CAAAxB,IAAA,EAAA8D,IAAA;UACA,KAAA1D,KAAA,GAAAoB,GAAA,CAAAxB,IAAA,EAAAI,KAAA;QACA;UACA,KAAAsB,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAsB,GAAA;QACA;MACA;IACA;IAEA;IACAiB,MAAA;MACA,KAAAhE,IAAA;MACA,KAAAkB,IAAA;IACA;IAEA;IACA+C,oBAAA9D,OAAA;MACA,KAAAe,IAAA,CAAAf,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}