{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as numberUtil from '../../util/number.js';\nimport { isDimensionStacked } from '../../data/helper/dataStackHelper.js';\nimport { indexOf, curry, clone, isArray } from 'zrender/lib/core/util.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nfunction hasXOrY(item) {\n  return !(isNaN(parseFloat(item.x)) && isNaN(parseFloat(item.y)));\n}\nfunction hasXAndY(item) {\n  return !isNaN(parseFloat(item.x)) && !isNaN(parseFloat(item.y));\n}\nfunction markerTypeCalculatorWithExtent(markerType, data, otherDataDim, targetDataDim, otherCoordIndex, targetCoordIndex) {\n  var coordArr = [];\n  var stacked = isDimensionStacked(data, targetDataDim /* , otherDataDim */);\n  var calcDataDim = stacked ? data.getCalculationInfo('stackResultDimension') : targetDataDim;\n  var value = numCalculate(data, calcDataDim, markerType);\n  var dataIndex = data.indicesOfNearest(calcDataDim, value)[0];\n  coordArr[otherCoordIndex] = data.get(otherDataDim, dataIndex);\n  coordArr[targetCoordIndex] = data.get(calcDataDim, dataIndex);\n  var coordArrValue = data.get(targetDataDim, dataIndex);\n  // Make it simple, do not visit all stacked value to count precision.\n  var precision = numberUtil.getPrecision(data.get(targetDataDim, dataIndex));\n  precision = Math.min(precision, 20);\n  if (precision >= 0) {\n    coordArr[targetCoordIndex] = +coordArr[targetCoordIndex].toFixed(precision);\n  }\n  return [coordArr, coordArrValue];\n}\n// TODO Specified percent\nvar markerTypeCalculator = {\n  min: curry(markerTypeCalculatorWithExtent, 'min'),\n  max: curry(markerTypeCalculatorWithExtent, 'max'),\n  average: curry(markerTypeCalculatorWithExtent, 'average'),\n  median: curry(markerTypeCalculatorWithExtent, 'median')\n};\n/**\r\n * Transform markPoint data item to format used in List by do the following\r\n * 1. Calculate statistic like `max`, `min`, `average`\r\n * 2. Convert `item.xAxis`, `item.yAxis` to `item.coord` array\r\n */\nexport function dataTransform(seriesModel, item) {\n  if (!item) {\n    return;\n  }\n  var data = seriesModel.getData();\n  var coordSys = seriesModel.coordinateSystem;\n  var dims = coordSys && coordSys.dimensions;\n  // 1. If not specify the position with pixel directly\n  // 2. If `coord` is not a data array. Which uses `xAxis`,\n  // `yAxis` to specify the coord on each dimension\n  // parseFloat first because item.x and item.y can be percent string like '20%'\n  if (!hasXAndY(item) && !isArray(item.coord) && isArray(dims)) {\n    var axisInfo = getAxisInfo(item, data, coordSys, seriesModel);\n    // Clone the option\n    // Transform the properties xAxis, yAxis, radiusAxis, angleAxis, geoCoord to value\n    item = clone(item);\n    if (item.type && markerTypeCalculator[item.type] && axisInfo.baseAxis && axisInfo.valueAxis) {\n      var otherCoordIndex = indexOf(dims, axisInfo.baseAxis.dim);\n      var targetCoordIndex = indexOf(dims, axisInfo.valueAxis.dim);\n      var coordInfo = markerTypeCalculator[item.type](data, axisInfo.baseDataDim, axisInfo.valueDataDim, otherCoordIndex, targetCoordIndex);\n      item.coord = coordInfo[0];\n      // Force to use the value of calculated value.\n      // let item use the value without stack.\n      item.value = coordInfo[1];\n    } else {\n      // FIXME Only has one of xAxis and yAxis.\n      item.coord = [item.xAxis != null ? item.xAxis : item.radiusAxis, item.yAxis != null ? item.yAxis : item.angleAxis];\n    }\n  }\n  // x y is provided\n  if (item.coord == null || !isArray(dims)) {\n    item.coord = [];\n  } else {\n    // Each coord support max, min, average\n    var coord = item.coord;\n    for (var i = 0; i < 2; i++) {\n      if (markerTypeCalculator[coord[i]]) {\n        coord[i] = numCalculate(data, data.mapDimension(dims[i]), coord[i]);\n      }\n    }\n  }\n  return item;\n}\nexport function getAxisInfo(item, data, coordSys, seriesModel) {\n  var ret = {};\n  if (item.valueIndex != null || item.valueDim != null) {\n    ret.valueDataDim = item.valueIndex != null ? data.getDimension(item.valueIndex) : item.valueDim;\n    ret.valueAxis = coordSys.getAxis(dataDimToCoordDim(seriesModel, ret.valueDataDim));\n    ret.baseAxis = coordSys.getOtherAxis(ret.valueAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n  } else {\n    ret.baseAxis = seriesModel.getBaseAxis();\n    ret.valueAxis = coordSys.getOtherAxis(ret.baseAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n    ret.valueDataDim = data.mapDimension(ret.valueAxis.dim);\n  }\n  return ret;\n}\nfunction dataDimToCoordDim(seriesModel, dataDim) {\n  var dimItem = seriesModel.getData().getDimensionInfo(dataDim);\n  return dimItem && dimItem.coordDim;\n}\n/**\r\n * Filter data which is out of coordinateSystem range\r\n * [dataFilter description]\r\n */\nexport function dataFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containData && item.coord && !hasXOrY(item) ? coordSys.containData(item.coord) : true;\n}\nexport function zoneFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item1, item2) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containZone && item1.coord && item2.coord && !hasXOrY(item1) && !hasXOrY(item2) ? coordSys.containZone(item1.coord, item2.coord) : true;\n}\nexport function createMarkerDimValueGetter(inCoordSys, dims) {\n  return inCoordSys ? function (item, dimName, dataIndex, dimIndex) {\n    var rawVal = dimIndex < 2\n    // x, y, radius, angle\n    ? item.coord && item.coord[dimIndex] : item.value;\n    return parseDataValue(rawVal, dims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dims[dimIndex]);\n  };\n}\nexport function numCalculate(data, valueDataDim, type) {\n  if (type === 'average') {\n    var sum_1 = 0;\n    var count_1 = 0;\n    data.each(valueDataDim, function (val, idx) {\n      if (!isNaN(val)) {\n        sum_1 += val;\n        count_1++;\n      }\n    });\n    return sum_1 / count_1;\n  } else if (type === 'median') {\n    return data.getMedian(valueDataDim);\n  } else {\n    // max & min\n    return data.getDataExtent(valueDataDim)[type === 'max' ? 1 : 0];\n  }\n}", "map": {"version": 3, "names": ["numberUtil", "isDimensionStacked", "indexOf", "curry", "clone", "isArray", "parseDataValue", "hasXOrY", "item", "isNaN", "parseFloat", "x", "y", "hasXAndY", "markerTypeCalculatorWithExtent", "markerType", "data", "otherDataDim", "targetDataDim", "otherCoordIndex", "targetCoordIndex", "coordArr", "stacked", "calcDataDim", "getCalculationInfo", "value", "numCalculate", "dataIndex", "indicesOfNearest", "get", "coordArrValue", "precision", "getPrecision", "Math", "min", "toFixed", "markerTypeCalculator", "max", "average", "median", "dataTransform", "seriesModel", "getData", "coordSys", "coordinateSystem", "dims", "dimensions", "coord", "axisInfo", "getAxisInfo", "type", "baseAxis", "valueAxis", "dim", "coordInfo", "baseDataDim", "valueDataDim", "xAxis", "radiusAxis", "yAxis", "angleAxis", "i", "mapDimension", "ret", "valueIndex", "valueDim", "getDimension", "getAxis", "dataDimToCoordDim", "getOtherAxis", "getBaseAxis", "dataDim", "dimItem", "getDimensionInfo", "coordDim", "dataFilter", "containData", "zoneFilter", "item1", "item2", "containZone", "createMarkerDimValueGetter", "inCoordSys", "dimName", "dimIndex", "rawVal", "sum_1", "count_1", "each", "val", "idx", "getMedian", "getDataExtent"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/component/marker/markerHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as numberUtil from '../../util/number.js';\nimport { isDimensionStacked } from '../../data/helper/dataStackHelper.js';\nimport { indexOf, curry, clone, isArray } from 'zrender/lib/core/util.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nfunction hasXOrY(item) {\n  return !(isNaN(parseFloat(item.x)) && isNaN(parseFloat(item.y)));\n}\nfunction hasXAndY(item) {\n  return !isNaN(parseFloat(item.x)) && !isNaN(parseFloat(item.y));\n}\nfunction markerTypeCalculatorWithExtent(markerType, data, otherDataDim, targetDataDim, otherCoordIndex, targetCoordIndex) {\n  var coordArr = [];\n  var stacked = isDimensionStacked(data, targetDataDim /* , otherDataDim */);\n  var calcDataDim = stacked ? data.getCalculationInfo('stackResultDimension') : targetDataDim;\n  var value = numCalculate(data, calcDataDim, markerType);\n  var dataIndex = data.indicesOfNearest(calcDataDim, value)[0];\n  coordArr[otherCoordIndex] = data.get(otherDataDim, dataIndex);\n  coordArr[targetCoordIndex] = data.get(calcDataDim, dataIndex);\n  var coordArrValue = data.get(targetDataDim, dataIndex);\n  // Make it simple, do not visit all stacked value to count precision.\n  var precision = numberUtil.getPrecision(data.get(targetDataDim, dataIndex));\n  precision = Math.min(precision, 20);\n  if (precision >= 0) {\n    coordArr[targetCoordIndex] = +coordArr[targetCoordIndex].toFixed(precision);\n  }\n  return [coordArr, coordArrValue];\n}\n// TODO Specified percent\nvar markerTypeCalculator = {\n  min: curry(markerTypeCalculatorWithExtent, 'min'),\n  max: curry(markerTypeCalculatorWithExtent, 'max'),\n  average: curry(markerTypeCalculatorWithExtent, 'average'),\n  median: curry(markerTypeCalculatorWithExtent, 'median')\n};\n/**\r\n * Transform markPoint data item to format used in List by do the following\r\n * 1. Calculate statistic like `max`, `min`, `average`\r\n * 2. Convert `item.xAxis`, `item.yAxis` to `item.coord` array\r\n */\nexport function dataTransform(seriesModel, item) {\n  if (!item) {\n    return;\n  }\n  var data = seriesModel.getData();\n  var coordSys = seriesModel.coordinateSystem;\n  var dims = coordSys && coordSys.dimensions;\n  // 1. If not specify the position with pixel directly\n  // 2. If `coord` is not a data array. Which uses `xAxis`,\n  // `yAxis` to specify the coord on each dimension\n  // parseFloat first because item.x and item.y can be percent string like '20%'\n  if (!hasXAndY(item) && !isArray(item.coord) && isArray(dims)) {\n    var axisInfo = getAxisInfo(item, data, coordSys, seriesModel);\n    // Clone the option\n    // Transform the properties xAxis, yAxis, radiusAxis, angleAxis, geoCoord to value\n    item = clone(item);\n    if (item.type && markerTypeCalculator[item.type] && axisInfo.baseAxis && axisInfo.valueAxis) {\n      var otherCoordIndex = indexOf(dims, axisInfo.baseAxis.dim);\n      var targetCoordIndex = indexOf(dims, axisInfo.valueAxis.dim);\n      var coordInfo = markerTypeCalculator[item.type](data, axisInfo.baseDataDim, axisInfo.valueDataDim, otherCoordIndex, targetCoordIndex);\n      item.coord = coordInfo[0];\n      // Force to use the value of calculated value.\n      // let item use the value without stack.\n      item.value = coordInfo[1];\n    } else {\n      // FIXME Only has one of xAxis and yAxis.\n      item.coord = [item.xAxis != null ? item.xAxis : item.radiusAxis, item.yAxis != null ? item.yAxis : item.angleAxis];\n    }\n  }\n  // x y is provided\n  if (item.coord == null || !isArray(dims)) {\n    item.coord = [];\n  } else {\n    // Each coord support max, min, average\n    var coord = item.coord;\n    for (var i = 0; i < 2; i++) {\n      if (markerTypeCalculator[coord[i]]) {\n        coord[i] = numCalculate(data, data.mapDimension(dims[i]), coord[i]);\n      }\n    }\n  }\n  return item;\n}\nexport function getAxisInfo(item, data, coordSys, seriesModel) {\n  var ret = {};\n  if (item.valueIndex != null || item.valueDim != null) {\n    ret.valueDataDim = item.valueIndex != null ? data.getDimension(item.valueIndex) : item.valueDim;\n    ret.valueAxis = coordSys.getAxis(dataDimToCoordDim(seriesModel, ret.valueDataDim));\n    ret.baseAxis = coordSys.getOtherAxis(ret.valueAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n  } else {\n    ret.baseAxis = seriesModel.getBaseAxis();\n    ret.valueAxis = coordSys.getOtherAxis(ret.baseAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n    ret.valueDataDim = data.mapDimension(ret.valueAxis.dim);\n  }\n  return ret;\n}\nfunction dataDimToCoordDim(seriesModel, dataDim) {\n  var dimItem = seriesModel.getData().getDimensionInfo(dataDim);\n  return dimItem && dimItem.coordDim;\n}\n/**\r\n * Filter data which is out of coordinateSystem range\r\n * [dataFilter description]\r\n */\nexport function dataFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containData && item.coord && !hasXOrY(item) ? coordSys.containData(item.coord) : true;\n}\nexport function zoneFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item1, item2) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containZone && item1.coord && item2.coord && !hasXOrY(item1) && !hasXOrY(item2) ? coordSys.containZone(item1.coord, item2.coord) : true;\n}\nexport function createMarkerDimValueGetter(inCoordSys, dims) {\n  return inCoordSys ? function (item, dimName, dataIndex, dimIndex) {\n    var rawVal = dimIndex < 2\n    // x, y, radius, angle\n    ? item.coord && item.coord[dimIndex] : item.value;\n    return parseDataValue(rawVal, dims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dims[dimIndex]);\n  };\n}\nexport function numCalculate(data, valueDataDim, type) {\n  if (type === 'average') {\n    var sum_1 = 0;\n    var count_1 = 0;\n    data.each(valueDataDim, function (val, idx) {\n      if (!isNaN(val)) {\n        sum_1 += val;\n        count_1++;\n      }\n    });\n    return sum_1 / count_1;\n  } else if (type === 'median') {\n    return data.getMedian(valueDataDim);\n  } else {\n    // max & min\n    return data.getDataExtent(valueDataDim)[type === 'max' ? 1 : 0];\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,UAAU,MAAM,sBAAsB;AAClD,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,QAAQ,0BAA0B;AACzE,SAASC,cAAc,QAAQ,sCAAsC;AACrE,SAASC,OAAOA,CAACC,IAAI,EAAE;EACrB,OAAO,EAAEC,KAAK,CAACC,UAAU,CAACF,IAAI,CAACG,CAAC,CAAC,CAAC,IAAIF,KAAK,CAACC,UAAU,CAACF,IAAI,CAACI,CAAC,CAAC,CAAC,CAAC;AAClE;AACA,SAASC,QAAQA,CAACL,IAAI,EAAE;EACtB,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,IAAI,CAACG,CAAC,CAAC,CAAC,IAAI,CAACF,KAAK,CAACC,UAAU,CAACF,IAAI,CAACI,CAAC,CAAC,CAAC;AACjE;AACA,SAASE,8BAA8BA,CAACC,UAAU,EAAEC,IAAI,EAAEC,YAAY,EAAEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,EAAE;EACxH,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAGrB,kBAAkB,CAACe,IAAI,EAAEE,aAAa,CAAC,oBAAoB,CAAC;EAC1E,IAAIK,WAAW,GAAGD,OAAO,GAAGN,IAAI,CAACQ,kBAAkB,CAAC,sBAAsB,CAAC,GAAGN,aAAa;EAC3F,IAAIO,KAAK,GAAGC,YAAY,CAACV,IAAI,EAAEO,WAAW,EAAER,UAAU,CAAC;EACvD,IAAIY,SAAS,GAAGX,IAAI,CAACY,gBAAgB,CAACL,WAAW,EAAEE,KAAK,CAAC,CAAC,CAAC,CAAC;EAC5DJ,QAAQ,CAACF,eAAe,CAAC,GAAGH,IAAI,CAACa,GAAG,CAACZ,YAAY,EAAEU,SAAS,CAAC;EAC7DN,QAAQ,CAACD,gBAAgB,CAAC,GAAGJ,IAAI,CAACa,GAAG,CAACN,WAAW,EAAEI,SAAS,CAAC;EAC7D,IAAIG,aAAa,GAAGd,IAAI,CAACa,GAAG,CAACX,aAAa,EAAES,SAAS,CAAC;EACtD;EACA,IAAII,SAAS,GAAG/B,UAAU,CAACgC,YAAY,CAAChB,IAAI,CAACa,GAAG,CAACX,aAAa,EAAES,SAAS,CAAC,CAAC;EAC3EI,SAAS,GAAGE,IAAI,CAACC,GAAG,CAACH,SAAS,EAAE,EAAE,CAAC;EACnC,IAAIA,SAAS,IAAI,CAAC,EAAE;IAClBV,QAAQ,CAACD,gBAAgB,CAAC,GAAG,CAACC,QAAQ,CAACD,gBAAgB,CAAC,CAACe,OAAO,CAACJ,SAAS,CAAC;EAC7E;EACA,OAAO,CAACV,QAAQ,EAAES,aAAa,CAAC;AAClC;AACA;AACA,IAAIM,oBAAoB,GAAG;EACzBF,GAAG,EAAE/B,KAAK,CAACW,8BAA8B,EAAE,KAAK,CAAC;EACjDuB,GAAG,EAAElC,KAAK,CAACW,8BAA8B,EAAE,KAAK,CAAC;EACjDwB,OAAO,EAAEnC,KAAK,CAACW,8BAA8B,EAAE,SAAS,CAAC;EACzDyB,MAAM,EAAEpC,KAAK,CAACW,8BAA8B,EAAE,QAAQ;AACxD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0B,aAAaA,CAACC,WAAW,EAAEjC,IAAI,EAAE;EAC/C,IAAI,CAACA,IAAI,EAAE;IACT;EACF;EACA,IAAIQ,IAAI,GAAGyB,WAAW,CAACC,OAAO,CAAC,CAAC;EAChC,IAAIC,QAAQ,GAAGF,WAAW,CAACG,gBAAgB;EAC3C,IAAIC,IAAI,GAAGF,QAAQ,IAAIA,QAAQ,CAACG,UAAU;EAC1C;EACA;EACA;EACA;EACA,IAAI,CAACjC,QAAQ,CAACL,IAAI,CAAC,IAAI,CAACH,OAAO,CAACG,IAAI,CAACuC,KAAK,CAAC,IAAI1C,OAAO,CAACwC,IAAI,CAAC,EAAE;IAC5D,IAAIG,QAAQ,GAAGC,WAAW,CAACzC,IAAI,EAAEQ,IAAI,EAAE2B,QAAQ,EAAEF,WAAW,CAAC;IAC7D;IACA;IACAjC,IAAI,GAAGJ,KAAK,CAACI,IAAI,CAAC;IAClB,IAAIA,IAAI,CAAC0C,IAAI,IAAId,oBAAoB,CAAC5B,IAAI,CAAC0C,IAAI,CAAC,IAAIF,QAAQ,CAACG,QAAQ,IAAIH,QAAQ,CAACI,SAAS,EAAE;MAC3F,IAAIjC,eAAe,GAAGjB,OAAO,CAAC2C,IAAI,EAAEG,QAAQ,CAACG,QAAQ,CAACE,GAAG,CAAC;MAC1D,IAAIjC,gBAAgB,GAAGlB,OAAO,CAAC2C,IAAI,EAAEG,QAAQ,CAACI,SAAS,CAACC,GAAG,CAAC;MAC5D,IAAIC,SAAS,GAAGlB,oBAAoB,CAAC5B,IAAI,CAAC0C,IAAI,CAAC,CAAClC,IAAI,EAAEgC,QAAQ,CAACO,WAAW,EAAEP,QAAQ,CAACQ,YAAY,EAAErC,eAAe,EAAEC,gBAAgB,CAAC;MACrIZ,IAAI,CAACuC,KAAK,GAAGO,SAAS,CAAC,CAAC,CAAC;MACzB;MACA;MACA9C,IAAI,CAACiB,KAAK,GAAG6B,SAAS,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACL;MACA9C,IAAI,CAACuC,KAAK,GAAG,CAACvC,IAAI,CAACiD,KAAK,IAAI,IAAI,GAAGjD,IAAI,CAACiD,KAAK,GAAGjD,IAAI,CAACkD,UAAU,EAAElD,IAAI,CAACmD,KAAK,IAAI,IAAI,GAAGnD,IAAI,CAACmD,KAAK,GAAGnD,IAAI,CAACoD,SAAS,CAAC;IACpH;EACF;EACA;EACA,IAAIpD,IAAI,CAACuC,KAAK,IAAI,IAAI,IAAI,CAAC1C,OAAO,CAACwC,IAAI,CAAC,EAAE;IACxCrC,IAAI,CAACuC,KAAK,GAAG,EAAE;EACjB,CAAC,MAAM;IACL;IACA,IAAIA,KAAK,GAAGvC,IAAI,CAACuC,KAAK;IACtB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAIzB,oBAAoB,CAACW,KAAK,CAACc,CAAC,CAAC,CAAC,EAAE;QAClCd,KAAK,CAACc,CAAC,CAAC,GAAGnC,YAAY,CAACV,IAAI,EAAEA,IAAI,CAAC8C,YAAY,CAACjB,IAAI,CAACgB,CAAC,CAAC,CAAC,EAAEd,KAAK,CAACc,CAAC,CAAC,CAAC;MACrE;IACF;EACF;EACA,OAAOrD,IAAI;AACb;AACA,OAAO,SAASyC,WAAWA,CAACzC,IAAI,EAAEQ,IAAI,EAAE2B,QAAQ,EAAEF,WAAW,EAAE;EAC7D,IAAIsB,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIvD,IAAI,CAACwD,UAAU,IAAI,IAAI,IAAIxD,IAAI,CAACyD,QAAQ,IAAI,IAAI,EAAE;IACpDF,GAAG,CAACP,YAAY,GAAGhD,IAAI,CAACwD,UAAU,IAAI,IAAI,GAAGhD,IAAI,CAACkD,YAAY,CAAC1D,IAAI,CAACwD,UAAU,CAAC,GAAGxD,IAAI,CAACyD,QAAQ;IAC/FF,GAAG,CAACX,SAAS,GAAGT,QAAQ,CAACwB,OAAO,CAACC,iBAAiB,CAAC3B,WAAW,EAAEsB,GAAG,CAACP,YAAY,CAAC,CAAC;IAClFO,GAAG,CAACZ,QAAQ,GAAGR,QAAQ,CAAC0B,YAAY,CAACN,GAAG,CAACX,SAAS,CAAC;IACnDW,GAAG,CAACR,WAAW,GAAGvC,IAAI,CAAC8C,YAAY,CAACC,GAAG,CAACZ,QAAQ,CAACE,GAAG,CAAC;EACvD,CAAC,MAAM;IACLU,GAAG,CAACZ,QAAQ,GAAGV,WAAW,CAAC6B,WAAW,CAAC,CAAC;IACxCP,GAAG,CAACX,SAAS,GAAGT,QAAQ,CAAC0B,YAAY,CAACN,GAAG,CAACZ,QAAQ,CAAC;IACnDY,GAAG,CAACR,WAAW,GAAGvC,IAAI,CAAC8C,YAAY,CAACC,GAAG,CAACZ,QAAQ,CAACE,GAAG,CAAC;IACrDU,GAAG,CAACP,YAAY,GAAGxC,IAAI,CAAC8C,YAAY,CAACC,GAAG,CAACX,SAAS,CAACC,GAAG,CAAC;EACzD;EACA,OAAOU,GAAG;AACZ;AACA,SAASK,iBAAiBA,CAAC3B,WAAW,EAAE8B,OAAO,EAAE;EAC/C,IAAIC,OAAO,GAAG/B,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC+B,gBAAgB,CAACF,OAAO,CAAC;EAC7D,OAAOC,OAAO,IAAIA,OAAO,CAACE,QAAQ;AACpC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA;AAC1B;AACAhC,QAAQ,EAAEnC,IAAI,EAAE;EACd;EACA,OAAOmC,QAAQ,IAAIA,QAAQ,CAACiC,WAAW,IAAIpE,IAAI,CAACuC,KAAK,IAAI,CAACxC,OAAO,CAACC,IAAI,CAAC,GAAGmC,QAAQ,CAACiC,WAAW,CAACpE,IAAI,CAACuC,KAAK,CAAC,GAAG,IAAI;AACnH;AACA,OAAO,SAAS8B,UAAUA;AAC1B;AACAlC,QAAQ,EAAEmC,KAAK,EAAEC,KAAK,EAAE;EACtB;EACA,OAAOpC,QAAQ,IAAIA,QAAQ,CAACqC,WAAW,IAAIF,KAAK,CAAC/B,KAAK,IAAIgC,KAAK,CAAChC,KAAK,IAAI,CAACxC,OAAO,CAACuE,KAAK,CAAC,IAAI,CAACvE,OAAO,CAACwE,KAAK,CAAC,GAAGpC,QAAQ,CAACqC,WAAW,CAACF,KAAK,CAAC/B,KAAK,EAAEgC,KAAK,CAAChC,KAAK,CAAC,GAAG,IAAI;AACrK;AACA,OAAO,SAASkC,0BAA0BA,CAACC,UAAU,EAAErC,IAAI,EAAE;EAC3D,OAAOqC,UAAU,GAAG,UAAU1E,IAAI,EAAE2E,OAAO,EAAExD,SAAS,EAAEyD,QAAQ,EAAE;IAChE,IAAIC,MAAM,GAAGD,QAAQ,GAAG;IACxB;IAAA,EACE5E,IAAI,CAACuC,KAAK,IAAIvC,IAAI,CAACuC,KAAK,CAACqC,QAAQ,CAAC,GAAG5E,IAAI,CAACiB,KAAK;IACjD,OAAOnB,cAAc,CAAC+E,MAAM,EAAExC,IAAI,CAACuC,QAAQ,CAAC,CAAC;EAC/C,CAAC,GAAG,UAAU5E,IAAI,EAAE2E,OAAO,EAAExD,SAAS,EAAEyD,QAAQ,EAAE;IAChD,OAAO9E,cAAc,CAACE,IAAI,CAACiB,KAAK,EAAEoB,IAAI,CAACuC,QAAQ,CAAC,CAAC;EACnD,CAAC;AACH;AACA,OAAO,SAAS1D,YAAYA,CAACV,IAAI,EAAEwC,YAAY,EAAEN,IAAI,EAAE;EACrD,IAAIA,IAAI,KAAK,SAAS,EAAE;IACtB,IAAIoC,KAAK,GAAG,CAAC;IACb,IAAIC,OAAO,GAAG,CAAC;IACfvE,IAAI,CAACwE,IAAI,CAAChC,YAAY,EAAE,UAAUiC,GAAG,EAAEC,GAAG,EAAE;MAC1C,IAAI,CAACjF,KAAK,CAACgF,GAAG,CAAC,EAAE;QACfH,KAAK,IAAIG,GAAG;QACZF,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IACF,OAAOD,KAAK,GAAGC,OAAO;EACxB,CAAC,MAAM,IAAIrC,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOlC,IAAI,CAAC2E,SAAS,CAACnC,YAAY,CAAC;EACrC,CAAC,MAAM;IACL;IACA,OAAOxC,IAAI,CAAC4E,aAAa,CAACpC,YAAY,CAAC,CAACN,IAAI,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EACjE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}