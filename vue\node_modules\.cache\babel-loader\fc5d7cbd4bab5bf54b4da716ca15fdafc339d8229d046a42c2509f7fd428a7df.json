{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.isDef = isDef;\nexports.isKorean = isKorean;\nfunction isDef(val) {\n  return val !== undefined && val !== null;\n}\nfunction isKorean(text) {\n  var reg = /([(\\uAC00-\\uD7AF)|(\\u3130-\\u318F)])+/gi;\n  return reg.test(text);\n}", "map": {"version": 3, "names": ["exports", "__esModule", "isDef", "isKorean", "val", "undefined", "text", "reg", "test"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/element-ui/lib/utils/shared.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.isDef = isDef;\nexports.isKorean = isKorean;\nfunction isDef(val) {\n  return val !== undefined && val !== null;\n}\nfunction isKorean(text) {\n  var reg = /([(\\uAC00-\\uD7AF)|(\\u3130-\\u318F)])+/gi;\n  return reg.test(text);\n}"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,KAAK,GAAGA,KAAK;AACrBF,OAAO,CAACG,QAAQ,GAAGA,QAAQ;AAC3B,SAASD,KAAKA,CAACE,GAAG,EAAE;EAClB,OAAOA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI;AAC1C;AACA,SAASD,QAAQA,CAACG,IAAI,EAAE;EACtB,IAAIC,GAAG,GAAG,wCAAwC;EAClD,OAAOA,GAAG,CAACC,IAAI,CAACF,IAAI,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}