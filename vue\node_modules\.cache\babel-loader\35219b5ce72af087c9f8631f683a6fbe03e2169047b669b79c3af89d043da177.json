{"ast": null, "code": "export default {\n  name: \"Freemovies\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前页码\n      pageSize: 12,\n      // 每页显示个数\n      total: 0,\n      // 总记录数\n      name: null,\n      // 搜索关键字\n      videoVisible: false,\n      // 是否显示播放视频的弹窗\n      currentVideoUrl: null // 当前要播放的视频地址\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    // 播放视频\n    playVideo(url) {\n      this.currentVideoUrl = url;\n      this.videoVisible = true;\n    },\n    // 视频弹窗关闭\n    handleVideoClose() {\n      this.currentVideoUrl = null;\n    },\n    // 查询数据\n    load(pageNum) {\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get(\"/freemovies/selectPage\", {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          name: this.name\n        }\n      }).then(res => {\n        if (res.code === \"200\") {\n          this.tableData = res.data?.list;\n          this.total = res.data?.total;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    // 分页组件切换页码\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "videoVisible", "currentVideoUrl", "created", "load", "methods", "playVideo", "url", "handleVideoClose", "$request", "get", "params", "then", "res", "code", "list", "$message", "error", "msg", "handleCurrentChange"], "sources": ["src/views/front/Freemovies.vue"], "sourcesContent": ["<template>\r\n    <div class=\"container\">\r\n        <!-- 搜索区域 -->\r\n        <div class=\"search\">\r\n            <el-input\r\n                placeholder=\"请输入关键字查询\"\r\n                style=\"width: 300px\"\r\n                v-model=\"name\"\r\n                clearable\r\n                class=\"search-input\"\r\n            ></el-input>\r\n            <el-button\r\n                type=\"primary\"\r\n                plain\r\n                style=\"margin-left: 10px\"\r\n                @click=\"load(1)\"\r\n                class=\"search-btn\"\r\n            >\r\n                查询\r\n            </el-button>\r\n        </div>\r\n\r\n        <!-- 商品展示 -->\r\n        <div class=\"product-list\">\r\n            <el-row :gutter=\"20\">\r\n                <el-col\r\n                    v-for=\"item in tableData\"\r\n                    :key=\"item.id\"\r\n                    :span=\"6\"\r\n                    class=\"product-item\"\r\n                >\r\n                    <div class=\"product-card\">\r\n                        <div class=\"image-container\">\r\n                            <el-image\r\n                                :src=\"item.img\"\r\n                                style=\"width: 100%; height: 100%; border-radius: 8px;\"\r\n                                :preview-src-list=\"[item.img]\"\r\n                                fit=\"cover\"\r\n                            />\r\n                        </div>\r\n                        <div class=\"product-info\">\r\n                            <h3 class=\"product-title\">\r\n                                {{ item.name }}\r\n                            </h3>\r\n                            <p class=\"product-content\">{{ item.content }}</p>\r\n                            <el-button\r\n                                size=\"small\"\r\n                                type=\"success\"\r\n                                @click=\"playVideo(item.video)\"\r\n                            >\r\n                                播放视频\r\n                            </el-button>\r\n                        </div>\r\n                    </div>\r\n                </el-col>\r\n            </el-row>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination\">\r\n            <el-pagination\r\n                background\r\n                @current-change=\"handleCurrentChange\"\r\n                :current-page=\"pageNum\"\r\n                :page-sizes=\"[12]\"\r\n                :page-size=\"pageSize\"\r\n                layout=\"prev, pager, next, sizes, total\"\r\n                :total=\"total\"\r\n            >\r\n            </el-pagination>\r\n        </div>\r\n\r\n        <!-- 播放视频的弹窗 -->\r\n        <el-dialog\r\n            title=\"播放视频\"\r\n            :visible.sync=\"videoVisible\"\r\n            width=\"50%\"\r\n            @close=\"handleVideoClose\"\r\n            append-to-body\r\n            center\r\n        >\r\n            <video\r\n                v-if=\"currentVideoUrl\"\r\n                :src=\"currentVideoUrl\"\r\n                controls\r\n                autoplay\r\n                style=\"width: 100%; border-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\"\r\n            ></video>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Freemovies\",\r\n    data() {\r\n        return {\r\n            tableData: [],    // 所有的数据\r\n            pageNum: 1,       // 当前页码\r\n            pageSize: 12,     // 每页显示个数\r\n            total: 0,         // 总记录数\r\n            name: null,       // 搜索关键字\r\n            videoVisible: false,  // 是否显示播放视频的弹窗\r\n            currentVideoUrl: null // 当前要播放的视频地址\r\n        };\r\n    },\r\n    created() {\r\n        this.load(1);\r\n    },\r\n    methods: {\r\n        // 播放视频\r\n        playVideo(url) {\r\n            this.currentVideoUrl = url;\r\n            this.videoVisible = true;\r\n        },\r\n\r\n        // 视频弹窗关闭\r\n        handleVideoClose() {\r\n            this.currentVideoUrl = null;\r\n        },\r\n\r\n        // 查询数据\r\n        load(pageNum) {\r\n            if (pageNum) this.pageNum = pageNum;\r\n            this.$request\r\n                .get(\"/freemovies/selectPage\", {\r\n                    params: {\r\n                        pageNum: this.pageNum,\r\n                        pageSize: this.pageSize,\r\n                        name: this.name\r\n                    }\r\n                })\r\n                .then(res => {\r\n                    if (res.code === \"200\") {\r\n                        this.tableData = res.data?.list;\r\n                        this.total = res.data?.total;\r\n                    } else {\r\n                        this.$message.error(res.msg);\r\n                    }\r\n                });\r\n        },\r\n\r\n        // 分页组件切换页码\r\n        handleCurrentChange(pageNum) {\r\n            this.load(pageNum);\r\n        }\r\n    }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n    padding: 20px;\r\n    background-color: #333;\r\n    color: #fff;\r\n    min-height: 100vh;\r\n}\r\n\r\n.search {\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.search-input {\r\n    border-radius: 20px;\r\n}\r\n\r\n.search-input ::v-deep .el-input__inner {\r\n    background-color: #444;\r\n    color: #fff;\r\n    border-color: #555;\r\n}\r\n\r\n.search-btn {\r\n    font-size: 14px;\r\n    color: #fff;\r\n    background-color: #ff6a00;\r\n    border-color: #ff6a00;\r\n}\r\n\r\n.search-btn:hover {\r\n    background-color: #e55c00;\r\n    border-color: #e55c00;\r\n}\r\n\r\n.product-list {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.product-item {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.product-card {\r\n    background-color: #444;\r\n    border-radius: 8px;\r\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\r\n    overflow: hidden;\r\n    color: #fff;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.image-container {\r\n    width: 100%;\r\n    aspect-ratio: 16/9;\r\n    overflow: hidden;\r\n}\r\n\r\n.product-info {\r\n    padding: 15px;\r\n    text-align: center;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.product-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #ff6a00;\r\n    margin: 10px 0;\r\n}\r\n\r\n.product-content {\r\n    font-size: 14px;\r\n    color: #bbb;\r\n    margin-bottom: 15px;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 3;\r\n    -webkit-box-orient: vertical;\r\n}\r\n\r\n.pagination {\r\n    margin-top: 20px;\r\n    text-align: right;\r\n}\r\n\r\n.el-button {\r\n    font-size: 12px;\r\n}\r\n\r\n.el-dialog .el-dialog__body {\r\n    padding: 0;\r\n}\r\n\r\n.el-pagination {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    align-items: center;\r\n}\r\n\r\n.el-pagination ::v-deep .btn-prev,\r\n.el-pagination ::v-deep .btn-next {\r\n    background-color: #ff6a00;\r\n    color: #fff;\r\n    border-color: #ff6a00;\r\n}\r\n\r\n.el-pagination ::v-deep .btn-prev:hover,\r\n.el-pagination ::v-deep .btn-next:hover {\r\n    background-color: #e55c00;\r\n    border-color: #e55c00;\r\n}\r\n\r\n.el-pagination ::v-deep .el-pager li {\r\n    background-color: #444;\r\n    color: #fff;\r\n}\r\n\r\n.el-pagination ::v-deep .el-pager li.active {\r\n    background-color: #ff6a00;\r\n    color: #fff;\r\n}\r\n</style>"], "mappings": "AA6FA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAAA;MACAL,IAAA;MAAA;MACAM,YAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACA;IACAC,UAAAC,GAAA;MACA,KAAAL,eAAA,GAAAK,GAAA;MACA,KAAAN,YAAA;IACA;IAEA;IACAO,iBAAA;MACA,KAAAN,eAAA;IACA;IAEA;IACAE,KAAAN,OAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAW,QAAA,CACAC,GAAA;QACAC,MAAA;UACAb,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAJ,IAAA,OAAAA;QACA;MACA,GACAiB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAjB,SAAA,GAAAgB,GAAA,CAAAjB,IAAA,EAAAmB,IAAA;UACA,KAAAf,KAAA,GAAAa,GAAA,CAAAjB,IAAA,EAAAI,KAAA;QACA;UACA,KAAAgB,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IAEA;IACAC,oBAAArB,OAAA;MACA,KAAAM,IAAA,CAAAN,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}