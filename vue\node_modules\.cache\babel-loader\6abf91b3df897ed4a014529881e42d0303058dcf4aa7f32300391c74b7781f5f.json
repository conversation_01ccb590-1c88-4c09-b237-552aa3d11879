{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/* global document */\nimport * as echarts from '../../../core/echarts.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { ToolboxFeature } from '../featureManager.js';\nimport { addEventListener } from 'zrender/lib/core/event.js';\nimport { warn } from '../../../util/log.js';\n/* global document */\nvar BLOCK_SPLITER = new Array(60).join('-');\nvar ITEM_SPLITER = '\\t';\n/**\r\n * Group series into two types\r\n *  1. on category axis, like line, bar\r\n *  2. others, like scatter, pie\r\n */\nfunction groupSeries(ecModel) {\n  var seriesGroupByCategoryAxis = {};\n  var otherSeries = [];\n  var meta = [];\n  ecModel.eachRawSeries(function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys && (coordSys.type === 'cartesian2d' || coordSys.type === 'polar')) {\n      // TODO: TYPE Consider polar? Include polar may increase unecessary bundle size.\n      var baseAxis = coordSys.getBaseAxis();\n      if (baseAxis.type === 'category') {\n        var key = baseAxis.dim + '_' + baseAxis.index;\n        if (!seriesGroupByCategoryAxis[key]) {\n          seriesGroupByCategoryAxis[key] = {\n            categoryAxis: baseAxis,\n            valueAxis: coordSys.getOtherAxis(baseAxis),\n            series: []\n          };\n          meta.push({\n            axisDim: baseAxis.dim,\n            axisIndex: baseAxis.index\n          });\n        }\n        seriesGroupByCategoryAxis[key].series.push(seriesModel);\n      } else {\n        otherSeries.push(seriesModel);\n      }\n    } else {\n      otherSeries.push(seriesModel);\n    }\n  });\n  return {\n    seriesGroupByCategoryAxis: seriesGroupByCategoryAxis,\n    other: otherSeries,\n    meta: meta\n  };\n}\n/**\r\n * Assemble content of series on cateogory axis\r\n * @inner\r\n */\nfunction assembleSeriesWithCategoryAxis(groups) {\n  var tables = [];\n  zrUtil.each(groups, function (group, key) {\n    var categoryAxis = group.categoryAxis;\n    var valueAxis = group.valueAxis;\n    var valueAxisDim = valueAxis.dim;\n    var headers = [' '].concat(zrUtil.map(group.series, function (series) {\n      return series.name;\n    }));\n    // @ts-ignore TODO Polar\n    var columns = [categoryAxis.model.getCategories()];\n    zrUtil.each(group.series, function (series) {\n      var rawData = series.getRawData();\n      columns.push(series.getRawData().mapArray(rawData.mapDimension(valueAxisDim), function (val) {\n        return val;\n      }));\n    });\n    // Assemble table content\n    var lines = [headers.join(ITEM_SPLITER)];\n    for (var i = 0; i < columns[0].length; i++) {\n      var items = [];\n      for (var j = 0; j < columns.length; j++) {\n        items.push(columns[j][i]);\n      }\n      lines.push(items.join(ITEM_SPLITER));\n    }\n    tables.push(lines.join('\\n'));\n  });\n  return tables.join('\\n\\n' + BLOCK_SPLITER + '\\n\\n');\n}\n/**\r\n * Assemble content of other series\r\n */\nfunction assembleOtherSeries(series) {\n  return zrUtil.map(series, function (series) {\n    var data = series.getRawData();\n    var lines = [series.name];\n    var vals = [];\n    data.each(data.dimensions, function () {\n      var argLen = arguments.length;\n      var dataIndex = arguments[argLen - 1];\n      var name = data.getName(dataIndex);\n      for (var i = 0; i < argLen - 1; i++) {\n        vals[i] = arguments[i];\n      }\n      lines.push((name ? name + ITEM_SPLITER : '') + vals.join(ITEM_SPLITER));\n    });\n    return lines.join('\\n');\n  }).join('\\n\\n' + BLOCK_SPLITER + '\\n\\n');\n}\nfunction getContentFromModel(ecModel) {\n  var result = groupSeries(ecModel);\n  return {\n    value: zrUtil.filter([assembleSeriesWithCategoryAxis(result.seriesGroupByCategoryAxis), assembleOtherSeries(result.other)], function (str) {\n      return !!str.replace(/[\\n\\t\\s]/g, '');\n    }).join('\\n\\n' + BLOCK_SPLITER + '\\n\\n'),\n    meta: result.meta\n  };\n}\nfunction trim(str) {\n  return str.replace(/^\\s\\s*/, '').replace(/\\s\\s*$/, '');\n}\n/**\r\n * If a block is tsv format\r\n */\nfunction isTSVFormat(block) {\n  // Simple method to find out if a block is tsv format\n  var firstLine = block.slice(0, block.indexOf('\\n'));\n  if (firstLine.indexOf(ITEM_SPLITER) >= 0) {\n    return true;\n  }\n}\nvar itemSplitRegex = new RegExp('[' + ITEM_SPLITER + ']+', 'g');\n/**\r\n * @param {string} tsv\r\n * @return {Object}\r\n */\nfunction parseTSVContents(tsv) {\n  var tsvLines = tsv.split(/\\n+/g);\n  var headers = trim(tsvLines.shift()).split(itemSplitRegex);\n  var categories = [];\n  var series = zrUtil.map(headers, function (header) {\n    return {\n      name: header,\n      data: []\n    };\n  });\n  for (var i = 0; i < tsvLines.length; i++) {\n    var items = trim(tsvLines[i]).split(itemSplitRegex);\n    categories.push(items.shift());\n    for (var j = 0; j < items.length; j++) {\n      series[j] && (series[j].data[i] = items[j]);\n    }\n  }\n  return {\n    series: series,\n    categories: categories\n  };\n}\nfunction parseListContents(str) {\n  var lines = str.split(/\\n+/g);\n  var seriesName = trim(lines.shift());\n  var data = [];\n  for (var i = 0; i < lines.length; i++) {\n    // if line is empty, ignore it.\n    // there is a case that a user forgot to delete `\\n`.\n    var line = trim(lines[i]);\n    if (!line) {\n      continue;\n    }\n    var items = line.split(itemSplitRegex);\n    var name_1 = '';\n    var value = void 0;\n    var hasName = false;\n    if (isNaN(items[0])) {\n      // First item is name\n      hasName = true;\n      name_1 = items[0];\n      items = items.slice(1);\n      data[i] = {\n        name: name_1,\n        value: []\n      };\n      value = data[i].value;\n    } else {\n      value = data[i] = [];\n    }\n    for (var j = 0; j < items.length; j++) {\n      value.push(+items[j]);\n    }\n    if (value.length === 1) {\n      hasName ? data[i].value = value[0] : data[i] = value[0];\n    }\n  }\n  return {\n    name: seriesName,\n    data: data\n  };\n}\nfunction parseContents(str, blockMetaList) {\n  var blocks = str.split(new RegExp('\\n*' + BLOCK_SPLITER + '\\n*', 'g'));\n  var newOption = {\n    series: []\n  };\n  zrUtil.each(blocks, function (block, idx) {\n    if (isTSVFormat(block)) {\n      var result = parseTSVContents(block);\n      var blockMeta = blockMetaList[idx];\n      var axisKey = blockMeta.axisDim + 'Axis';\n      if (blockMeta) {\n        newOption[axisKey] = newOption[axisKey] || [];\n        newOption[axisKey][blockMeta.axisIndex] = {\n          data: result.categories\n        };\n        newOption.series = newOption.series.concat(result.series);\n      }\n    } else {\n      var result = parseListContents(block);\n      newOption.series.push(result);\n    }\n  });\n  return newOption;\n}\nvar DataView = /** @class */function (_super) {\n  __extends(DataView, _super);\n  function DataView() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  DataView.prototype.onclick = function (ecModel, api) {\n    // FIXME: better way?\n    setTimeout(function () {\n      api.dispatchAction({\n        type: 'hideTip'\n      });\n    });\n    var container = api.getDom();\n    var model = this.model;\n    if (this._dom) {\n      container.removeChild(this._dom);\n    }\n    var root = document.createElement('div');\n    // use padding to avoid 5px whitespace\n    root.style.cssText = 'position:absolute;top:0;bottom:0;left:0;right:0;padding:5px';\n    root.style.backgroundColor = model.get('backgroundColor') || '#fff';\n    // Create elements\n    var header = document.createElement('h4');\n    var lang = model.get('lang') || [];\n    header.innerHTML = lang[0] || model.get('title');\n    header.style.cssText = 'margin:10px 20px';\n    header.style.color = model.get('textColor');\n    var viewMain = document.createElement('div');\n    var textarea = document.createElement('textarea');\n    viewMain.style.cssText = 'overflow:auto';\n    var optionToContent = model.get('optionToContent');\n    var contentToOption = model.get('contentToOption');\n    var result = getContentFromModel(ecModel);\n    if (zrUtil.isFunction(optionToContent)) {\n      var htmlOrDom = optionToContent(api.getOption());\n      if (zrUtil.isString(htmlOrDom)) {\n        viewMain.innerHTML = htmlOrDom;\n      } else if (zrUtil.isDom(htmlOrDom)) {\n        viewMain.appendChild(htmlOrDom);\n      }\n    } else {\n      // Use default textarea\n      textarea.readOnly = model.get('readOnly');\n      var style = textarea.style;\n      // eslint-disable-next-line max-len\n      style.cssText = 'display:block;width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;resize:none;box-sizing:border-box;outline:none';\n      style.color = model.get('textColor');\n      style.borderColor = model.get('textareaBorderColor');\n      style.backgroundColor = model.get('textareaColor');\n      textarea.value = result.value;\n      viewMain.appendChild(textarea);\n    }\n    var blockMetaList = result.meta;\n    var buttonContainer = document.createElement('div');\n    buttonContainer.style.cssText = 'position:absolute;bottom:5px;left:0;right:0';\n    // eslint-disable-next-line max-len\n    var buttonStyle = 'float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px';\n    var closeButton = document.createElement('div');\n    var refreshButton = document.createElement('div');\n    buttonStyle += ';background-color:' + model.get('buttonColor');\n    buttonStyle += ';color:' + model.get('buttonTextColor');\n    var self = this;\n    function close() {\n      container.removeChild(root);\n      self._dom = null;\n    }\n    addEventListener(closeButton, 'click', close);\n    addEventListener(refreshButton, 'click', function () {\n      if (contentToOption == null && optionToContent != null || contentToOption != null && optionToContent == null) {\n        if (process.env.NODE_ENV !== 'production') {\n          // eslint-disable-next-line\n          warn('It seems you have just provided one of `contentToOption` and `optionToContent` functions but missed the other one. Data change is ignored.');\n        }\n        close();\n        return;\n      }\n      var newOption;\n      try {\n        if (zrUtil.isFunction(contentToOption)) {\n          newOption = contentToOption(viewMain, api.getOption());\n        } else {\n          newOption = parseContents(textarea.value, blockMetaList);\n        }\n      } catch (e) {\n        close();\n        throw new Error('Data view format error ' + e);\n      }\n      if (newOption) {\n        api.dispatchAction({\n          type: 'changeDataView',\n          newOption: newOption\n        });\n      }\n      close();\n    });\n    closeButton.innerHTML = lang[1];\n    refreshButton.innerHTML = lang[2];\n    refreshButton.style.cssText = closeButton.style.cssText = buttonStyle;\n    !model.get('readOnly') && buttonContainer.appendChild(refreshButton);\n    buttonContainer.appendChild(closeButton);\n    root.appendChild(header);\n    root.appendChild(viewMain);\n    root.appendChild(buttonContainer);\n    viewMain.style.height = container.clientHeight - 80 + 'px';\n    container.appendChild(root);\n    this._dom = root;\n  };\n  DataView.prototype.remove = function (ecModel, api) {\n    this._dom && api.getDom().removeChild(this._dom);\n  };\n  DataView.prototype.dispose = function (ecModel, api) {\n    this.remove(ecModel, api);\n  };\n  DataView.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      readOnly: false,\n      optionToContent: null,\n      contentToOption: null,\n      // eslint-disable-next-line\n      icon: 'M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28',\n      title: ecModel.getLocaleModel().get(['toolbox', 'dataView', 'title']),\n      lang: ecModel.getLocaleModel().get(['toolbox', 'dataView', 'lang']),\n      backgroundColor: '#fff',\n      textColor: '#000',\n      textareaColor: '#fff',\n      textareaBorderColor: '#333',\n      buttonColor: '#c23531',\n      buttonTextColor: '#fff'\n    };\n    return defaultOption;\n  };\n  return DataView;\n}(ToolboxFeature);\n/**\r\n * @inner\r\n */\nfunction tryMergeDataOption(newData, originalData) {\n  return zrUtil.map(newData, function (newVal, idx) {\n    var original = originalData && originalData[idx];\n    if (zrUtil.isObject(original) && !zrUtil.isArray(original)) {\n      var newValIsObject = zrUtil.isObject(newVal) && !zrUtil.isArray(newVal);\n      if (!newValIsObject) {\n        newVal = {\n          value: newVal\n        };\n      }\n      // original data has name but new data has no name\n      var shouldDeleteName = original.name != null && newVal.name == null;\n      // Original data has option\n      newVal = zrUtil.defaults(newVal, original);\n      shouldDeleteName && delete newVal.name;\n      return newVal;\n    } else {\n      return newVal;\n    }\n  });\n}\n// TODO: SELF REGISTERED.\necharts.registerAction({\n  type: 'changeDataView',\n  event: 'dataViewChanged',\n  update: 'prepareAndUpdate'\n}, function (payload, ecModel) {\n  var newSeriesOptList = [];\n  zrUtil.each(payload.newOption.series, function (seriesOpt) {\n    var seriesModel = ecModel.getSeriesByName(seriesOpt.name)[0];\n    if (!seriesModel) {\n      // New created series\n      // Geuss the series type\n      newSeriesOptList.push(zrUtil.extend({\n        // Default is scatter\n        type: 'scatter'\n      }, seriesOpt));\n    } else {\n      var originalData = seriesModel.get('data');\n      newSeriesOptList.push({\n        name: seriesOpt.name,\n        data: tryMergeDataOption(seriesOpt.data, originalData)\n      });\n    }\n  });\n  ecModel.mergeOption(zrUtil.defaults({\n    series: newSeriesOptList\n  }, payload.newOption));\n});\nexport default DataView;", "map": {"version": 3, "names": ["__extends", "echarts", "zrUtil", "ToolboxFeature", "addEventListener", "warn", "BLOCK_SPLITER", "Array", "join", "ITEM_SPLITER", "groupSeries", "ecModel", "seriesGroupByCategoryAxis", "otherSeries", "meta", "eachRawSeries", "seriesModel", "coordSys", "coordinateSystem", "type", "baseAxis", "getBaseAxis", "key", "dim", "index", "categoryAxis", "valueAxis", "getOtherAxis", "series", "push", "axisDim", "axisIndex", "other", "assembleSeriesWithCategoryAxis", "groups", "tables", "each", "group", "valueAxisDim", "headers", "concat", "map", "name", "columns", "model", "getCategories", "rawData", "getRawData", "mapArray", "mapDimension", "val", "lines", "i", "length", "items", "j", "assembleOtherSeries", "data", "vals", "dimensions", "argLen", "arguments", "dataIndex", "getName", "getContentFromModel", "result", "value", "filter", "str", "replace", "trim", "isTSVFormat", "block", "firstLine", "slice", "indexOf", "itemSplitRegex", "RegExp", "parseTSVContents", "tsv", "tsvLines", "split", "shift", "categories", "header", "parseListContents", "seriesName", "line", "name_1", "<PERSON><PERSON><PERSON>", "isNaN", "parseContents", "blockMetaList", "blocks", "newOption", "idx", "blockMeta", "axisKey", "DataView", "_super", "apply", "prototype", "onclick", "api", "setTimeout", "dispatchAction", "container", "getDom", "_dom", "<PERSON><PERSON><PERSON><PERSON>", "root", "document", "createElement", "style", "cssText", "backgroundColor", "get", "lang", "innerHTML", "color", "viewMain", "textarea", "optionToContent", "contentToOption", "isFunction", "htmlOrDom", "getOption", "isString", "isDom", "append<PERSON><PERSON><PERSON>", "readOnly", "borderColor", "buttonContainer", "buttonStyle", "closeButton", "refreshButton", "self", "close", "process", "env", "NODE_ENV", "e", "Error", "height", "clientHeight", "remove", "dispose", "getDefaultOption", "defaultOption", "show", "icon", "title", "getLocaleModel", "textColor", "textareaColor", "textareaBorderColor", "buttonColor", "buttonTextColor", "tryMergeDataOption", "newData", "originalData", "newVal", "original", "isObject", "isArray", "newValIsObject", "shouldDeleteName", "defaults", "registerAction", "event", "update", "payload", "newSeriesOptList", "seriesOpt", "getSeriesByName", "extend", "mergeOption"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/component/toolbox/feature/DataView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/* global document */\nimport * as echarts from '../../../core/echarts.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { ToolboxFeature } from '../featureManager.js';\nimport { addEventListener } from 'zrender/lib/core/event.js';\nimport { warn } from '../../../util/log.js';\n/* global document */\nvar BLOCK_SPLITER = new Array(60).join('-');\nvar ITEM_SPLITER = '\\t';\n/**\r\n * Group series into two types\r\n *  1. on category axis, like line, bar\r\n *  2. others, like scatter, pie\r\n */\nfunction groupSeries(ecModel) {\n  var seriesGroupByCategoryAxis = {};\n  var otherSeries = [];\n  var meta = [];\n  ecModel.eachRawSeries(function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys && (coordSys.type === 'cartesian2d' || coordSys.type === 'polar')) {\n      // TODO: TYPE Consider polar? Include polar may increase unecessary bundle size.\n      var baseAxis = coordSys.getBaseAxis();\n      if (baseAxis.type === 'category') {\n        var key = baseAxis.dim + '_' + baseAxis.index;\n        if (!seriesGroupByCategoryAxis[key]) {\n          seriesGroupByCategoryAxis[key] = {\n            categoryAxis: baseAxis,\n            valueAxis: coordSys.getOtherAxis(baseAxis),\n            series: []\n          };\n          meta.push({\n            axisDim: baseAxis.dim,\n            axisIndex: baseAxis.index\n          });\n        }\n        seriesGroupByCategoryAxis[key].series.push(seriesModel);\n      } else {\n        otherSeries.push(seriesModel);\n      }\n    } else {\n      otherSeries.push(seriesModel);\n    }\n  });\n  return {\n    seriesGroupByCategoryAxis: seriesGroupByCategoryAxis,\n    other: otherSeries,\n    meta: meta\n  };\n}\n/**\r\n * Assemble content of series on cateogory axis\r\n * @inner\r\n */\nfunction assembleSeriesWithCategoryAxis(groups) {\n  var tables = [];\n  zrUtil.each(groups, function (group, key) {\n    var categoryAxis = group.categoryAxis;\n    var valueAxis = group.valueAxis;\n    var valueAxisDim = valueAxis.dim;\n    var headers = [' '].concat(zrUtil.map(group.series, function (series) {\n      return series.name;\n    }));\n    // @ts-ignore TODO Polar\n    var columns = [categoryAxis.model.getCategories()];\n    zrUtil.each(group.series, function (series) {\n      var rawData = series.getRawData();\n      columns.push(series.getRawData().mapArray(rawData.mapDimension(valueAxisDim), function (val) {\n        return val;\n      }));\n    });\n    // Assemble table content\n    var lines = [headers.join(ITEM_SPLITER)];\n    for (var i = 0; i < columns[0].length; i++) {\n      var items = [];\n      for (var j = 0; j < columns.length; j++) {\n        items.push(columns[j][i]);\n      }\n      lines.push(items.join(ITEM_SPLITER));\n    }\n    tables.push(lines.join('\\n'));\n  });\n  return tables.join('\\n\\n' + BLOCK_SPLITER + '\\n\\n');\n}\n/**\r\n * Assemble content of other series\r\n */\nfunction assembleOtherSeries(series) {\n  return zrUtil.map(series, function (series) {\n    var data = series.getRawData();\n    var lines = [series.name];\n    var vals = [];\n    data.each(data.dimensions, function () {\n      var argLen = arguments.length;\n      var dataIndex = arguments[argLen - 1];\n      var name = data.getName(dataIndex);\n      for (var i = 0; i < argLen - 1; i++) {\n        vals[i] = arguments[i];\n      }\n      lines.push((name ? name + ITEM_SPLITER : '') + vals.join(ITEM_SPLITER));\n    });\n    return lines.join('\\n');\n  }).join('\\n\\n' + BLOCK_SPLITER + '\\n\\n');\n}\nfunction getContentFromModel(ecModel) {\n  var result = groupSeries(ecModel);\n  return {\n    value: zrUtil.filter([assembleSeriesWithCategoryAxis(result.seriesGroupByCategoryAxis), assembleOtherSeries(result.other)], function (str) {\n      return !!str.replace(/[\\n\\t\\s]/g, '');\n    }).join('\\n\\n' + BLOCK_SPLITER + '\\n\\n'),\n    meta: result.meta\n  };\n}\nfunction trim(str) {\n  return str.replace(/^\\s\\s*/, '').replace(/\\s\\s*$/, '');\n}\n/**\r\n * If a block is tsv format\r\n */\nfunction isTSVFormat(block) {\n  // Simple method to find out if a block is tsv format\n  var firstLine = block.slice(0, block.indexOf('\\n'));\n  if (firstLine.indexOf(ITEM_SPLITER) >= 0) {\n    return true;\n  }\n}\nvar itemSplitRegex = new RegExp('[' + ITEM_SPLITER + ']+', 'g');\n/**\r\n * @param {string} tsv\r\n * @return {Object}\r\n */\nfunction parseTSVContents(tsv) {\n  var tsvLines = tsv.split(/\\n+/g);\n  var headers = trim(tsvLines.shift()).split(itemSplitRegex);\n  var categories = [];\n  var series = zrUtil.map(headers, function (header) {\n    return {\n      name: header,\n      data: []\n    };\n  });\n  for (var i = 0; i < tsvLines.length; i++) {\n    var items = trim(tsvLines[i]).split(itemSplitRegex);\n    categories.push(items.shift());\n    for (var j = 0; j < items.length; j++) {\n      series[j] && (series[j].data[i] = items[j]);\n    }\n  }\n  return {\n    series: series,\n    categories: categories\n  };\n}\nfunction parseListContents(str) {\n  var lines = str.split(/\\n+/g);\n  var seriesName = trim(lines.shift());\n  var data = [];\n  for (var i = 0; i < lines.length; i++) {\n    // if line is empty, ignore it.\n    // there is a case that a user forgot to delete `\\n`.\n    var line = trim(lines[i]);\n    if (!line) {\n      continue;\n    }\n    var items = line.split(itemSplitRegex);\n    var name_1 = '';\n    var value = void 0;\n    var hasName = false;\n    if (isNaN(items[0])) {\n      // First item is name\n      hasName = true;\n      name_1 = items[0];\n      items = items.slice(1);\n      data[i] = {\n        name: name_1,\n        value: []\n      };\n      value = data[i].value;\n    } else {\n      value = data[i] = [];\n    }\n    for (var j = 0; j < items.length; j++) {\n      value.push(+items[j]);\n    }\n    if (value.length === 1) {\n      hasName ? data[i].value = value[0] : data[i] = value[0];\n    }\n  }\n  return {\n    name: seriesName,\n    data: data\n  };\n}\nfunction parseContents(str, blockMetaList) {\n  var blocks = str.split(new RegExp('\\n*' + BLOCK_SPLITER + '\\n*', 'g'));\n  var newOption = {\n    series: []\n  };\n  zrUtil.each(blocks, function (block, idx) {\n    if (isTSVFormat(block)) {\n      var result = parseTSVContents(block);\n      var blockMeta = blockMetaList[idx];\n      var axisKey = blockMeta.axisDim + 'Axis';\n      if (blockMeta) {\n        newOption[axisKey] = newOption[axisKey] || [];\n        newOption[axisKey][blockMeta.axisIndex] = {\n          data: result.categories\n        };\n        newOption.series = newOption.series.concat(result.series);\n      }\n    } else {\n      var result = parseListContents(block);\n      newOption.series.push(result);\n    }\n  });\n  return newOption;\n}\nvar DataView = /** @class */function (_super) {\n  __extends(DataView, _super);\n  function DataView() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  DataView.prototype.onclick = function (ecModel, api) {\n    // FIXME: better way?\n    setTimeout(function () {\n      api.dispatchAction({\n        type: 'hideTip'\n      });\n    });\n    var container = api.getDom();\n    var model = this.model;\n    if (this._dom) {\n      container.removeChild(this._dom);\n    }\n    var root = document.createElement('div');\n    // use padding to avoid 5px whitespace\n    root.style.cssText = 'position:absolute;top:0;bottom:0;left:0;right:0;padding:5px';\n    root.style.backgroundColor = model.get('backgroundColor') || '#fff';\n    // Create elements\n    var header = document.createElement('h4');\n    var lang = model.get('lang') || [];\n    header.innerHTML = lang[0] || model.get('title');\n    header.style.cssText = 'margin:10px 20px';\n    header.style.color = model.get('textColor');\n    var viewMain = document.createElement('div');\n    var textarea = document.createElement('textarea');\n    viewMain.style.cssText = 'overflow:auto';\n    var optionToContent = model.get('optionToContent');\n    var contentToOption = model.get('contentToOption');\n    var result = getContentFromModel(ecModel);\n    if (zrUtil.isFunction(optionToContent)) {\n      var htmlOrDom = optionToContent(api.getOption());\n      if (zrUtil.isString(htmlOrDom)) {\n        viewMain.innerHTML = htmlOrDom;\n      } else if (zrUtil.isDom(htmlOrDom)) {\n        viewMain.appendChild(htmlOrDom);\n      }\n    } else {\n      // Use default textarea\n      textarea.readOnly = model.get('readOnly');\n      var style = textarea.style;\n      // eslint-disable-next-line max-len\n      style.cssText = 'display:block;width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;resize:none;box-sizing:border-box;outline:none';\n      style.color = model.get('textColor');\n      style.borderColor = model.get('textareaBorderColor');\n      style.backgroundColor = model.get('textareaColor');\n      textarea.value = result.value;\n      viewMain.appendChild(textarea);\n    }\n    var blockMetaList = result.meta;\n    var buttonContainer = document.createElement('div');\n    buttonContainer.style.cssText = 'position:absolute;bottom:5px;left:0;right:0';\n    // eslint-disable-next-line max-len\n    var buttonStyle = 'float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px';\n    var closeButton = document.createElement('div');\n    var refreshButton = document.createElement('div');\n    buttonStyle += ';background-color:' + model.get('buttonColor');\n    buttonStyle += ';color:' + model.get('buttonTextColor');\n    var self = this;\n    function close() {\n      container.removeChild(root);\n      self._dom = null;\n    }\n    addEventListener(closeButton, 'click', close);\n    addEventListener(refreshButton, 'click', function () {\n      if (contentToOption == null && optionToContent != null || contentToOption != null && optionToContent == null) {\n        if (process.env.NODE_ENV !== 'production') {\n          // eslint-disable-next-line\n          warn('It seems you have just provided one of `contentToOption` and `optionToContent` functions but missed the other one. Data change is ignored.');\n        }\n        close();\n        return;\n      }\n      var newOption;\n      try {\n        if (zrUtil.isFunction(contentToOption)) {\n          newOption = contentToOption(viewMain, api.getOption());\n        } else {\n          newOption = parseContents(textarea.value, blockMetaList);\n        }\n      } catch (e) {\n        close();\n        throw new Error('Data view format error ' + e);\n      }\n      if (newOption) {\n        api.dispatchAction({\n          type: 'changeDataView',\n          newOption: newOption\n        });\n      }\n      close();\n    });\n    closeButton.innerHTML = lang[1];\n    refreshButton.innerHTML = lang[2];\n    refreshButton.style.cssText = closeButton.style.cssText = buttonStyle;\n    !model.get('readOnly') && buttonContainer.appendChild(refreshButton);\n    buttonContainer.appendChild(closeButton);\n    root.appendChild(header);\n    root.appendChild(viewMain);\n    root.appendChild(buttonContainer);\n    viewMain.style.height = container.clientHeight - 80 + 'px';\n    container.appendChild(root);\n    this._dom = root;\n  };\n  DataView.prototype.remove = function (ecModel, api) {\n    this._dom && api.getDom().removeChild(this._dom);\n  };\n  DataView.prototype.dispose = function (ecModel, api) {\n    this.remove(ecModel, api);\n  };\n  DataView.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      readOnly: false,\n      optionToContent: null,\n      contentToOption: null,\n      // eslint-disable-next-line\n      icon: 'M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28',\n      title: ecModel.getLocaleModel().get(['toolbox', 'dataView', 'title']),\n      lang: ecModel.getLocaleModel().get(['toolbox', 'dataView', 'lang']),\n      backgroundColor: '#fff',\n      textColor: '#000',\n      textareaColor: '#fff',\n      textareaBorderColor: '#333',\n      buttonColor: '#c23531',\n      buttonTextColor: '#fff'\n    };\n    return defaultOption;\n  };\n  return DataView;\n}(ToolboxFeature);\n/**\r\n * @inner\r\n */\nfunction tryMergeDataOption(newData, originalData) {\n  return zrUtil.map(newData, function (newVal, idx) {\n    var original = originalData && originalData[idx];\n    if (zrUtil.isObject(original) && !zrUtil.isArray(original)) {\n      var newValIsObject = zrUtil.isObject(newVal) && !zrUtil.isArray(newVal);\n      if (!newValIsObject) {\n        newVal = {\n          value: newVal\n        };\n      }\n      // original data has name but new data has no name\n      var shouldDeleteName = original.name != null && newVal.name == null;\n      // Original data has option\n      newVal = zrUtil.defaults(newVal, original);\n      shouldDeleteName && delete newVal.name;\n      return newVal;\n    } else {\n      return newVal;\n    }\n  });\n}\n// TODO: SELF REGISTERED.\necharts.registerAction({\n  type: 'changeDataView',\n  event: 'dataViewChanged',\n  update: 'prepareAndUpdate'\n}, function (payload, ecModel) {\n  var newSeriesOptList = [];\n  zrUtil.each(payload.newOption.series, function (seriesOpt) {\n    var seriesModel = ecModel.getSeriesByName(seriesOpt.name)[0];\n    if (!seriesModel) {\n      // New created series\n      // Geuss the series type\n      newSeriesOptList.push(zrUtil.extend({\n        // Default is scatter\n        type: 'scatter'\n      }, seriesOpt));\n    } else {\n      var originalData = seriesModel.get('data');\n      newSeriesOptList.push({\n        name: seriesOpt.name,\n        data: tryMergeDataOption(seriesOpt.data, originalData)\n      });\n    }\n  });\n  ecModel.mergeOption(zrUtil.defaults({\n    series: newSeriesOptList\n  }, payload.newOption));\n});\nexport default DataView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA,OAAO,KAAKC,OAAO,MAAM,0BAA0B;AACnD,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,IAAI,QAAQ,sBAAsB;AAC3C;AACA,IAAIC,aAAa,GAAG,IAAIC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC3C,IAAIC,YAAY,GAAG,IAAI;AACvB;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC5B,IAAIC,yBAAyB,GAAG,CAAC,CAAC;EAClC,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,IAAI,GAAG,EAAE;EACbH,OAAO,CAACI,aAAa,CAAC,UAAUC,WAAW,EAAE;IAC3C,IAAIC,QAAQ,GAAGD,WAAW,CAACE,gBAAgB;IAC3C,IAAID,QAAQ,KAAKA,QAAQ,CAACE,IAAI,KAAK,aAAa,IAAIF,QAAQ,CAACE,IAAI,KAAK,OAAO,CAAC,EAAE;MAC9E;MACA,IAAIC,QAAQ,GAAGH,QAAQ,CAACI,WAAW,CAAC,CAAC;MACrC,IAAID,QAAQ,CAACD,IAAI,KAAK,UAAU,EAAE;QAChC,IAAIG,GAAG,GAAGF,QAAQ,CAACG,GAAG,GAAG,GAAG,GAAGH,QAAQ,CAACI,KAAK;QAC7C,IAAI,CAACZ,yBAAyB,CAACU,GAAG,CAAC,EAAE;UACnCV,yBAAyB,CAACU,GAAG,CAAC,GAAG;YAC/BG,YAAY,EAAEL,QAAQ;YACtBM,SAAS,EAAET,QAAQ,CAACU,YAAY,CAACP,QAAQ,CAAC;YAC1CQ,MAAM,EAAE;UACV,CAAC;UACDd,IAAI,CAACe,IAAI,CAAC;YACRC,OAAO,EAAEV,QAAQ,CAACG,GAAG;YACrBQ,SAAS,EAAEX,QAAQ,CAACI;UACtB,CAAC,CAAC;QACJ;QACAZ,yBAAyB,CAACU,GAAG,CAAC,CAACM,MAAM,CAACC,IAAI,CAACb,WAAW,CAAC;MACzD,CAAC,MAAM;QACLH,WAAW,CAACgB,IAAI,CAACb,WAAW,CAAC;MAC/B;IACF,CAAC,MAAM;MACLH,WAAW,CAACgB,IAAI,CAACb,WAAW,CAAC;IAC/B;EACF,CAAC,CAAC;EACF,OAAO;IACLJ,yBAAyB,EAAEA,yBAAyB;IACpDoB,KAAK,EAAEnB,WAAW;IAClBC,IAAI,EAAEA;EACR,CAAC;AACH;AACA;AACA;AACA;AACA;AACA,SAASmB,8BAA8BA,CAACC,MAAM,EAAE;EAC9C,IAAIC,MAAM,GAAG,EAAE;EACfjC,MAAM,CAACkC,IAAI,CAACF,MAAM,EAAE,UAAUG,KAAK,EAAEf,GAAG,EAAE;IACxC,IAAIG,YAAY,GAAGY,KAAK,CAACZ,YAAY;IACrC,IAAIC,SAAS,GAAGW,KAAK,CAACX,SAAS;IAC/B,IAAIY,YAAY,GAAGZ,SAAS,CAACH,GAAG;IAChC,IAAIgB,OAAO,GAAG,CAAC,GAAG,CAAC,CAACC,MAAM,CAACtC,MAAM,CAACuC,GAAG,CAACJ,KAAK,CAACT,MAAM,EAAE,UAAUA,MAAM,EAAE;MACpE,OAAOA,MAAM,CAACc,IAAI;IACpB,CAAC,CAAC,CAAC;IACH;IACA,IAAIC,OAAO,GAAG,CAAClB,YAAY,CAACmB,KAAK,CAACC,aAAa,CAAC,CAAC,CAAC;IAClD3C,MAAM,CAACkC,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,UAAUA,MAAM,EAAE;MAC1C,IAAIkB,OAAO,GAAGlB,MAAM,CAACmB,UAAU,CAAC,CAAC;MACjCJ,OAAO,CAACd,IAAI,CAACD,MAAM,CAACmB,UAAU,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAACG,YAAY,CAACX,YAAY,CAAC,EAAE,UAAUY,GAAG,EAAE;QAC3F,OAAOA,GAAG;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACF;IACA,IAAIC,KAAK,GAAG,CAACZ,OAAO,CAAC/B,IAAI,CAACC,YAAY,CAAC,CAAC;IACxC,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,OAAO,CAAC,CAAC,CAAC,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1C,IAAIE,KAAK,GAAG,EAAE;MACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,OAAO,CAACU,MAAM,EAAEE,CAAC,EAAE,EAAE;QACvCD,KAAK,CAACzB,IAAI,CAACc,OAAO,CAACY,CAAC,CAAC,CAACH,CAAC,CAAC,CAAC;MAC3B;MACAD,KAAK,CAACtB,IAAI,CAACyB,KAAK,CAAC9C,IAAI,CAACC,YAAY,CAAC,CAAC;IACtC;IACA0B,MAAM,CAACN,IAAI,CAACsB,KAAK,CAAC3C,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/B,CAAC,CAAC;EACF,OAAO2B,MAAM,CAAC3B,IAAI,CAAC,MAAM,GAAGF,aAAa,GAAG,MAAM,CAAC;AACrD;AACA;AACA;AACA;AACA,SAASkD,mBAAmBA,CAAC5B,MAAM,EAAE;EACnC,OAAO1B,MAAM,CAACuC,GAAG,CAACb,MAAM,EAAE,UAAUA,MAAM,EAAE;IAC1C,IAAI6B,IAAI,GAAG7B,MAAM,CAACmB,UAAU,CAAC,CAAC;IAC9B,IAAII,KAAK,GAAG,CAACvB,MAAM,CAACc,IAAI,CAAC;IACzB,IAAIgB,IAAI,GAAG,EAAE;IACbD,IAAI,CAACrB,IAAI,CAACqB,IAAI,CAACE,UAAU,EAAE,YAAY;MACrC,IAAIC,MAAM,GAAGC,SAAS,CAACR,MAAM;MAC7B,IAAIS,SAAS,GAAGD,SAAS,CAACD,MAAM,GAAG,CAAC,CAAC;MACrC,IAAIlB,IAAI,GAAGe,IAAI,CAACM,OAAO,CAACD,SAAS,CAAC;MAClC,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,MAAM,GAAG,CAAC,EAAER,CAAC,EAAE,EAAE;QACnCM,IAAI,CAACN,CAAC,CAAC,GAAGS,SAAS,CAACT,CAAC,CAAC;MACxB;MACAD,KAAK,CAACtB,IAAI,CAAC,CAACa,IAAI,GAAGA,IAAI,GAAGjC,YAAY,GAAG,EAAE,IAAIiD,IAAI,CAAClD,IAAI,CAACC,YAAY,CAAC,CAAC;IACzE,CAAC,CAAC;IACF,OAAO0C,KAAK,CAAC3C,IAAI,CAAC,IAAI,CAAC;EACzB,CAAC,CAAC,CAACA,IAAI,CAAC,MAAM,GAAGF,aAAa,GAAG,MAAM,CAAC;AAC1C;AACA,SAAS0D,mBAAmBA,CAACrD,OAAO,EAAE;EACpC,IAAIsD,MAAM,GAAGvD,WAAW,CAACC,OAAO,CAAC;EACjC,OAAO;IACLuD,KAAK,EAAEhE,MAAM,CAACiE,MAAM,CAAC,CAAClC,8BAA8B,CAACgC,MAAM,CAACrD,yBAAyB,CAAC,EAAE4C,mBAAmB,CAACS,MAAM,CAACjC,KAAK,CAAC,CAAC,EAAE,UAAUoC,GAAG,EAAE;MACzI,OAAO,CAAC,CAACA,GAAG,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACvC,CAAC,CAAC,CAAC7D,IAAI,CAAC,MAAM,GAAGF,aAAa,GAAG,MAAM,CAAC;IACxCQ,IAAI,EAAEmD,MAAM,CAACnD;EACf,CAAC;AACH;AACA,SAASwD,IAAIA,CAACF,GAAG,EAAE;EACjB,OAAOA,GAAG,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;AACxD;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACC,KAAK,EAAE;EAC1B;EACA,IAAIC,SAAS,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC,EAAEF,KAAK,CAACG,OAAO,CAAC,IAAI,CAAC,CAAC;EACnD,IAAIF,SAAS,CAACE,OAAO,CAAClE,YAAY,CAAC,IAAI,CAAC,EAAE;IACxC,OAAO,IAAI;EACb;AACF;AACA,IAAImE,cAAc,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAGpE,YAAY,GAAG,IAAI,EAAE,GAAG,CAAC;AAC/D;AACA;AACA;AACA;AACA,SAASqE,gBAAgBA,CAACC,GAAG,EAAE;EAC7B,IAAIC,QAAQ,GAAGD,GAAG,CAACE,KAAK,CAAC,MAAM,CAAC;EAChC,IAAI1C,OAAO,GAAG+B,IAAI,CAACU,QAAQ,CAACE,KAAK,CAAC,CAAC,CAAC,CAACD,KAAK,CAACL,cAAc,CAAC;EAC1D,IAAIO,UAAU,GAAG,EAAE;EACnB,IAAIvD,MAAM,GAAG1B,MAAM,CAACuC,GAAG,CAACF,OAAO,EAAE,UAAU6C,MAAM,EAAE;IACjD,OAAO;MACL1C,IAAI,EAAE0C,MAAM;MACZ3B,IAAI,EAAE;IACR,CAAC;EACH,CAAC,CAAC;EACF,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,QAAQ,CAAC3B,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,IAAIE,KAAK,GAAGgB,IAAI,CAACU,QAAQ,CAAC5B,CAAC,CAAC,CAAC,CAAC6B,KAAK,CAACL,cAAc,CAAC;IACnDO,UAAU,CAACtD,IAAI,CAACyB,KAAK,CAAC4B,KAAK,CAAC,CAAC,CAAC;IAC9B,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACD,MAAM,EAAEE,CAAC,EAAE,EAAE;MACrC3B,MAAM,CAAC2B,CAAC,CAAC,KAAK3B,MAAM,CAAC2B,CAAC,CAAC,CAACE,IAAI,CAACL,CAAC,CAAC,GAAGE,KAAK,CAACC,CAAC,CAAC,CAAC;IAC7C;EACF;EACA,OAAO;IACL3B,MAAM,EAAEA,MAAM;IACduD,UAAU,EAAEA;EACd,CAAC;AACH;AACA,SAASE,iBAAiBA,CAACjB,GAAG,EAAE;EAC9B,IAAIjB,KAAK,GAAGiB,GAAG,CAACa,KAAK,CAAC,MAAM,CAAC;EAC7B,IAAIK,UAAU,GAAGhB,IAAI,CAACnB,KAAK,CAAC+B,KAAK,CAAC,CAAC,CAAC;EACpC,IAAIzB,IAAI,GAAG,EAAE;EACb,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC;IACA;IACA,IAAImC,IAAI,GAAGjB,IAAI,CAACnB,KAAK,CAACC,CAAC,CAAC,CAAC;IACzB,IAAI,CAACmC,IAAI,EAAE;MACT;IACF;IACA,IAAIjC,KAAK,GAAGiC,IAAI,CAACN,KAAK,CAACL,cAAc,CAAC;IACtC,IAAIY,MAAM,GAAG,EAAE;IACf,IAAItB,KAAK,GAAG,KAAK,CAAC;IAClB,IAAIuB,OAAO,GAAG,KAAK;IACnB,IAAIC,KAAK,CAACpC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB;MACAmC,OAAO,GAAG,IAAI;MACdD,MAAM,GAAGlC,KAAK,CAAC,CAAC,CAAC;MACjBA,KAAK,GAAGA,KAAK,CAACoB,KAAK,CAAC,CAAC,CAAC;MACtBjB,IAAI,CAACL,CAAC,CAAC,GAAG;QACRV,IAAI,EAAE8C,MAAM;QACZtB,KAAK,EAAE;MACT,CAAC;MACDA,KAAK,GAAGT,IAAI,CAACL,CAAC,CAAC,CAACc,KAAK;IACvB,CAAC,MAAM;MACLA,KAAK,GAAGT,IAAI,CAACL,CAAC,CAAC,GAAG,EAAE;IACtB;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACD,MAAM,EAAEE,CAAC,EAAE,EAAE;MACrCW,KAAK,CAACrC,IAAI,CAAC,CAACyB,KAAK,CAACC,CAAC,CAAC,CAAC;IACvB;IACA,IAAIW,KAAK,CAACb,MAAM,KAAK,CAAC,EAAE;MACtBoC,OAAO,GAAGhC,IAAI,CAACL,CAAC,CAAC,CAACc,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGT,IAAI,CAACL,CAAC,CAAC,GAAGc,KAAK,CAAC,CAAC,CAAC;IACzD;EACF;EACA,OAAO;IACLxB,IAAI,EAAE4C,UAAU;IAChB7B,IAAI,EAAEA;EACR,CAAC;AACH;AACA,SAASkC,aAAaA,CAACvB,GAAG,EAAEwB,aAAa,EAAE;EACzC,IAAIC,MAAM,GAAGzB,GAAG,CAACa,KAAK,CAAC,IAAIJ,MAAM,CAAC,KAAK,GAAGvE,aAAa,GAAG,KAAK,EAAE,GAAG,CAAC,CAAC;EACtE,IAAIwF,SAAS,GAAG;IACdlE,MAAM,EAAE;EACV,CAAC;EACD1B,MAAM,CAACkC,IAAI,CAACyD,MAAM,EAAE,UAAUrB,KAAK,EAAEuB,GAAG,EAAE;IACxC,IAAIxB,WAAW,CAACC,KAAK,CAAC,EAAE;MACtB,IAAIP,MAAM,GAAGa,gBAAgB,CAACN,KAAK,CAAC;MACpC,IAAIwB,SAAS,GAAGJ,aAAa,CAACG,GAAG,CAAC;MAClC,IAAIE,OAAO,GAAGD,SAAS,CAAClE,OAAO,GAAG,MAAM;MACxC,IAAIkE,SAAS,EAAE;QACbF,SAAS,CAACG,OAAO,CAAC,GAAGH,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE;QAC7CH,SAAS,CAACG,OAAO,CAAC,CAACD,SAAS,CAACjE,SAAS,CAAC,GAAG;UACxC0B,IAAI,EAAEQ,MAAM,CAACkB;QACf,CAAC;QACDW,SAAS,CAAClE,MAAM,GAAGkE,SAAS,CAAClE,MAAM,CAACY,MAAM,CAACyB,MAAM,CAACrC,MAAM,CAAC;MAC3D;IACF,CAAC,MAAM;MACL,IAAIqC,MAAM,GAAGoB,iBAAiB,CAACb,KAAK,CAAC;MACrCsB,SAAS,CAAClE,MAAM,CAACC,IAAI,CAACoC,MAAM,CAAC;IAC/B;EACF,CAAC,CAAC;EACF,OAAO6B,SAAS;AAClB;AACA,IAAII,QAAQ,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC5CnG,SAAS,CAACkG,QAAQ,EAAEC,MAAM,CAAC;EAC3B,SAASD,QAAQA,CAAA,EAAG;IAClB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEvC,SAAS,CAAC,IAAI,IAAI;EACjE;EACAqC,QAAQ,CAACG,SAAS,CAACC,OAAO,GAAG,UAAU3F,OAAO,EAAE4F,GAAG,EAAE;IACnD;IACAC,UAAU,CAAC,YAAY;MACrBD,GAAG,CAACE,cAAc,CAAC;QACjBtF,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIuF,SAAS,GAAGH,GAAG,CAACI,MAAM,CAAC,CAAC;IAC5B,IAAI/D,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI,IAAI,CAACgE,IAAI,EAAE;MACbF,SAAS,CAACG,WAAW,CAAC,IAAI,CAACD,IAAI,CAAC;IAClC;IACA,IAAIE,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACxC;IACAF,IAAI,CAACG,KAAK,CAACC,OAAO,GAAG,6DAA6D;IAClFJ,IAAI,CAACG,KAAK,CAACE,eAAe,GAAGvE,KAAK,CAACwE,GAAG,CAAC,iBAAiB,CAAC,IAAI,MAAM;IACnE;IACA,IAAIhC,MAAM,GAAG2B,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IACzC,IAAIK,IAAI,GAAGzE,KAAK,CAACwE,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;IAClChC,MAAM,CAACkC,SAAS,GAAGD,IAAI,CAAC,CAAC,CAAC,IAAIzE,KAAK,CAACwE,GAAG,CAAC,OAAO,CAAC;IAChDhC,MAAM,CAAC6B,KAAK,CAACC,OAAO,GAAG,kBAAkB;IACzC9B,MAAM,CAAC6B,KAAK,CAACM,KAAK,GAAG3E,KAAK,CAACwE,GAAG,CAAC,WAAW,CAAC;IAC3C,IAAII,QAAQ,GAAGT,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAIS,QAAQ,GAAGV,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;IACjDQ,QAAQ,CAACP,KAAK,CAACC,OAAO,GAAG,eAAe;IACxC,IAAIQ,eAAe,GAAG9E,KAAK,CAACwE,GAAG,CAAC,iBAAiB,CAAC;IAClD,IAAIO,eAAe,GAAG/E,KAAK,CAACwE,GAAG,CAAC,iBAAiB,CAAC;IAClD,IAAInD,MAAM,GAAGD,mBAAmB,CAACrD,OAAO,CAAC;IACzC,IAAIT,MAAM,CAAC0H,UAAU,CAACF,eAAe,CAAC,EAAE;MACtC,IAAIG,SAAS,GAAGH,eAAe,CAACnB,GAAG,CAACuB,SAAS,CAAC,CAAC,CAAC;MAChD,IAAI5H,MAAM,CAAC6H,QAAQ,CAACF,SAAS,CAAC,EAAE;QAC9BL,QAAQ,CAACF,SAAS,GAAGO,SAAS;MAChC,CAAC,MAAM,IAAI3H,MAAM,CAAC8H,KAAK,CAACH,SAAS,CAAC,EAAE;QAClCL,QAAQ,CAACS,WAAW,CAACJ,SAAS,CAAC;MACjC;IACF,CAAC,MAAM;MACL;MACAJ,QAAQ,CAACS,QAAQ,GAAGtF,KAAK,CAACwE,GAAG,CAAC,UAAU,CAAC;MACzC,IAAIH,KAAK,GAAGQ,QAAQ,CAACR,KAAK;MAC1B;MACAA,KAAK,CAACC,OAAO,GAAG,6IAA6I;MAC7JD,KAAK,CAACM,KAAK,GAAG3E,KAAK,CAACwE,GAAG,CAAC,WAAW,CAAC;MACpCH,KAAK,CAACkB,WAAW,GAAGvF,KAAK,CAACwE,GAAG,CAAC,qBAAqB,CAAC;MACpDH,KAAK,CAACE,eAAe,GAAGvE,KAAK,CAACwE,GAAG,CAAC,eAAe,CAAC;MAClDK,QAAQ,CAACvD,KAAK,GAAGD,MAAM,CAACC,KAAK;MAC7BsD,QAAQ,CAACS,WAAW,CAACR,QAAQ,CAAC;IAChC;IACA,IAAI7B,aAAa,GAAG3B,MAAM,CAACnD,IAAI;IAC/B,IAAIsH,eAAe,GAAGrB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACnDoB,eAAe,CAACnB,KAAK,CAACC,OAAO,GAAG,6CAA6C;IAC7E;IACA,IAAImB,WAAW,GAAG,2GAA2G;IAC7H,IAAIC,WAAW,GAAGvB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC/C,IAAIuB,aAAa,GAAGxB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACjDqB,WAAW,IAAI,oBAAoB,GAAGzF,KAAK,CAACwE,GAAG,CAAC,aAAa,CAAC;IAC9DiB,WAAW,IAAI,SAAS,GAAGzF,KAAK,CAACwE,GAAG,CAAC,iBAAiB,CAAC;IACvD,IAAIoB,IAAI,GAAG,IAAI;IACf,SAASC,KAAKA,CAAA,EAAG;MACf/B,SAAS,CAACG,WAAW,CAACC,IAAI,CAAC;MAC3B0B,IAAI,CAAC5B,IAAI,GAAG,IAAI;IAClB;IACAxG,gBAAgB,CAACkI,WAAW,EAAE,OAAO,EAAEG,KAAK,CAAC;IAC7CrI,gBAAgB,CAACmI,aAAa,EAAE,OAAO,EAAE,YAAY;MACnD,IAAIZ,eAAe,IAAI,IAAI,IAAID,eAAe,IAAI,IAAI,IAAIC,eAAe,IAAI,IAAI,IAAID,eAAe,IAAI,IAAI,EAAE;QAC5G,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC;UACAvI,IAAI,CAAC,4IAA4I,CAAC;QACpJ;QACAoI,KAAK,CAAC,CAAC;QACP;MACF;MACA,IAAI3C,SAAS;MACb,IAAI;QACF,IAAI5F,MAAM,CAAC0H,UAAU,CAACD,eAAe,CAAC,EAAE;UACtC7B,SAAS,GAAG6B,eAAe,CAACH,QAAQ,EAAEjB,GAAG,CAACuB,SAAS,CAAC,CAAC,CAAC;QACxD,CAAC,MAAM;UACLhC,SAAS,GAAGH,aAAa,CAAC8B,QAAQ,CAACvD,KAAK,EAAE0B,aAAa,CAAC;QAC1D;MACF,CAAC,CAAC,OAAOiD,CAAC,EAAE;QACVJ,KAAK,CAAC,CAAC;QACP,MAAM,IAAIK,KAAK,CAAC,yBAAyB,GAAGD,CAAC,CAAC;MAChD;MACA,IAAI/C,SAAS,EAAE;QACbS,GAAG,CAACE,cAAc,CAAC;UACjBtF,IAAI,EAAE,gBAAgB;UACtB2E,SAAS,EAAEA;QACb,CAAC,CAAC;MACJ;MACA2C,KAAK,CAAC,CAAC;IACT,CAAC,CAAC;IACFH,WAAW,CAAChB,SAAS,GAAGD,IAAI,CAAC,CAAC,CAAC;IAC/BkB,aAAa,CAACjB,SAAS,GAAGD,IAAI,CAAC,CAAC,CAAC;IACjCkB,aAAa,CAACtB,KAAK,CAACC,OAAO,GAAGoB,WAAW,CAACrB,KAAK,CAACC,OAAO,GAAGmB,WAAW;IACrE,CAACzF,KAAK,CAACwE,GAAG,CAAC,UAAU,CAAC,IAAIgB,eAAe,CAACH,WAAW,CAACM,aAAa,CAAC;IACpEH,eAAe,CAACH,WAAW,CAACK,WAAW,CAAC;IACxCxB,IAAI,CAACmB,WAAW,CAAC7C,MAAM,CAAC;IACxB0B,IAAI,CAACmB,WAAW,CAACT,QAAQ,CAAC;IAC1BV,IAAI,CAACmB,WAAW,CAACG,eAAe,CAAC;IACjCZ,QAAQ,CAACP,KAAK,CAAC8B,MAAM,GAAGrC,SAAS,CAACsC,YAAY,GAAG,EAAE,GAAG,IAAI;IAC1DtC,SAAS,CAACuB,WAAW,CAACnB,IAAI,CAAC;IAC3B,IAAI,CAACF,IAAI,GAAGE,IAAI;EAClB,CAAC;EACDZ,QAAQ,CAACG,SAAS,CAAC4C,MAAM,GAAG,UAAUtI,OAAO,EAAE4F,GAAG,EAAE;IAClD,IAAI,CAACK,IAAI,IAAIL,GAAG,CAACI,MAAM,CAAC,CAAC,CAACE,WAAW,CAAC,IAAI,CAACD,IAAI,CAAC;EAClD,CAAC;EACDV,QAAQ,CAACG,SAAS,CAAC6C,OAAO,GAAG,UAAUvI,OAAO,EAAE4F,GAAG,EAAE;IACnD,IAAI,CAAC0C,MAAM,CAACtI,OAAO,EAAE4F,GAAG,CAAC;EAC3B,CAAC;EACDL,QAAQ,CAACiD,gBAAgB,GAAG,UAAUxI,OAAO,EAAE;IAC7C,IAAIyI,aAAa,GAAG;MAClBC,IAAI,EAAE,IAAI;MACVnB,QAAQ,EAAE,KAAK;MACfR,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAE,IAAI;MACrB;MACA2B,IAAI,EAAE,6GAA6G;MACnHC,KAAK,EAAE5I,OAAO,CAAC6I,cAAc,CAAC,CAAC,CAACpC,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;MACrEC,IAAI,EAAE1G,OAAO,CAAC6I,cAAc,CAAC,CAAC,CAACpC,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;MACnED,eAAe,EAAE,MAAM;MACvBsC,SAAS,EAAE,MAAM;MACjBC,aAAa,EAAE,MAAM;MACrBC,mBAAmB,EAAE,MAAM;MAC3BC,WAAW,EAAE,SAAS;MACtBC,eAAe,EAAE;IACnB,CAAC;IACD,OAAOT,aAAa;EACtB,CAAC;EACD,OAAOlD,QAAQ;AACjB,CAAC,CAAC/F,cAAc,CAAC;AACjB;AACA;AACA;AACA,SAAS2J,kBAAkBA,CAACC,OAAO,EAAEC,YAAY,EAAE;EACjD,OAAO9J,MAAM,CAACuC,GAAG,CAACsH,OAAO,EAAE,UAAUE,MAAM,EAAElE,GAAG,EAAE;IAChD,IAAImE,QAAQ,GAAGF,YAAY,IAAIA,YAAY,CAACjE,GAAG,CAAC;IAChD,IAAI7F,MAAM,CAACiK,QAAQ,CAACD,QAAQ,CAAC,IAAI,CAAChK,MAAM,CAACkK,OAAO,CAACF,QAAQ,CAAC,EAAE;MAC1D,IAAIG,cAAc,GAAGnK,MAAM,CAACiK,QAAQ,CAACF,MAAM,CAAC,IAAI,CAAC/J,MAAM,CAACkK,OAAO,CAACH,MAAM,CAAC;MACvE,IAAI,CAACI,cAAc,EAAE;QACnBJ,MAAM,GAAG;UACP/F,KAAK,EAAE+F;QACT,CAAC;MACH;MACA;MACA,IAAIK,gBAAgB,GAAGJ,QAAQ,CAACxH,IAAI,IAAI,IAAI,IAAIuH,MAAM,CAACvH,IAAI,IAAI,IAAI;MACnE;MACAuH,MAAM,GAAG/J,MAAM,CAACqK,QAAQ,CAACN,MAAM,EAAEC,QAAQ,CAAC;MAC1CI,gBAAgB,IAAI,OAAOL,MAAM,CAACvH,IAAI;MACtC,OAAOuH,MAAM;IACf,CAAC,MAAM;MACL,OAAOA,MAAM;IACf;EACF,CAAC,CAAC;AACJ;AACA;AACAhK,OAAO,CAACuK,cAAc,CAAC;EACrBrJ,IAAI,EAAE,gBAAgB;EACtBsJ,KAAK,EAAE,iBAAiB;EACxBC,MAAM,EAAE;AACV,CAAC,EAAE,UAAUC,OAAO,EAAEhK,OAAO,EAAE;EAC7B,IAAIiK,gBAAgB,GAAG,EAAE;EACzB1K,MAAM,CAACkC,IAAI,CAACuI,OAAO,CAAC7E,SAAS,CAAClE,MAAM,EAAE,UAAUiJ,SAAS,EAAE;IACzD,IAAI7J,WAAW,GAAGL,OAAO,CAACmK,eAAe,CAACD,SAAS,CAACnI,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5D,IAAI,CAAC1B,WAAW,EAAE;MAChB;MACA;MACA4J,gBAAgB,CAAC/I,IAAI,CAAC3B,MAAM,CAAC6K,MAAM,CAAC;QAClC;QACA5J,IAAI,EAAE;MACR,CAAC,EAAE0J,SAAS,CAAC,CAAC;IAChB,CAAC,MAAM;MACL,IAAIb,YAAY,GAAGhJ,WAAW,CAACoG,GAAG,CAAC,MAAM,CAAC;MAC1CwD,gBAAgB,CAAC/I,IAAI,CAAC;QACpBa,IAAI,EAAEmI,SAAS,CAACnI,IAAI;QACpBe,IAAI,EAAEqG,kBAAkB,CAACe,SAAS,CAACpH,IAAI,EAAEuG,YAAY;MACvD,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACFrJ,OAAO,CAACqK,WAAW,CAAC9K,MAAM,CAACqK,QAAQ,CAAC;IAClC3I,MAAM,EAAEgJ;EACV,CAAC,EAAED,OAAO,CAAC7E,SAAS,CAAC,CAAC;AACxB,CAAC,CAAC;AACF,eAAeI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}