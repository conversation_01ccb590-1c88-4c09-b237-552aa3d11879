{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend } from 'zrender/lib/core/util.js';\nimport { deprecateReplaceLog } from '../../util/log.js';\nimport { retrieveTargetInfo, aboveViewRoot } from '../helper/treeHelper.js';\nexport var ROOT_TO_NODE_ACTION = 'sunburstRootToNode';\nvar HIGHLIGHT_ACTION = 'sunburstHighlight';\nvar UNHIGHLIGHT_ACTION = 'sunburstUnhighlight';\nexport function installSunburstAction(registers) {\n  registers.registerAction({\n    type: ROOT_TO_NODE_ACTION,\n    update: 'updateView'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'sunburst',\n      query: payload\n    }, handleRootToNode);\n    function handleRootToNode(model, index) {\n      var targetInfo = retrieveTargetInfo(payload, [ROOT_TO_NODE_ACTION], model);\n      if (targetInfo) {\n        var originViewRoot = model.getViewRoot();\n        if (originViewRoot) {\n          payload.direction = aboveViewRoot(originViewRoot, targetInfo.node) ? 'rollUp' : 'drillDown';\n        }\n        model.resetViewRoot(targetInfo.node);\n      }\n    }\n  });\n  registers.registerAction({\n    type: HIGHLIGHT_ACTION,\n    update: 'none'\n  }, function (payload, ecModel, api) {\n    // Clone\n    payload = extend({}, payload);\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'sunburst',\n      query: payload\n    }, handleHighlight);\n    function handleHighlight(model) {\n      var targetInfo = retrieveTargetInfo(payload, [HIGHLIGHT_ACTION], model);\n      if (targetInfo) {\n        payload.dataIndex = targetInfo.node.dataIndex;\n      }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('sunburstHighlight', 'highlight');\n    }\n    // Fast forward action\n    api.dispatchAction(extend(payload, {\n      type: 'highlight'\n    }));\n  });\n  registers.registerAction({\n    type: UNHIGHLIGHT_ACTION,\n    update: 'updateView'\n  }, function (payload, ecModel, api) {\n    payload = extend({}, payload);\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('sunburstUnhighlight', 'downplay');\n    }\n    api.dispatchAction(extend(payload, {\n      type: 'downplay'\n    }));\n  });\n}", "map": {"version": 3, "names": ["extend", "deprecateReplaceLog", "retrieveTargetInfo", "aboveViewRoot", "ROOT_TO_NODE_ACTION", "HIGHLIGHT_ACTION", "UNHIGHLIGHT_ACTION", "installSunburstAction", "registers", "registerAction", "type", "update", "payload", "ecModel", "eachComponent", "mainType", "subType", "query", "handleRootToNode", "model", "index", "targetInfo", "originViewRoot", "getViewRoot", "direction", "node", "resetViewRoot", "api", "handleHighlight", "dataIndex", "process", "env", "NODE_ENV", "dispatchAction"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/chart/sunburst/sunburstAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend } from 'zrender/lib/core/util.js';\nimport { deprecateReplaceLog } from '../../util/log.js';\nimport { retrieveTargetInfo, aboveViewRoot } from '../helper/treeHelper.js';\nexport var ROOT_TO_NODE_ACTION = 'sunburstRootToNode';\nvar HIGHLIGHT_ACTION = 'sunburstHighlight';\nvar UNHIGHLIGHT_ACTION = 'sunburstUnhighlight';\nexport function installSunburstAction(registers) {\n  registers.registerAction({\n    type: ROOT_TO_NODE_ACTION,\n    update: 'updateView'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'sunburst',\n      query: payload\n    }, handleRootToNode);\n    function handleRootToNode(model, index) {\n      var targetInfo = retrieveTargetInfo(payload, [ROOT_TO_NODE_ACTION], model);\n      if (targetInfo) {\n        var originViewRoot = model.getViewRoot();\n        if (originViewRoot) {\n          payload.direction = aboveViewRoot(originViewRoot, targetInfo.node) ? 'rollUp' : 'drillDown';\n        }\n        model.resetViewRoot(targetInfo.node);\n      }\n    }\n  });\n  registers.registerAction({\n    type: HIGHLIGHT_ACTION,\n    update: 'none'\n  }, function (payload, ecModel, api) {\n    // Clone\n    payload = extend({}, payload);\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'sunburst',\n      query: payload\n    }, handleHighlight);\n    function handleHighlight(model) {\n      var targetInfo = retrieveTargetInfo(payload, [HIGHLIGHT_ACTION], model);\n      if (targetInfo) {\n        payload.dataIndex = targetInfo.node.dataIndex;\n      }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('sunburstHighlight', 'highlight');\n    }\n    // Fast forward action\n    api.dispatchAction(extend(payload, {\n      type: 'highlight'\n    }));\n  });\n  registers.registerAction({\n    type: UNHIGHLIGHT_ACTION,\n    update: 'updateView'\n  }, function (payload, ecModel, api) {\n    payload = extend({}, payload);\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('sunburstUnhighlight', 'downplay');\n    }\n    api.dispatchAction(extend(payload, {\n      type: 'downplay'\n    }));\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,0BAA0B;AACjD,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,kBAAkB,EAAEC,aAAa,QAAQ,yBAAyB;AAC3E,OAAO,IAAIC,mBAAmB,GAAG,oBAAoB;AACrD,IAAIC,gBAAgB,GAAG,mBAAmB;AAC1C,IAAIC,kBAAkB,GAAG,qBAAqB;AAC9C,OAAO,SAASC,qBAAqBA,CAACC,SAAS,EAAE;EAC/CA,SAAS,CAACC,cAAc,CAAC;IACvBC,IAAI,EAAEN,mBAAmB;IACzBO,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAE;IAC7BA,OAAO,CAACC,aAAa,CAAC;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,UAAU;MACnBC,KAAK,EAAEL;IACT,CAAC,EAAEM,gBAAgB,CAAC;IACpB,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,KAAK,EAAE;MACtC,IAAIC,UAAU,GAAGnB,kBAAkB,CAACU,OAAO,EAAE,CAACR,mBAAmB,CAAC,EAAEe,KAAK,CAAC;MAC1E,IAAIE,UAAU,EAAE;QACd,IAAIC,cAAc,GAAGH,KAAK,CAACI,WAAW,CAAC,CAAC;QACxC,IAAID,cAAc,EAAE;UAClBV,OAAO,CAACY,SAAS,GAAGrB,aAAa,CAACmB,cAAc,EAAED,UAAU,CAACI,IAAI,CAAC,GAAG,QAAQ,GAAG,WAAW;QAC7F;QACAN,KAAK,CAACO,aAAa,CAACL,UAAU,CAACI,IAAI,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EACFjB,SAAS,CAACC,cAAc,CAAC;IACvBC,IAAI,EAAEL,gBAAgB;IACtBM,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAEc,GAAG,EAAE;IAClC;IACAf,OAAO,GAAGZ,MAAM,CAAC,CAAC,CAAC,EAAEY,OAAO,CAAC;IAC7BC,OAAO,CAACC,aAAa,CAAC;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,UAAU;MACnBC,KAAK,EAAEL;IACT,CAAC,EAAEgB,eAAe,CAAC;IACnB,SAASA,eAAeA,CAACT,KAAK,EAAE;MAC9B,IAAIE,UAAU,GAAGnB,kBAAkB,CAACU,OAAO,EAAE,CAACP,gBAAgB,CAAC,EAAEc,KAAK,CAAC;MACvE,IAAIE,UAAU,EAAE;QACdT,OAAO,CAACiB,SAAS,GAAGR,UAAU,CAACI,IAAI,CAACI,SAAS;MAC/C;IACF;IACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC/B,mBAAmB,CAAC,mBAAmB,EAAE,WAAW,CAAC;IACvD;IACA;IACA0B,GAAG,CAACM,cAAc,CAACjC,MAAM,CAACY,OAAO,EAAE;MACjCF,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACFF,SAAS,CAACC,cAAc,CAAC;IACvBC,IAAI,EAAEJ,kBAAkB;IACxBK,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAEc,GAAG,EAAE;IAClCf,OAAO,GAAGZ,MAAM,CAAC,CAAC,CAAC,EAAEY,OAAO,CAAC;IAC7B,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC/B,mBAAmB,CAAC,qBAAqB,EAAE,UAAU,CAAC;IACxD;IACA0B,GAAG,CAACM,cAAc,CAACjC,MAAM,CAACY,OAAO,EAAE;MACjCF,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}