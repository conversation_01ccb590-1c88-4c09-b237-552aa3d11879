{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.filter.js\";\nimport \"core-js/modules/esnext.iterator.for-each.js\";\nimport \"core-js/modules/esnext.iterator.map.js\";\nexport default {\n  name: \"<PERSON><PERSON><PERSON>\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前的页码\n      pageSize: 10,\n      // 每页显示的个数\n      total: 0,\n      fromVisible: false,\n      form: {},\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      rules: {\n        sfUserName: [{\n          required: true,\n          message: '请输入用户名',\n          trigger: 'blur'\n        }],\n        sfUserId: [{\n          required: true,\n          message: '请输入用户ID',\n          trigger: 'blur'\n        }],\n        status: [{\n          required: true,\n          message: '请选择订单状态',\n          trigger: 'change'\n        }],\n        sfOrderNumber: [{\n          required: true,\n          message: '请输入订单编号',\n          trigger: 'blur'\n        }],\n        sfCreateTime: [{\n          required: true,\n          message: '请选择下单时间',\n          trigger: 'change'\n        }],\n        sfTotalPrice: [{\n          required: true,\n          message: '请输入订单价格',\n          trigger: 'blur'\n        }]\n      },\n      ids: [],\n      selectedOrders: [],\n      // 评价相关\n      commentDialogVisible: false,\n      commentForm: {\n        id: null,\n        sfOrderNumber: '',\n        goodsName: '',\n        sfEvaluation: ''\n      }\n    };\n  },\n  computed: {\n    filteredOrders() {\n      // 只显示真正的订单，排除购物车数据\n      return this.tableData.filter(item => item.status !== '未付款' && !item.sfCartStatus // 排除有购物车状态的记录\n      );\n    }\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    toggleSelection(order) {\n      const index = this.selectedOrders.indexOf(order.id);\n      if (index > -1) {\n        this.selectedOrders.splice(index, 1);\n      } else {\n        this.selectedOrders.push(order.id);\n      }\n      this.ids = this.selectedOrders;\n    },\n    getStatusTagType(status) {\n      switch (status) {\n        case '待支付':\n          return 'danger';\n        case '已支付':\n          return 'warning';\n        case '配送中':\n          return '';\n        case '已完成':\n          return 'success';\n        case '已取消':\n          return 'info';\n        case '退款中':\n          return 'danger';\n        case '已退款':\n          return 'info';\n        default:\n          return '';\n      }\n    },\n    // 显示评价对话框\n    showCommentDialog(row) {\n      // 获取订单详情来显示商品名称\n      this.$request.get(`/dingdan/details/${row.id}`).then(res => {\n        let goodsName = '订单商品';\n        if (res.code === '200' && res.data && res.data.length > 0) {\n          if (res.data.length === 1) {\n            goodsName = res.data[0].foodName;\n          } else {\n            goodsName = `${res.data[0].foodName} 等${res.data.length}件商品`;\n          }\n        }\n        this.commentForm = {\n          id: row.id,\n          sfOrderNumber: row.sfOrderNumber,\n          goodsName: goodsName,\n          sfEvaluation: row.sfEvaluation || ''\n        };\n        this.commentDialogVisible = true;\n      }).catch(() => {\n        // 如果获取失败，使用默认名称\n        this.commentForm = {\n          id: row.id,\n          sfOrderNumber: row.sfOrderNumber,\n          goodsName: '订单商品',\n          sfEvaluation: row.sfEvaluation || ''\n        };\n        this.commentDialogVisible = true;\n      });\n    },\n    // 提交评价\n    submitComment() {\n      if (!this.commentForm.sfEvaluation.trim()) {\n        this.$message.warning('请填写评价内容');\n        return;\n      }\n      this.$request.put('/dingdan/update', {\n        id: this.commentForm.id,\n        sfEvaluation: this.commentForm.sfEvaluation\n      }).then(res => {\n        if (res.code === '200') {\n          this.$message.success('评价提交成功');\n          this.commentDialogVisible = false;\n          this.load(this.pageNum); // 刷新列表\n        } else {\n          this.$message.error(res.msg || '评价提交失败');\n        }\n      }).catch(() => {\n        this.$message.error('评价提交失败，请重试');\n      });\n    },\n    // 申请退款方法\n    applyRefund(row) {\n      this.$confirm('确定要申请退款吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$request.put('/dingdan/update', {\n          ...row,\n          status: '退款中'\n        }).then(res => {\n          if (res.code === '200') {\n            this.$message.success('退款申请已提交，等待商家处理！');\n            this.load(this.pageNum);\n          } else {\n            this.$message.error(res.msg || '退款申请失败');\n          }\n        }).catch(() => {\n          this.$message.error('退款申请失败，请重试');\n        });\n      }).catch(() => {\n        this.$message.info('已取消退款申请');\n      });\n    },\n    // 显示订单详情\n    showOrderDetails(order) {\n      this.$request.get(`/dingdan/details/${order.id}`).then(res => {\n        if (res.code === '200') {\n          const orderItems = res.data || [];\n          if (orderItems.length === 0) {\n            this.$message.info('该订单暂无详情信息');\n            return;\n          }\n\n          // 构建详情显示内容\n          let detailsHtml = '<div style=\"max-height: 400px; overflow-y: auto;\">';\n          detailsHtml += `<h4>订单编号：${order.sfOrderNumber}</h4>`;\n          detailsHtml += '<table style=\"width: 100%; border-collapse: collapse;\">';\n          detailsHtml += '<tr style=\"background: #f5f5f5;\"><th style=\"padding: 8px; border: 1px solid #ddd;\">商品名称</th><th style=\"padding: 8px; border: 1px solid #ddd;\">单价</th><th style=\"padding: 8px; border: 1px solid #ddd;\">数量</th><th style=\"padding: 8px; border: 1px solid #ddd;\">小计</th></tr>';\n          orderItems.forEach(item => {\n            detailsHtml += `<tr>\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">${item.foodName}</td>\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">¥${item.foodPrice}</td>\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">${item.quantity}</td>\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">¥${item.subtotal}</td>\n                        </tr>`;\n          });\n          detailsHtml += '</table></div>';\n          this.$alert(detailsHtml, '订单详情', {\n            dangerouslyUseHTMLString: true,\n            customClass: 'order-details-dialog'\n          });\n        } else {\n          this.$message.error('获取订单详情失败');\n        }\n      }).catch(() => {\n        this.$message.error('获取订单详情失败');\n      });\n    },\n    handleAdd() {\n      this.form = {\n        status: '已付款',\n        // 默认状态设为已付款\n        sfCreateTime: new Date()\n      };\n      this.fromVisible = true;\n    },\n    handleEdit(row) {\n      this.form = JSON.parse(JSON.stringify(row));\n      this.fromVisible = true;\n    },\n    save() {\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          if (this.form.sfCreateTime instanceof Date) {\n            this.form.sfCreateTime = this.formatDateTime(this.form.sfCreateTime);\n          }\n          this.$request({\n            url: this.form.id ? '/dingdan/update' : '/dingdan/add',\n            method: this.form.id ? 'PUT' : 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              this.$message.success('保存成功');\n              this.load(1);\n              this.fromVisible = false;\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    },\n    formatDateTime(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      const seconds = String(date.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n    del(id) {\n      this.$confirm('您确定删除吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/dingdan/delete/' + id).then(res => {\n          if (res.code === '200') {\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {});\n    },\n    handleSelectionChange(rows) {\n      this.ids = rows.map(v => v.id);\n    },\n    delBatch() {\n      if (!this.ids.length) {\n        this.$message.warning('请选择数据');\n        return;\n      }\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/dingdan/delete/batch', {\n          data: this.ids\n        }).then(res => {\n          if (res.code === '200') {\n            this.$message.success('操作成功');\n            this.selectedOrders = [];\n            this.load(1);\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {});\n    },\n    load(pageNum) {\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/dingdan/selectPages', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list || [];\n          this.total = res.data?.total || 0;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "fromVisible", "form", "user", "JSON", "parse", "localStorage", "getItem", "rules", "sfUserName", "required", "message", "trigger", "sfUserId", "status", "sfOrderNumber", "sfCreateTime", "sfTotalPrice", "ids", "selectedOrders", "commentDialogVisible", "commentForm", "id", "goodsName", "sfEvaluation", "computed", "filteredOrders", "filter", "item", "sfCartStatus", "created", "load", "methods", "toggleSelection", "order", "index", "indexOf", "splice", "push", "getStatusTagType", "showCommentDialog", "row", "$request", "get", "then", "res", "code", "length", "foodName", "catch", "submitComment", "trim", "$message", "warning", "put", "success", "error", "msg", "applyRefund", "$confirm", "confirmButtonText", "cancelButtonText", "type", "info", "showOrderDetails", "orderItems", "detailsHtml", "for<PERSON>ach", "foodPrice", "quantity", "subtotal", "$alert", "dangerouslyUseHTMLString", "customClass", "handleAdd", "Date", "handleEdit", "stringify", "save", "$refs", "formRef", "validate", "valid", "formatDateTime", "url", "method", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "del", "response", "delete", "handleSelectionChange", "rows", "map", "v", "delBatch", "params", "list", "handleCurrentChange"], "sources": ["src/views/front/Dingdan.vue"], "sourcesContent": ["<template>\r\n    <div class=\"orders-container\">\r\n        <!-- 操作按钮 -->\r\n        <div class=\"content-section\">\r\n            <div class=\"operation-section\">\r\n                <el-button \r\n                    type=\"danger\" \r\n                    size=\"medium\"\r\n                    @click=\"delBatch\"\r\n                    :disabled=\"!ids.length\"\r\n                    class=\"batch-delete-btn\">\r\n                    <i class=\"el-icon-delete\"></i>\r\n                    批量删除 ({{ ids.length }})\r\n                </el-button>\r\n            </div>\r\n\r\n            <!-- 订单列表 -->\r\n            <div class=\"orders-list\">\r\n                <div v-if=\"filteredOrders.length === 0\" class=\"empty-state\">\r\n                    <i class=\"el-icon-document\"></i>\r\n                    <h3>暂无订单</h3>\r\n                    <p>您还没有任何订单记录</p>\r\n                </div>\r\n                \r\n                <div v-else class=\"orders-grid\">\r\n                    <div \r\n                        v-for=\"order in filteredOrders\" \r\n                        :key=\"order.id\"\r\n                        class=\"order-card\"\r\n                        :class=\"{ 'selected': selectedOrders.includes(order.id) }\"\r\n                        @click=\"toggleSelection(order)\">\r\n                        \r\n                        <div class=\"order-header\">\r\n                            <div class=\"order-info\">\r\n                                <div class=\"order-id\">订单编号：{{ order.sfOrderNumber }}</div>\r\n                                <div class=\"order-time\">{{ order.sfCreateTime }}</div>\r\n                            </div>\r\n                            <div class=\"order-status\">\r\n                                <el-tag \r\n                                    :type=\"getStatusTagType(order.status)\"\r\n                                    size=\"medium\"\r\n                                    class=\"status-tag\">\r\n                                    {{ order.status }}\r\n                                </el-tag>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"order-content\">\r\n                            <div class=\"order-details\">\r\n                                <div class=\"detail-item\">\r\n                                    <i class=\"el-icon-user detail-icon\"></i>\r\n                                    <span class=\"detail-label\">用户：</span>\r\n                                    <span class=\"detail-value\">{{ order.sfUserName }}</span>\r\n                                </div>\r\n                                <div class=\"detail-item\" v-if=\"order.sfRemark\">\r\n                                    <i class=\"el-icon-chat-line-square detail-icon\"></i>\r\n                                    <span class=\"detail-label\">备注：</span>\r\n                                    <span class=\"detail-value\">{{ order.sfRemark }}</span>\r\n                                </div>\r\n                                <div class=\"detail-item\" v-if=\"order.sfEvaluation\">\r\n                                    <i class=\"el-icon-star-on detail-icon\"></i>\r\n                                    <span class=\"detail-label\">评价：</span>\r\n                                    <span class=\"detail-value\">{{ order.sfEvaluation }}</span>\r\n                                </div>\r\n                            </div>\r\n                            \r\n                            <div class=\"order-price\">\r\n                                <div class=\"price-label\">订单金额</div>\r\n                                <div class=\"price-value\">¥{{ order.sfTotalPrice }}</div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"order-actions\">\r\n                            <el-button\r\n                                type=\"info\"\r\n                                size=\"small\"\r\n                                @click.stop=\"showOrderDetails(order)\"\r\n                                class=\"action-btn\">\r\n                                <i class=\"el-icon-view\"></i>\r\n                                查看详情\r\n                            </el-button>\r\n                            <el-button\r\n                                v-if=\"order.status === '配送中' && !order.sfEvaluation\"\r\n                                type=\"primary\"\r\n                                size=\"small\"\r\n                                @click.stop=\"showCommentDialog(order)\"\r\n                                class=\"action-btn\">\r\n                                <i class=\"el-icon-edit\"></i>\r\n                                评价\r\n                            </el-button>\r\n                            <el-button\r\n                                v-if=\"order.status === '配送中' || order.status === '已完成'\"\r\n                                type=\"warning\"\r\n                                size=\"small\"\r\n                                @click.stop=\"applyRefund(order)\"\r\n                                class=\"action-btn\">\r\n                                <i class=\"el-icon-refresh-left\"></i>\r\n                                申请退款\r\n                            </el-button>\r\n                            <el-button\r\n                                type=\"danger\"\r\n                                size=\"small\"\r\n                                @click.stop=\"del(order.id)\"\r\n                                class=\"action-btn\">\r\n                                <i class=\"el-icon-delete\"></i>\r\n                                删除\r\n                            </el-button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 分页 -->\r\n            <div class=\"pagination-section\">\r\n                <el-pagination\r\n                    background\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageNum\"\r\n                    :page-size=\"pageSize\"\r\n                    layout=\"prev, pager, next\"\r\n                    :total=\"total\"\r\n                    class=\"custom-pagination\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 评价对话框 -->\r\n        <el-dialog \r\n            title=\"订单评价\" \r\n            :visible.sync=\"commentDialogVisible\" \r\n            width=\"500px\" \r\n            :close-on-click-modal=\"false\"\r\n            custom-class=\"comment-dialog\">\r\n            <div class=\"comment-form\">\r\n                <div class=\"order-summary\">\r\n                    <div class=\"summary-item\">\r\n                        <span class=\"summary-label\">订单编号：</span>\r\n                        <span class=\"summary-value\">{{ commentForm.sfOrderNumber }}</span>\r\n                    </div>\r\n                    <div class=\"summary-item\">\r\n                        <span class=\"summary-label\">商品名称：</span>\r\n                        <span class=\"summary-value\">{{ commentForm.goodsName }}</span>\r\n                    </div>\r\n                </div>\r\n                \r\n                <el-form :model=\"commentForm\" label-width=\"80px\">\r\n                    <el-form-item label=\"评价内容\" prop=\"sfEvaluation\">\r\n                        <el-input\r\n                            type=\"textarea\"\r\n                            v-model=\"commentForm.sfEvaluation\"\r\n                            placeholder=\"请输入您的评价，分享您的用餐体验...\"\r\n                            :rows=\"4\"\r\n                            maxlength=\"200\"\r\n                            show-word-limit\r\n                            class=\"comment-textarea\">\r\n                        </el-input>\r\n                    </el-form-item>\r\n                </el-form>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"commentDialogVisible = false\" class=\"cancel-btn\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"submitComment\" class=\"submit-btn\">提交评价</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Dingdan\",\r\n    data() {\r\n        return {\r\n            tableData: [],  // 所有的数据\r\n            pageNum: 1,   // 当前的页码\r\n            pageSize: 10,  // 每页显示的个数\r\n            total: 0,\r\n\r\n            fromVisible: false,\r\n            form: {},\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n            rules: {\r\n                sfUserName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],\r\n                sfUserId: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],\r\n                status: [{ required: true, message: '请选择订单状态', trigger: 'change' }],\r\n                sfOrderNumber: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],\r\n                sfCreateTime: [{ required: true, message: '请选择下单时间', trigger: 'change' }],\r\n                sfTotalPrice: [{ required: true, message: '请输入订单价格', trigger: 'blur' }],\r\n            },\r\n            ids: [],\r\n            selectedOrders: [],\r\n\r\n            // 评价相关\r\n            commentDialogVisible: false,\r\n            commentForm: {\r\n                id: null,\r\n                sfOrderNumber: '',\r\n                goodsName: '',\r\n                sfEvaluation: ''\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n        filteredOrders() {\r\n            // 只显示真正的订单，排除购物车数据\r\n            return this.tableData.filter(item => \r\n                item.status !== '未付款' && \r\n                !item.sfCartStatus // 排除有购物车状态的记录\r\n            )\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)\r\n    },\r\n    methods: {\r\n        toggleSelection(order) {\r\n            const index = this.selectedOrders.indexOf(order.id)\r\n            if (index > -1) {\r\n                this.selectedOrders.splice(index, 1)\r\n            } else {\r\n                this.selectedOrders.push(order.id)\r\n            }\r\n            this.ids = this.selectedOrders\r\n        },\r\n\r\n        getStatusTagType(status) {\r\n            switch (status) {\r\n                case '待支付': return 'danger';\r\n                case '已支付': return 'warning';\r\n                case '配送中': return '';\r\n                case '已完成': return 'success';\r\n                case '已取消': return 'info';\r\n                case '退款中': return 'danger';\r\n                case '已退款': return 'info';\r\n                default: return '';\r\n            }\r\n        },\r\n\r\n        // 显示评价对话框\r\n        showCommentDialog(row) {\r\n            // 获取订单详情来显示商品名称\r\n            this.$request.get(`/dingdan/details/${row.id}`).then(res => {\r\n                let goodsName = '订单商品'\r\n                if (res.code === '200' && res.data && res.data.length > 0) {\r\n                    if (res.data.length === 1) {\r\n                        goodsName = res.data[0].foodName\r\n                    } else {\r\n                        goodsName = `${res.data[0].foodName} 等${res.data.length}件商品`\r\n                    }\r\n                }\r\n                \r\n                this.commentForm = {\r\n                    id: row.id,\r\n                    sfOrderNumber: row.sfOrderNumber,\r\n                    goodsName: goodsName,\r\n                    sfEvaluation: row.sfEvaluation || ''\r\n                }\r\n                this.commentDialogVisible = true\r\n            }).catch(() => {\r\n                // 如果获取失败，使用默认名称\r\n                this.commentForm = {\r\n                    id: row.id,\r\n                    sfOrderNumber: row.sfOrderNumber,\r\n                    goodsName: '订单商品',\r\n                    sfEvaluation: row.sfEvaluation || ''\r\n                }\r\n                this.commentDialogVisible = true\r\n            })\r\n        },\r\n\r\n        // 提交评价\r\n        submitComment() {\r\n            if (!this.commentForm.sfEvaluation.trim()) {\r\n                this.$message.warning('请填写评价内容')\r\n                return\r\n            }\r\n\r\n            this.$request.put('/dingdan/update', {\r\n                id: this.commentForm.id,\r\n                sfEvaluation: this.commentForm.sfEvaluation\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.$message.success('评价提交成功')\r\n                    this.commentDialogVisible = false\r\n                    this.load(this.pageNum) // 刷新列表\r\n                } else {\r\n                    this.$message.error(res.msg || '评价提交失败')\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('评价提交失败，请重试')\r\n            })\r\n        },\r\n\r\n        // 申请退款方法\r\n        applyRefund(row) {\r\n            this.$confirm('确定要申请退款吗?', '提示', {\r\n                confirmButtonText: '确定',\r\n                cancelButtonText: '取消',\r\n                type: 'warning'\r\n            }).then(() => {\r\n                this.$request.put('/dingdan/update', {\r\n                    ...row,\r\n                    status: '退款中'\r\n                }).then(res => {\r\n                    if (res.code === '200') {\r\n                        this.$message.success('退款申请已提交，等待商家处理！')\r\n                        this.load(this.pageNum)\r\n                    } else {\r\n                        this.$message.error(res.msg || '退款申请失败')\r\n                    }\r\n                }).catch(() => {\r\n                    this.$message.error('退款申请失败，请重试')\r\n                })\r\n            }).catch(() => {\r\n                this.$message.info('已取消退款申请')\r\n            })\r\n        },\r\n\r\n        // 显示订单详情\r\n        showOrderDetails(order) {\r\n            this.$request.get(`/dingdan/details/${order.id}`).then(res => {\r\n                if (res.code === '200') {\r\n                    const orderItems = res.data || []\r\n                    if (orderItems.length === 0) {\r\n                        this.$message.info('该订单暂无详情信息')\r\n                        return\r\n                    }\r\n                    \r\n                    // 构建详情显示内容\r\n                    let detailsHtml = '<div style=\"max-height: 400px; overflow-y: auto;\">'\r\n                    detailsHtml += `<h4>订单编号：${order.sfOrderNumber}</h4>`\r\n                    detailsHtml += '<table style=\"width: 100%; border-collapse: collapse;\">'\r\n                    detailsHtml += '<tr style=\"background: #f5f5f5;\"><th style=\"padding: 8px; border: 1px solid #ddd;\">商品名称</th><th style=\"padding: 8px; border: 1px solid #ddd;\">单价</th><th style=\"padding: 8px; border: 1px solid #ddd;\">数量</th><th style=\"padding: 8px; border: 1px solid #ddd;\">小计</th></tr>'\r\n                    \r\n                    orderItems.forEach(item => {\r\n                        detailsHtml += `<tr>\r\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">${item.foodName}</td>\r\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">¥${item.foodPrice}</td>\r\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">${item.quantity}</td>\r\n                            <td style=\"padding: 8px; border: 1px solid #ddd;\">¥${item.subtotal}</td>\r\n                        </tr>`\r\n                    })\r\n                    \r\n                    detailsHtml += '</table></div>'\r\n                    \r\n                    this.$alert(detailsHtml, '订单详情', {\r\n                        dangerouslyUseHTMLString: true,\r\n                        customClass: 'order-details-dialog'\r\n                    })\r\n                } else {\r\n                    this.$message.error('获取订单详情失败')\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('获取订单详情失败')\r\n            })\r\n        },\r\n\r\n        handleAdd() {\r\n            this.form = {\r\n                status: '已付款', // 默认状态设为已付款\r\n                sfCreateTime: new Date()\r\n            }\r\n            this.fromVisible = true\r\n        },\r\n\r\n        handleEdit(row) {\r\n            this.form = JSON.parse(JSON.stringify(row))\r\n            this.fromVisible = true\r\n        },\r\n\r\n        save() {\r\n            this.$refs.formRef.validate((valid) => {\r\n                if (valid) {\r\n                    if (this.form.sfCreateTime instanceof Date) {\r\n                        this.form.sfCreateTime = this.formatDateTime(this.form.sfCreateTime);\r\n                    }\r\n\r\n                    this.$request({\r\n                        url: this.form.id ? '/dingdan/update' : '/dingdan/add',\r\n                        method: this.form.id ? 'PUT' : 'POST',\r\n                        data: this.form\r\n                    }).then(res => {\r\n                        if (res.code === '200') {\r\n                            this.$message.success('保存成功')\r\n                            this.load(1)\r\n                            this.fromVisible = false\r\n                        } else {\r\n                            this.$message.error(res.msg)\r\n                        }\r\n                    })\r\n                }\r\n            })\r\n        },\r\n\r\n        formatDateTime(date) {\r\n            const year = date.getFullYear();\r\n            const month = String(date.getMonth() + 1).padStart(2, '0');\r\n            const day = String(date.getDate()).padStart(2, '0');\r\n            const hours = String(date.getHours()).padStart(2, '0');\r\n            const minutes = String(date.getMinutes()).padStart(2, '0');\r\n            const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n        },\r\n\r\n        del(id) {\r\n            this.$confirm('您确定删除吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n                this.$request.delete('/dingdan/delete/' + id).then(res => {\r\n                    if (res.code === '200') {\r\n                        this.$message.success('操作成功')\r\n                        this.load(1)\r\n                    } else {\r\n                        this.$message.error(res.msg)\r\n                    }\r\n                })\r\n            }).catch(() => {})\r\n        },\r\n\r\n        handleSelectionChange(rows) {\r\n            this.ids = rows.map(v => v.id)\r\n        },\r\n\r\n        delBatch() {\r\n            if (!this.ids.length) {\r\n                this.$message.warning('请选择数据')\r\n                return\r\n            }\r\n            this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n                this.$request.delete('/dingdan/delete/batch', {data: this.ids}).then(res => {\r\n                    if (res.code === '200') {\r\n                        this.$message.success('操作成功')\r\n                        this.selectedOrders = []\r\n                        this.load(1)\r\n                    } else {\r\n                        this.$message.error(res.msg)\r\n                    }\r\n                })\r\n            }).catch(() => {})\r\n        },\r\n\r\n        load(pageNum) {\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/dingdan/selectPages', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list || []\r\n                    this.total = res.data?.total || 0\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            })\r\n        },\r\n\r\n        handleCurrentChange(pageNum) {\r\n            this.load(pageNum)\r\n        },\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.orders-container {\r\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n    min-height: 100vh;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-section {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 40px 20px;\r\n}\r\n\r\n\r\n\r\n/* 操作区域 */\r\n.operation-section {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.batch-delete-btn {\r\n    background: #ef4444;\r\n    border-color: #ef4444;\r\n    border-radius: 8px;\r\n    font-weight: 500;\r\n}\r\n\r\n.batch-delete-btn:hover {\r\n    background: #dc2626;\r\n    border-color: #dc2626;\r\n}\r\n\r\n.batch-delete-btn:disabled {\r\n    background: #e5e7eb;\r\n    border-color: #e5e7eb;\r\n    color: #9ca3af;\r\n}\r\n\r\n/* 订单列表 */\r\n.orders-list {\r\n    margin-bottom: 40px;\r\n}\r\n\r\n.empty-state {\r\n    text-align: center;\r\n    padding: 80px 20px;\r\n    color: #64748b;\r\n}\r\n\r\n.empty-state i {\r\n    font-size: 64px;\r\n    margin-bottom: 16px;\r\n    color: #cbd5e1;\r\n}\r\n\r\n.empty-state h3 {\r\n    font-size: 20px;\r\n    margin-bottom: 8px;\r\n    color: #475569;\r\n}\r\n\r\n.orders-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\r\n    gap: 24px;\r\n}\r\n\r\n.order-card {\r\n    background: white;\r\n    border-radius: 16px;\r\n    padding: 24px;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n    border: 2px solid transparent;\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n}\r\n\r\n.order-card:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);\r\n    border-color: #3b82f6;\r\n}\r\n\r\n.order-card.selected {\r\n    border-color: #3b82f6;\r\n    background: #f0f9ff;\r\n}\r\n\r\n.order-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.order-info {\r\n    flex: 1;\r\n}\r\n\r\n.order-id {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #1e293b;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.order-time {\r\n    font-size: 14px;\r\n    color: #64748b;\r\n}\r\n\r\n.order-status {\r\n    flex-shrink: 0;\r\n}\r\n\r\n.status-tag {\r\n    font-weight: 500;\r\n    border-radius: 12px;\r\n    padding: 4px 12px;\r\n}\r\n\r\n.order-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.order-details {\r\n    flex: 1;\r\n}\r\n\r\n.detail-item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 8px;\r\n    font-size: 14px;\r\n}\r\n\r\n.detail-item:last-child {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.detail-icon {\r\n    width: 16px;\r\n    color: #3b82f6;\r\n    margin-right: 8px;\r\n}\r\n\r\n.detail-label {\r\n    color: #64748b;\r\n    margin-right: 8px;\r\n}\r\n\r\n.detail-value {\r\n    color: #1e293b;\r\n    font-weight: 500;\r\n}\r\n\r\n.order-price {\r\n    text-align: right;\r\n    flex-shrink: 0;\r\n    margin-left: 20px;\r\n}\r\n\r\n.price-label {\r\n    font-size: 12px;\r\n    color: #64748b;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.price-value {\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    color: #3b82f6;\r\n}\r\n\r\n.order-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n    border-radius: 8px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n    transform: translateY(-2px);\r\n}\r\n\r\n/* 分页 */\r\n.pagination-section {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 20px 0;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li {\r\n    background: white;\r\n    border: 1px solid #e5e7eb;\r\n    border-radius: 8px;\r\n    margin: 0 4px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li:hover {\r\n    border-color: #3b82f6;\r\n    color: #3b82f6;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li.active {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    color: white;\r\n}\r\n\r\n/* 评价对话框 */\r\n.comment-dialog >>> .el-dialog {\r\n    border-radius: 16px;\r\n}\r\n\r\n.comment-dialog >>> .el-dialog__header {\r\n    background: #f8fafc;\r\n    border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.comment-form {\r\n    padding: 20px 0;\r\n}\r\n\r\n.order-summary {\r\n    background: #f8fafc;\r\n    border-radius: 12px;\r\n    padding: 16px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.summary-item {\r\n    display: flex;\r\n    margin-bottom: 8px;\r\n    font-size: 14px;\r\n}\r\n\r\n.summary-item:last-child {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.summary-label {\r\n    color: #64748b;\r\n    margin-right: 8px;\r\n}\r\n\r\n.summary-value {\r\n    color: #1e293b;\r\n    font-weight: 500;\r\n}\r\n\r\n.comment-textarea >>> .el-textarea__inner {\r\n    border-radius: 8px;\r\n    border: 1px solid #e2e8f0;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.comment-textarea >>> .el-textarea__inner:focus {\r\n    border-color: #3b82f6;\r\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.dialog-footer {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    gap: 12px;\r\n}\r\n\r\n.cancel-btn {\r\n    border-radius: 8px;\r\n    padding: 10px 20px;\r\n}\r\n\r\n.submit-btn {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    border-radius: 8px;\r\n    padding: 10px 20px;\r\n}\r\n\r\n.submit-btn:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n}\r\n\r\n/* 订单详情对话框样式 */\r\n.order-details-dialog >>> .el-message-box {\r\n    min-width: 500px;\r\n    max-width: 80vw;\r\n}\r\n\r\n.order-details-dialog >>> .el-message-box__content {\r\n    padding: 20px;\r\n}\r\n\r\n.order-details-dialog >>> table {\r\n    margin-top: 10px;\r\n}\r\n\r\n.order-details-dialog >>> th {\r\n    background: #f8fafc !important;\r\n    font-weight: 600;\r\n    color: #374151;\r\n}\r\n\r\n.order-details-dialog >>> td {\r\n    color: #6b7280;\r\n}\r\n\r\n.order-details-dialog >>> tr:nth-child(even) {\r\n    background: #f9fafb;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .orders-grid {\r\n        grid-template-columns: 1fr;\r\n    }\r\n    \r\n\r\n    \r\n    .order-content {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .order-price {\r\n        margin-left: 0;\r\n        margin-top: 16px;\r\n        text-align: left;\r\n    }\r\n}\r\n</style>"], "mappings": ";;;;;AAwKA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAEAC,WAAA;MACAC,IAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,KAAA;QACAC,UAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,QAAA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAE,MAAA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAG,aAAA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAI,YAAA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAK,YAAA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAM,GAAA;MACAC,cAAA;MAEA;MACAC,oBAAA;MACAC,WAAA;QACAC,EAAA;QACAP,aAAA;QACAQ,SAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,eAAA;MACA;MACA,YAAA7B,SAAA,CAAA8B,MAAA,CAAAC,IAAA,IACAA,IAAA,CAAAd,MAAA,cACA,CAAAc,IAAA,CAAAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAC,gBAAAC,KAAA;MACA,MAAAC,KAAA,QAAAhB,cAAA,CAAAiB,OAAA,CAAAF,KAAA,CAAAZ,EAAA;MACA,IAAAa,KAAA;QACA,KAAAhB,cAAA,CAAAkB,MAAA,CAAAF,KAAA;MACA;QACA,KAAAhB,cAAA,CAAAmB,IAAA,CAAAJ,KAAA,CAAAZ,EAAA;MACA;MACA,KAAAJ,GAAA,QAAAC,cAAA;IACA;IAEAoB,iBAAAzB,MAAA;MACA,QAAAA,MAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA;IACA0B,kBAAAC,GAAA;MACA;MACA,KAAAC,QAAA,CAAAC,GAAA,qBAAAF,GAAA,CAAAnB,EAAA,IAAAsB,IAAA,CAAAC,GAAA;QACA,IAAAtB,SAAA;QACA,IAAAsB,GAAA,CAAAC,IAAA,cAAAD,GAAA,CAAAjD,IAAA,IAAAiD,GAAA,CAAAjD,IAAA,CAAAmD,MAAA;UACA,IAAAF,GAAA,CAAAjD,IAAA,CAAAmD,MAAA;YACAxB,SAAA,GAAAsB,GAAA,CAAAjD,IAAA,IAAAoD,QAAA;UACA;YACAzB,SAAA,MAAAsB,GAAA,CAAAjD,IAAA,IAAAoD,QAAA,KAAAH,GAAA,CAAAjD,IAAA,CAAAmD,MAAA;UACA;QACA;QAEA,KAAA1B,WAAA;UACAC,EAAA,EAAAmB,GAAA,CAAAnB,EAAA;UACAP,aAAA,EAAA0B,GAAA,CAAA1B,aAAA;UACAQ,SAAA,EAAAA,SAAA;UACAC,YAAA,EAAAiB,GAAA,CAAAjB,YAAA;QACA;QACA,KAAAJ,oBAAA;MACA,GAAA6B,KAAA;QACA;QACA,KAAA5B,WAAA;UACAC,EAAA,EAAAmB,GAAA,CAAAnB,EAAA;UACAP,aAAA,EAAA0B,GAAA,CAAA1B,aAAA;UACAQ,SAAA;UACAC,YAAA,EAAAiB,GAAA,CAAAjB,YAAA;QACA;QACA,KAAAJ,oBAAA;MACA;IACA;IAEA;IACA8B,cAAA;MACA,UAAA7B,WAAA,CAAAG,YAAA,CAAA2B,IAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAX,QAAA,CAAAY,GAAA;QACAhC,EAAA,OAAAD,WAAA,CAAAC,EAAA;QACAE,YAAA,OAAAH,WAAA,CAAAG;MACA,GAAAoB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAM,QAAA,CAAAG,OAAA;UACA,KAAAnC,oBAAA;UACA,KAAAW,IAAA,MAAAjC,OAAA;QACA;UACA,KAAAsD,QAAA,CAAAI,KAAA,CAAAX,GAAA,CAAAY,GAAA;QACA;MACA,GAAAR,KAAA;QACA,KAAAG,QAAA,CAAAI,KAAA;MACA;IACA;IAEA;IACAE,YAAAjB,GAAA;MACA,KAAAkB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAlB,IAAA;QACA,KAAAF,QAAA,CAAAY,GAAA;UACA,GAAAb,GAAA;UACA3B,MAAA;QACA,GAAA8B,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAAM,QAAA,CAAAG,OAAA;YACA,KAAAxB,IAAA,MAAAjC,OAAA;UACA;YACA,KAAAsD,QAAA,CAAAI,KAAA,CAAAX,GAAA,CAAAY,GAAA;UACA;QACA,GAAAR,KAAA;UACA,KAAAG,QAAA,CAAAI,KAAA;QACA;MACA,GAAAP,KAAA;QACA,KAAAG,QAAA,CAAAW,IAAA;MACA;IACA;IAEA;IACAC,iBAAA9B,KAAA;MACA,KAAAQ,QAAA,CAAAC,GAAA,qBAAAT,KAAA,CAAAZ,EAAA,IAAAsB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,MAAAmB,UAAA,GAAApB,GAAA,CAAAjD,IAAA;UACA,IAAAqE,UAAA,CAAAlB,MAAA;YACA,KAAAK,QAAA,CAAAW,IAAA;YACA;UACA;;UAEA;UACA,IAAAG,WAAA;UACAA,WAAA,gBAAAhC,KAAA,CAAAnB,aAAA;UACAmD,WAAA;UACAA,WAAA;UAEAD,UAAA,CAAAE,OAAA,CAAAvC,IAAA;YACAsC,WAAA;AACA,gFAAAtC,IAAA,CAAAoB,QAAA;AACA,iFAAApB,IAAA,CAAAwC,SAAA;AACA,gFAAAxC,IAAA,CAAAyC,QAAA;AACA,iFAAAzC,IAAA,CAAA0C,QAAA;AACA;UACA;UAEAJ,WAAA;UAEA,KAAAK,MAAA,CAAAL,WAAA;YACAM,wBAAA;YACAC,WAAA;UACA;QACA;UACA,KAAArB,QAAA,CAAAI,KAAA;QACA;MACA,GAAAP,KAAA;QACA,KAAAG,QAAA,CAAAI,KAAA;MACA;IACA;IAEAkB,UAAA;MACA,KAAAxE,IAAA;QACAY,MAAA;QAAA;QACAE,YAAA,MAAA2D,IAAA;MACA;MACA,KAAA1E,WAAA;IACA;IAEA2E,WAAAnC,GAAA;MACA,KAAAvC,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAyE,SAAA,CAAApC,GAAA;MACA,KAAAxC,WAAA;IACA;IAEA6E,KAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,SAAAhF,IAAA,CAAAc,YAAA,YAAA2D,IAAA;YACA,KAAAzE,IAAA,CAAAc,YAAA,QAAAmE,cAAA,MAAAjF,IAAA,CAAAc,YAAA;UACA;UAEA,KAAA0B,QAAA;YACA0C,GAAA,OAAAlF,IAAA,CAAAoB,EAAA;YACA+D,MAAA,OAAAnF,IAAA,CAAAoB,EAAA;YACA1B,IAAA,OAAAM;UACA,GAAA0C,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA,KAAAM,QAAA,CAAAG,OAAA;cACA,KAAAxB,IAAA;cACA,KAAA9B,WAAA;YACA;cACA,KAAAmD,QAAA,CAAAI,KAAA,CAAAX,GAAA,CAAAY,GAAA;YACA;UACA;QACA;MACA;IACA;IAEA0B,eAAAG,IAAA;MACA,MAAAC,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,MAAAC,KAAA,GAAAC,MAAA,CAAAJ,IAAA,CAAAK,QAAA,QAAAC,QAAA;MACA,MAAAC,GAAA,GAAAH,MAAA,CAAAJ,IAAA,CAAAQ,OAAA,IAAAF,QAAA;MACA,MAAAG,KAAA,GAAAL,MAAA,CAAAJ,IAAA,CAAAU,QAAA,IAAAJ,QAAA;MACA,MAAAK,OAAA,GAAAP,MAAA,CAAAJ,IAAA,CAAAY,UAAA,IAAAN,QAAA;MACA,MAAAO,OAAA,GAAAT,MAAA,CAAAJ,IAAA,CAAAc,UAAA,IAAAR,QAAA;MAEA,UAAAL,IAAA,IAAAE,KAAA,IAAAI,GAAA,IAAAE,KAAA,IAAAE,OAAA,IAAAE,OAAA;IACA;IAEAE,IAAA/E,EAAA;MACA,KAAAqC,QAAA;QAAAG,IAAA;MAAA,GAAAlB,IAAA,CAAA0D,QAAA;QACA,KAAA5D,QAAA,CAAA6D,MAAA,sBAAAjF,EAAA,EAAAsB,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAAM,QAAA,CAAAG,OAAA;YACA,KAAAxB,IAAA;UACA;YACA,KAAAqB,QAAA,CAAAI,KAAA,CAAAX,GAAA,CAAAY,GAAA;UACA;QACA;MACA,GAAAR,KAAA;IACA;IAEAuD,sBAAAC,IAAA;MACA,KAAAvF,GAAA,GAAAuF,IAAA,CAAAC,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAArF,EAAA;IACA;IAEAsF,SAAA;MACA,UAAA1F,GAAA,CAAA6B,MAAA;QACA,KAAAK,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAM,QAAA;QAAAG,IAAA;MAAA,GAAAlB,IAAA,CAAA0D,QAAA;QACA,KAAA5D,QAAA,CAAA6D,MAAA;UAAA3G,IAAA,OAAAsB;QAAA,GAAA0B,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAAM,QAAA,CAAAG,OAAA;YACA,KAAApC,cAAA;YACA,KAAAY,IAAA;UACA;YACA,KAAAqB,QAAA,CAAAI,KAAA,CAAAX,GAAA,CAAAY,GAAA;UACA;QACA;MACA,GAAAR,KAAA;IACA;IAEAlB,KAAAjC,OAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAA4C,QAAA,CAAAC,GAAA;QACAkE,MAAA;UACA/G,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA;QAEA;MACA,GAAA6C,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAjD,SAAA,GAAAgD,GAAA,CAAAjD,IAAA,EAAAkH,IAAA;UACA,KAAA9G,KAAA,GAAA6C,GAAA,CAAAjD,IAAA,EAAAI,KAAA;QACA;UACA,KAAAoD,QAAA,CAAAI,KAAA,CAAAX,GAAA,CAAAY,GAAA;QACA;MACA;IACA;IAEAsD,oBAAAjH,OAAA;MACA,KAAAiC,IAAA,CAAAjC,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}