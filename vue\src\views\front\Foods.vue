<template>
    <div class="taobao-container">
        <!-- 商品展示区 -->
        <div class="goods-container">
            <div
                class="goods-item"
                v-for="item in tableData"
                :key="item.id"
                @click="showDetail(item)"
            >
                <div class="goods-img-container">
                    <el-image
                        :src="item.sfImage"
                        fit="cover"
                        class="goods-img"
                    ></el-image>
                </div>
                <div class="goods-info">
                    <div class="goods-title">{{ item.name }}</div>
                    <div class="goods-price">¥{{ item.sfPrice }}</div>

                    <div class="goods-actions">
                        <el-button
                            type="warning"
                            size="mini"
                            @click.stop="addToCart(item.id)"
                            class="cart-btn"
                        >
                            <i class="el-icon-goods"></i> 加购物车
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                background
                @current-change="handleCurrentChange"
                :current-page="pageNum"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="total"
                :pager-count="5"
                prev-text="上一页"
                next-text="下一页"
            >
            </el-pagination>
        </div>

        <!-- 商品详情弹窗 -->
        <el-dialog
            :visible.sync="detailVisible"
            width="60%"
            top="5vh"
            custom-class="goods-detail-dialog"
        >
            <div class="detail-container" v-if="currentGoods">
                <div class="detail-left">
                    <el-image
                        :src="currentGoods.sfImage"
                        fit="contain"
                        class="detail-img"
                    ></el-image>
                </div>
                <div class="detail-right">
                    <h2 class="detail-title">{{ currentGoods.name }}</h2>
                    <div class="detail-price">
                        <span class="price-symbol">¥</span>
                        <span class="price-number">{{ currentGoods.sfPrice }}</span>
                    </div>
                    <div class="detail-info">
                        <div class="info-item">
                            <span class="info-label">商品类型:</span>
                            <span class="info-value">{{ currentGoods.sfCategory }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">库存状态:</span>
                            <span class="info-value">{{ currentGoods.sfStock }}件</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">上架状态:</span>
                            <span class="info-value">{{ currentGoods.sfShelfStatus }}</span>
                        </div>
                    </div>
                    <div class="detail-desc">
                        <h3>商品描述</h3>
                        <p>{{ currentGoods.sfDescription }}</p>
                    </div>
                    <div class="detail-actions">
                        <el-button
                            type="warning"
                            class="cart-btn"
                            @click="addToCart(currentGoods.id)"
                        >
                            <i class="el-icon-goods"></i> 加入购物车
                        </el-button>
                        <el-button
                            type="danger"
                            class="buy-btn"
                            @click="showBuyDialog(currentGoods)"
                        >
                            立即购买
                        </el-button>
                    </div>
                </div>
            </div>
        </el-dialog>

        <!-- 餐桌选择对话框（立即购买用） -->
        <el-dialog 
            title="选择餐桌" 
            :visible.sync="buyTableDialogVisible" 
            width="800px"
            :close-on-click-modal="false">
            <div class="table-selection-container">
                <div class="table-selection-header">
                    <div class="selection-info">
                        <p v-if="buyOrderForm.goodsName">立即购买：{{ buyOrderForm.goodsName }}，总金额：<span class="total-price">¥{{ buyOrderForm.goodsPrice }}</span></p>
                        <p v-if="selectedBuyTable">已选择：{{ selectedBuyTable.tableNumber }}号桌 ({{ selectedBuyTable.seats }}人座，{{ selectedBuyTable.area }})</p>
                    </div>
                </div>
                
                <!-- 餐桌选择组件 -->
                <table-select 
                    v-model="selectedBuyTable" 
                    @table-selected="handleBuyTableSelected">
                </table-select>
            </div>
            
            <template slot="footer">
                <span class="dialog-footer">
                    <el-button @click="buyTableDialogVisible = false">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="confirmBuyOrder"
                        :disabled="!selectedBuyTable">
                        确认下单
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import TableSelect from '@/components/TableSelect.vue'

export default {
    name: "GoodsList",
    components: {
        TableSelect
    },
    data() {
        return {
            tableData: [],  // 商品数据
            pageNum: 1,     // 当前页码
            pageSize: 12,   // 每页12条
            total: 0,       // 总数

            detailVisible: false, // 详情弹窗显示
            currentGoods: null,   // 当前查看的商品
            user: JSON.parse(localStorage.getItem('xm-user') || '{}'), // 当前登录用户

            // 立即购买餐桌选择相关
            buyTableDialogVisible: false,
            selectedBuyTable: null,
            buyOrderForm: {
                goodsId: null,
                goodsName: '',
                goodsPrice: 0
            },
            buySubmitting: false // 防重复提交
        }
    },
    created() {
        this.load(1)
    },
    methods: {
        load(pageNum) {  // 加载商品数据
            if (pageNum) this.pageNum = pageNum
            this.$request.get('/foods/selectPage', {
                params: {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,

                }
            }).then(res => {
                if (res.code === '200') {
                    this.tableData = res.data?.list
                    this.total = res.data?.total
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        showDetail(item) {  // 显示商品详情
            this.currentGoods = item
            this.detailVisible = true
        },
        // 加入购物车
        addToCart(goodsId) {
            this.$request.post('/dingdan/add', {
                sfUserName: this.user.name || '匿名用户',
                sfUserId: this.user.id || 0,
                sfProductIds: goodsId.toString(),
                status: '未付款',
                sfCartStatus: '已加入购物车',
                sfTotalPrice: 0, // 购物车状态下价格为0
                sfCreateTime: new Date().toISOString()
            }).then(res => {
                if (res.code === '200') {
                    this.$message.success('商品已加入购物车！')
                } else {
                    this.$message.error(res.msg || '操作失败')
                }
            }).catch(() => {
                this.$message.error('操作失败，请重试')
            })
        },
        // 显示立即购买对话框
        showBuyDialog(goods) {
            this.buyOrderForm = {
                goodsId: goods.id,
                goodsName: goods.name,
                goodsPrice: goods.sfPrice
            }
            this.detailVisible = false
            this.selectedBuyTable = null
            this.buyTableDialogVisible = true
        },

        // 处理立即购买的餐桌选择
        handleBuyTableSelected(table) {
            this.selectedBuyTable = table
        },

        // 确认立即购买订单（选择餐桌后）
        confirmBuyOrder() {
            if (!this.selectedBuyTable) {
                this.$message.warning('请选择餐桌')
                return
            }

            // 防重复提交
            if (this.buySubmitting) {
                this.$message.warning('正在处理中，请勿重复提交')
                return
            }

            this.$confirm(`确认要在${this.selectedBuyTable.tableNumber}号桌下单吗？总金额：¥${this.buyOrderForm.goodsPrice}`, '立即购买确认', {
                confirmButtonText: '确定下单',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.buySubmitting = true
                this.$request.post('/dingdan/add', {
                    sfUserName: this.user.name || '匿名用户',
                    sfUserId: this.user.id || 0,
                    sfProductIds: this.buyOrderForm.goodsId.toString(),
                    status: '已支付', // 立即购买直接设为已支付状态
                    sfCartStatus: '', // 购物车状态为空
                    sfTotalPrice: this.buyOrderForm.goodsPrice, // 商品价格放入订单价格字段
                    tableNumber: this.selectedBuyTable.tableNumber, // 添加餐桌号
                    sfCreateTime: new Date().toISOString()
                }).then(res => {
                    if (res.code === '200') {
                        this.$message.success(`订单创建成功！餐桌：${this.selectedBuyTable.tableNumber}号`)
                        this.buyTableDialogVisible = false
                        this.selectedBuyTable = null
                    } else {
                        this.$message.error(res.msg || '下单失败')
                    }
                }).catch(err => {
                    console.error('下单失败:', err)
                    if (err.response && err.response.data && err.response.data.msg) {
                        this.$message.error(err.response.data.msg)
                    } else {
                        this.$message.error('下单失败，请重试')
                    }
                }).finally(() => {
                    this.buySubmitting = false
                })
            }).catch(() => {
                this.$message.info('已取消下单')
            })
        },
        handleCurrentChange(pageNum) {  // 分页变化
            this.load(pageNum)
        }
    }
}
</script>

<style scoped>
/* 样式部分保持不变 */
.taobao-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}



.goods-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.goods-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.goods-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.goods-img-container {
    height: 200px;
    overflow: hidden;
}

.goods-img {
    width: 100%;
    height: 100%;
}

.goods-info {
    padding: 15px;
}

.goods-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
    height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.goods-price {
    color: #FF0036;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.goods-sales {
    font-size: 12px;
    color: #999;
}

.goods-actions {
    margin-top: 10px;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.detail-container {
    display: flex;
}

.detail-left {
    flex: 1;
    padding: 20px;
}

.detail-right {
    flex: 1;
    padding: 20px;
}

.detail-img {
    width: 100%;
    height: 400px;
}

.detail-title {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
}

.detail-price {
    margin-bottom: 20px;
}

.price-symbol {
    color: #FF0036;
    font-size: 20px;
}

.price-number {
    color: #FF0036;
    font-size: 28px;
    font-weight: bold;
}

.detail-info {
    margin-bottom: 20px;
}

.info-item {
    margin-bottom: 10px;
    font-size: 14px;
}

.info-label {
    color: #999;
    margin-right: 10px;
}

.info-value {
    color: #333;
}

.detail-desc {
    margin-top: 30px;
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.detail-desc h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
}

.detail-desc p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
}

.detail-actions {
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
}

.cart-btn {
    background-color: #FF9500;
    border-color: #FF9500;
    color: white;
    width: 48%;
    height: 50px;
    font-size: 18px;
}

.cart-btn:hover {
    background-color: #FFAA33;
    border-color: #FFAA33;
}

.buy-btn {
    width: 48%;
    height: 50px;
    font-size: 18px;
    background: linear-gradient(to right, #FF0036, #FF0036);
    border: none;
}

/deep/ .goods-detail-dialog {
    border-radius: 8px;
}

/deep/ .goods-detail-dialog .el-dialog__header {
    border-bottom: 1px solid #eee;
}

/deep/ .goods-detail-dialog .el-dialog__body {
    padding: 0;
}
</style>