{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each } from 'zrender/lib/core/util.js';\nimport { simpleLayout, simpleLayoutEdge } from './simpleLayoutHelper.js';\nexport default function graphSimpleLayout(ecModel, api) {\n  ecModel.eachSeriesByType('graph', function (seriesModel) {\n    var layout = seriesModel.get('layout');\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys && coordSys.type !== 'view') {\n      var data_1 = seriesModel.getData();\n      var dimensions_1 = [];\n      each(coordSys.dimensions, function (coordDim) {\n        dimensions_1 = dimensions_1.concat(data_1.mapDimensionsAll(coordDim));\n      });\n      for (var dataIndex = 0; dataIndex < data_1.count(); dataIndex++) {\n        var value = [];\n        var hasValue = false;\n        for (var i = 0; i < dimensions_1.length; i++) {\n          var val = data_1.get(dimensions_1[i], dataIndex);\n          if (!isNaN(val)) {\n            hasValue = true;\n          }\n          value.push(val);\n        }\n        if (hasValue) {\n          data_1.setItemLayout(dataIndex, coordSys.dataToPoint(value));\n        } else {\n          // Also {Array.<number>}, not undefined to avoid if...else... statement\n          data_1.setItemLayout(dataIndex, [NaN, NaN]);\n        }\n      }\n      simpleLayoutEdge(data_1.graph, seriesModel);\n    } else if (!layout || layout === 'none') {\n      simpleLayout(seriesModel);\n    }\n  });\n}", "map": {"version": 3, "names": ["each", "simpleLayout", "simpleLayoutEdge", "graphSimpleLayout", "ecModel", "api", "eachSeriesByType", "seriesModel", "layout", "get", "coordSys", "coordinateSystem", "type", "data_1", "getData", "dimensions_1", "dimensions", "coordDim", "concat", "mapDimensionsAll", "dataIndex", "count", "value", "hasValue", "i", "length", "val", "isNaN", "push", "setItemLayout", "dataToPoint", "NaN", "graph"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/chart/graph/simpleLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each } from 'zrender/lib/core/util.js';\nimport { simpleLayout, simpleLayoutEdge } from './simpleLayoutHelper.js';\nexport default function graphSimpleLayout(ecModel, api) {\n  ecModel.eachSeriesByType('graph', function (seriesModel) {\n    var layout = seriesModel.get('layout');\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys && coordSys.type !== 'view') {\n      var data_1 = seriesModel.getData();\n      var dimensions_1 = [];\n      each(coordSys.dimensions, function (coordDim) {\n        dimensions_1 = dimensions_1.concat(data_1.mapDimensionsAll(coordDim));\n      });\n      for (var dataIndex = 0; dataIndex < data_1.count(); dataIndex++) {\n        var value = [];\n        var hasValue = false;\n        for (var i = 0; i < dimensions_1.length; i++) {\n          var val = data_1.get(dimensions_1[i], dataIndex);\n          if (!isNaN(val)) {\n            hasValue = true;\n          }\n          value.push(val);\n        }\n        if (hasValue) {\n          data_1.setItemLayout(dataIndex, coordSys.dataToPoint(value));\n        } else {\n          // Also {Array.<number>}, not undefined to avoid if...else... statement\n          data_1.setItemLayout(dataIndex, [NaN, NaN]);\n        }\n      }\n      simpleLayoutEdge(data_1.graph, seriesModel);\n    } else if (!layout || layout === 'none') {\n      simpleLayout(seriesModel);\n    }\n  });\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,yBAAyB;AACxE,eAAe,SAASC,iBAAiBA,CAACC,OAAO,EAAEC,GAAG,EAAE;EACtDD,OAAO,CAACE,gBAAgB,CAAC,OAAO,EAAE,UAAUC,WAAW,EAAE;IACvD,IAAIC,MAAM,GAAGD,WAAW,CAACE,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAIC,QAAQ,GAAGH,WAAW,CAACI,gBAAgB;IAC3C,IAAID,QAAQ,IAAIA,QAAQ,CAACE,IAAI,KAAK,MAAM,EAAE;MACxC,IAAIC,MAAM,GAAGN,WAAW,CAACO,OAAO,CAAC,CAAC;MAClC,IAAIC,YAAY,GAAG,EAAE;MACrBf,IAAI,CAACU,QAAQ,CAACM,UAAU,EAAE,UAAUC,QAAQ,EAAE;QAC5CF,YAAY,GAAGA,YAAY,CAACG,MAAM,CAACL,MAAM,CAACM,gBAAgB,CAACF,QAAQ,CAAC,CAAC;MACvE,CAAC,CAAC;MACF,KAAK,IAAIG,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGP,MAAM,CAACQ,KAAK,CAAC,CAAC,EAAED,SAAS,EAAE,EAAE;QAC/D,IAAIE,KAAK,GAAG,EAAE;QACd,IAAIC,QAAQ,GAAG,KAAK;QACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,YAAY,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5C,IAAIE,GAAG,GAAGb,MAAM,CAACJ,GAAG,CAACM,YAAY,CAACS,CAAC,CAAC,EAAEJ,SAAS,CAAC;UAChD,IAAI,CAACO,KAAK,CAACD,GAAG,CAAC,EAAE;YACfH,QAAQ,GAAG,IAAI;UACjB;UACAD,KAAK,CAACM,IAAI,CAACF,GAAG,CAAC;QACjB;QACA,IAAIH,QAAQ,EAAE;UACZV,MAAM,CAACgB,aAAa,CAACT,SAAS,EAAEV,QAAQ,CAACoB,WAAW,CAACR,KAAK,CAAC,CAAC;QAC9D,CAAC,MAAM;UACL;UACAT,MAAM,CAACgB,aAAa,CAACT,SAAS,EAAE,CAACW,GAAG,EAAEA,GAAG,CAAC,CAAC;QAC7C;MACF;MACA7B,gBAAgB,CAACW,MAAM,CAACmB,KAAK,EAAEzB,WAAW,CAAC;IAC7C,CAAC,MAAM,IAAI,CAACC,MAAM,IAAIA,MAAM,KAAK,MAAM,EAAE;MACvCP,YAAY,CAACM,WAAW,CAAC;IAC3B;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}