{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Traverse the tree from bottom to top and do something\r\n */\nfunction eachAfter(root, callback, separation) {\n  var nodes = [root];\n  var next = [];\n  var node;\n  while (node = nodes.pop()) {\n    // jshint ignore:line\n    next.push(node);\n    if (node.isExpand) {\n      var children = node.children;\n      if (children.length) {\n        for (var i = 0; i < children.length; i++) {\n          nodes.push(children[i]);\n        }\n      }\n    }\n  }\n  while (node = next.pop()) {\n    // jshint ignore:line\n    callback(node, separation);\n  }\n}\n/**\r\n * Traverse the tree from top to bottom and do something\r\n */\nfunction eachBefore(root, callback) {\n  var nodes = [root];\n  var node;\n  while (node = nodes.pop()) {\n    // jshint ignore:line\n    callback(node);\n    if (node.isExpand) {\n      var children = node.children;\n      if (children.length) {\n        for (var i = children.length - 1; i >= 0; i--) {\n          nodes.push(children[i]);\n        }\n      }\n    }\n  }\n}\nexport { eachAfter, eachBefore };", "map": {"version": 3, "names": ["eachAfter", "root", "callback", "separation", "nodes", "next", "node", "pop", "push", "isExpand", "children", "length", "i", "eachBefore"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/chart/tree/traversalHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Traverse the tree from bottom to top and do something\r\n */\nfunction eachAfter(root, callback, separation) {\n  var nodes = [root];\n  var next = [];\n  var node;\n  while (node = nodes.pop()) {\n    // jshint ignore:line\n    next.push(node);\n    if (node.isExpand) {\n      var children = node.children;\n      if (children.length) {\n        for (var i = 0; i < children.length; i++) {\n          nodes.push(children[i]);\n        }\n      }\n    }\n  }\n  while (node = next.pop()) {\n    // jshint ignore:line\n    callback(node, separation);\n  }\n}\n/**\r\n * Traverse the tree from top to bottom and do something\r\n */\nfunction eachBefore(root, callback) {\n  var nodes = [root];\n  var node;\n  while (node = nodes.pop()) {\n    // jshint ignore:line\n    callback(node);\n    if (node.isExpand) {\n      var children = node.children;\n      if (children.length) {\n        for (var i = children.length - 1; i >= 0; i--) {\n          nodes.push(children[i]);\n        }\n      }\n    }\n  }\n}\nexport { eachAfter, eachBefore };"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC7C,IAAIC,KAAK,GAAG,CAACH,IAAI,CAAC;EAClB,IAAII,IAAI,GAAG,EAAE;EACb,IAAIC,IAAI;EACR,OAAOA,IAAI,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE;IACzB;IACAF,IAAI,CAACG,IAAI,CAACF,IAAI,CAAC;IACf,IAAIA,IAAI,CAACG,QAAQ,EAAE;MACjB,IAAIC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;MAC5B,IAAIA,QAAQ,CAACC,MAAM,EAAE;QACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACC,MAAM,EAAEC,CAAC,EAAE,EAAE;UACxCR,KAAK,CAACI,IAAI,CAACE,QAAQ,CAACE,CAAC,CAAC,CAAC;QACzB;MACF;IACF;EACF;EACA,OAAON,IAAI,GAAGD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE;IACxB;IACAL,QAAQ,CAACI,IAAI,EAAEH,UAAU,CAAC;EAC5B;AACF;AACA;AACA;AACA;AACA,SAASU,UAAUA,CAACZ,IAAI,EAAEC,QAAQ,EAAE;EAClC,IAAIE,KAAK,GAAG,CAACH,IAAI,CAAC;EAClB,IAAIK,IAAI;EACR,OAAOA,IAAI,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE;IACzB;IACAL,QAAQ,CAACI,IAAI,CAAC;IACd,IAAIA,IAAI,CAACG,QAAQ,EAAE;MACjB,IAAIC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;MAC5B,IAAIA,QAAQ,CAACC,MAAM,EAAE;QACnB,KAAK,IAAIC,CAAC,GAAGF,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC7CR,KAAK,CAACI,IAAI,CAACE,QAAQ,CAACE,CAAC,CAAC,CAAC;QACzB;MACF;IACF;EACF;AACF;AACA,SAASZ,SAAS,EAAEa,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}