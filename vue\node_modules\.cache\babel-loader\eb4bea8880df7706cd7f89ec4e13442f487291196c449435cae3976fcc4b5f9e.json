{"ast": null, "code": "export default {\n  name: \"Complaint\",\n  data() {\n    return {\n      successVisible: false,\n      // 控制成功提交提示的显示\n      form: {\n        // 表单数据\n        complaintType: '',\n        title: '',\n        sfContent: '',\n        phone: '',\n        sfImage: ''\n      },\n      rules: {} // 表单验证规则\n    };\n  },\n  methods: {\n    save() {\n      // 保存按钮的逻辑，新增数据\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          this.$request({\n            url: this.form.id ? '/complaint/update' : '/complaint/add',\n            // 根据是否有ID决定是新增还是更新\n            method: this.form.id ? 'PUT' : 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              // 提交成功\n              this.successVisible = true; // 显示提交成功弹窗\n              this.form = {}; // 清空表单，但页面依旧保留\n            } else {\n              this.$message.error(res.msg); // 提交失败提示\n            }\n          });\n        }\n      });\n    },\n    handleAvatarSuccess(response, file, fileList) {\n      // 上传图片成功后的回调函数\n      this.form.sfImage = response.data; // 将图片 URL 存入表单数据\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "successVisible", "form", "complaintType", "title", "sf<PERSON><PERSON>nt", "phone", "sfImage", "rules", "methods", "save", "$refs", "formRef", "validate", "valid", "$request", "url", "id", "method", "then", "res", "code", "$message", "error", "msg", "handleAvatarSuccess", "response", "file", "fileList"], "sources": ["src/views/front/Complaint.vue"], "sourcesContent": ["<template>\r\n    <div class=\"complaint-container\">\r\n        <!-- 点餐投诉表单区域 -->\r\n        <el-form :model=\"form\" label-width=\"100px\" style=\"padding: 30px 50px; font-size: 14px;\" :rules=\"rules\" ref=\"formRef\" class=\"form-container\">\r\n            <!-- 标题输入框 -->\r\n            <el-form-item label=\"点餐投诉标题\" prop=\"title\">\r\n                <el-input v-model=\"form.title\" placeholder=\"请输入点餐投诉标题\" class=\"input-field\"></el-input>\r\n            </el-form-item>\r\n\r\n            <!-- 文字内容输入框 -->\r\n            <el-form-item label=\"点餐投诉内容\" prop=\"sfContent\">\r\n                <el-input v-model=\"form.sfContent\" placeholder=\"请详细投诉点餐投诉内容\" type=\"textarea\" class=\"input-field\"></el-input>\r\n            </el-form-item>\r\n\r\n            <!-- 点餐投诉图片上传 -->\r\n            <el-form-item label=\"点餐投诉图片\" prop=\"sfImage\">\r\n                <el-upload\r\n                    class=\"avatar-uploader\"\r\n                    :action=\"$baseUrl + '/files/upload'\"\r\n                    :show-file-list=\"false\"\r\n                    :on-success=\"handleAvatarSuccess\">\r\n                    <el-button type=\"primary\" class=\"upload-btn\">点击上传图片</el-button>\r\n                </el-upload>\r\n                <!-- 显示上传成功后的图片 -->\r\n                <el-image\r\n                    v-if=\"form.sfImage\"\r\n                    class=\"uploaded-image\"\r\n                    :src=\"form.sfImage\"\r\n                    :preview-src-list=\"[form.sfImage]\">\r\n                </el-image>\r\n            </el-form-item>\r\n\r\n            <!-- 提交按钮 -->\r\n            <div class=\"form-footer\">\r\n                <el-button type=\"primary\" class=\"submit-btn\" @click=\"save\">提交点餐投诉</el-button>\r\n            </div>\r\n        </el-form>\r\n\r\n        <!-- 提交成功提示 -->\r\n        <el-dialog :visible.sync=\"successVisible\" title=\"点餐投诉提交成功\" width=\"40%\" :close-on-click-modal=\"false\" destroy-on-close>\r\n            <div class=\"dialog-content\">\r\n                <p class=\"dialog-title\">感谢您的反馈！</p>\r\n                <p>您的点餐投诉已经成功提交，客服团队会尽快处理您的问题。</p>\r\n                <p class=\"dialog-text\">我们深知您的不便，我们正在认真对待您的点餐投诉，并尽力寻找最合适的解决方案。</p>\r\n                <p class=\"dialog-text\">为了更好地解决您的问题，我们会及时与您沟通，请您耐心等待。</p>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"successVisible = false\">确认</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Complaint\",\r\n    data() {\r\n        return {\r\n            successVisible: false,  // 控制成功提交提示的显示\r\n            form: {              // 表单数据\r\n                complaintType: '',\r\n                title: '',\r\n                sfContent: '',\r\n                phone: '',\r\n                sfImage: ''\r\n            },\r\n            rules: {},  // 表单验证规则\r\n        }\r\n    },\r\n    methods: {\r\n        save() {   // 保存按钮的逻辑，新增数据\r\n            this.$refs.formRef.validate((valid) => {\r\n                if (valid) {\r\n                    this.$request({\r\n                        url: this.form.id ? '/complaint/update' : '/complaint/add',  // 根据是否有ID决定是新增还是更新\r\n                        method: this.form.id ? 'PUT' : 'POST',\r\n                        data: this.form\r\n                    }).then(res => {\r\n                        if (res.code === '200') {  // 提交成功\r\n                            this.successVisible = true;  // 显示提交成功弹窗\r\n                            this.form = {};  // 清空表单，但页面依旧保留\r\n                        } else {\r\n                            this.$message.error(res.msg);  // 提交失败提示\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        },\r\n        handleAvatarSuccess(response, file, fileList) {  // 上传图片成功后的回调函数\r\n            this.form.sfImage = response.data;  // 将图片 URL 存入表单数据\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.complaint-container {\r\n    background-color: #f5f8fa;  /* 更清新的背景色 */\r\n    min-height: 100vh;  /* 覆盖整个视口 */\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    padding: 50px 20px;\r\n}\r\n\r\n.form-container {\r\n    background-color: #ffffff;\r\n    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.12);  /* 更强的阴影效果 */\r\n    border-radius: 12px;\r\n    width: 100%;\r\n    max-width: 950px;  /* 限制表单最大宽度 */\r\n    padding: 50px;  /* 增加内边距，让表单更宽松 */\r\n    min-height: 700px;\r\n    font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n.input-field {\r\n    border-radius: 8px;\r\n    background-color: #f1f5f9;\r\n    border: 1px solid #e2e8f0;\r\n    padding: 12px;\r\n    font-size: 15px;\r\n    transition: border-color 0.3s ease;\r\n}\r\n\r\n.input-field:focus {\r\n    border-color: #5c6bc0;  /* 聚焦时边框颜色 */\r\n}\r\n\r\n.upload-btn {\r\n    background-color: #5c6bc0;\r\n    border-color: #5c6bc0;\r\n    color: white;\r\n    border-radius: 6px;\r\n    font-size: 15px;\r\n    padding: 10px 25px;\r\n    font-weight: 500;\r\n    transition: background-color 0.3s ease;\r\n}\r\n\r\n.upload-btn:hover {\r\n    background-color: #3f51b5;  /* 更深的蓝色 */\r\n}\r\n\r\n.uploaded-image {\r\n    width: 130px;\r\n    height: 130px;\r\n    margin-top: 15px;\r\n    border-radius: 12px;\r\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.form-footer {\r\n    text-align: right;\r\n    margin-top: 30px;\r\n}\r\n\r\n.submit-btn {\r\n    background-color: #5c6bc0;\r\n    border-color: #5c6bc0;\r\n    color: white;\r\n    border-radius: 6px;\r\n    padding: 12px 35px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    transition: background-color 0.3s ease;\r\n}\r\n\r\n.submit-btn:hover {\r\n    background-color: #3f51b5;\r\n}\r\n\r\n.dialog-footer {\r\n    text-align: center;\r\n}\r\n\r\n.el-dialog {\r\n    border-radius: 12px;  /* 更圆润的弹窗 */\r\n}\r\n\r\n.el-dialog .el-button {\r\n    border-radius: 6px;\r\n    font-size: 15px;\r\n}\r\n\r\n.el-dialog .el-button.primary {\r\n    background-color: #5c6bc0;\r\n    color: white;\r\n    border-color: #5c6bc0;\r\n}\r\n\r\n.el-dialog .el-button.primary:hover {\r\n    background-color: #3f51b5;\r\n}\r\n\r\n.dialog-content {\r\n    text-align: center;\r\n    font-size: 16px;\r\n    line-height: 1.8;\r\n    color: #4a4a4a;\r\n}\r\n\r\n.dialog-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #2e7d32;  /* 安抚顾客的颜色 */\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.dialog-text {\r\n    color: #555;\r\n}\r\n\r\n</style>\r\n"], "mappings": "AAsDA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,cAAA;MAAA;MACAC,IAAA;QAAA;QACAC,aAAA;QACAC,KAAA;QACAC,SAAA;QACAC,KAAA;QACAC,OAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA;IACAC,KAAA;MAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,QAAA;YACAC,GAAA,OAAAd,IAAA,CAAAe,EAAA;YAAA;YACAC,MAAA,OAAAhB,IAAA,CAAAe,EAAA;YACAjB,IAAA,OAAAE;UACA,GAAAiB,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cAAA;cACA,KAAApB,cAAA;cACA,KAAAC,IAAA;YACA;cACA,KAAAoB,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAI,GAAA;YACA;UACA;QACA;MACA;IACA;IACAC,oBAAAC,QAAA,EAAAC,IAAA,EAAAC,QAAA;MAAA;MACA,KAAA1B,IAAA,CAAAK,OAAA,GAAAmB,QAAA,CAAA1B,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}