{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getTooltipMarker, encodeHTML, makeValueReadable, convertToColorString } from '../../util/format.js';\nimport { isString, each, hasOwn, isArray, map, assert, extend } from 'zrender/lib/core/util.js';\nimport { SortOrderComparator } from '../../data/helper/dataValueHelper.js';\nimport { getRandomIdBase } from '../../util/number.js';\nvar TOOLTIP_LINE_HEIGHT_CSS = 'line-height:1';\nfunction getTooltipLineHeight(textStyle) {\n  var lineHeight = textStyle.lineHeight;\n  if (lineHeight == null) {\n    return TOOLTIP_LINE_HEIGHT_CSS;\n  } else {\n    return \"line-height:\" + encodeHTML(lineHeight + '') + \"px\";\n  }\n}\n// TODO: more textStyle option\nfunction getTooltipTextStyle(textStyle, renderMode) {\n  var nameFontColor = textStyle.color || '#6e7079';\n  var nameFontSize = textStyle.fontSize || 12;\n  var nameFontWeight = textStyle.fontWeight || '400';\n  var valueFontColor = textStyle.color || '#464646';\n  var valueFontSize = textStyle.fontSize || 14;\n  var valueFontWeight = textStyle.fontWeight || '900';\n  if (renderMode === 'html') {\n    // `textStyle` is probably from user input, should be encoded to reduce security risk.\n    return {\n      // eslint-disable-next-line max-len\n      nameStyle: \"font-size:\" + encodeHTML(nameFontSize + '') + \"px;color:\" + encodeHTML(nameFontColor) + \";font-weight:\" + encodeHTML(nameFontWeight + ''),\n      // eslint-disable-next-line max-len\n      valueStyle: \"font-size:\" + encodeHTML(valueFontSize + '') + \"px;color:\" + encodeHTML(valueFontColor) + \";font-weight:\" + encodeHTML(valueFontWeight + '')\n    };\n  } else {\n    return {\n      nameStyle: {\n        fontSize: nameFontSize,\n        fill: nameFontColor,\n        fontWeight: nameFontWeight\n      },\n      valueStyle: {\n        fontSize: valueFontSize,\n        fill: valueFontColor,\n        fontWeight: valueFontWeight\n      }\n    };\n  }\n}\n// See `TooltipMarkupLayoutIntent['innerGapLevel']`.\n// (value from UI design)\nvar HTML_GAPS = [0, 10, 20, 30];\nvar RICH_TEXT_GAPS = ['', '\\n', '\\n\\n', '\\n\\n\\n'];\n// eslint-disable-next-line max-len\nexport function createTooltipMarkup(type, option) {\n  option.type = type;\n  return option;\n}\nfunction isSectionFragment(frag) {\n  return frag.type === 'section';\n}\nfunction getBuilder(frag) {\n  return isSectionFragment(frag) ? buildSection : buildNameValue;\n}\nfunction getBlockGapLevel(frag) {\n  if (isSectionFragment(frag)) {\n    var gapLevel_1 = 0;\n    var subBlockLen = frag.blocks.length;\n    var hasInnerGap_1 = subBlockLen > 1 || subBlockLen > 0 && !frag.noHeader;\n    each(frag.blocks, function (subBlock) {\n      var subGapLevel = getBlockGapLevel(subBlock);\n      // If the some of the sub-blocks have some gaps (like 10px) inside, this block\n      // should use a larger gap (like 20px) to distinguish those sub-blocks.\n      if (subGapLevel >= gapLevel_1) {\n        gapLevel_1 = subGapLevel + +(hasInnerGap_1 && (\n        // 0 always can not be readable gap level.\n        !subGapLevel\n        // If no header, always keep the sub gap level. Otherwise\n        // look weird in case `multipleSeries`.\n        || isSectionFragment(subBlock) && !subBlock.noHeader));\n      }\n    });\n    return gapLevel_1;\n  }\n  return 0;\n}\nfunction buildSection(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var noHeader = fragment.noHeader;\n  var gaps = getGap(getBlockGapLevel(fragment));\n  var subMarkupTextList = [];\n  var subBlocks = fragment.blocks || [];\n  assert(!subBlocks || isArray(subBlocks));\n  subBlocks = subBlocks || [];\n  var orderMode = ctx.orderMode;\n  if (fragment.sortBlocks && orderMode) {\n    subBlocks = subBlocks.slice();\n    var orderMap = {\n      valueAsc: 'asc',\n      valueDesc: 'desc'\n    };\n    if (hasOwn(orderMap, orderMode)) {\n      var comparator_1 = new SortOrderComparator(orderMap[orderMode], null);\n      subBlocks.sort(function (a, b) {\n        return comparator_1.evaluate(a.sortParam, b.sortParam);\n      });\n    }\n    // FIXME 'seriesDesc' necessary?\n    else if (orderMode === 'seriesDesc') {\n      subBlocks.reverse();\n    }\n  }\n  each(subBlocks, function (subBlock, idx) {\n    var valueFormatter = fragment.valueFormatter;\n    var subMarkupText = getBuilder(subBlock)(\n    // Inherit valueFormatter\n    valueFormatter ? extend(extend({}, ctx), {\n      valueFormatter: valueFormatter\n    }) : ctx, subBlock, idx > 0 ? gaps.html : 0, toolTipTextStyle);\n    subMarkupText != null && subMarkupTextList.push(subMarkupText);\n  });\n  var subMarkupText = ctx.renderMode === 'richText' ? subMarkupTextList.join(gaps.richText) : wrapBlockHTML(toolTipTextStyle, subMarkupTextList.join(''), noHeader ? topMarginForOuterGap : gaps.html);\n  if (noHeader) {\n    return subMarkupText;\n  }\n  var displayableHeader = makeValueReadable(fragment.header, 'ordinal', ctx.useUTC);\n  var nameStyle = getTooltipTextStyle(toolTipTextStyle, ctx.renderMode).nameStyle;\n  var tooltipLineHeight = getTooltipLineHeight(toolTipTextStyle);\n  if (ctx.renderMode === 'richText') {\n    return wrapInlineNameRichText(ctx, displayableHeader, nameStyle) + gaps.richText + subMarkupText;\n  } else {\n    return wrapBlockHTML(toolTipTextStyle, \"<div style=\\\"\" + nameStyle + \";\" + tooltipLineHeight + \";\\\">\" + encodeHTML(displayableHeader) + '</div>' + subMarkupText, topMarginForOuterGap);\n  }\n}\nfunction buildNameValue(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var renderMode = ctx.renderMode;\n  var noName = fragment.noName;\n  var noValue = fragment.noValue;\n  var noMarker = !fragment.markerType;\n  var name = fragment.name;\n  var useUTC = ctx.useUTC;\n  var valueFormatter = fragment.valueFormatter || ctx.valueFormatter || function (value) {\n    value = isArray(value) ? value : [value];\n    return map(value, function (val, idx) {\n      return makeValueReadable(val, isArray(valueTypeOption) ? valueTypeOption[idx] : valueTypeOption, useUTC);\n    });\n  };\n  if (noName && noValue) {\n    return;\n  }\n  var markerStr = noMarker ? '' : ctx.markupStyleCreator.makeTooltipMarker(fragment.markerType, fragment.markerColor || '#333', renderMode);\n  var readableName = noName ? '' : makeValueReadable(name, 'ordinal', useUTC);\n  var valueTypeOption = fragment.valueType;\n  var readableValueList = noValue ? [] : valueFormatter(fragment.value, fragment.dataIndex);\n  var valueAlignRight = !noMarker || !noName;\n  // It little weird if only value next to marker but far from marker.\n  var valueCloseToMarker = !noMarker && noName;\n  var _a = getTooltipTextStyle(toolTipTextStyle, renderMode),\n    nameStyle = _a.nameStyle,\n    valueStyle = _a.valueStyle;\n  return renderMode === 'richText' ? (noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameRichText(ctx, readableName, nameStyle))\n  // Value has commas inside, so use ' ' as delimiter for multiple values.\n  + (noValue ? '' : wrapInlineValueRichText(ctx, readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)) : wrapBlockHTML(toolTipTextStyle, (noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameHTML(readableName, !noMarker, nameStyle)) + (noValue ? '' : wrapInlineValueHTML(readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)), topMarginForOuterGap);\n}\n/**\r\n * @return markupText. null/undefined means no content.\r\n */\nexport function buildTooltipMarkup(fragment, markupStyleCreator, renderMode, orderMode, useUTC, toolTipTextStyle) {\n  if (!fragment) {\n    return;\n  }\n  var builder = getBuilder(fragment);\n  var ctx = {\n    useUTC: useUTC,\n    renderMode: renderMode,\n    orderMode: orderMode,\n    markupStyleCreator: markupStyleCreator,\n    valueFormatter: fragment.valueFormatter\n  };\n  return builder(ctx, fragment, 0, toolTipTextStyle);\n}\nfunction getGap(gapLevel) {\n  return {\n    html: HTML_GAPS[gapLevel],\n    richText: RICH_TEXT_GAPS[gapLevel]\n  };\n}\nfunction wrapBlockHTML(textStyle, encodedContent, topGap) {\n  var clearfix = '<div style=\"clear:both\"></div>';\n  var marginCSS = \"margin: \" + topGap + \"px 0 0\";\n  var tooltipLineHeight = getTooltipLineHeight(textStyle);\n  return \"<div style=\\\"\" + marginCSS + \";\" + tooltipLineHeight + \";\\\">\" + encodedContent + clearfix + '</div>';\n}\nfunction wrapInlineNameHTML(name, leftHasMarker, style) {\n  var marginCss = leftHasMarker ? 'margin-left:2px' : '';\n  return \"<span style=\\\"\" + style + \";\" + marginCss + \"\\\">\" + encodeHTML(name) + '</span>';\n}\nfunction wrapInlineValueHTML(valueList, alignRight, valueCloseToMarker, style) {\n  // Do not too close to marker, considering there are multiple values separated by spaces.\n  var paddingStr = valueCloseToMarker ? '10px' : '20px';\n  var alignCSS = alignRight ? \"float:right;margin-left:\" + paddingStr : '';\n  valueList = isArray(valueList) ? valueList : [valueList];\n  return \"<span style=\\\"\" + alignCSS + \";\" + style + \"\\\">\"\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  + map(valueList, function (value) {\n    return encodeHTML(value);\n  }).join('&nbsp;&nbsp;') + '</span>';\n}\nfunction wrapInlineNameRichText(ctx, name, style) {\n  return ctx.markupStyleCreator.wrapRichTextStyle(name, style);\n}\nfunction wrapInlineValueRichText(ctx, values, alignRight, valueCloseToMarker, style) {\n  var styles = [style];\n  var paddingLeft = valueCloseToMarker ? 10 : 20;\n  alignRight && styles.push({\n    padding: [0, 0, 0, paddingLeft],\n    align: 'right'\n  });\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  return ctx.markupStyleCreator.wrapRichTextStyle(isArray(values) ? values.join('  ') : values, styles);\n}\nexport function retrieveVisualColorForTooltipMarker(series, dataIndex) {\n  var style = series.getData().getItemVisual(dataIndex, 'style');\n  var color = style[series.visualDrawType];\n  return convertToColorString(color);\n}\nexport function getPaddingFromTooltipModel(model, renderMode) {\n  var padding = model.get('padding');\n  return padding != null ? padding\n  // We give slightly different to look pretty.\n  : renderMode === 'richText' ? [8, 10] : 10;\n}\n/**\r\n * The major feature is generate styles for `renderMode: 'richText'`.\r\n * But it also serves `renderMode: 'html'` to provide\r\n * \"renderMode-independent\" API.\r\n */\nvar TooltipMarkupStyleCreator = /** @class */function () {\n  function TooltipMarkupStyleCreator() {\n    this.richTextStyles = {};\n    // Notice that \"generate a style name\" usually happens repeatedly when mouse is moving and\n    // a tooltip is displayed. So we put the `_nextStyleNameId` as a member of each creator\n    // rather than static shared by all creators (which will cause it increase to fast).\n    this._nextStyleNameId = getRandomIdBase();\n  }\n  TooltipMarkupStyleCreator.prototype._generateStyleName = function () {\n    return '__EC_aUTo_' + this._nextStyleNameId++;\n  };\n  TooltipMarkupStyleCreator.prototype.makeTooltipMarker = function (markerType, colorStr, renderMode) {\n    var markerId = renderMode === 'richText' ? this._generateStyleName() : null;\n    var marker = getTooltipMarker({\n      color: colorStr,\n      type: markerType,\n      renderMode: renderMode,\n      markerId: markerId\n    });\n    if (isString(marker)) {\n      return marker;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(markerId);\n      }\n      this.richTextStyles[markerId] = marker.style;\n      return marker.content;\n    }\n  };\n  /**\r\n   * @usage\r\n   * ```ts\r\n   * const styledText = markupStyleCreator.wrapRichTextStyle([\r\n   *     // The styles will be auto merged.\r\n   *     {\r\n   *         fontSize: 12,\r\n   *         color: 'blue'\r\n   *     },\r\n   *     {\r\n   *         padding: 20\r\n   *     }\r\n   * ]);\r\n   * ```\r\n   */\n  TooltipMarkupStyleCreator.prototype.wrapRichTextStyle = function (text, styles) {\n    var finalStl = {};\n    if (isArray(styles)) {\n      each(styles, function (stl) {\n        return extend(finalStl, stl);\n      });\n    } else {\n      extend(finalStl, styles);\n    }\n    var styleName = this._generateStyleName();\n    this.richTextStyles[styleName] = finalStl;\n    return \"{\" + styleName + \"|\" + text + \"}\";\n  };\n  return TooltipMarkupStyleCreator;\n}();\nexport { TooltipMarkupStyleCreator };", "map": {"version": 3, "names": ["getTooltipMarker", "encodeHTML", "makeValueReadable", "convertToColorString", "isString", "each", "hasOwn", "isArray", "map", "assert", "extend", "SortOrderComparator", "getRandomIdBase", "TOOLTIP_LINE_HEIGHT_CSS", "getTooltipLineHeight", "textStyle", "lineHeight", "getTooltipTextStyle", "renderMode", "nameFontColor", "color", "nameFontSize", "fontSize", "nameFontWeight", "fontWeight", "valueFontColor", "valueFontSize", "valueFontWeight", "nameStyle", "valueStyle", "fill", "HTML_GAPS", "RICH_TEXT_GAPS", "createTooltipMarkup", "type", "option", "isSectionFragment", "frag", "getBuilder", "buildSection", "buildNameValue", "getBlockGapLevel", "gapLevel_1", "subBlockLen", "blocks", "length", "hasInnerGap_1", "<PERSON><PERSON><PERSON><PERSON>", "subBlock", "subGapLevel", "ctx", "fragment", "topMarginForOuterGap", "toolTipTextStyle", "gaps", "getGap", "subMarkupTextList", "subBlocks", "orderMode", "sortBlocks", "slice", "orderMap", "valueAsc", "valueDesc", "comparator_1", "sort", "a", "b", "evaluate", "sortParam", "reverse", "idx", "valueFormatter", "subMarkupText", "html", "push", "join", "richText", "wrapBlockHTML", "displayableHeader", "header", "useUTC", "tooltipLineHeight", "wrapInlineNameRichText", "noName", "noValue", "<PERSON><PERSON><PERSON><PERSON>", "markerType", "name", "value", "val", "valueTypeOption", "markerStr", "markupStyleCreator", "makeTooltipMarker", "markerColor", "readableName", "valueType", "readableValueList", "dataIndex", "valueAlignRight", "valueCloseToMarker", "_a", "wrapInlineValueRichText", "wrapInlineNameHTML", "wrapInlineValueHTML", "buildTooltipMarkup", "builder", "gapLevel", "encodedContent", "topGap", "clearfix", "marginCSS", "leftHasMarker", "style", "marginCss", "valueList", "alignRight", "paddingStr", "alignCSS", "wrapRichTextStyle", "values", "styles", "paddingLeft", "padding", "align", "retrieveVisualColorForTooltipMarker", "series", "getData", "getItemVisual", "visualDrawType", "getPaddingFromTooltipModel", "model", "get", "TooltipMarkupStyleCreator", "richTextStyles", "_nextStyleNameId", "prototype", "_generateStyleName", "colorStr", "markerId", "marker", "process", "env", "NODE_ENV", "content", "text", "finalStl", "stl", "styleName"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/component/tooltip/tooltipMarkup.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getTooltipMarker, encodeHTML, makeValueReadable, convertToColorString } from '../../util/format.js';\nimport { isString, each, hasOwn, isArray, map, assert, extend } from 'zrender/lib/core/util.js';\nimport { SortOrderComparator } from '../../data/helper/dataValueHelper.js';\nimport { getRandomIdBase } from '../../util/number.js';\nvar TOOLTIP_LINE_HEIGHT_CSS = 'line-height:1';\nfunction getTooltipLineHeight(textStyle) {\n  var lineHeight = textStyle.lineHeight;\n  if (lineHeight == null) {\n    return TOOLTIP_LINE_HEIGHT_CSS;\n  } else {\n    return \"line-height:\" + encodeHTML(lineHeight + '') + \"px\";\n  }\n}\n// TODO: more textStyle option\nfunction getTooltipTextStyle(textStyle, renderMode) {\n  var nameFontColor = textStyle.color || '#6e7079';\n  var nameFontSize = textStyle.fontSize || 12;\n  var nameFontWeight = textStyle.fontWeight || '400';\n  var valueFontColor = textStyle.color || '#464646';\n  var valueFontSize = textStyle.fontSize || 14;\n  var valueFontWeight = textStyle.fontWeight || '900';\n  if (renderMode === 'html') {\n    // `textStyle` is probably from user input, should be encoded to reduce security risk.\n    return {\n      // eslint-disable-next-line max-len\n      nameStyle: \"font-size:\" + encodeHTML(nameFontSize + '') + \"px;color:\" + encodeHTML(nameFontColor) + \";font-weight:\" + encodeHTML(nameFontWeight + ''),\n      // eslint-disable-next-line max-len\n      valueStyle: \"font-size:\" + encodeHTML(valueFontSize + '') + \"px;color:\" + encodeHTML(valueFontColor) + \";font-weight:\" + encodeHTML(valueFontWeight + '')\n    };\n  } else {\n    return {\n      nameStyle: {\n        fontSize: nameFontSize,\n        fill: nameFontColor,\n        fontWeight: nameFontWeight\n      },\n      valueStyle: {\n        fontSize: valueFontSize,\n        fill: valueFontColor,\n        fontWeight: valueFontWeight\n      }\n    };\n  }\n}\n// See `TooltipMarkupLayoutIntent['innerGapLevel']`.\n// (value from UI design)\nvar HTML_GAPS = [0, 10, 20, 30];\nvar RICH_TEXT_GAPS = ['', '\\n', '\\n\\n', '\\n\\n\\n'];\n// eslint-disable-next-line max-len\nexport function createTooltipMarkup(type, option) {\n  option.type = type;\n  return option;\n}\nfunction isSectionFragment(frag) {\n  return frag.type === 'section';\n}\nfunction getBuilder(frag) {\n  return isSectionFragment(frag) ? buildSection : buildNameValue;\n}\nfunction getBlockGapLevel(frag) {\n  if (isSectionFragment(frag)) {\n    var gapLevel_1 = 0;\n    var subBlockLen = frag.blocks.length;\n    var hasInnerGap_1 = subBlockLen > 1 || subBlockLen > 0 && !frag.noHeader;\n    each(frag.blocks, function (subBlock) {\n      var subGapLevel = getBlockGapLevel(subBlock);\n      // If the some of the sub-blocks have some gaps (like 10px) inside, this block\n      // should use a larger gap (like 20px) to distinguish those sub-blocks.\n      if (subGapLevel >= gapLevel_1) {\n        gapLevel_1 = subGapLevel + +(hasInnerGap_1 && (\n        // 0 always can not be readable gap level.\n        !subGapLevel\n        // If no header, always keep the sub gap level. Otherwise\n        // look weird in case `multipleSeries`.\n        || isSectionFragment(subBlock) && !subBlock.noHeader));\n      }\n    });\n    return gapLevel_1;\n  }\n  return 0;\n}\nfunction buildSection(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var noHeader = fragment.noHeader;\n  var gaps = getGap(getBlockGapLevel(fragment));\n  var subMarkupTextList = [];\n  var subBlocks = fragment.blocks || [];\n  assert(!subBlocks || isArray(subBlocks));\n  subBlocks = subBlocks || [];\n  var orderMode = ctx.orderMode;\n  if (fragment.sortBlocks && orderMode) {\n    subBlocks = subBlocks.slice();\n    var orderMap = {\n      valueAsc: 'asc',\n      valueDesc: 'desc'\n    };\n    if (hasOwn(orderMap, orderMode)) {\n      var comparator_1 = new SortOrderComparator(orderMap[orderMode], null);\n      subBlocks.sort(function (a, b) {\n        return comparator_1.evaluate(a.sortParam, b.sortParam);\n      });\n    }\n    // FIXME 'seriesDesc' necessary?\n    else if (orderMode === 'seriesDesc') {\n      subBlocks.reverse();\n    }\n  }\n  each(subBlocks, function (subBlock, idx) {\n    var valueFormatter = fragment.valueFormatter;\n    var subMarkupText = getBuilder(subBlock)(\n    // Inherit valueFormatter\n    valueFormatter ? extend(extend({}, ctx), {\n      valueFormatter: valueFormatter\n    }) : ctx, subBlock, idx > 0 ? gaps.html : 0, toolTipTextStyle);\n    subMarkupText != null && subMarkupTextList.push(subMarkupText);\n  });\n  var subMarkupText = ctx.renderMode === 'richText' ? subMarkupTextList.join(gaps.richText) : wrapBlockHTML(toolTipTextStyle, subMarkupTextList.join(''), noHeader ? topMarginForOuterGap : gaps.html);\n  if (noHeader) {\n    return subMarkupText;\n  }\n  var displayableHeader = makeValueReadable(fragment.header, 'ordinal', ctx.useUTC);\n  var nameStyle = getTooltipTextStyle(toolTipTextStyle, ctx.renderMode).nameStyle;\n  var tooltipLineHeight = getTooltipLineHeight(toolTipTextStyle);\n  if (ctx.renderMode === 'richText') {\n    return wrapInlineNameRichText(ctx, displayableHeader, nameStyle) + gaps.richText + subMarkupText;\n  } else {\n    return wrapBlockHTML(toolTipTextStyle, \"<div style=\\\"\" + nameStyle + \";\" + tooltipLineHeight + \";\\\">\" + encodeHTML(displayableHeader) + '</div>' + subMarkupText, topMarginForOuterGap);\n  }\n}\nfunction buildNameValue(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var renderMode = ctx.renderMode;\n  var noName = fragment.noName;\n  var noValue = fragment.noValue;\n  var noMarker = !fragment.markerType;\n  var name = fragment.name;\n  var useUTC = ctx.useUTC;\n  var valueFormatter = fragment.valueFormatter || ctx.valueFormatter || function (value) {\n    value = isArray(value) ? value : [value];\n    return map(value, function (val, idx) {\n      return makeValueReadable(val, isArray(valueTypeOption) ? valueTypeOption[idx] : valueTypeOption, useUTC);\n    });\n  };\n  if (noName && noValue) {\n    return;\n  }\n  var markerStr = noMarker ? '' : ctx.markupStyleCreator.makeTooltipMarker(fragment.markerType, fragment.markerColor || '#333', renderMode);\n  var readableName = noName ? '' : makeValueReadable(name, 'ordinal', useUTC);\n  var valueTypeOption = fragment.valueType;\n  var readableValueList = noValue ? [] : valueFormatter(fragment.value, fragment.dataIndex);\n  var valueAlignRight = !noMarker || !noName;\n  // It little weird if only value next to marker but far from marker.\n  var valueCloseToMarker = !noMarker && noName;\n  var _a = getTooltipTextStyle(toolTipTextStyle, renderMode),\n    nameStyle = _a.nameStyle,\n    valueStyle = _a.valueStyle;\n  return renderMode === 'richText' ? (noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameRichText(ctx, readableName, nameStyle))\n  // Value has commas inside, so use ' ' as delimiter for multiple values.\n  + (noValue ? '' : wrapInlineValueRichText(ctx, readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)) : wrapBlockHTML(toolTipTextStyle, (noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameHTML(readableName, !noMarker, nameStyle)) + (noValue ? '' : wrapInlineValueHTML(readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)), topMarginForOuterGap);\n}\n/**\r\n * @return markupText. null/undefined means no content.\r\n */\nexport function buildTooltipMarkup(fragment, markupStyleCreator, renderMode, orderMode, useUTC, toolTipTextStyle) {\n  if (!fragment) {\n    return;\n  }\n  var builder = getBuilder(fragment);\n  var ctx = {\n    useUTC: useUTC,\n    renderMode: renderMode,\n    orderMode: orderMode,\n    markupStyleCreator: markupStyleCreator,\n    valueFormatter: fragment.valueFormatter\n  };\n  return builder(ctx, fragment, 0, toolTipTextStyle);\n}\nfunction getGap(gapLevel) {\n  return {\n    html: HTML_GAPS[gapLevel],\n    richText: RICH_TEXT_GAPS[gapLevel]\n  };\n}\nfunction wrapBlockHTML(textStyle, encodedContent, topGap) {\n  var clearfix = '<div style=\"clear:both\"></div>';\n  var marginCSS = \"margin: \" + topGap + \"px 0 0\";\n  var tooltipLineHeight = getTooltipLineHeight(textStyle);\n  return \"<div style=\\\"\" + marginCSS + \";\" + tooltipLineHeight + \";\\\">\" + encodedContent + clearfix + '</div>';\n}\nfunction wrapInlineNameHTML(name, leftHasMarker, style) {\n  var marginCss = leftHasMarker ? 'margin-left:2px' : '';\n  return \"<span style=\\\"\" + style + \";\" + marginCss + \"\\\">\" + encodeHTML(name) + '</span>';\n}\nfunction wrapInlineValueHTML(valueList, alignRight, valueCloseToMarker, style) {\n  // Do not too close to marker, considering there are multiple values separated by spaces.\n  var paddingStr = valueCloseToMarker ? '10px' : '20px';\n  var alignCSS = alignRight ? \"float:right;margin-left:\" + paddingStr : '';\n  valueList = isArray(valueList) ? valueList : [valueList];\n  return \"<span style=\\\"\" + alignCSS + \";\" + style + \"\\\">\"\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  + map(valueList, function (value) {\n    return encodeHTML(value);\n  }).join('&nbsp;&nbsp;') + '</span>';\n}\nfunction wrapInlineNameRichText(ctx, name, style) {\n  return ctx.markupStyleCreator.wrapRichTextStyle(name, style);\n}\nfunction wrapInlineValueRichText(ctx, values, alignRight, valueCloseToMarker, style) {\n  var styles = [style];\n  var paddingLeft = valueCloseToMarker ? 10 : 20;\n  alignRight && styles.push({\n    padding: [0, 0, 0, paddingLeft],\n    align: 'right'\n  });\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  return ctx.markupStyleCreator.wrapRichTextStyle(isArray(values) ? values.join('  ') : values, styles);\n}\nexport function retrieveVisualColorForTooltipMarker(series, dataIndex) {\n  var style = series.getData().getItemVisual(dataIndex, 'style');\n  var color = style[series.visualDrawType];\n  return convertToColorString(color);\n}\nexport function getPaddingFromTooltipModel(model, renderMode) {\n  var padding = model.get('padding');\n  return padding != null ? padding\n  // We give slightly different to look pretty.\n  : renderMode === 'richText' ? [8, 10] : 10;\n}\n/**\r\n * The major feature is generate styles for `renderMode: 'richText'`.\r\n * But it also serves `renderMode: 'html'` to provide\r\n * \"renderMode-independent\" API.\r\n */\nvar TooltipMarkupStyleCreator = /** @class */function () {\n  function TooltipMarkupStyleCreator() {\n    this.richTextStyles = {};\n    // Notice that \"generate a style name\" usually happens repeatedly when mouse is moving and\n    // a tooltip is displayed. So we put the `_nextStyleNameId` as a member of each creator\n    // rather than static shared by all creators (which will cause it increase to fast).\n    this._nextStyleNameId = getRandomIdBase();\n  }\n  TooltipMarkupStyleCreator.prototype._generateStyleName = function () {\n    return '__EC_aUTo_' + this._nextStyleNameId++;\n  };\n  TooltipMarkupStyleCreator.prototype.makeTooltipMarker = function (markerType, colorStr, renderMode) {\n    var markerId = renderMode === 'richText' ? this._generateStyleName() : null;\n    var marker = getTooltipMarker({\n      color: colorStr,\n      type: markerType,\n      renderMode: renderMode,\n      markerId: markerId\n    });\n    if (isString(marker)) {\n      return marker;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(markerId);\n      }\n      this.richTextStyles[markerId] = marker.style;\n      return marker.content;\n    }\n  };\n  /**\r\n   * @usage\r\n   * ```ts\r\n   * const styledText = markupStyleCreator.wrapRichTextStyle([\r\n   *     // The styles will be auto merged.\r\n   *     {\r\n   *         fontSize: 12,\r\n   *         color: 'blue'\r\n   *     },\r\n   *     {\r\n   *         padding: 20\r\n   *     }\r\n   * ]);\r\n   * ```\r\n   */\n  TooltipMarkupStyleCreator.prototype.wrapRichTextStyle = function (text, styles) {\n    var finalStl = {};\n    if (isArray(styles)) {\n      each(styles, function (stl) {\n        return extend(finalStl, stl);\n      });\n    } else {\n      extend(finalStl, styles);\n    }\n    var styleName = this._generateStyleName();\n    this.richTextStyles[styleName] = finalStl;\n    return \"{\" + styleName + \"|\" + text + \"}\";\n  };\n  return TooltipMarkupStyleCreator;\n}();\nexport { TooltipMarkupStyleCreator };"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,oBAAoB,QAAQ,sBAAsB;AAC5G,SAASC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,QAAQ,0BAA0B;AAC/F,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,eAAe,QAAQ,sBAAsB;AACtD,IAAIC,uBAAuB,GAAG,eAAe;AAC7C,SAASC,oBAAoBA,CAACC,SAAS,EAAE;EACvC,IAAIC,UAAU,GAAGD,SAAS,CAACC,UAAU;EACrC,IAAIA,UAAU,IAAI,IAAI,EAAE;IACtB,OAAOH,uBAAuB;EAChC,CAAC,MAAM;IACL,OAAO,cAAc,GAAGZ,UAAU,CAACe,UAAU,GAAG,EAAE,CAAC,GAAG,IAAI;EAC5D;AACF;AACA;AACA,SAASC,mBAAmBA,CAACF,SAAS,EAAEG,UAAU,EAAE;EAClD,IAAIC,aAAa,GAAGJ,SAAS,CAACK,KAAK,IAAI,SAAS;EAChD,IAAIC,YAAY,GAAGN,SAAS,CAACO,QAAQ,IAAI,EAAE;EAC3C,IAAIC,cAAc,GAAGR,SAAS,CAACS,UAAU,IAAI,KAAK;EAClD,IAAIC,cAAc,GAAGV,SAAS,CAACK,KAAK,IAAI,SAAS;EACjD,IAAIM,aAAa,GAAGX,SAAS,CAACO,QAAQ,IAAI,EAAE;EAC5C,IAAIK,eAAe,GAAGZ,SAAS,CAACS,UAAU,IAAI,KAAK;EACnD,IAAIN,UAAU,KAAK,MAAM,EAAE;IACzB;IACA,OAAO;MACL;MACAU,SAAS,EAAE,YAAY,GAAG3B,UAAU,CAACoB,YAAY,GAAG,EAAE,CAAC,GAAG,WAAW,GAAGpB,UAAU,CAACkB,aAAa,CAAC,GAAG,eAAe,GAAGlB,UAAU,CAACsB,cAAc,GAAG,EAAE,CAAC;MACrJ;MACAM,UAAU,EAAE,YAAY,GAAG5B,UAAU,CAACyB,aAAa,GAAG,EAAE,CAAC,GAAG,WAAW,GAAGzB,UAAU,CAACwB,cAAc,CAAC,GAAG,eAAe,GAAGxB,UAAU,CAAC0B,eAAe,GAAG,EAAE;IAC1J,CAAC;EACH,CAAC,MAAM;IACL,OAAO;MACLC,SAAS,EAAE;QACTN,QAAQ,EAAED,YAAY;QACtBS,IAAI,EAAEX,aAAa;QACnBK,UAAU,EAAED;MACd,CAAC;MACDM,UAAU,EAAE;QACVP,QAAQ,EAAEI,aAAa;QACvBI,IAAI,EAAEL,cAAc;QACpBD,UAAU,EAAEG;MACd;IACF,CAAC;EACH;AACF;AACA;AACA;AACA,IAAII,SAAS,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC/B,IAAIC,cAAc,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC;AACjD;AACA,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAChDA,MAAM,CAACD,IAAI,GAAGA,IAAI;EAClB,OAAOC,MAAM;AACf;AACA,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,OAAOA,IAAI,CAACH,IAAI,KAAK,SAAS;AAChC;AACA,SAASI,UAAUA,CAACD,IAAI,EAAE;EACxB,OAAOD,iBAAiB,CAACC,IAAI,CAAC,GAAGE,YAAY,GAAGC,cAAc;AAChE;AACA,SAASC,gBAAgBA,CAACJ,IAAI,EAAE;EAC9B,IAAID,iBAAiB,CAACC,IAAI,CAAC,EAAE;IAC3B,IAAIK,UAAU,GAAG,CAAC;IAClB,IAAIC,WAAW,GAAGN,IAAI,CAACO,MAAM,CAACC,MAAM;IACpC,IAAIC,aAAa,GAAGH,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAG,CAAC,IAAI,CAACN,IAAI,CAACU,QAAQ;IACxE1C,IAAI,CAACgC,IAAI,CAACO,MAAM,EAAE,UAAUI,QAAQ,EAAE;MACpC,IAAIC,WAAW,GAAGR,gBAAgB,CAACO,QAAQ,CAAC;MAC5C;MACA;MACA,IAAIC,WAAW,IAAIP,UAAU,EAAE;QAC7BA,UAAU,GAAGO,WAAW,GAAG,EAAEH,aAAa;QAC1C;QACA,CAACG;QACD;QACA;QAAA,GACGb,iBAAiB,CAACY,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACD,QAAQ,CAAC,CAAC;MACxD;IACF,CAAC,CAAC;IACF,OAAOL,UAAU;EACnB;EACA,OAAO,CAAC;AACV;AACA,SAASH,YAAYA,CAACW,GAAG,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAE;EAC3E,IAAIN,QAAQ,GAAGI,QAAQ,CAACJ,QAAQ;EAChC,IAAIO,IAAI,GAAGC,MAAM,CAACd,gBAAgB,CAACU,QAAQ,CAAC,CAAC;EAC7C,IAAIK,iBAAiB,GAAG,EAAE;EAC1B,IAAIC,SAAS,GAAGN,QAAQ,CAACP,MAAM,IAAI,EAAE;EACrCnC,MAAM,CAAC,CAACgD,SAAS,IAAIlD,OAAO,CAACkD,SAAS,CAAC,CAAC;EACxCA,SAAS,GAAGA,SAAS,IAAI,EAAE;EAC3B,IAAIC,SAAS,GAAGR,GAAG,CAACQ,SAAS;EAC7B,IAAIP,QAAQ,CAACQ,UAAU,IAAID,SAAS,EAAE;IACpCD,SAAS,GAAGA,SAAS,CAACG,KAAK,CAAC,CAAC;IAC7B,IAAIC,QAAQ,GAAG;MACbC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE;IACb,CAAC;IACD,IAAIzD,MAAM,CAACuD,QAAQ,EAAEH,SAAS,CAAC,EAAE;MAC/B,IAAIM,YAAY,GAAG,IAAIrD,mBAAmB,CAACkD,QAAQ,CAACH,SAAS,CAAC,EAAE,IAAI,CAAC;MACrED,SAAS,CAACQ,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;QAC7B,OAAOH,YAAY,CAACI,QAAQ,CAACF,CAAC,CAACG,SAAS,EAAEF,CAAC,CAACE,SAAS,CAAC;MACxD,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IAAIX,SAAS,KAAK,YAAY,EAAE;MACnCD,SAAS,CAACa,OAAO,CAAC,CAAC;IACrB;EACF;EACAjE,IAAI,CAACoD,SAAS,EAAE,UAAUT,QAAQ,EAAEuB,GAAG,EAAE;IACvC,IAAIC,cAAc,GAAGrB,QAAQ,CAACqB,cAAc;IAC5C,IAAIC,aAAa,GAAGnC,UAAU,CAACU,QAAQ,CAAC;IACxC;IACAwB,cAAc,GAAG9D,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC,EAAEwC,GAAG,CAAC,EAAE;MACvCsB,cAAc,EAAEA;IAClB,CAAC,CAAC,GAAGtB,GAAG,EAAEF,QAAQ,EAAEuB,GAAG,GAAG,CAAC,GAAGjB,IAAI,CAACoB,IAAI,GAAG,CAAC,EAAErB,gBAAgB,CAAC;IAC9DoB,aAAa,IAAI,IAAI,IAAIjB,iBAAiB,CAACmB,IAAI,CAACF,aAAa,CAAC;EAChE,CAAC,CAAC;EACF,IAAIA,aAAa,GAAGvB,GAAG,CAAChC,UAAU,KAAK,UAAU,GAAGsC,iBAAiB,CAACoB,IAAI,CAACtB,IAAI,CAACuB,QAAQ,CAAC,GAAGC,aAAa,CAACzB,gBAAgB,EAAEG,iBAAiB,CAACoB,IAAI,CAAC,EAAE,CAAC,EAAE7B,QAAQ,GAAGK,oBAAoB,GAAGE,IAAI,CAACoB,IAAI,CAAC;EACpM,IAAI3B,QAAQ,EAAE;IACZ,OAAO0B,aAAa;EACtB;EACA,IAAIM,iBAAiB,GAAG7E,iBAAiB,CAACiD,QAAQ,CAAC6B,MAAM,EAAE,SAAS,EAAE9B,GAAG,CAAC+B,MAAM,CAAC;EACjF,IAAIrD,SAAS,GAAGX,mBAAmB,CAACoC,gBAAgB,EAAEH,GAAG,CAAChC,UAAU,CAAC,CAACU,SAAS;EAC/E,IAAIsD,iBAAiB,GAAGpE,oBAAoB,CAACuC,gBAAgB,CAAC;EAC9D,IAAIH,GAAG,CAAChC,UAAU,KAAK,UAAU,EAAE;IACjC,OAAOiE,sBAAsB,CAACjC,GAAG,EAAE6B,iBAAiB,EAAEnD,SAAS,CAAC,GAAG0B,IAAI,CAACuB,QAAQ,GAAGJ,aAAa;EAClG,CAAC,MAAM;IACL,OAAOK,aAAa,CAACzB,gBAAgB,EAAE,eAAe,GAAGzB,SAAS,GAAG,GAAG,GAAGsD,iBAAiB,GAAG,MAAM,GAAGjF,UAAU,CAAC8E,iBAAiB,CAAC,GAAG,QAAQ,GAAGN,aAAa,EAAErB,oBAAoB,CAAC;EACzL;AACF;AACA,SAASZ,cAAcA,CAACU,GAAG,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAE;EAC7E,IAAInC,UAAU,GAAGgC,GAAG,CAAChC,UAAU;EAC/B,IAAIkE,MAAM,GAAGjC,QAAQ,CAACiC,MAAM;EAC5B,IAAIC,OAAO,GAAGlC,QAAQ,CAACkC,OAAO;EAC9B,IAAIC,QAAQ,GAAG,CAACnC,QAAQ,CAACoC,UAAU;EACnC,IAAIC,IAAI,GAAGrC,QAAQ,CAACqC,IAAI;EACxB,IAAIP,MAAM,GAAG/B,GAAG,CAAC+B,MAAM;EACvB,IAAIT,cAAc,GAAGrB,QAAQ,CAACqB,cAAc,IAAItB,GAAG,CAACsB,cAAc,IAAI,UAAUiB,KAAK,EAAE;IACrFA,KAAK,GAAGlF,OAAO,CAACkF,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;IACxC,OAAOjF,GAAG,CAACiF,KAAK,EAAE,UAAUC,GAAG,EAAEnB,GAAG,EAAE;MACpC,OAAOrE,iBAAiB,CAACwF,GAAG,EAAEnF,OAAO,CAACoF,eAAe,CAAC,GAAGA,eAAe,CAACpB,GAAG,CAAC,GAAGoB,eAAe,EAAEV,MAAM,CAAC;IAC1G,CAAC,CAAC;EACJ,CAAC;EACD,IAAIG,MAAM,IAAIC,OAAO,EAAE;IACrB;EACF;EACA,IAAIO,SAAS,GAAGN,QAAQ,GAAG,EAAE,GAAGpC,GAAG,CAAC2C,kBAAkB,CAACC,iBAAiB,CAAC3C,QAAQ,CAACoC,UAAU,EAAEpC,QAAQ,CAAC4C,WAAW,IAAI,MAAM,EAAE7E,UAAU,CAAC;EACzI,IAAI8E,YAAY,GAAGZ,MAAM,GAAG,EAAE,GAAGlF,iBAAiB,CAACsF,IAAI,EAAE,SAAS,EAAEP,MAAM,CAAC;EAC3E,IAAIU,eAAe,GAAGxC,QAAQ,CAAC8C,SAAS;EACxC,IAAIC,iBAAiB,GAAGb,OAAO,GAAG,EAAE,GAAGb,cAAc,CAACrB,QAAQ,CAACsC,KAAK,EAAEtC,QAAQ,CAACgD,SAAS,CAAC;EACzF,IAAIC,eAAe,GAAG,CAACd,QAAQ,IAAI,CAACF,MAAM;EAC1C;EACA,IAAIiB,kBAAkB,GAAG,CAACf,QAAQ,IAAIF,MAAM;EAC5C,IAAIkB,EAAE,GAAGrF,mBAAmB,CAACoC,gBAAgB,EAAEnC,UAAU,CAAC;IACxDU,SAAS,GAAG0E,EAAE,CAAC1E,SAAS;IACxBC,UAAU,GAAGyE,EAAE,CAACzE,UAAU;EAC5B,OAAOX,UAAU,KAAK,UAAU,GAAG,CAACoE,QAAQ,GAAG,EAAE,GAAGM,SAAS,KAAKR,MAAM,GAAG,EAAE,GAAGD,sBAAsB,CAACjC,GAAG,EAAE8C,YAAY,EAAEpE,SAAS,CAAC;EACpI;EAAA,GACGyD,OAAO,GAAG,EAAE,GAAGkB,uBAAuB,CAACrD,GAAG,EAAEgD,iBAAiB,EAAEE,eAAe,EAAEC,kBAAkB,EAAExE,UAAU,CAAC,CAAC,GAAGiD,aAAa,CAACzB,gBAAgB,EAAE,CAACiC,QAAQ,GAAG,EAAE,GAAGM,SAAS,KAAKR,MAAM,GAAG,EAAE,GAAGoB,kBAAkB,CAACR,YAAY,EAAE,CAACV,QAAQ,EAAE1D,SAAS,CAAC,CAAC,IAAIyD,OAAO,GAAG,EAAE,GAAGoB,mBAAmB,CAACP,iBAAiB,EAAEE,eAAe,EAAEC,kBAAkB,EAAExE,UAAU,CAAC,CAAC,EAAEuB,oBAAoB,CAAC;AAC9X;AACA;AACA;AACA;AACA,OAAO,SAASsD,kBAAkBA,CAACvD,QAAQ,EAAE0C,kBAAkB,EAAE3E,UAAU,EAAEwC,SAAS,EAAEuB,MAAM,EAAE5B,gBAAgB,EAAE;EAChH,IAAI,CAACF,QAAQ,EAAE;IACb;EACF;EACA,IAAIwD,OAAO,GAAGrE,UAAU,CAACa,QAAQ,CAAC;EAClC,IAAID,GAAG,GAAG;IACR+B,MAAM,EAAEA,MAAM;IACd/D,UAAU,EAAEA,UAAU;IACtBwC,SAAS,EAAEA,SAAS;IACpBmC,kBAAkB,EAAEA,kBAAkB;IACtCrB,cAAc,EAAErB,QAAQ,CAACqB;EAC3B,CAAC;EACD,OAAOmC,OAAO,CAACzD,GAAG,EAAEC,QAAQ,EAAE,CAAC,EAAEE,gBAAgB,CAAC;AACpD;AACA,SAASE,MAAMA,CAACqD,QAAQ,EAAE;EACxB,OAAO;IACLlC,IAAI,EAAE3C,SAAS,CAAC6E,QAAQ,CAAC;IACzB/B,QAAQ,EAAE7C,cAAc,CAAC4E,QAAQ;EACnC,CAAC;AACH;AACA,SAAS9B,aAAaA,CAAC/D,SAAS,EAAE8F,cAAc,EAAEC,MAAM,EAAE;EACxD,IAAIC,QAAQ,GAAG,gCAAgC;EAC/C,IAAIC,SAAS,GAAG,UAAU,GAAGF,MAAM,GAAG,QAAQ;EAC9C,IAAI5B,iBAAiB,GAAGpE,oBAAoB,CAACC,SAAS,CAAC;EACvD,OAAO,eAAe,GAAGiG,SAAS,GAAG,GAAG,GAAG9B,iBAAiB,GAAG,MAAM,GAAG2B,cAAc,GAAGE,QAAQ,GAAG,QAAQ;AAC9G;AACA,SAASP,kBAAkBA,CAAChB,IAAI,EAAEyB,aAAa,EAAEC,KAAK,EAAE;EACtD,IAAIC,SAAS,GAAGF,aAAa,GAAG,iBAAiB,GAAG,EAAE;EACtD,OAAO,gBAAgB,GAAGC,KAAK,GAAG,GAAG,GAAGC,SAAS,GAAG,KAAK,GAAGlH,UAAU,CAACuF,IAAI,CAAC,GAAG,SAAS;AAC1F;AACA,SAASiB,mBAAmBA,CAACW,SAAS,EAAEC,UAAU,EAAEhB,kBAAkB,EAAEa,KAAK,EAAE;EAC7E;EACA,IAAII,UAAU,GAAGjB,kBAAkB,GAAG,MAAM,GAAG,MAAM;EACrD,IAAIkB,QAAQ,GAAGF,UAAU,GAAG,0BAA0B,GAAGC,UAAU,GAAG,EAAE;EACxEF,SAAS,GAAG7G,OAAO,CAAC6G,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;EACxD,OAAO,gBAAgB,GAAGG,QAAQ,GAAG,GAAG,GAAGL,KAAK,GAAG;EACnD;EAAA,EACE1G,GAAG,CAAC4G,SAAS,EAAE,UAAU3B,KAAK,EAAE;IAChC,OAAOxF,UAAU,CAACwF,KAAK,CAAC;EAC1B,CAAC,CAAC,CAACb,IAAI,CAAC,cAAc,CAAC,GAAG,SAAS;AACrC;AACA,SAASO,sBAAsBA,CAACjC,GAAG,EAAEsC,IAAI,EAAE0B,KAAK,EAAE;EAChD,OAAOhE,GAAG,CAAC2C,kBAAkB,CAAC2B,iBAAiB,CAAChC,IAAI,EAAE0B,KAAK,CAAC;AAC9D;AACA,SAASX,uBAAuBA,CAACrD,GAAG,EAAEuE,MAAM,EAAEJ,UAAU,EAAEhB,kBAAkB,EAAEa,KAAK,EAAE;EACnF,IAAIQ,MAAM,GAAG,CAACR,KAAK,CAAC;EACpB,IAAIS,WAAW,GAAGtB,kBAAkB,GAAG,EAAE,GAAG,EAAE;EAC9CgB,UAAU,IAAIK,MAAM,CAAC/C,IAAI,CAAC;IACxBiD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,WAAW,CAAC;IAC/BE,KAAK,EAAE;EACT,CAAC,CAAC;EACF;EACA,OAAO3E,GAAG,CAAC2C,kBAAkB,CAAC2B,iBAAiB,CAACjH,OAAO,CAACkH,MAAM,CAAC,GAAGA,MAAM,CAAC7C,IAAI,CAAC,IAAI,CAAC,GAAG6C,MAAM,EAAEC,MAAM,CAAC;AACvG;AACA,OAAO,SAASI,mCAAmCA,CAACC,MAAM,EAAE5B,SAAS,EAAE;EACrE,IAAIe,KAAK,GAAGa,MAAM,CAACC,OAAO,CAAC,CAAC,CAACC,aAAa,CAAC9B,SAAS,EAAE,OAAO,CAAC;EAC9D,IAAI/E,KAAK,GAAG8F,KAAK,CAACa,MAAM,CAACG,cAAc,CAAC;EACxC,OAAO/H,oBAAoB,CAACiB,KAAK,CAAC;AACpC;AACA,OAAO,SAAS+G,0BAA0BA,CAACC,KAAK,EAAElH,UAAU,EAAE;EAC5D,IAAI0G,OAAO,GAAGQ,KAAK,CAACC,GAAG,CAAC,SAAS,CAAC;EAClC,OAAOT,OAAO,IAAI,IAAI,GAAGA;EACzB;EAAA,EACE1G,UAAU,KAAK,UAAU,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,IAAIoH,yBAAyB,GAAG,aAAa,YAAY;EACvD,SAASA,yBAAyBA,CAAA,EAAG;IACnC,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB;IACA;IACA;IACA,IAAI,CAACC,gBAAgB,GAAG5H,eAAe,CAAC,CAAC;EAC3C;EACA0H,yBAAyB,CAACG,SAAS,CAACC,kBAAkB,GAAG,YAAY;IACnE,OAAO,YAAY,GAAG,IAAI,CAACF,gBAAgB,EAAE;EAC/C,CAAC;EACDF,yBAAyB,CAACG,SAAS,CAAC3C,iBAAiB,GAAG,UAAUP,UAAU,EAAEoD,QAAQ,EAAEzH,UAAU,EAAE;IAClG,IAAI0H,QAAQ,GAAG1H,UAAU,KAAK,UAAU,GAAG,IAAI,CAACwH,kBAAkB,CAAC,CAAC,GAAG,IAAI;IAC3E,IAAIG,MAAM,GAAG7I,gBAAgB,CAAC;MAC5BoB,KAAK,EAAEuH,QAAQ;MACfzG,IAAI,EAAEqD,UAAU;MAChBrE,UAAU,EAAEA,UAAU;MACtB0H,QAAQ,EAAEA;IACZ,CAAC,CAAC;IACF,IAAIxI,QAAQ,CAACyI,MAAM,CAAC,EAAE;MACpB,OAAOA,MAAM;IACf,CAAC,MAAM;MACL,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCvI,MAAM,CAACmI,QAAQ,CAAC;MAClB;MACA,IAAI,CAACL,cAAc,CAACK,QAAQ,CAAC,GAAGC,MAAM,CAAC3B,KAAK;MAC5C,OAAO2B,MAAM,CAACI,OAAO;IACvB;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEX,yBAAyB,CAACG,SAAS,CAACjB,iBAAiB,GAAG,UAAU0B,IAAI,EAAExB,MAAM,EAAE;IAC9E,IAAIyB,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI5I,OAAO,CAACmH,MAAM,CAAC,EAAE;MACnBrH,IAAI,CAACqH,MAAM,EAAE,UAAU0B,GAAG,EAAE;QAC1B,OAAO1I,MAAM,CAACyI,QAAQ,EAAEC,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL1I,MAAM,CAACyI,QAAQ,EAAEzB,MAAM,CAAC;IAC1B;IACA,IAAI2B,SAAS,GAAG,IAAI,CAACX,kBAAkB,CAAC,CAAC;IACzC,IAAI,CAACH,cAAc,CAACc,SAAS,CAAC,GAAGF,QAAQ;IACzC,OAAO,GAAG,GAAGE,SAAS,GAAG,GAAG,GAAGH,IAAI,GAAG,GAAG;EAC3C,CAAC;EACD,OAAOZ,yBAAyB;AAClC,CAAC,CAAC,CAAC;AACH,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}