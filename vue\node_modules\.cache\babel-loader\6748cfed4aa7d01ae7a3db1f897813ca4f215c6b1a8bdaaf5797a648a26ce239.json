{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: \"FrontLayout\",\n  data() {\n    return {\n      top: '',\n      notice: [],\n      user: JSON.parse(localStorage.getItem(\"xm-user\") || '{}'),\n      name: null,\n      navItems: [{\n        name: '系统首页',\n        path: '/front/home',\n        icon: 'el-icon-house'\n      }, {\n        name: '订单信息',\n        path: '/front/dingdan',\n        icon: 'el-icon-document'\n      }, {\n        name: '购物车',\n        path: '/front/dingdan2',\n        icon: 'el-icon-shopping-cart-2'\n      }, {\n        name: '系统讨论',\n        path: '/front/blogs',\n        icon: 'el-icon-chat-dot-round'\n      }, {\n        name: '点餐推荐',\n        path: '/front/freemovies',\n        icon: 'el-icon-star-on'\n      }, {\n        name: '公告信息',\n        path: '/front/notice',\n        icon: 'el-icon-bell'\n      }]\n    };\n  },\n  mounted() {\n    this.loadNotice();\n  },\n  methods: {\n    loadNotice() {\n      this.$request.get('/notice/selectAll').then(res => {\n        this.notice = res.data;\n        let i = 0;\n        if (this.notice && this.notice.length) {\n          this.top = this.notice[0].content;\n          setInterval(() => {\n            this.top = this.notice[i].content;\n            i++;\n            if (i === this.notice.length) {\n              i = 0;\n            }\n          }, 2500);\n        }\n      });\n    },\n    updateUser() {\n      this.user = JSON.parse(localStorage.getItem('xm-user') || '{}');\n    },\n    navTo(url) {\n      this.$router.push(url);\n    },\n    getRoleText(role) {\n      const roleMap = {\n        'ADMIN': '管理员',\n        'BUSINESS': '商家',\n        'USER': '用户'\n      };\n      return roleMap[role] || '用户';\n    },\n    handleCommand(command) {\n      if (command === 'person') {\n        this.navTo('/front/person');\n      } else if (command === 'logout') {\n        this.logout();\n      }\n    },\n    logout() {\n      this.$confirm('确定要退出登录吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        localStorage.removeItem(\"xm-user\");\n        this.$router.push(\"/login\");\n        this.$message.success('退出登录成功');\n      }).catch(() => {\n        // 取消退出\n      });\n    },\n    search() {\n      let name = this.name ? this.name : '';\n      location.href = '/front/search?name=' + name;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "top", "notice", "user", "JSON", "parse", "localStorage", "getItem", "navItems", "path", "icon", "mounted", "loadNotice", "methods", "$request", "get", "then", "res", "i", "length", "content", "setInterval", "updateUser", "navTo", "url", "$router", "push", "getRoleText", "role", "roleMap", "handleCommand", "command", "logout", "$confirm", "confirmButtonText", "cancelButtonText", "type", "removeItem", "$message", "success", "catch", "search", "location", "href"], "sources": ["src/views/Front.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!--头部-->\r\n    <div class=\"front-header\">\r\n      <div class=\"front-header-left\" @click=\"navTo('/front/home')\">\r\n        <div class=\"logo-container\">\r\n          <img src=\"@/assets/imgs/logo.png\" alt=\"\" class=\"logo-image\">\r\n          <div class=\"logo-text\">\r\n            <div class=\"title\">点餐系统</div>\r\n            <div class=\"subtitle\">美食优选</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"front-header-center\">\r\n        <div class=\"front-header-nav\">\r\n          <div class=\"nav-container\">\r\n            <div \r\n              v-for=\"(item, index) in navItems\" \r\n              :key=\"index\"\r\n              @click=\"navTo(item.path)\"\r\n              :class=\"['nav-item', { 'nav-item-active': $route.path === item.path }]\">\r\n              <i :class=\"item.icon\" class=\"nav-icon\"></i>\r\n              <span class=\"nav-text\">{{ item.name }}</span>\r\n              <div class=\"nav-indicator\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"front-header-right\">\r\n        <div v-if=\"!user.username\" class=\"auth-buttons\">\r\n          <el-button \r\n            @click=\"$router.push('/login')\" \r\n            class=\"auth-btn login-btn\"\r\n            size=\"medium\">\r\n            登录\r\n          </el-button>\r\n          <el-button \r\n            @click=\"$router.push('/register')\" \r\n            class=\"auth-btn register-btn\"\r\n            size=\"medium\">\r\n            注册\r\n          </el-button>\r\n        </div>\r\n        <div v-else class=\"user-info\">\r\n          <el-dropdown trigger=\"hover\" @command=\"handleCommand\">\r\n            <div class=\"user-dropdown\">\r\n              <div class=\"user-avatar\">\r\n                <img :src=\"user.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'\" alt=\"\">\r\n              </div>\r\n              <div class=\"user-details\">\r\n                <div class=\"user-name\">{{ user.name || '用户' }}</div>\r\n                <div class=\"user-role\">{{ getRoleText(user.role) }}</div>\r\n              </div>\r\n              <i class=\"el-icon-arrow-down dropdown-arrow\"></i>\r\n            </div>\r\n            <el-dropdown-menu slot=\"dropdown\" class=\"custom-dropdown\">\r\n              <el-dropdown-item command=\"person\" class=\"dropdown-item\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>个人中心</span>\r\n              </el-dropdown-item>\r\n              <el-dropdown-item command=\"logout\" class=\"dropdown-item logout-item\">\r\n                <i class=\"el-icon-switch-button\"></i>\r\n                <span>退出登录</span>\r\n              </el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!--主体-->\r\n    <div class=\"main-body\">\r\n      <router-view ref=\"child\" @update:user=\"updateUser\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"FrontLayout\",\r\n  data() {\r\n    return {\r\n      top: '',\r\n      notice: [],\r\n      user: JSON.parse(localStorage.getItem(\"xm-user\") || '{}'),\r\n      name: null,\r\n      navItems: [\r\n        { name: '系统首页', path: '/front/home', icon: 'el-icon-house' },\r\n        { name: '订单信息', path: '/front/dingdan', icon: 'el-icon-document' },\r\n        { name: '购物车', path: '/front/dingdan2', icon: 'el-icon-shopping-cart-2' },\r\n        { name: '系统讨论', path: '/front/blogs', icon: 'el-icon-chat-dot-round' },\r\n        { name: '点餐推荐', path: '/front/freemovies', icon: 'el-icon-star-on' },\r\n        { name: '公告信息', path: '/front/notice', icon: 'el-icon-bell' }\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadNotice()\r\n  },\r\n  methods: {\r\n    loadNotice() {\r\n      this.$request.get('/notice/selectAll').then(res => {\r\n        this.notice = res.data\r\n        let i = 0\r\n        if (this.notice && this.notice.length) {\r\n          this.top = this.notice[0].content\r\n          setInterval(() => {\r\n            this.top = this.notice[i].content\r\n            i++\r\n            if (i === this.notice.length) {\r\n              i = 0\r\n            }\r\n          }, 2500)\r\n        }\r\n      })\r\n    },\r\n    updateUser() {\r\n      this.user = JSON.parse(localStorage.getItem('xm-user') || '{}')\r\n    },\r\n    navTo(url) {\r\n      this.$router.push(url)\r\n    },\r\n    getRoleText(role) {\r\n      const roleMap = {\r\n        'ADMIN': '管理员',\r\n        'BUSINESS': '商家',\r\n        'USER': '用户'\r\n      }\r\n      return roleMap[role] || '用户'\r\n    },\r\n    handleCommand(command) {\r\n      if (command === 'person') {\r\n        this.navTo('/front/person')\r\n      } else if (command === 'logout') {\r\n        this.logout()\r\n      }\r\n    },\r\n    logout() {\r\n      this.$confirm('确定要退出登录吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        localStorage.removeItem(\"xm-user\")\r\n        this.$router.push(\"/login\")\r\n        this.$message.success('退出登录成功')\r\n      }).catch(() => {\r\n        // 取消退出\r\n      })\r\n    },\r\n    search() {\r\n      let name = this.name ? this.name : ''\r\n      location.href = '/front/search?name=' + name\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.front-header {\r\n  height: 70px;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);\r\n  box-shadow: 0 2px 20px rgba(59, 130, 246, 0.15);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 30px;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(10px);\r\n  border-bottom: 1px solid rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.front-header-left {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.front-header-left:hover {\r\n  transform: scale(1.02);\r\n}\r\n\r\n.logo-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.logo-image {\r\n  width: 45px;\r\n  height: 45px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.logo-image:hover {\r\n  transform: rotate(5deg) scale(1.05);\r\n  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.logo-text {\r\n  color: #1e40af;\r\n}\r\n\r\n.title {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  line-height: 1.2;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.subtitle {\r\n  font-size: 12px;\r\n  opacity: 0.9;\r\n  font-weight: 400;\r\n}\r\n\r\n.front-header-center {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  margin: 0 40px;\r\n}\r\n\r\n.nav-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 50px;\r\n  padding: 8px 12px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.nav-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 12px 20px;\r\n  border-radius: 25px;\r\n  cursor: pointer;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  position: relative;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  overflow: hidden;\r\n}\r\n\r\n.nav-item:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n  color: white;\r\n}\r\n\r\n.nav-item-active {\r\n  background: rgba(255, 255, 255, 0.25);\r\n  color: white;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.nav-item-active .nav-indicator {\r\n  opacity: 1;\r\n  transform: scaleX(1);\r\n}\r\n\r\n.nav-icon {\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-item:hover .nav-icon {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.nav-text {\r\n  white-space: nowrap;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-indicator {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%) scaleX(0);\r\n  width: 20px;\r\n  height: 2px;\r\n  background: white;\r\n  border-radius: 2px;\r\n  opacity: 0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.front-header-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.auth-buttons {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.auth-btn {\r\n  border-radius: 25px;\r\n  padding: 10px 24px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.login-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  border-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.login-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.register-btn {\r\n  background: white;\r\n  color: #667eea;\r\n  border-color: white;\r\n}\r\n\r\n.register-btn:hover {\r\n  background: #f8f9fa;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.user-info {\r\n  margin-left: 20px;\r\n}\r\n\r\n.user-dropdown {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 8px 16px;\r\n  border-radius: 25px;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  color: white;\r\n}\r\n\r\n.user-dropdown:hover {\r\n  background: rgba(255, 255, 255, 0.25);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.user-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.user-avatar img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.user-dropdown:hover .user-avatar {\r\n  border-color: white;\r\n  transform: scale(1.05);\r\n}\r\n\r\n.user-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n}\r\n\r\n.user-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  line-height: 1.2;\r\n}\r\n\r\n.user-role {\r\n  font-size: 12px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.dropdown-arrow {\r\n  font-size: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.user-dropdown:hover .dropdown-arrow {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.main-body {\r\n  min-height: calc(100vh - 70px);\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 下拉菜单样式 */\r\n.custom-dropdown {\r\n  border-radius: 12px;\r\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);\r\n  border: 1px solid rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n  background: rgba(255, 255, 255, 0.95);\r\n}\r\n\r\n.dropdown-item {\r\n  padding: 12px 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  color: #333;\r\n}\r\n\r\n.dropdown-item:hover {\r\n  background: #f0f2ff;\r\n  color: #667eea;\r\n}\r\n\r\n.dropdown-item i {\r\n  font-size: 16px;\r\n  width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.logout-item:hover {\r\n  background: #fff2f2;\r\n  color: #f56565;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .front-header {\r\n    padding: 0 20px;\r\n  }\r\n  \r\n  .front-header-center {\r\n    margin: 0 20px;\r\n  }\r\n  \r\n  .nav-item {\r\n    padding: 10px 16px;\r\n  }\r\n  \r\n  .nav-text {\r\n    display: none;\r\n  }\r\n  \r\n  .nav-item {\r\n    gap: 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .front-header {\r\n    padding: 0 15px;\r\n  }\r\n  \r\n  .front-header-center {\r\n    margin: 0 15px;\r\n  }\r\n  \r\n  .nav-container {\r\n    gap: 4px;\r\n    padding: 6px 8px;\r\n  }\r\n  \r\n  .nav-item {\r\n    padding: 8px 12px;\r\n  }\r\n  \r\n  .logo-text .title {\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .logo-text .subtitle {\r\n    display: none;\r\n  }\r\n  \r\n  .user-details {\r\n    display: none;\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.front-header {\r\n  animation: fadeInUp 0.6s ease-out;\r\n}\r\n\r\n/* 滚动效果 */\r\n.front-header.scrolled {\r\n  background: rgba(102, 126, 234, 0.95);\r\n  backdrop-filter: blur(20px);\r\n}\r\n</style>"], "mappings": ";AAgFA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,GAAA;MACAC,MAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAR,IAAA;MACAS,QAAA,GACA;QAAAT,IAAA;QAAAU,IAAA;QAAAC,IAAA;MAAA,GACA;QAAAX,IAAA;QAAAU,IAAA;QAAAC,IAAA;MAAA,GACA;QAAAX,IAAA;QAAAU,IAAA;QAAAC,IAAA;MAAA,GACA;QAAAX,IAAA;QAAAU,IAAA;QAAAC,IAAA;MAAA,GACA;QAAAX,IAAA;QAAAU,IAAA;QAAAC,IAAA;MAAA,GACA;QAAAX,IAAA;QAAAU,IAAA;QAAAC,IAAA;MAAA;IAEA;EACA;EACAC,QAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACAD,WAAA;MACA,KAAAE,QAAA,CAAAC,GAAA,sBAAAC,IAAA,CAAAC,GAAA;QACA,KAAAf,MAAA,GAAAe,GAAA,CAAAjB,IAAA;QACA,IAAAkB,CAAA;QACA,SAAAhB,MAAA,SAAAA,MAAA,CAAAiB,MAAA;UACA,KAAAlB,GAAA,QAAAC,MAAA,IAAAkB,OAAA;UACAC,WAAA;YACA,KAAApB,GAAA,QAAAC,MAAA,CAAAgB,CAAA,EAAAE,OAAA;YACAF,CAAA;YACA,IAAAA,CAAA,UAAAhB,MAAA,CAAAiB,MAAA;cACAD,CAAA;YACA;UACA;QACA;MACA;IACA;IACAI,WAAA;MACA,KAAAnB,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA;IACAgB,MAAAC,GAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,CAAAF,GAAA;IACA;IACAG,YAAAC,IAAA;MACA,MAAAC,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IACAE,cAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAR,KAAA;MACA,WAAAQ,OAAA;QACA,KAAAC,MAAA;MACA;IACA;IACAA,OAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApB,IAAA;QACAV,YAAA,CAAA+B,UAAA;QACA,KAAAZ,OAAA,CAAAC,IAAA;QACA,KAAAY,QAAA,CAAAC,OAAA;MACA,GAAAC,KAAA;QACA;MAAA,CACA;IACA;IACAC,OAAA;MACA,IAAA1C,IAAA,QAAAA,IAAA,QAAAA,IAAA;MACA2C,QAAA,CAAAC,IAAA,2BAAA5C,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}