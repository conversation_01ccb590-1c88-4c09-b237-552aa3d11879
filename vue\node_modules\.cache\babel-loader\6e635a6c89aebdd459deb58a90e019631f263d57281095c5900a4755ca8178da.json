{"ast": null, "code": "import \"core-js/modules/esnext.iterator.map.js\";\nexport default {\n  name: \"Complaint\",\n  data() {\n    return {\n      tableData: [],\n      // 表格数据\n      pageNum: 1,\n      // 当前页码\n      pageSize: 10,\n      // 每页显示数量\n      total: 0,\n      // 数据总数\n      title: null,\n      // 查询的关键字（由 name 改为 title）\n      fromVisible: false,\n      // 控制弹窗的显示\n      form: {},\n      // 表单数据\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      // 获取用户信息\n      rules: {},\n      // 表单验证规则\n      ids: [] // 存储选中的数据的ID\n    };\n  },\n  created() {\n    this.load(1); // 初始化时加载第一页数据\n  },\n  methods: {\n    handleAdd() {\n      // 点击新增按钮时，清空表单并显示弹窗\n      this.form = {};\n      this.fromVisible = true;\n    },\n    handleEdit(row) {\n      // 点击编辑按钮时，赋值表单并显示弹窗\n      this.form = JSON.parse(JSON.stringify(row)); // 深拷贝防止数据引用问题\n      this.fromVisible = true;\n    },\n    save() {\n      // 保存按钮的逻辑，新增或更新数据\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          this.$request({\n            url: this.form.id ? '/complaint/update' : '/complaint/add',\n            // 根据是否有ID决定是新增还是更新\n            method: this.form.id ? 'PUT' : 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              // 保存成功\n              this.$message.success('保存成功');\n              this.load(1); // 重新加载数据\n              this.fromVisible = false; // 关闭弹窗\n            } else {\n              this.$message.error(res.msg); // 弹出错误信息\n            }\n          });\n        }\n      });\n    },\n    del(id) {\n      // 删除单个数据\n      this.$confirm('您确定删除吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/complaint/delete/' + id).then(res => {\n          if (res.code === '200') {\n            // 删除成功\n            this.$message.success('操作成功');\n            this.load(1); // 重新加载数据\n          } else {\n            this.$message.error(res.msg); // 弹出错误信息\n          }\n        });\n      }).catch(() => {});\n    },\n    handleSelectionChange(rows) {\n      // 处理表格选择框变化，记录选中的行\n      this.ids = rows.map(v => v.id);\n    },\n    delBatch() {\n      // 批量删除\n      if (!this.ids.length) {\n        this.$message.warning('请选择数据'); // 提示没有选择数据\n        return;\n      }\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/complaint/delete/batch', {\n          data: this.ids\n        }).then(res => {\n          if (res.code === '200') {\n            // 批量删除成功\n            this.$message.success('操作成功');\n            this.load(1); // 重新加载数据\n          } else {\n            this.$message.error(res.msg); // 弹出错误信息\n          }\n        });\n      }).catch(() => {});\n    },\n    load(pageNum) {\n      // 分页加载数据\n      if (pageNum) this.pageNum = pageNum; // 如果传入了页码，更新当前页码\n      this.$request.get('/complaint/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          title: this.title // 查询关键字（由 name 改为 title）\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list; // 更新表格数据\n          this.total = res.data?.total; // 更新数据总数\n        } else {\n          this.$message.error(res.msg); // 弹出错误信息\n        }\n      });\n    },\n    reset() {\n      // 重置查询条件\n      this.title = null; // 将 name 改为 title\n      this.load(1); // 重新加载第一页数据\n    },\n    handleCurrentChange(pageNum) {\n      // 处理分页器页码变化\n      this.load(pageNum);\n    },\n    handleAvatarSuccess(response, file, fileList) {\n      // 上传图片成功后的回调函数\n      this.form.sfImage = response.data; // 将图片 URL 存入表单数据\n      this.load(1); // 图片上传成功后自动刷新数据\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "title", "fromVisible", "form", "user", "JSON", "parse", "localStorage", "getItem", "rules", "ids", "created", "load", "methods", "handleAdd", "handleEdit", "row", "stringify", "save", "$refs", "formRef", "validate", "valid", "$request", "url", "id", "method", "then", "res", "code", "$message", "success", "error", "msg", "del", "$confirm", "type", "response", "delete", "catch", "handleSelectionChange", "rows", "map", "v", "delBatch", "length", "warning", "get", "params", "list", "reset", "handleCurrentChange", "handleAvatarSuccess", "file", "fileList", "sfImage"], "sources": ["src/views/manager/Complaint.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <!-- 搜索框区域 -->\r\n        <div class=\"search\">\r\n            <!-- 输入框用于标题查询 -->\r\n            <el-input placeholder=\"请输入标题查询\" style=\"width: 200px\" v-model=\"title\"></el-input>\r\n            <!-- 查询按钮，点击时调用 load 方法进行数据加载 -->\r\n            <el-button type=\"info\" plain style=\"margin-left: 10px\" @click=\"load(1)\">查询</el-button>\r\n            <!-- 重置按钮，点击时清空查询条件并重新加载数据 -->\r\n            <el-button type=\"warning\" plain style=\"margin-left: 10px\" @click=\"reset\">重置</el-button>\r\n        </div>\r\n\r\n        <!-- 操作按钮区域 -->\r\n        <div class=\"operation\">\r\n            <!-- 新增按钮，点击时调用 handleAdd 方法进行新增操作 -->\r\n            <el-button type=\"primary\" plain @click=\"handleAdd\">新增</el-button>\r\n            <!-- 批量删除按钮，点击时调用 delBatch 方法进行批量删除 -->\r\n            <el-button type=\"danger\" plain @click=\"delBatch\">批量删除</el-button>\r\n        </div>\r\n\r\n        <!-- 表格区域 -->\r\n        <div class=\"table\">\r\n            <el-table :data=\"tableData\" stripe @selection-change=\"handleSelectionChange\">\r\n                <!-- 选择框列 -->\r\n                <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n                <!-- 序号列 -->\r\n                <el-table-column prop=\"id\" label=\"序号\" width=\"70\" align=\"center\" sortable></el-table-column>\r\n                <!-- 标题列 -->\r\n                <el-table-column prop=\"title\" label=\"标题\"></el-table-column>\r\n                <!-- 文字内容列 -->\r\n                <el-table-column prop=\"sfContent\" label=\"文字内容\"></el-table-column>\r\n                <!-- 投诉图片列 -->\r\n                <el-table-column prop=\"sfImage\" label=\"投诉图片\">\r\n                    <template v-slot=\"scope\">\r\n                        <!-- 显示上传的图片，如果存在图片 URL -->\r\n                        <el-image\r\n                            v-if=\"scope.row.sfImage\"\r\n                            style=\"width: 60px; height: 60px; border-radius: 5px;\"\r\n                            :src=\"scope.row.sfImage\"\r\n                            :preview-src-list=\"[scope.row.sfImage]\">\r\n                        </el-image>\r\n                    </template>\r\n                </el-table-column>\r\n                <!-- 投诉状态列 -->\r\n                <el-table-column prop=\"status\" label=\"投诉状态\"></el-table-column>\r\n                <!-- 投诉日期列 -->\r\n                <el-table-column prop=\"sfComplaintDate\" label=\"投诉日期\" ></el-table-column>\r\n                <!-- 回复信息列 -->\r\n                <el-table-column prop=\"reply\" label=\"回复信息\"></el-table-column>\r\n                <!-- 用户ID列 -->\r\n                <el-table-column prop=\"sfUserId\" label=\"用户ID\"></el-table-column>\r\n                <!-- 操作列 -->\r\n                <el-table-column label=\"操作\" align=\"center\" width=\"180\">\r\n                    <template v-slot=\"scope\">\r\n                        <!-- 编辑按钮，点击时调用 handleEdit 方法编辑该条数据 -->\r\n                        <el-button size=\"mini\" type=\"primary\" plain @click=\"handleEdit(scope.row)\">回复</el-button>\r\n                        <!-- 删除按钮，点击时调用 del 方法删除该条数据 -->\r\n                        <el-button size=\"mini\" type=\"danger\" plain @click=\"del(scope.row.id)\">删除</el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n\r\n            <!-- 分页器 -->\r\n            <div class=\"pagination\">\r\n                <el-pagination\r\n                    background\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageNum\"\r\n                    :page-sizes=\"[5, 10, 20]\"\r\n                    :page-size=\"pageSize\"\r\n                    layout=\"total, prev, pager, next\"\r\n                    :total=\"total\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 新增/编辑弹窗 -->\r\n        <el-dialog title=\"投诉信息\" :visible.sync=\"fromVisible\" width=\"40%\" :close-on-click-modal=\"false\" destroy-on-close>\r\n            <el-form :model=\"form\" label-width=\"100px\" style=\"padding-right: 50px\" :rules=\"rules\" ref=\"formRef\">\r\n                <!-- 标题输入框 -->\r\n                <el-form-item label=\"标题\" prop=\"title\">\r\n                    <el-input v-model=\"form.title\" placeholder=\"标题\" readonly show-overflow-tooltip></el-input>\r\n                </el-form-item>\r\n                <!-- 文字内容输入框 -->\r\n                <el-form-item label=\"文字内容\" prop=\"sfContent\" >\r\n                    <el-input v-model=\"form.sfContent\" placeholder=\"文字内容\" readonly show-overflow-tooltip></el-input>\r\n                </el-form-item>\r\n                <!-- 投诉图片上传 -->\r\n                <el-form-item label=\"投诉图片\" prop=\"sfImage\" readonly>\r\n                    <!-- 上传图片按钮 -->\r\n                    <el-upload\r\n                        class=\"avatar-uploader\"\r\n                        :action=\"$baseUrl + '/files/upload'\"\r\n                        :show-file-list=\"false\"\r\n                        :on-success=\"handleAvatarSuccess\">\r\n                        <el-button type=\"primary\">点击上传</el-button>\r\n                    </el-upload>\r\n                    <!-- 显示上传成功后的图片 -->\r\n                    <el-image\r\n                        v-if=\"form.sfImage\"\r\n                        style=\"width: 100px; height: 100px; margin-top: 10px;\"\r\n                        :src=\"form.sfImage\"\r\n                        :preview-src-list=\"[form.sfImage]\">\r\n                    </el-image>\r\n                </el-form-item>\r\n\r\n                <!-- 投诉状态下拉选择框 -->\r\n                <el-form-item label=\"投诉状态\" prop=\"status\">\r\n                    <el-select v-model=\"form.status\" placeholder=\"请选择投诉状态\">\r\n                        <el-option label=\"未审核\" value=\"未审核\"></el-option>\r\n                        <el-option label=\"已审核\" value=\"已审核\"></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n\r\n                <!-- 投诉日期输入框 -->\r\n                <el-form-item label=\"投诉日期\" prop=\"sfComplaintDate\">\r\n                    <el-input v-model=\"form.sfComplaintDate\" placeholder=\"投诉日期\" readonly></el-input>\r\n                </el-form-item>\r\n                <!-- 回复信息输入框 -->\r\n                <el-form-item label=\"回复信息\" prop=\"reply\">\r\n                    <el-input v-model=\"form.reply\" placeholder=\"回复信息\" show-overflow-tooltip></el-input>\r\n                </el-form-item>\r\n                <!-- 用户ID输入框 -->\r\n                <el-form-item label=\"用户id\" prop=\"sfUserId\">\r\n                    <el-input v-model=\"form.sfUserId\" placeholder=\"用户id\" readonly></el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <!-- 取消按钮，关闭弹窗 -->\r\n                <el-button @click=\"fromVisible = false\">取 消</el-button>\r\n                <!-- 确定按钮，保存数据 -->\r\n                <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Complaint\",\r\n    data() {\r\n        return {\r\n            tableData: [],  // 表格数据\r\n            pageNum: 1,     // 当前页码\r\n            pageSize: 10,   // 每页显示数量\r\n            total: 0,       // 数据总数\r\n            title: null,    // 查询的关键字（由 name 改为 title）\r\n            fromVisible: false,  // 控制弹窗的显示\r\n            form: {},        // 表单数据\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),  // 获取用户信息\r\n            rules: {},       // 表单验证规则\r\n            ids: []          // 存储选中的数据的ID\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)  // 初始化时加载第一页数据\r\n    },\r\n    methods: {\r\n        handleAdd() {   // 点击新增按钮时，清空表单并显示弹窗\r\n            this.form = {}\r\n            this.fromVisible = true\r\n        },\r\n        handleEdit(row) {   // 点击编辑按钮时，赋值表单并显示弹窗\r\n            this.form = JSON.parse(JSON.stringify(row))  // 深拷贝防止数据引用问题\r\n            this.fromVisible = true\r\n        },\r\n        save() {   // 保存按钮的逻辑，新增或更新数据\r\n            this.$refs.formRef.validate((valid) => {\r\n                if (valid) {\r\n                    this.$request({\r\n                        url: this.form.id ? '/complaint/update' : '/complaint/add',  // 根据是否有ID决定是新增还是更新\r\n                        method: this.form.id ? 'PUT' : 'POST',\r\n                        data: this.form\r\n                    }).then(res => {\r\n                        if (res.code === '200') {  // 保存成功\r\n                            this.$message.success('保存成功')\r\n                            this.load(1)  // 重新加载数据\r\n                            this.fromVisible = false  // 关闭弹窗\r\n                        } else {\r\n                            this.$message.error(res.msg)  // 弹出错误信息\r\n                        }\r\n                    })\r\n                }\r\n            })\r\n        },\r\n        del(id) {   // 删除单个数据\r\n            this.$confirm('您确定删除吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n                this.$request.delete('/complaint/delete/' + id).then(res => {\r\n                    if (res.code === '200') {   // 删除成功\r\n                        this.$message.success('操作成功')\r\n                        this.load(1)  // 重新加载数据\r\n                    } else {\r\n                        this.$message.error(res.msg)  // 弹出错误信息\r\n                    }\r\n                })\r\n            }).catch(() => {})\r\n        },\r\n        handleSelectionChange(rows) {   // 处理表格选择框变化，记录选中的行\r\n            this.ids = rows.map(v => v.id)\r\n        },\r\n        delBatch() {   // 批量删除\r\n            if (!this.ids.length) {\r\n                this.$message.warning('请选择数据')  // 提示没有选择数据\r\n                return\r\n            }\r\n            this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n                this.$request.delete('/complaint/delete/batch', {data: this.ids}).then(res => {\r\n                    if (res.code === '200') {   // 批量删除成功\r\n                        this.$message.success('操作成功')\r\n                        this.load(1)  // 重新加载数据\r\n                    } else {\r\n                        this.$message.error(res.msg)  // 弹出错误信息\r\n                    }\r\n                })\r\n            }).catch(() => {})\r\n        },\r\n        load(pageNum) {  // 分页加载数据\r\n            if (pageNum) this.pageNum = pageNum  // 如果传入了页码，更新当前页码\r\n            this.$request.get('/complaint/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n                    title: this.title,  // 查询关键字（由 name 改为 title）\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list  // 更新表格数据\r\n                    this.total = res.data?.total    // 更新数据总数\r\n                } else {\r\n                    this.$message.error(res.msg)  // 弹出错误信息\r\n                }\r\n            })\r\n        },\r\n        reset() {  // 重置查询条件\r\n            this.title = null  // 将 name 改为 title\r\n            this.load(1)  // 重新加载第一页数据\r\n        },\r\n        handleCurrentChange(pageNum) {  // 处理分页器页码变化\r\n            this.load(pageNum)\r\n        },\r\n        handleAvatarSuccess(response, file, fileList) {  // 上传图片成功后的回调函数\r\n            this.form.sfImage = response.data  // 将图片 URL 存入表单数据\r\n            this.load(1)  // 图片上传成功后自动刷新数据\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n</style>\r\n"], "mappings": ";AA0IA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAAA;MACAC,KAAA;MAAA;MACAC,WAAA;MAAA;MACAC,IAAA;MAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MAAA;MACAC,KAAA;MAAA;MACAC,GAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAC,UAAA;MAAA;MACA,KAAAX,IAAA;MACA,KAAAD,WAAA;IACA;IACAa,WAAAC,GAAA;MAAA;MACA,KAAAb,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAY,SAAA,CAAAD,GAAA;MACA,KAAAd,WAAA;IACA;IACAgB,KAAA;MAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,QAAA;YACAC,GAAA,OAAArB,IAAA,CAAAsB,EAAA;YAAA;YACAC,MAAA,OAAAvB,IAAA,CAAAsB,EAAA;YACA7B,IAAA,OAAAO;UACA,GAAAwB,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cAAA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA,KAAAnB,IAAA;cACA,KAAAV,WAAA;YACA;cACA,KAAA4B,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;YACA;UACA;QACA;MACA;IACA;IACAC,IAAAT,EAAA;MAAA;MACA,KAAAU,QAAA;QAAAC,IAAA;MAAA,GAAAT,IAAA,CAAAU,QAAA;QACA,KAAAd,QAAA,CAAAe,MAAA,wBAAAb,EAAA,EAAAE,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAnB,IAAA;UACA;YACA,KAAAkB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAM,KAAA;IACA;IACAC,sBAAAC,IAAA;MAAA;MACA,KAAA/B,GAAA,GAAA+B,IAAA,CAAAC,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAlB,EAAA;IACA;IACAmB,SAAA;MAAA;MACA,UAAAlC,GAAA,CAAAmC,MAAA;QACA,KAAAf,QAAA,CAAAgB,OAAA;QACA;MACA;MACA,KAAAX,QAAA;QAAAC,IAAA;MAAA,GAAAT,IAAA,CAAAU,QAAA;QACA,KAAAd,QAAA,CAAAe,MAAA;UAAA1C,IAAA,OAAAc;QAAA,GAAAiB,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAnB,IAAA;UACA;YACA,KAAAkB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAM,KAAA;IACA;IACA3B,KAAAd,OAAA;MAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAyB,QAAA,CAAAwB,GAAA;QACAC,MAAA;UACAlD,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAE,KAAA,OAAAA,KAAA;QACA;MACA,GAAA0B,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAhC,SAAA,GAAA+B,GAAA,CAAAhC,IAAA,EAAAqD,IAAA;UACA,KAAAjD,KAAA,GAAA4B,GAAA,CAAAhC,IAAA,EAAAI,KAAA;QACA;UACA,KAAA8B,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IACAiB,MAAA;MAAA;MACA,KAAAjD,KAAA;MACA,KAAAW,IAAA;IACA;IACAuC,oBAAArD,OAAA;MAAA;MACA,KAAAc,IAAA,CAAAd,OAAA;IACA;IACAsD,oBAAAf,QAAA,EAAAgB,IAAA,EAAAC,QAAA;MAAA;MACA,KAAAnD,IAAA,CAAAoD,OAAA,GAAAlB,QAAA,CAAAzC,IAAA;MACA,KAAAgB,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}