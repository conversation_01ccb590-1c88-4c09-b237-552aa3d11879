{"ast": null, "code": "import \"core-js/modules/esnext.iterator.map.js\";\nexport default {\n  name: \"Admin\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前的页码\n      pageSize: 10,\n      // 每页显示的个数\n      total: 0,\n      username: null,\n      fromVisible: false,\n      form: {},\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      rules: {\n        username: [{\n          required: true,\n          message: '请输入账号',\n          trigger: 'blur'\n        }]\n      },\n      ids: []\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    handleAdd() {\n      // 新增数据\n      this.form = {}; // 新增数据的时候清空数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    handleEdit(row) {\n      // 编辑数据\n      this.form = JSON.parse(JSON.stringify(row)); // 给form对象赋值  注意要深拷贝数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    save() {\n      // 保存按钮触发的逻辑  它会触发新增或者更新\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          this.$request({\n            url: this.form.id ? '/user/update' : '/user/add',\n            method: this.form.id ? 'PUT' : 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              // 表示成功保存\n              this.$message.success('保存成功');\n              this.load(1);\n              this.fromVisible = false;\n            } else {\n              this.$message.error(res.msg); // 弹出错误的信息\n            }\n          });\n        }\n      });\n    },\n    del(id) {\n      // 单个删除\n      this.$confirm('您确定删除吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/user/delete/' + id).then(res => {\n          if (res.code === '200') {\n            // 表示操作成功\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg); // 弹出错误的信息\n          }\n        });\n      }).catch(() => {});\n    },\n    handleSelectionChange(rows) {\n      // 当前选中的所有的行数据\n      this.ids = rows.map(v => v.id);\n    },\n    delBatch() {\n      // 批量删除\n      if (!this.ids.length) {\n        this.$message.warning('请选择数据');\n        return;\n      }\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/user/delete/batch', {\n          data: this.ids\n        }).then(res => {\n          if (res.code === '200') {\n            // 表示操作成功\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg); // 弹出错误的信息\n          }\n        });\n      }).catch(() => {});\n    },\n    load(pageNum) {\n      // 分页查询\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/user/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          username: this.username\n        }\n      }).then(res => {\n        this.tableData = res.data?.list;\n        this.total = res.data?.total;\n      });\n    },\n    reset() {\n      this.username = null;\n      this.load(1);\n    },\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    },\n    handleAvatarSuccess(response, file, fileList) {\n      // 把头像属性换成上传的图片的链接\n      this.form.avatar = response.data;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "username", "fromVisible", "form", "user", "JSON", "parse", "localStorage", "getItem", "rules", "required", "message", "trigger", "ids", "created", "load", "methods", "handleAdd", "handleEdit", "row", "stringify", "save", "$refs", "formRef", "validate", "valid", "$request", "url", "id", "method", "then", "res", "code", "$message", "success", "error", "msg", "del", "$confirm", "type", "response", "delete", "catch", "handleSelectionChange", "rows", "map", "v", "delBatch", "length", "warning", "get", "params", "list", "reset", "handleCurrentChange", "handleAvatarSuccess", "file", "fileList", "avatar"], "sources": ["src/views/manager/User.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"search\">\r\n      <el-input placeholder=\"请输入账号查询\" style=\"width: 200px\" v-model=\"username\"></el-input>\r\n      <el-button type=\"info\" plain style=\"margin-left: 10px\" @click=\"load(1)\">查询</el-button>\r\n      <el-button type=\"warning\" plain style=\"margin-left: 10px\" @click=\"reset\">重置</el-button>\r\n    </div>\r\n\r\n    <div class=\"operation\">\r\n      <el-button type=\"primary\" plain @click=\"handleAdd\">新增</el-button>\r\n      <el-button type=\"danger\" plain @click=\"delBatch\">批量删除</el-button>\r\n    </div>\r\n\r\n    <div class=\"table\">\r\n      <el-table :data=\"tableData\" strip @selection-change=\"handleSelectionChange\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"id\" label=\"序号\" width=\"70\" align=\"center\" sortable></el-table-column>\r\n        <el-table-column prop=\"username\" label=\"账号\"></el-table-column>\r\n        <el-table-column prop=\"name\" label=\"姓名\"></el-table-column>\r\n        <el-table-column prop=\"phone\" label=\"电话\"></el-table-column>\r\n        <el-table-column prop=\"email\" label=\"邮箱\"></el-table-column>\r\n        <el-table-column label=\"头像\">\r\n          <template v-slot=\"scope\">\r\n            <div style=\"display: flex; align-items: center\">\r\n              <el-image style=\"width: 40px; height: 40px; border-radius: 50%\" v-if=\"scope.row.avatar\"\r\n                        :src=\"scope.row.avatar\" :preview-src-list=\"[scope.row.avatar]\"></el-image>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"role\" label=\"角色\"></el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"180\">\r\n          <template v-slot=\"scope\">\r\n            <el-button size=\"mini\" type=\"primary\" plain @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button size=\"mini\" type=\"danger\" plain @click=\"del(scope.row.id)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"pagination\">\r\n        <el-pagination\r\n            background\r\n            @current-change=\"handleCurrentChange\"\r\n            :current-page=\"pageNum\"\r\n            :page-sizes=\"[5, 10, 20]\"\r\n            :page-size=\"pageSize\"\r\n            layout=\"total, prev, pager, next\"\r\n            :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n\r\n\r\n    <el-dialog title=\"管理员\" :visible.sync=\"fromVisible\" width=\"40%\" :close-on-click-modal=\"false\" destroy-on-close>\r\n      <el-form :model=\"form\" label-width=\"100px\" style=\"padding-right: 50px\" :rules=\"rules\" ref=\"formRef\">\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"form.username\" placeholder=\"用户名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"昵称\" prop=\"name\">\r\n          <el-input v-model=\"form.name\" placeholder=\"昵称\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"电话\" prop=\"phone\">\r\n          <el-input v-model=\"form.phone\" placeholder=\"电话\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"birth\">\r\n          <el-input v-model=\"form.email\" placeholder=\"邮箱\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"头像\">\r\n          <el-upload\r\n              class=\"avatar-uploader\"\r\n              :action=\"$baseUrl + '/files/upload'\"\r\n              :headers=\"{ token: user.token }\"\r\n              list-type=\"picture\"\r\n              :on-success=\"handleAvatarSuccess\"\r\n          >\r\n            <el-button type=\"primary\">上传头像</el-button>\r\n          </el-upload>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"fromVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Admin\",\r\n  data() {\r\n    return {\r\n      tableData: [],  // 所有的数据\r\n      pageNum: 1,   // 当前的页码\r\n      pageSize: 10,  // 每页显示的个数\r\n      total: 0,\r\n      username: null,\r\n      fromVisible: false,\r\n      form: {},\r\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n      rules: {\r\n        username: [\r\n          {required: true, message: '请输入账号', trigger: 'blur'},\r\n        ]\r\n      },\r\n      ids: []\r\n    }\r\n  },\r\n  created() {\r\n    this.load(1)\r\n  },\r\n  methods: {\r\n    handleAdd() {   // 新增数据\r\n      this.form = {}  // 新增数据的时候清空数据\r\n      this.fromVisible = true   // 打开弹窗\r\n    },\r\n    handleEdit(row) {   // 编辑数据\r\n      this.form = JSON.parse(JSON.stringify(row))  // 给form对象赋值  注意要深拷贝数据\r\n      this.fromVisible = true   // 打开弹窗\r\n    },\r\n    save() {   // 保存按钮触发的逻辑  它会触发新增或者更新\r\n      this.$refs.formRef.validate((valid) => {\r\n        if (valid) {\r\n          this.$request({\r\n            url: this.form.id ? '/user/update' : '/user/add',\r\n            method: this.form.id ? 'PUT' : 'POST',\r\n            data: this.form\r\n          }).then(res => {\r\n            if (res.code === '200') {  // 表示成功保存\r\n              this.$message.success('保存成功')\r\n              this.load(1)\r\n              this.fromVisible = false\r\n            } else {\r\n              this.$message.error(res.msg)  // 弹出错误的信息\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    del(id) {   // 单个删除\r\n      this.$confirm('您确定删除吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n        this.$request.delete('/user/delete/' + id).then(res => {\r\n          if (res.code === '200') {   // 表示操作成功\r\n            this.$message.success('操作成功')\r\n            this.load(1)\r\n          } else {\r\n            this.$message.error(res.msg)  // 弹出错误的信息\r\n          }\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleSelectionChange(rows) {   // 当前选中的所有的行数据\r\n      this.ids = rows.map(v => v.id)\r\n    },\r\n    delBatch() {   // 批量删除\r\n      if (!this.ids.length) {\r\n        this.$message.warning('请选择数据')\r\n        return\r\n      }\r\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n        this.$request.delete('/user/delete/batch', {data: this.ids}).then(res => {\r\n          if (res.code === '200') {   // 表示操作成功\r\n            this.$message.success('操作成功')\r\n            this.load(1)\r\n          } else {\r\n            this.$message.error(res.msg)  // 弹出错误的信息\r\n          }\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    load(pageNum) {  // 分页查询\r\n      if (pageNum) this.pageNum = pageNum\r\n      this.$request.get('/user/selectPage', {\r\n        params: {\r\n          pageNum: this.pageNum,\r\n          pageSize: this.pageSize,\r\n          username: this.username,\r\n        }\r\n      }).then(res => {\r\n        this.tableData = res.data?.list\r\n        this.total = res.data?.total\r\n      })\r\n    },\r\n    reset() {\r\n      this.username = null\r\n      this.load(1)\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.load(pageNum)\r\n    },\r\n    handleAvatarSuccess(response, file, fileList) {\r\n      // 把头像属性换成上传的图片的链接\r\n      this.form.avatar = response.data\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>"], "mappings": ";AA0FA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MACAC,QAAA;MACAC,WAAA;MACAC,IAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,KAAA;QACAR,QAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,GAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAC,UAAA;MAAA;MACA,KAAAd,IAAA;MACA,KAAAD,WAAA;IACA;IACAgB,WAAAC,GAAA;MAAA;MACA,KAAAhB,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAe,SAAA,CAAAD,GAAA;MACA,KAAAjB,WAAA;IACA;IACAmB,KAAA;MAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,QAAA;YACAC,GAAA,OAAAxB,IAAA,CAAAyB,EAAA;YACAC,MAAA,OAAA1B,IAAA,CAAAyB,EAAA;YACAhC,IAAA,OAAAO;UACA,GAAA2B,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cAAA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA,KAAAnB,IAAA;cACA,KAAAb,WAAA;YACA;cACA,KAAA+B,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;YACA;UACA;QACA;MACA;IACA;IACAC,IAAAT,EAAA;MAAA;MACA,KAAAU,QAAA;QAAAC,IAAA;MAAA,GAAAT,IAAA,CAAAU,QAAA;QACA,KAAAd,QAAA,CAAAe,MAAA,mBAAAb,EAAA,EAAAE,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAnB,IAAA;UACA;YACA,KAAAkB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAM,KAAA,QACA;IACA;IACAC,sBAAAC,IAAA;MAAA;MACA,KAAA/B,GAAA,GAAA+B,IAAA,CAAAC,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAlB,EAAA;IACA;IACAmB,SAAA;MAAA;MACA,UAAAlC,GAAA,CAAAmC,MAAA;QACA,KAAAf,QAAA,CAAAgB,OAAA;QACA;MACA;MACA,KAAAX,QAAA;QAAAC,IAAA;MAAA,GAAAT,IAAA,CAAAU,QAAA;QACA,KAAAd,QAAA,CAAAe,MAAA;UAAA7C,IAAA,OAAAiB;QAAA,GAAAiB,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAnB,IAAA;UACA;YACA,KAAAkB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAM,KAAA,QACA;IACA;IACA3B,KAAAjB,OAAA;MAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAA4B,QAAA,CAAAwB,GAAA;QACAC,MAAA;UACArD,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAE,QAAA,OAAAA;QACA;MACA,GAAA6B,IAAA,CAAAC,GAAA;QACA,KAAAlC,SAAA,GAAAkC,GAAA,CAAAnC,IAAA,EAAAwD,IAAA;QACA,KAAApD,KAAA,GAAA+B,GAAA,CAAAnC,IAAA,EAAAI,KAAA;MACA;IACA;IACAqD,MAAA;MACA,KAAApD,QAAA;MACA,KAAAc,IAAA;IACA;IACAuC,oBAAAxD,OAAA;MACA,KAAAiB,IAAA,CAAAjB,OAAA;IACA;IACAyD,oBAAAf,QAAA,EAAAgB,IAAA,EAAAC,QAAA;MACA;MACA,KAAAtD,IAAA,CAAAuD,MAAA,GAAAlB,QAAA,CAAA5C,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}