{"ast": null, "code": "export default {\n  name: \"Notice\",\n  data() {\n    return {\n      noticeList: [],\n      pageNum: 1,\n      pageSize: 10,\n      total: 0\n    };\n  },\n  mounted() {\n    this.load();\n  },\n  methods: {\n    load(pageNum) {\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/notice/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          status: 1 // 已发布的公告\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.noticeList = res.data?.list;\n          this.total = res.data?.total;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "noticeList", "pageNum", "pageSize", "total", "mounted", "load", "methods", "$request", "get", "params", "status", "then", "res", "code", "list", "$message", "error", "msg", "handleCurrentChange"], "sources": ["src/views/front/Notice.vue"], "sourcesContent": ["<template>\r\n    <div class=\"main-content\">\r\n        <div class=\"notice-container\">\r\n            <!-- 公告列表卡片 -->\r\n            <div class=\"notice-card\">\r\n                <div class=\"section-title\">\r\n                    公告信息\r\n                </div>\r\n                \r\n                <div class=\"notice-list\">\r\n                    <div v-for=\"item in noticeList\" \r\n                         :key=\"item.id\" \r\n                         class=\"notice-item\">\r\n                        <div class=\"notice-header\">\r\n                            <span class=\"notice-title\">{{item.title}}</span>\r\n                        </div>\r\n                        <div class=\"notice-content\">{{item.content}}</div>\r\n                        <div class=\"notice-footer\">\r\n                            <div class=\"notice-info\">\r\n                                <span class=\"publisher\">发布人：{{item.user}}</span>\r\n                                <span class=\"publish-time\">发布时间：{{item.time}}</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div v-if=\"!noticeList.length\" class=\"empty-notice\">\r\n                        暂无公告\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 分页 -->\r\n                <div class=\"pagination-wrap\">\r\n                    <el-pagination\r\n                        background\r\n                        @current-change=\"handleCurrentChange\"\r\n                        :current-page=\"pageNum\"\r\n                        :page-size=\"pageSize\"\r\n                        layout=\"total, prev, pager, next\"\r\n                        :total=\"total\">\r\n                    </el-pagination>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Notice\",\r\n    data() {\r\n        return {\r\n            noticeList: [],\r\n            pageNum: 1,\r\n            pageSize: 10,\r\n            total: 0\r\n        }\r\n    },\r\n    mounted() {\r\n        this.load()\r\n    },\r\n    methods: {\r\n        load(pageNum) {\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/notice/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n                    status: 1  // 已发布的公告\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.noticeList = res.data?.list\r\n                    this.total = res.data?.total\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            })\r\n        },\r\n        handleCurrentChange(pageNum) {\r\n            this.load(pageNum)\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.main-content {\r\n    min-height: 100vh;\r\n    background-color: #fff;\r\n\r\n    padding: 20px;\r\n}\r\n\r\n.notice-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n}\r\n\r\n.notice-card {\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);\r\n}\r\n\r\n.section-title {\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n    color: #303133;\r\n    padding-bottom: 15px;\r\n    border-bottom: 1px solid #ebeef5;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.notice-list {\r\n    padding: 10px 0;\r\n}\r\n\r\n.notice-item {\r\n    padding: 20px;\r\n    margin-bottom: 15px;\r\n    border-radius: 8px;\r\n    background: #f8f9fa;\r\n    transition: all 0.3s ease;\r\n    border: 1px solid #ebeef5;\r\n}\r\n\r\n.notice-item:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 2px 12px rgba(0,0,0,0.1);\r\n}\r\n\r\n.notice-header {\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.notice-title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n    color: #303133;\r\n}\r\n\r\n.notice-content {\r\n    color: #606266;\r\n    line-height: 1.8;\r\n    font-size: 14px;\r\n    white-space: pre-wrap;\r\n    padding: 10px 0;\r\n    min-height: 50px;\r\n    border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.notice-footer {\r\n    margin-top: 15px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    color: #909399;\r\n    font-size: 13px;\r\n}\r\n\r\n.notice-info {\r\n    display: flex;\r\n    gap: 20px;\r\n}\r\n\r\n.publisher {\r\n    color: #409EFF;\r\n}\r\n\r\n.publish-time, .notice-time {\r\n    color: #909399;\r\n}\r\n\r\n.empty-notice {\r\n    text-align: center;\r\n    padding: 30px 0;\r\n    color: #909399;\r\n    font-size: 14px;\r\n}\r\n\r\n.pagination-wrap {\r\n    margin-top: 20px;\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n\r\n/* 响应式处理 */\r\n@media screen and (max-width: 768px) {\r\n    .main-content {\r\n        padding: 10px;\r\n    }\r\n    \r\n    .notice-item {\r\n        padding: 15px;\r\n    }\r\n    \r\n    .notice-footer {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 5px;\r\n    }\r\n    \r\n    .notice-info {\r\n        flex-direction: column;\r\n        gap: 5px;\r\n    }\r\n}\r\n</style>\r\n"], "mappings": "AA+CA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,KAAAJ,OAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAM,QAAA,CAAAC,GAAA;QACAC,MAAA;UACAR,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAQ,MAAA;QACA;MACA,GAAAC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAb,UAAA,GAAAY,GAAA,CAAAb,IAAA,EAAAe,IAAA;UACA,KAAAX,KAAA,GAAAS,GAAA,CAAAb,IAAA,EAAAI,KAAA;QACA;UACA,KAAAY,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IACAC,oBAAAjB,OAAA;MACA,KAAAI,IAAA,CAAAJ,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}