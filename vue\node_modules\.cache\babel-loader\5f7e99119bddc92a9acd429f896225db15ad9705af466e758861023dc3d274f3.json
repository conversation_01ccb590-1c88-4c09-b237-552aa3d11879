{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport ElementUI from 'element-ui';\nimport 'element-ui/lib/theme-chalk/index.css';\nimport '@/assets/css/global.css';\nimport '@/assets/css/theme/index.css';\nimport request from \"@/utils/request\";\nVue.config.productionTip = false;\nVue.prototype.$request = request;\nVue.prototype.$baseUrl = process.env.VUE_APP_BASEURL;\nVue.use(ElementUI, {\n  size: \"small\"\n});\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "ElementUI", "request", "config", "productionTip", "prototype", "$request", "$baseUrl", "process", "env", "VUE_APP_BASEURL", "use", "size", "render", "h", "$mount"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/src/main.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport ElementUI from 'element-ui'\r\nimport 'element-ui/lib/theme-chalk/index.css'\r\nimport '@/assets/css/global.css'\r\nimport '@/assets/css/theme/index.css'\r\nimport request from \"@/utils/request\";\r\n\r\nVue.config.productionTip = false\r\n\r\nVue.prototype.$request = request\r\nVue.prototype.$baseUrl = process.env.VUE_APP_BASEURL\r\n\r\nVue.use(ElementUI, {size: \"small\"})\r\n\r\nnew Vue({\r\n    router,\r\n    render: h => h(App)\r\n}).$mount('#app')\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAC7C,OAAO,yBAAyB;AAChC,OAAO,8BAA8B;AACrC,OAAOC,OAAO,MAAM,iBAAiB;AAErCJ,GAAG,CAACK,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCN,GAAG,CAACO,SAAS,CAACC,QAAQ,GAAGJ,OAAO;AAChCJ,GAAG,CAACO,SAAS,CAACE,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe;AAEpDZ,GAAG,CAACa,GAAG,CAACV,SAAS,EAAE;EAACW,IAAI,EAAE;AAAO,CAAC,CAAC;AAEnC,IAAId,GAAG,CAAC;EACJE,MAAM;EACNa,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACf,GAAG;AACtB,CAAC,CAAC,CAACgB,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}