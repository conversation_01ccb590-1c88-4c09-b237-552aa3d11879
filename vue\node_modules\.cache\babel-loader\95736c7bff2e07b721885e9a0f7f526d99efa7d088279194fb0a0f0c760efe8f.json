{"ast": null, "code": "export default {\n  name: \"BlogsDetails\",\n  data() {\n    return {\n      blogData: {},\n      // 存储博客详情数据\n      comments: [],\n      // 存储评论数据\n      newComment: \"\" // 新评论的内容\n    };\n  },\n  created() {\n    const blogId = this.$route.query.id;\n    this.loadBlogDetail(blogId);\n    this.loadComments(blogId);\n  },\n  methods: {\n    // 加载博客详情\n    loadBlogDetail(id) {\n      this.$request.get(`/blogs/selectById/${id}`).then(res => {\n        if (res.code === '200') {\n          this.blogData = res.data;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    // 加载评论列表\n    loadComments(blogId) {\n      this.$request.get(`/pinglun/selectAll?bokeid=${blogId}`).then(res => {\n        if (res.code === '200') {\n          this.comments = res.data;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    // 提交评论\n    submitComment() {\n      if (!this.newComment.trim()) {\n        this.$message.error(\"评论内容不能为空\");\n        return;\n      }\n      const commentData = {\n        massage: this.newComment,\n        bokeid: this.blogData.id\n      };\n      this.$request.post('/pinglun/add', commentData).then(res => {\n        if (res.code === '200') {\n          this.$message.success(\"评论成功\");\n          // 重点：提交成功后重新加载评论\n          this.loadComments(this.blogData.id);\n          // 清空输入框\n          this.newComment = \"\";\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "blogData", "comments", "newComment", "created", "blogId", "$route", "query", "id", "loadBlogDetail", "loadComments", "methods", "$request", "get", "then", "res", "code", "$message", "error", "msg", "submitComment", "trim", "commentData", "massage", "boke<PERSON>", "post", "success"], "sources": ["src/views/front/BlogsDetails.vue"], "sourcesContent": ["<template>\r\n    <div class=\"blog-detail\">\r\n        <div class=\"blog-header\">\r\n            <h2>{{ blogData.title }}</h2>\r\n            <div class=\"blog-meta\">\r\n                <span>类别: {{ blogData.categoryname }}</span>\r\n                <span>标签: {{ blogData.tags }}</span>\r\n                <span>创建时间: {{ blogData.createdat }}</span>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"blog-content\">\r\n            <img :src=\"blogData.blogimg\" alt=\"博客图片\" class=\"blog-image\" />\r\n            <p>{{ blogData.content }}</p>\r\n        </div>\r\n\r\n        <div class=\"blog-footer\">\r\n            <span>浏览次数: {{ blogData.views }}</span>\r\n        </div>\r\n\r\n        <div class=\"comments-section\">\r\n            <h3>评论区</h3>\r\n            <div v-for=\"comment in comments\" :key=\"comment.id\" class=\"comment\">\r\n                <div class=\"comment-header\">\r\n                    <span class=\"comment-username\">{{ comment.yonghuname }}</span>\r\n                    <span class=\"comment-time\">{{ comment.crearatime }}</span>\r\n                </div>\r\n                <div class=\"comment-content\">\r\n                    <p>{{ comment.massage }}</p>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"comment-form\">\r\n                <textarea v-model=\"newComment\" placeholder=\"添加评论...\" rows=\"4\"></textarea>\r\n                <button @click=\"submitComment\">提交评论</button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"BlogsDetails\",\r\n    data() {\r\n        return {\r\n            blogData: {},   // 存储博客详情数据\r\n            comments: [],   // 存储评论数据\r\n            newComment: \"\", // 新评论的内容\r\n        };\r\n    },\r\n    created() {\r\n        const blogId = this.$route.query.id;\r\n        this.loadBlogDetail(blogId);\r\n        this.loadComments(blogId);\r\n    },\r\n    methods: {\r\n        // 加载博客详情\r\n        loadBlogDetail(id) {\r\n            this.$request.get(`/blogs/selectById/${id}`).then(res => {\r\n                if (res.code === '200') {\r\n                    this.blogData = res.data;\r\n                } else {\r\n                    this.$message.error(res.msg);\r\n                }\r\n            });\r\n        },\r\n        // 加载评论列表\r\n        loadComments(blogId) {\r\n            this.$request.get(`/pinglun/selectAll?bokeid=${blogId}`).then(res => {\r\n                if (res.code === '200') {\r\n                    this.comments = res.data;\r\n                } else {\r\n                    this.$message.error(res.msg);\r\n                }\r\n            });\r\n        },\r\n        // 提交评论\r\n        submitComment() {\r\n            if (!this.newComment.trim()) {\r\n                this.$message.error(\"评论内容不能为空\");\r\n                return;\r\n            }\r\n            const commentData = {\r\n                massage: this.newComment,\r\n                bokeid: this.blogData.id,\r\n            };\r\n            this.$request.post('/pinglun/add', commentData).then(res => {\r\n                if (res.code === '200') {\r\n                    this.$message.success(\"评论成功\");\r\n                    // 重点：提交成功后重新加载评论\r\n                    this.loadComments(this.blogData.id);\r\n                    // 清空输入框\r\n                    this.newComment = \"\";\r\n                } else {\r\n                    this.$message.error(res.msg);\r\n                }\r\n            });\r\n        },\r\n    },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.blog-detail {\r\n    width: 70%;\r\n    margin: 30px auto;\r\n    padding: 20px;\r\n    background-color: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.blog-header h2 {\r\n    font-size: 24px;\r\n    color: #333;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.blog-meta span {\r\n    margin-right: 20px;\r\n    font-size: 14px;\r\n    color: #999;\r\n}\r\n\r\n.blog-content {\r\n    margin-top: 20px;\r\n}\r\n\r\n.blog-image {\r\n    width: 100%;\r\n    height: auto;\r\n    border-radius: 8px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.blog-footer {\r\n    margin-top: 20px;\r\n    font-size: 14px;\r\n    color: #999;\r\n}\r\n\r\n.comments-section {\r\n    margin-top: 30px;\r\n}\r\n\r\n.comment {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.comment-header {\r\n    font-weight: bold;\r\n    font-size: 14px;\r\n    color: #555;\r\n}\r\n\r\n.comment-time {\r\n    margin-left: 10px;\r\n    font-size: 12px;\r\n    color: #999;\r\n}\r\n\r\n.comment-content {\r\n    margin-top: 10px;\r\n    font-size: 14px;\r\n    color: #333;\r\n}\r\n\r\n.comment-form {\r\n    margin-top: 20px;\r\n}\r\n\r\n.comment-form textarea {\r\n    width: 100%;\r\n    padding: 10px;\r\n    margin-bottom: 10px;\r\n    border-radius: 5px;\r\n    border: 1px solid #ddd;\r\n}\r\n\r\n.comment-form button {\r\n    background-color: #4CAF50;\r\n    color: white;\r\n    padding: 10px 20px;\r\n    border: none;\r\n    border-radius: 5px;\r\n    cursor: pointer;\r\n}\r\n</style>\r\n"], "mappings": "AAyCA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,QAAA;MAAA;MACAC,QAAA;MAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACA,MAAAC,MAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,EAAA;IACA,KAAAC,cAAA,CAAAJ,MAAA;IACA,KAAAK,YAAA,CAAAL,MAAA;EACA;EACAM,OAAA;IACA;IACAF,eAAAD,EAAA;MACA,KAAAI,QAAA,CAAAC,GAAA,sBAAAL,EAAA,IAAAM,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAf,QAAA,GAAAc,GAAA,CAAAf,IAAA;QACA;UACA,KAAAiB,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAI,GAAA;QACA;MACA;IACA;IACA;IACAT,aAAAL,MAAA;MACA,KAAAO,QAAA,CAAAC,GAAA,8BAAAR,MAAA,IAAAS,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAd,QAAA,GAAAa,GAAA,CAAAf,IAAA;QACA;UACA,KAAAiB,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAI,GAAA;QACA;MACA;IACA;IACA;IACAC,cAAA;MACA,UAAAjB,UAAA,CAAAkB,IAAA;QACA,KAAAJ,QAAA,CAAAC,KAAA;QACA;MACA;MACA,MAAAI,WAAA;QACAC,OAAA,OAAApB,UAAA;QACAqB,MAAA,OAAAvB,QAAA,CAAAO;MACA;MACA,KAAAI,QAAA,CAAAa,IAAA,iBAAAH,WAAA,EAAAR,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAC,QAAA,CAAAS,OAAA;UACA;UACA,KAAAhB,YAAA,MAAAT,QAAA,CAAAO,EAAA;UACA;UACA,KAAAL,UAAA;QACA;UACA,KAAAc,QAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAI,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}