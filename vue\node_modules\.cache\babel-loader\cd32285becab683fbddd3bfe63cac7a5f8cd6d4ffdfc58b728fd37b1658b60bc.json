{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: \"Register\",\n  data() {\n    // 验证码校验\n    const validatePassword = (rule, confirmPass, callback) => {\n      if (confirmPass === '') {\n        callback(new Error('请确认密码'));\n      } else if (confirmPass !== this.form.password) {\n        callback(new Error('两次输入的密码不一致'));\n      } else {\n        callback();\n      }\n    };\n    return {\n      form: {},\n      rules: {\n        username: [{\n          required: true,\n          message: '请输入账号',\n          trigger: 'blur'\n        }],\n        password: [{\n          required: true,\n          message: '请输入密码',\n          trigger: 'blur'\n        }],\n        confirmPass: [{\n          validator: validatePassword,\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created() {},\n  methods: {\n    register() {\n      this.$refs['formRef'].validate(valid => {\n        if (valid) {\n          // 验证通过\n          this.$request.post('/register', this.form).then(res => {\n            if (res.code === '200') {\n              this.$router.push('/'); // 跳转登录页面\n              this.$message.success('注册成功');\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "validatePassword", "rule", "confirmPass", "callback", "Error", "form", "password", "rules", "username", "required", "message", "trigger", "validator", "created", "methods", "register", "$refs", "validate", "valid", "$request", "post", "then", "res", "code", "$router", "push", "$message", "success", "error", "msg"], "sources": ["src/views/Register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"container\">\r\n    <div style=\"width: 400px; padding: 30px; background-color: white; border-radius: 5px;\">\r\n      <div style=\"text-align: center; font-size: 20px; margin-bottom: 20px; color: #333\">欢迎注册</div>\r\n      <el-form :model=\"form\" :rules=\"rules\" ref=\"formRef\">\r\n        <el-form-item prop=\"username\">\r\n          <el-input prefix-icon=\"el-icon-user\" placeholder=\"请输入账号\" v-model=\"form.username\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item prop=\"password\">\r\n          <el-input prefix-icon=\"el-icon-lock\" placeholder=\"请输入密码\" show-password  v-model=\"form.password\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item prop=\"confirmPass\">\r\n          <el-input prefix-icon=\"el-icon-lock\" placeholder=\"请确认密码\" show-password  v-model=\"form.confirmPass\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"form.role\" placeholder=\"请选择角色\" style=\"width: 100%\">\r\n            <el-option label=\"商家\" value=\"BUSINESS\"></el-option>\r\n            <el-option label=\"用户\" value=\"USER\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button style=\"width: 100%; background-color: #333; border-color: #333; color: white\" @click=\"register\">注 册</el-button>\r\n        </el-form-item>\r\n        <div style=\"display: flex; align-items: center\">\r\n          <div style=\"flex: 1\"></div>\r\n          <div style=\"flex: 1; text-align: right\">\r\n            已有账号？请 <a href=\"/login\">登录</a>\r\n          </div>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Register\",\r\n  data() {\r\n    // 验证码校验\r\n    const validatePassword = (rule, confirmPass, callback) => {\r\n      if (confirmPass === '') {\r\n        callback(new Error('请确认密码'))\r\n      } else if (confirmPass !== this.form.password) {\r\n        callback(new Error('两次输入的密码不一致'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n    return {\r\n      form: {},\r\n      rules: {\r\n        username: [\r\n          { required: true, message: '请输入账号', trigger: 'blur' },\r\n        ],\r\n        password: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' },\r\n        ],\r\n        confirmPass: [\r\n          { validator: validatePassword, trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n\r\n  },\r\n  methods: {\r\n    register() {\r\n      this.$refs['formRef'].validate((valid) => {\r\n        if (valid) {\r\n          // 验证通过\r\n          this.$request.post('/register', this.form).then(res => {\r\n            if (res.code === '200') {\r\n              this.$router.push('/')  // 跳转登录页面\r\n              this.$message.success('注册成功')\r\n            } else {\r\n              this.$message.error(res.msg)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-image: url(\"@/assets/imgs/bg1.jpg\");\r\n  background-size: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #666;\r\n}\r\na {\r\n  color: #2a60c9;\r\n}\r\n</style>"], "mappings": ";AAmCA;EACAA,IAAA;EACAC,KAAA;IACA;IACA,MAAAC,gBAAA,GAAAA,CAAAC,IAAA,EAAAC,WAAA,EAAAC,QAAA;MACA,IAAAD,WAAA;QACAC,QAAA,KAAAC,KAAA;MACA,WAAAF,WAAA,UAAAG,IAAA,CAAAC,QAAA;QACAH,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IACA;MACAE,IAAA;MACAE,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,QAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,WAAA,GACA;UAAAU,SAAA,EAAAZ,gBAAA;UAAAW,OAAA;QAAA;MAEA;IACA;EACA;EACAE,QAAA,GAEA;EACAC,OAAA;IACAC,SAAA;MACA,KAAAC,KAAA,YAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAC,QAAA,CAAAC,IAAA,mBAAAf,IAAA,EAAAgB,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA,KAAAC,OAAA,CAAAC,IAAA;cACA,KAAAC,QAAA,CAAAC,OAAA;YACA;cACA,KAAAD,QAAA,CAAAE,KAAA,CAAAN,GAAA,CAAAO,GAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}