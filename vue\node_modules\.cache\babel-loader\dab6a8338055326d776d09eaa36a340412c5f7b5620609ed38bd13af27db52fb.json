{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport prepareBoxplotData from './prepareBoxplotData.js';\nimport { throwError, makePrintable } from '../../util/log.js';\nimport { SOURCE_FORMAT_ARRAY_ROWS } from '../../util/types.js';\nexport var boxplotTransform = {\n  type: 'echarts:boxplot',\n  transform: function transform(params) {\n    var upstream = params.upstream;\n    if (upstream.sourceFormat !== SOURCE_FORMAT_ARRAY_ROWS) {\n      var errMsg = '';\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = makePrintable('source data is not applicable for this boxplot transform. Expect number[][].');\n      }\n      throwError(errMsg);\n    }\n    var result = prepareBoxplotData(upstream.getRawData(), params.config);\n    return [{\n      dimensions: ['ItemName', 'Low', 'Q1', 'Q2', 'Q3', 'High'],\n      data: result.boxData\n    }, {\n      data: result.outliers\n    }];\n  }\n};", "map": {"version": 3, "names": ["prepareBoxplotData", "throwError", "makePrintable", "SOURCE_FORMAT_ARRAY_ROWS", "boxplotTransform", "type", "transform", "params", "upstream", "sourceFormat", "errMsg", "process", "env", "NODE_ENV", "result", "getRawData", "config", "dimensions", "data", "boxData", "outliers"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/chart/boxplot/boxplotTransform.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport prepareBoxplotData from './prepareBoxplotData.js';\nimport { throwError, makePrintable } from '../../util/log.js';\nimport { SOURCE_FORMAT_ARRAY_ROWS } from '../../util/types.js';\nexport var boxplotTransform = {\n  type: 'echarts:boxplot',\n  transform: function transform(params) {\n    var upstream = params.upstream;\n    if (upstream.sourceFormat !== SOURCE_FORMAT_ARRAY_ROWS) {\n      var errMsg = '';\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = makePrintable('source data is not applicable for this boxplot transform. Expect number[][].');\n      }\n      throwError(errMsg);\n    }\n    var result = prepareBoxplotData(upstream.getRawData(), params.config);\n    return [{\n      dimensions: ['ItemName', 'Low', 'Q1', 'Q2', 'Q3', 'High'],\n      data: result.boxData\n    }, {\n      data: result.outliers\n    }];\n  }\n};"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,kBAAkB,MAAM,yBAAyB;AACxD,SAASC,UAAU,EAAEC,aAAa,QAAQ,mBAAmB;AAC7D,SAASC,wBAAwB,QAAQ,qBAAqB;AAC9D,OAAO,IAAIC,gBAAgB,GAAG;EAC5BC,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAE,SAASA,SAASA,CAACC,MAAM,EAAE;IACpC,IAAIC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;IAC9B,IAAIA,QAAQ,CAACC,YAAY,KAAKN,wBAAwB,EAAE;MACtD,IAAIO,MAAM,GAAG,EAAE;MACf,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCH,MAAM,GAAGR,aAAa,CAAC,8EAA8E,CAAC;MACxG;MACAD,UAAU,CAACS,MAAM,CAAC;IACpB;IACA,IAAII,MAAM,GAAGd,kBAAkB,CAACQ,QAAQ,CAACO,UAAU,CAAC,CAAC,EAAER,MAAM,CAACS,MAAM,CAAC;IACrE,OAAO,CAAC;MACNC,UAAU,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;MACzDC,IAAI,EAAEJ,MAAM,CAACK;IACf,CAAC,EAAE;MACDD,IAAI,EAAEJ,MAAM,CAACM;IACf,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}