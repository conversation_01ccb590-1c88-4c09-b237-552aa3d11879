{"ast": null, "code": "export default {\n  name: \"<PERSON><PERSON><PERSON><PERSON>\",\n  data() {\n    return {\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}')\n    };\n  },\n  created() {},\n  methods: {\n    update() {\n      // 保存当前的用户信息到数据库\n      this.$request.put('/admin/update', this.user).then(res => {\n        if (res.code === '200') {\n          // 成功更新\n          this.$message.success('保存成功');\n\n          // 更新浏览器缓存里的用户信息\n          localStorage.setItem('xm-user', JSON.stringify(this.user));\n\n          // 触发父级的数据更新\n          this.$emit('update:user');\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    handleAvatarSuccess(response, file, fileList) {\n      // 把user的头像属性换成上传的图片的链接\n      this.$set(this.user, 'avatar', response.data);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "user", "JSON", "parse", "localStorage", "getItem", "created", "methods", "update", "$request", "put", "then", "res", "code", "$message", "success", "setItem", "stringify", "$emit", "error", "msg", "handleAvatarSuccess", "response", "file", "fileList", "$set"], "sources": ["src/views/manager/AdminPerson.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-card style=\"width: 50%\">\r\n      <el-form :model=\"user\" label-width=\"100px\" style=\"padding-right: 50px\">\r\n        <div style=\"margin: 15px; text-align: center\">\r\n          <el-upload\r\n              class=\"avatar-uploader\"\r\n              :action=\"$baseUrl + '/files/upload'\"\r\n              :show-file-list=\"false\"\r\n              :on-success=\"handleAvatarSuccess\"\r\n          >\r\n            <img v-if=\"user.avatar\" :src=\"user.avatar\" class=\"avatar\" />\r\n            <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\r\n          </el-upload>\r\n        </div>\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"user.username\" placeholder=\"用户名\" disabled></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input v-model=\"user.name\" placeholder=\"姓名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"电话\" prop=\"phone\">\r\n          <el-input v-model=\"user.phone\" placeholder=\"电话\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <el-input v-model=\"user.email\" placeholder=\"邮箱\"></el-input>\r\n        </el-form-item>\r\n        <div style=\"text-align: center; margin-bottom: 20px\">\r\n          <el-button type=\"primary\" @click=\"update\">保 存</el-button>\r\n        </div>\r\n      </el-form>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"AdminPerson\",\r\n  data() {\r\n    return {\r\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}')\r\n    }\r\n  },\r\n  created() {\r\n\r\n  },\r\n  methods: {\r\n    update() {\r\n      // 保存当前的用户信息到数据库\r\n      this.$request.put('/admin/update', this.user).then(res => {\r\n        if (res.code === '200') {\r\n          // 成功更新\r\n          this.$message.success('保存成功')\r\n\r\n          // 更新浏览器缓存里的用户信息\r\n          localStorage.setItem('xm-user', JSON.stringify(this.user))\r\n\r\n          // 触发父级的数据更新\r\n          this.$emit('update:user')\r\n        } else {\r\n          this.$message.error(res.msg)\r\n        }\r\n      })\r\n    },\r\n    handleAvatarSuccess(response, file, fileList) {\r\n      // 把user的头像属性换成上传的图片的链接\r\n      this.$set(this.user, 'avatar', response.data)\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/deep/.el-form-item__label {\r\n  font-weight: bold;\r\n}\r\n/deep/.el-upload {\r\n  border-radius: 50%;\r\n}\r\n/deep/.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  border-radius: 50%;\r\n}\r\n/deep/.avatar-uploader .el-upload:hover {\r\n  border-color: #409EFF;\r\n}\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 120px;\r\n  height: 120px;\r\n  line-height: 120px;\r\n  text-align: center;\r\n  border-radius: 50%;\r\n}\r\n.avatar {\r\n  width: 120px;\r\n  height: 120px;\r\n  display: block;\r\n  border-radius: 50%;\r\n}\r\n</style>"], "mappings": "AAoCA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA;EACA;EACAC,QAAA,GAEA;EACAC,OAAA;IACAC,OAAA;MACA;MACA,KAAAC,QAAA,CAAAC,GAAA,uBAAAT,IAAA,EAAAU,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA;UACA,KAAAC,QAAA,CAAAC,OAAA;;UAEA;UACAX,YAAA,CAAAY,OAAA,YAAAd,IAAA,CAAAe,SAAA,MAAAhB,IAAA;;UAEA;UACA,KAAAiB,KAAA;QACA;UACA,KAAAJ,QAAA,CAAAK,KAAA,CAAAP,GAAA,CAAAQ,GAAA;QACA;MACA;IACA;IACAC,oBAAAC,QAAA,EAAAC,IAAA,EAAAC,QAAA;MACA;MACA,KAAAC,IAAA,MAAAxB,IAAA,YAAAqB,QAAA,CAAAtB,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}