{"ast": null, "code": "import \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.find.js\";\nexport default {\n  name: \"GoodsList\",\n  data() {\n    return {\n      tableData: [],\n      // 商品数据\n      pageNum: 1,\n      // 当前页码\n      pageSize: 12,\n      // 每页12条\n      total: 0,\n      // 总数\n      name: null,\n      // 搜索关键词\n\n      // 分类相关\n      categories: [],\n      // 分类列表\n      selectedCategoryId: null,\n      // 选中的分类ID\n      categoriesLoading: false,\n      // 分类加载状态\n      goodsLoading: false,\n      // 商品加载状态\n      loadingRequest: null,\n      // 当前加载请求\n      searchTimer: null,\n      // 搜索防抖定时器\n      sidebarCollapsed: false,\n      // 侧边栏折叠状态\n\n      detailVisible: false,\n      // 详情弹窗显示\n      currentGoods: null,\n      // 当前查看的商品\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      // 当前登录用户\n\n      // 订单相关\n      orderDialogVisible: false,\n      dialogTitle: '',\n      dialogButtonText: '',\n      orderForm: {\n        goodsId: null,\n        goodsName: '',\n        goodsPrice: 0,\n        quantity: 1,\n        sfRemark: '',\n        actionType: '' // 'cart' or 'buy'\n      }\n    };\n  },\n  created() {\n    this.load(1);\n    this.loadCategories();\n  },\n  beforeDestroy() {\n    // 清理定时器\n    if (this.searchTimer) {\n      clearTimeout(this.searchTimer);\n    }\n\n    // 取消未完成的请求\n    if (this.loadingRequest) {\n      this.loadingRequest.cancel && this.loadingRequest.cancel();\n    }\n  },\n  methods: {\n    // 格式化时间为年月日时分秒\n    formatDateTime(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      const hours = String(d.getHours()).padStart(2, '0');\n      const minutes = String(d.getMinutes()).padStart(2, '0');\n      const seconds = String(d.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n    load(pageNum) {\n      // 加载商品数据\n      if (pageNum) this.pageNum = pageNum;\n      this.goodsLoading = true;\n      this.$request.get('/foods/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          name: this.name,\n          categoryId: this.selectedCategoryId\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list || [];\n          this.total = res.data?.total || 0;\n        } else {\n          this.$message.error(res.msg || '加载商品失败，请重试');\n        }\n      }).catch(err => {\n        console.error('加载商品失败:', err);\n        this.$message.error('网络异常，请检查网络连接后重试');\n      }).finally(() => {\n        this.goodsLoading = false;\n      });\n    },\n    showDetail(item) {\n      // 显示商品详情\n      this.currentGoods = item;\n      this.detailVisible = true;\n    },\n    // 显示购物车弹窗\n    showCartDialog(goods) {\n      this.orderForm = {\n        goodsId: goods.id,\n        goodsName: goods.name,\n        goodsPrice: goods.sfPrice,\n        sfRemark: '',\n        actionType: 'cart'\n      };\n      this.dialogTitle = '加入购物车';\n      this.dialogButtonText = '确认加入';\n      this.orderDialogVisible = true;\n    },\n    // 显示购买弹窗\n    showBuyDialog(goods) {\n      this.orderForm = {\n        goodsId: goods.id,\n        goodsName: goods.name,\n        goodsPrice: goods.sfPrice,\n        sfRemark: '',\n        actionType: 'buy'\n      };\n      this.dialogTitle = '立即购买';\n      this.dialogButtonText = '确认购买';\n      this.orderDialogVisible = true;\n    },\n    // 确认订单（购物车或购买）\n    confirmOrder() {\n      if (this.orderForm.actionType === 'cart') {\n        this.addToCart();\n      } else {\n        this.handleBuy();\n      }\n    },\n    // 加入购物车\n    addToCart() {\n      this.$request.post('/dingdan/add', {\n        sfUserName: this.user.name || '匿名用户',\n        sfUserId: this.user.id || 0,\n        sfProductIds: this.orderForm.goodsId.toString(),\n        status: '未付款',\n        sfCartStatus: '已加入购物车',\n        sfTotalPrice: 0,\n        // 购物车状态下价格为0\n        sfRemark: this.orderForm.sfRemark,\n        // 添加备注\n        sfCreateTime: this.formatDateTime(new Date()) // 格式化时间\n      }).then(res => {\n        if (res.code === '200') {\n          this.$message.success('商品已加入购物车！');\n          this.orderDialogVisible = false;\n        } else {\n          this.$message.error(res.msg || '操作失败');\n        }\n      }).catch(() => {\n        this.$message.error('操作失败，请重试');\n      });\n    },\n    // 立即购买\n    handleBuy() {\n      this.$request.post('/dingdan/add', {\n        sfUserName: this.user.name || '匿名用户',\n        sfUserId: this.user.id || 0,\n        sfProductIds: this.orderForm.goodsId.toString(),\n        status: '未出餐',\n        // 立即购买直接设为已付款状态\n        sfCartStatus: '',\n        // 购物车状态为空\n        sfTotalPrice: this.orderForm.goodsPrice,\n        // 商品价格放入订单价格字段\n        sfRemark: this.orderForm.sfRemark,\n        // 添加备注\n        sfCreateTime: this.formatDateTime(new Date()) // 格式化时间\n      }).then(res => {\n        if (res.code === '200') {\n          this.$message.success('订单创建成功！');\n          this.detailVisible = false;\n          this.orderDialogVisible = false;\n        } else {\n          this.$message.error(res.msg || '下单失败');\n        }\n      }).catch(() => {\n        this.$message.error('下单失败，请重试');\n      });\n    },\n    handleCurrentChange(pageNum) {\n      // 分页变化\n      this.load(pageNum);\n    },\n    // 加载分类数据\n    loadCategories() {\n      this.categoriesLoading = true;\n      this.$request.get('/category/selectEnabled').then(res => {\n        if (res.code === '200') {\n          this.categories = res.data || [];\n        } else {\n          console.error('加载分类失败:', res.msg);\n          this.$message.warning('分类加载失败，但不影响商品浏览');\n        }\n      }).catch(err => {\n        console.error('加载分类失败:', err);\n        this.$message.warning('分类加载失败，但不影响商品浏览');\n      }).finally(() => {\n        this.categoriesLoading = false;\n      });\n    },\n    // 选择分类\n    selectCategory(categoryId) {\n      if (this.selectedCategoryId === categoryId) {\n        return; // 避免重复选择\n      }\n\n      // 如果正在加载，取消之前的请求\n      if (this.loadingRequest) {\n        this.loadingRequest.cancel && this.loadingRequest.cancel();\n      }\n      this.selectedCategoryId = categoryId;\n\n      // 提供用户反馈\n      const categoryName = categoryId === null ? '全部商品' : this.categories.find(c => c.id === categoryId)?.name || '未知分类';\n\n      // 使用 Toast 提示而不是 Message，避免干扰用户\n      this.$message({\n        message: `正在加载${categoryName}...`,\n        type: 'info',\n        duration: 1000\n      });\n      this.load(1); // 重新加载第一页数据\n    },\n    // 搜索输入防抖处理\n    handleSearchInput() {\n      // 清除之前的定时器\n      if (this.searchTimer) {\n        clearTimeout(this.searchTimer);\n      }\n\n      // 设置新的定时器，500ms后执行搜索\n      this.searchTimer = setTimeout(() => {\n        this.load(1);\n      }, 500);\n    },\n    // 切换侧边栏\n    toggleSidebar() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "categories", "selectedCategoryId", "categoriesLoading", "goodsLoading", "loadingRequest", "searchTimer", "sidebarCollapsed", "detailVisible", "currentGoods", "user", "JSON", "parse", "localStorage", "getItem", "orderDialogVisible", "dialogTitle", "dialogButtonText", "orderForm", "goodsId", "goodsName", "goodsPrice", "quantity", "sfRemark", "actionType", "created", "load", "loadCategories", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "cancel", "methods", "formatDateTime", "date", "d", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "$request", "get", "params", "categoryId", "then", "res", "code", "list", "$message", "error", "msg", "catch", "err", "console", "finally", "showDetail", "item", "showCartDialog", "goods", "id", "sfPrice", "showBuyDialog", "confirmOrder", "addToCart", "handleBuy", "post", "sfUserName", "sfUserId", "sfProductIds", "toString", "status", "sfCartStatus", "sfTotalPrice", "sfCreateTime", "success", "handleCurrentChange", "warning", "selectCategory", "categoryName", "find", "c", "message", "type", "duration", "handleSearchInput", "setTimeout", "toggleSidebar"], "sources": ["src/views/front/Home.vue"], "sourcesContent": ["<template>\n    <div class=\"home-container\">\n        <!-- 搜索栏 -->\n        <div class=\"search-section\">\n            <div class=\"search-container\">\n                <el-input\n                    placeholder=\"搜索您想要的美食...\"\n                    v-model=\"name\"\n                    class=\"search-input\"\n                    clearable\n                    size=\"large\"\n                    @keyup.enter.native=\"load(1)\"\n                    @input=\"handleSearchInput\"\n                >\n                    <el-button\n                        slot=\"append\"\n                        icon=\"el-icon-search\"\n                        @click=\"load(1)\"\n                        class=\"search-btn\"\n                    ></el-button>\n                </el-input>\n            </div>\n        </div>\n\n        <!-- 主要内容区 -->\n        <div class=\"main-content\">\n            <!-- 左侧分类菜单 -->\n            <div class=\"sidebar-container\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n                <!-- 菜单切换按钮 -->\n                <div class=\"sidebar-toggle\" @click=\"toggleSidebar\">\n                    <i :class=\"sidebarCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'\"></i>\n                </div>\n                \n                <!-- 分类菜单 -->\n                <div class=\"category-sidebar\">\n                    <div class=\"sidebar-header\">\n                        <i class=\"el-icon-menu\"></i>\n                        <span v-show=\"!sidebarCollapsed\" class=\"sidebar-title\">商品分类</span>\n                    </div>\n                    \n                    <div v-if=\"categoriesLoading\" class=\"category-loading\">\n                        <i class=\"el-icon-loading\"></i>\n                        <span v-show=\"!sidebarCollapsed\">加载中...</span>\n                    </div>\n                    \n                    <div v-else class=\"category-menu\">\n                        <!-- 全部分类 -->\n                        <div \n                            class=\"category-menu-item\"\n                            :class=\"{ 'active': selectedCategoryId === null }\"\n                            @click=\"selectCategory(null)\"\n                        >\n                            <div class=\"menu-item-icon\">\n                                <i class=\"el-icon-s-grid\"></i>\n                            </div>\n                            <span v-show=\"!sidebarCollapsed\" class=\"menu-item-text\">全部商品</span>\n                            <div v-show=\"selectedCategoryId === null && !sidebarCollapsed\" class=\"menu-item-indicator\"></div>\n                        </div>\n                        \n                        <!-- 具体分类 -->\n                        <div \n                            class=\"category-menu-item\"\n                            :class=\"{ 'active': selectedCategoryId === category.id }\"\n                            v-for=\"category in categories\"\n                            :key=\"category.id\"\n                            @click=\"selectCategory(category.id)\"\n                        >\n                            <div class=\"menu-item-icon\">\n                                <el-image \n                                    v-if=\"category.icon\" \n                                    :src=\"category.icon\" \n                                    fit=\"cover\"\n                                    class=\"category-menu-image\"\n                                ></el-image>\n                                <i v-else class=\"el-icon-dish\"></i>\n                            </div>\n                            <span v-show=\"!sidebarCollapsed\" class=\"menu-item-text\">{{ category.name }}</span>\n                            <div v-show=\"selectedCategoryId === category.id && !sidebarCollapsed\" class=\"menu-item-indicator\"></div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <!-- 右侧内容区域 -->\n            <div class=\"content-area\" :class=\"{ 'content-expanded': sidebarCollapsed }\">\n                <!-- 商品展示区 -->\n                <div class=\"content-section\">\n                    <div class=\"section-header\">\n                        <h2 class=\"section-title\">精选美食</h2>\n                        <p class=\"section-subtitle\">为您精心挑选的优质美食</p>\n                    </div>\n                    \n                    <div v-if=\"goodsLoading\" class=\"loading-state\">\n                        <i class=\"el-icon-loading\"></i>\n                        <p>加载中...</p>\n                    </div>\n                    \n                    <div v-else-if=\"tableData.length === 0\" class=\"empty-state\">\n                        <i class=\"el-icon-dish\"></i>\n                        <h3>暂无商品</h3>\n                        <p v-if=\"selectedCategoryId\">该分类下暂无商品，试试其他分类吧</p>\n                        <p v-else-if=\"name\">没有找到相关商品，试试其他关键词吧</p>\n                        <p v-else>暂无商品信息，敬请期待</p>\n                    </div>\n                    \n                    <div v-else class=\"goods-grid\">\n                        <div\n                            class=\"goods-card\"\n                            v-for=\"item in tableData\"\n                            :key=\"item.id\"\n                            @click=\"showDetail(item)\"\n                        >\n                            <div class=\"card-image-container\">\n                                <el-image\n                                    :src=\"item.sfImage\"\n                                    fit=\"cover\"\n                                    class=\"card-image\"\n                                    lazy\n                                    :placeholder=\"'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7liqDovb3kuK08L3RleHQ+Cjwvc3ZnPg=='\"\n                                >\n                                    <div slot=\"error\" class=\"image-error\">\n                                        <i class=\"el-icon-picture-outline\"></i>\n                                        <span>图片加载失败</span>\n                                    </div>\n                                </el-image>\n                                <div class=\"card-overlay\">\n                                    <el-button\n                                        type=\"primary\"\n                                        size=\"small\"\n                                        circle\n                                        icon=\"el-icon-view\"\n                                        class=\"view-btn\"\n                                    ></el-button>\n                                </div>\n                            </div>\n                            <div class=\"card-content\">\n                                <h3 class=\"card-title\">{{ item.name }}</h3>\n                                <div class=\"card-price\">\n                                    <span class=\"price-symbol\">¥</span>\n                                    <span class=\"price-number\">{{ item.sfPrice }}</span>\n                                </div>\n                                <div class=\"card-actions\">\n                                    <el-button\n                                        type=\"primary\"\n                                        size=\"small\"\n                                        @click.stop=\"showCartDialog(item)\"\n                                        class=\"cart-btn\"\n                                    >\n                                        <i class=\"el-icon-shopping-cart-2\"></i>\n                                        加入购物车\n                                    </el-button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <!-- 分页 -->\n                <div class=\"pagination-section\">\n                    <el-pagination\n                        background\n                        @current-change=\"handleCurrentChange\"\n                        :current-page=\"pageNum\"\n                        :page-size=\"pageSize\"\n                        layout=\"prev, pager, next\"\n                        :total=\"total\"\n                        :pager-count=\"5\"\n                        prev-text=\"上一页\"\n                        next-text=\"下一页\"\n                        class=\"custom-pagination\"\n                    >\n                    </el-pagination>\n                </div>\n            </div>\n        </div>\n\n        <!-- 商品详情弹窗 -->\n        <el-dialog\n            :visible.sync=\"detailVisible\"\n            width=\"70%\"\n            top=\"5vh\"\n            custom-class=\"detail-dialog\"\n            :close-on-click-modal=\"false\"\n        >\n            <div class=\"detail-container\" v-if=\"currentGoods\">\n                <div class=\"detail-left\">\n                    <div class=\"detail-image-container\">\n                        <el-image\n                            :src=\"currentGoods.sfImage\"\n                            fit=\"contain\"\n                            class=\"detail-image\"\n                        ></el-image>\n                    </div>\n                </div>\n                <div class=\"detail-right\">\n                    <div class=\"detail-header\">\n                        <h2 class=\"detail-title\">{{ currentGoods.name }}</h2>\n                        <div class=\"detail-price\">\n                            <span class=\"price-symbol\">¥</span>\n                            <span class=\"price-number\">{{ currentGoods.sfPrice }}</span>\n                        </div>\n                    </div>\n                    \n                    <div class=\"detail-info\">\n                        <div class=\"info-card\">\n                            <div class=\"info-item\">\n                                <i class=\"el-icon-goods info-icon\"></i>\n                                <div class=\"info-content\">\n                                    <span class=\"info-label\">商品类型</span>\n                                    <span class=\"info-value\">{{ currentGoods.foodtyope }}</span>\n                                </div>\n                            </div>\n                            <div class=\"info-item\">\n                                <i class=\"el-icon-box info-icon\"></i>\n                                <div class=\"info-content\">\n                                    <span class=\"info-label\">库存状态</span>\n                                    <span class=\"info-value\">{{ currentGoods.amount }}件</span>\n                                </div>\n                            </div>\n                            <div class=\"info-item\">\n                                <i class=\"el-icon-check info-icon\"></i>\n                                <div class=\"info-content\">\n                                    <span class=\"info-label\">上架状态</span>\n                                    <span class=\"info-value\">{{ currentGoods.fstatus }}</span>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                    \n                    <div class=\"detail-description\">\n                        <h3 class=\"desc-title\">商品描述</h3>\n                        <p class=\"desc-content\">{{ currentGoods.sfDescription }}</p>\n                    </div>\n                    \n                    <div class=\"detail-actions\">\n                        <el-button\n                            type=\"primary\"\n                            size=\"large\"\n                            @click=\"showCartDialog(currentGoods)\"\n                            class=\"action-btn cart-action\"\n                        >\n                            <i class=\"el-icon-shopping-cart-2\"></i>\n                            加入购物车\n                        </el-button>\n                        <el-button\n                            type=\"danger\"\n                            size=\"large\"\n                            @click=\"showBuyDialog(currentGoods)\"\n                            class=\"action-btn buy-action\"\n                        >\n                            <i class=\"el-icon-lightning\"></i>\n                            立即购买\n                        </el-button>\n                    </div>\n                </div>\n            </div>\n        </el-dialog>\n\n        <!-- 购物车/购买备注弹窗 -->\n        <el-dialog\n            :title=\"dialogTitle\"\n            :visible.sync=\"orderDialogVisible\"\n            width=\"40%\"\n            :close-on-click-modal=\"false\"\n            custom-class=\"order-dialog\"\n        >\n            <div class=\"order-form\">\n                <el-form :model=\"orderForm\" label-width=\"80px\">\n                    <el-form-item label=\"商品名称\">\n                        <el-input v-model=\"orderForm.goodsName\" disabled class=\"form-input\"></el-input>\n                    </el-form-item>\n                    <el-form-item label=\"商品价格\">\n                        <el-input v-model=\"orderForm.goodsPrice\" disabled class=\"form-input\"></el-input>\n                    </el-form-item>\n                    <el-form-item label=\"购买数量\">\n                        <el-input-number \n                            v-model=\"orderForm.quantity\" \n                            :min=\"1\" \n                            :max=\"99\"\n                            controls-position=\"right\"\n                            class=\"quantity-input\">\n                        </el-input-number>\n                    </el-form-item>\n                    <el-form-item label=\"订单备注\" prop=\"sfRemark\">\n                        <el-input\n                            type=\"textarea\"\n                            v-model=\"orderForm.sfRemark\"\n                            placeholder=\"请输入订单备注（可选）\"\n                            :rows=\"3\"\n                            maxlength=\"200\"\n                            show-word-limit>\n                        </el-input>\n                    </el-form-item>\n                </el-form>\n            </div>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"orderDialogVisible = false\" class=\"cancel-btn\">取消</el-button>\n                <el-button type=\"primary\" @click=\"confirmOrder\" class=\"confirm-btn\">{{ dialogButtonText }}</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: \"GoodsList\",\n    data() {\n        return {\n            tableData: [],  // 商品数据\n            pageNum: 1,     // 当前页码\n            pageSize: 12,   // 每页12条\n            total: 0,       // 总数\n            name: null,     // 搜索关键词\n            \n            // 分类相关\n            categories: [],           // 分类列表\n            selectedCategoryId: null, // 选中的分类ID\n            categoriesLoading: false, // 分类加载状态\n            goodsLoading: false,      // 商品加载状态\n            loadingRequest: null,     // 当前加载请求\n            searchTimer: null,        // 搜索防抖定时器\n            sidebarCollapsed: false,  // 侧边栏折叠状态\n            \n            detailVisible: false, // 详情弹窗显示\n            currentGoods: null,   // 当前查看的商品\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'), // 当前登录用户\n\n            // 订单相关\n            orderDialogVisible: false,\n            dialogTitle: '',\n            dialogButtonText: '',\n            orderForm: {\n                goodsId: null,\n                goodsName: '',\n                goodsPrice: 0,\n                quantity: 1,\n                sfRemark: '',\n                actionType: '' // 'cart' or 'buy'\n            }\n        }\n    },\n    created() {\n        this.load(1)\n        this.loadCategories()\n    },\n    beforeDestroy() {\n        // 清理定时器\n        if (this.searchTimer) {\n            clearTimeout(this.searchTimer)\n        }\n        \n        // 取消未完成的请求\n        if (this.loadingRequest) {\n            this.loadingRequest.cancel && this.loadingRequest.cancel()\n        }\n    },\n    methods: {\n        // 格式化时间为年月日时分秒\n        formatDateTime(date) {\n            const d = new Date(date);\n            const year = d.getFullYear();\n            const month = String(d.getMonth() + 1).padStart(2, '0');\n            const day = String(d.getDate()).padStart(2, '0');\n            const hours = String(d.getHours()).padStart(2, '0');\n            const minutes = String(d.getMinutes()).padStart(2, '0');\n            const seconds = String(d.getSeconds()).padStart(2, '0');\n            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n        },\n\n        load(pageNum) {  // 加载商品数据\n            if (pageNum) this.pageNum = pageNum\n            \n            this.goodsLoading = true\n            this.$request.get('/foods/selectPage', {\n                params: {\n                    pageNum: this.pageNum,\n                    pageSize: this.pageSize,\n                    name: this.name,\n                    categoryId: this.selectedCategoryId,\n                }\n            }).then(res => {\n                if (res.code === '200') {\n                    this.tableData = res.data?.list || []\n                    this.total = res.data?.total || 0\n                } else {\n                    this.$message.error(res.msg || '加载商品失败，请重试')\n                }\n            }).catch(err => {\n                console.error('加载商品失败:', err)\n                this.$message.error('网络异常，请检查网络连接后重试')\n            }).finally(() => {\n                this.goodsLoading = false\n            })\n        },\n\n        showDetail(item) {  // 显示商品详情\n            this.currentGoods = item\n            this.detailVisible = true\n        },\n\n        // 显示购物车弹窗\n        showCartDialog(goods) {\n            this.orderForm = {\n                goodsId: goods.id,\n                goodsName: goods.name,\n                goodsPrice: goods.sfPrice,\n                sfRemark: '',\n                actionType: 'cart'\n            }\n            this.dialogTitle = '加入购物车'\n            this.dialogButtonText = '确认加入'\n            this.orderDialogVisible = true\n        },\n\n        // 显示购买弹窗\n        showBuyDialog(goods) {\n            this.orderForm = {\n                goodsId: goods.id,\n                goodsName: goods.name,\n                goodsPrice: goods.sfPrice,\n                sfRemark: '',\n                actionType: 'buy'\n            }\n            this.dialogTitle = '立即购买'\n            this.dialogButtonText = '确认购买'\n            this.orderDialogVisible = true\n        },\n\n        // 确认订单（购物车或购买）\n        confirmOrder() {\n            if (this.orderForm.actionType === 'cart') {\n                this.addToCart()\n            } else {\n                this.handleBuy()\n            }\n        },\n\n        // 加入购物车\n        addToCart() {\n            this.$request.post('/dingdan/add', {\n                sfUserName: this.user.name || '匿名用户',\n                sfUserId: this.user.id || 0,\n                sfProductIds: this.orderForm.goodsId.toString(),\n                status: '未付款',\n                sfCartStatus: '已加入购物车',\n                sfTotalPrice: 0, // 购物车状态下价格为0\n                sfRemark: this.orderForm.sfRemark, // 添加备注\n                sfCreateTime: this.formatDateTime(new Date()) // 格式化时间\n            }).then(res => {\n                if (res.code === '200') {\n                    this.$message.success('商品已加入购物车！')\n                    this.orderDialogVisible = false\n                } else {\n                    this.$message.error(res.msg || '操作失败')\n                }\n            }).catch(() => {\n                this.$message.error('操作失败，请重试')\n            })\n        },\n\n        // 立即购买\n        handleBuy() {\n            this.$request.post('/dingdan/add', {\n                sfUserName: this.user.name || '匿名用户',\n                sfUserId: this.user.id || 0,\n                sfProductIds: this.orderForm.goodsId.toString(),\n                status: '未出餐', // 立即购买直接设为已付款状态\n                sfCartStatus: '', // 购物车状态为空\n                sfTotalPrice: this.orderForm.goodsPrice, // 商品价格放入订单价格字段\n                sfRemark: this.orderForm.sfRemark, // 添加备注\n                sfCreateTime: this.formatDateTime(new Date()) // 格式化时间\n            }).then(res => {\n                if (res.code === '200') {\n                    this.$message.success('订单创建成功！')\n                    this.detailVisible = false\n                    this.orderDialogVisible = false\n                } else {\n                    this.$message.error(res.msg || '下单失败')\n                }\n            }).catch(() => {\n                this.$message.error('下单失败，请重试')\n            })\n        },\n\n        handleCurrentChange(pageNum) {  // 分页变化\n            this.load(pageNum)\n        },\n\n        // 加载分类数据\n        loadCategories() {\n            this.categoriesLoading = true\n            this.$request.get('/category/selectEnabled').then(res => {\n                if (res.code === '200') {\n                    this.categories = res.data || []\n                } else {\n                    console.error('加载分类失败:', res.msg)\n                    this.$message.warning('分类加载失败，但不影响商品浏览')\n                }\n            }).catch(err => {\n                console.error('加载分类失败:', err)\n                this.$message.warning('分类加载失败，但不影响商品浏览')\n            }).finally(() => {\n                this.categoriesLoading = false\n            })\n        },\n\n        // 选择分类\n        selectCategory(categoryId) {\n            if (this.selectedCategoryId === categoryId) {\n                return // 避免重复选择\n            }\n            \n            // 如果正在加载，取消之前的请求\n            if (this.loadingRequest) {\n                this.loadingRequest.cancel && this.loadingRequest.cancel()\n            }\n            \n            this.selectedCategoryId = categoryId\n            \n            // 提供用户反馈\n            const categoryName = categoryId === null ? '全部商品' : \n                this.categories.find(c => c.id === categoryId)?.name || '未知分类'\n            \n            // 使用 Toast 提示而不是 Message，避免干扰用户\n            this.$message({\n                message: `正在加载${categoryName}...`,\n                type: 'info',\n                duration: 1000\n            })\n            \n            this.load(1) // 重新加载第一页数据\n        },\n\n        // 搜索输入防抖处理\n        handleSearchInput() {\n            // 清除之前的定时器\n            if (this.searchTimer) {\n                clearTimeout(this.searchTimer)\n            }\n            \n            // 设置新的定时器，500ms后执行搜索\n            this.searchTimer = setTimeout(() => {\n                this.load(1)\n            }, 500)\n        },\n\n        // 切换侧边栏\n        toggleSidebar() {\n            this.sidebarCollapsed = !this.sidebarCollapsed\n        }\n    }\n}\n</script>\n\n<style scoped>\n.home-container {\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n    min-height: 100vh;\n}\n\n/* 搜索区域 */\n.search-section {\n    padding: 40px 0;\n    background: white;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    z-index: 10;\n    position: relative;\n}\n\n.search-container {\n    max-width: 600px;\n    margin: 0 auto;\n    padding: 0 20px;\n}\n\n.search-input {\n    width: 100%;\n}\n\n.search-input >>> .el-input__inner {\n    height: 50px;\n    border-radius: 25px;\n    border: 2px solid #e5e7eb;\n    padding-left: 20px;\n    font-size: 16px;\n    transition: all 0.3s ease;\n}\n\n.search-input >>> .el-input__inner:focus {\n    border-color: #3b82f6;\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.search-btn {\n    background: #3b82f6;\n    border-color: #3b82f6;\n    color: white;\n    border-radius: 0 25px 25px 0;\n    padding: 0 20px;\n    font-size: 16px;\n    transition: all 0.3s ease;\n}\n\n.search-btn:hover {\n    background: #1e40af;\n    border-color: #1e40af;\n}\n\n/* 主要内容区域 */\n.main-content {\n    display: flex;\n    min-height: calc(100vh - 140px);\n}\n\n/* 左侧分类菜单 */\n.sidebar-container {\n    width: 280px;\n    background: white;\n    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    position: relative;\n    z-index: 5;\n}\n\n.sidebar-container.sidebar-collapsed {\n    width: 80px;\n}\n\n.sidebar-toggle {\n    position: absolute;\n    top: 20px;\n    right: -15px;\n    width: 30px;\n    height: 30px;\n    background: #3b82f6;\n    color: white;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    font-size: 14px;\n    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n    transition: all 0.3s ease;\n    z-index: 10;\n}\n\n.sidebar-toggle:hover {\n    background: #1e40af;\n    transform: scale(1.1);\n}\n\n.category-sidebar {\n    height: 100%;\n    overflow-y: auto;\n    padding: 20px 0;\n}\n\n.sidebar-header {\n    display: flex;\n    align-items: center;\n    padding: 0 20px 20px 20px;\n    border-bottom: 1px solid #e2e8f0;\n    margin-bottom: 20px;\n}\n\n.sidebar-header i {\n    font-size: 20px;\n    color: #3b82f6;\n    margin-right: 12px;\n}\n\n.sidebar-title {\n    font-size: 16px;\n    font-weight: 600;\n    color: #1e293b;\n    transition: opacity 0.3s ease;\n}\n\n.sidebar-collapsed .sidebar-title {\n    opacity: 0;\n}\n\n/* 分类菜单 */\n.category-menu {\n    padding: 0 10px;\n}\n\n.category-menu-item {\n    display: flex;\n    align-items: center;\n    padding: 12px 16px;\n    margin-bottom: 8px;\n    border-radius: 12px;\n    cursor: pointer;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    position: relative;\n    overflow: hidden;\n}\n\n.category-menu-item:hover {\n    background: #f1f5f9;\n    transform: translateX(4px);\n}\n\n.category-menu-item.active {\n    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);\n    color: white;\n    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.category-menu-item.active:hover {\n    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);\n}\n\n.menu-item-icon {\n    width: 32px;\n    height: 32px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-right: 12px;\n    flex-shrink: 0;\n}\n\n.menu-item-icon i {\n    font-size: 18px;\n    color: #64748b;\n    transition: color 0.3s ease;\n}\n\n.category-menu-item.active .menu-item-icon i {\n    color: white;\n}\n\n.category-menu-image {\n    width: 28px;\n    height: 28px;\n    border-radius: 50%;\n    object-fit: cover;\n}\n\n.menu-item-text {\n    flex: 1;\n    font-size: 14px;\n    font-weight: 500;\n    color: #475569;\n    transition: all 0.3s ease;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n\n.category-menu-item.active .menu-item-text {\n    color: white;\n    font-weight: 600;\n}\n\n.menu-item-indicator {\n    width: 4px;\n    height: 20px;\n    background: white;\n    border-radius: 2px;\n    margin-left: 8px;\n    opacity: 0.8;\n}\n\n/* 分类加载状态 */\n.category-loading {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 40px 20px;\n    color: #64748b;\n    flex-direction: column;\n}\n\n.sidebar-collapsed .category-loading {\n    flex-direction: column;\n}\n\n.category-loading i {\n    font-size: 24px;\n    margin-bottom: 8px;\n    color: #3b82f6;\n    animation: rotating 2s linear infinite;\n}\n\n.sidebar-collapsed .category-loading i {\n    margin-right: 0;\n    margin-bottom: 8px;\n}\n\n.category-loading span {\n    font-size: 12px;\n    transition: opacity 0.3s ease;\n}\n\n/* 右侧内容区域 */\n.content-area {\n    flex: 1;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    margin-left: 20px;\n}\n\n.content-area.content-expanded {\n    margin-left: 20px;\n}\n\n/* 内容区域 */\n.content-section {\n    padding: 40px 20px;\n}\n\n.section-header {\n    text-align: center;\n    margin-bottom: 40px;\n}\n\n.section-title {\n    font-size: 32px;\n    font-weight: 700;\n    color: #1e40af;\n    margin-bottom: 12px;\n}\n\n.section-subtitle {\n    font-size: 16px;\n    color: #64748b;\n    margin: 0;\n}\n\n/* 商品加载状态 */\n.loading-state {\n    text-align: center;\n    padding: 80px 20px;\n    color: #64748b;\n}\n\n.loading-state i {\n    font-size: 48px;\n    margin-bottom: 16px;\n    color: #3b82f6;\n    animation: rotating 2s linear infinite;\n}\n\n.loading-state p {\n    font-size: 16px;\n    margin: 0;\n}\n\n/* 旋转动画 */\n@keyframes rotating {\n    0% {\n        transform: rotate(0deg);\n    }\n    100% {\n        transform: rotate(360deg);\n    }\n}\n\n/* 图片错误状态 */\n.image-error {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    background: #f5f5f5;\n    color: #999;\n}\n\n.image-error i {\n    font-size: 32px;\n    margin-bottom: 8px;\n}\n\n.image-error span {\n    font-size: 12px;\n}\n\n/* 空状态 */\n.empty-state {\n    text-align: center;\n    padding: 80px 20px;\n    color: #64748b;\n}\n\n.empty-state i {\n    font-size: 64px;\n    margin-bottom: 16px;\n    color: #3b82f6;\n}\n\n.empty-state h3 {\n    font-size: 20px;\n    margin-bottom: 8px;\n    color: #475569;\n}\n\n.empty-state p {\n    margin-bottom: 0;\n    font-size: 16px;\n}\n\n/* 商品网格 */\n.goods-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n    gap: 24px;\n    margin-bottom: 40px;\n}\n\n.goods-card {\n    background: white;\n    border-radius: 16px;\n    overflow: hidden;\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    cursor: pointer;\n    border: 1px solid #f1f5f9;\n}\n\n.goods-card:hover {\n    transform: translateY(-8px);\n    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);\n    border-color: #3b82f6;\n}\n\n.card-image-container {\n    position: relative;\n    height: 200px;\n    overflow: hidden;\n}\n\n.card-image {\n    width: 100%;\n    height: 100%;\n    transition: transform 0.3s ease;\n}\n\n.goods-card:hover .card-image {\n    transform: scale(1.05);\n}\n\n.card-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: rgba(0, 0, 0, 0.5);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    opacity: 0;\n    transition: opacity 0.3s ease;\n}\n\n.goods-card:hover .card-overlay {\n    opacity: 1;\n}\n\n.view-btn {\n    background: white;\n    color: #3b82f6;\n    border: none;\n    width: 50px;\n    height: 50px;\n    font-size: 20px;\n}\n\n.card-content {\n    padding: 20px;\n}\n\n.card-title {\n    font-size: 16px;\n    font-weight: 600;\n    color: #1e293b;\n    margin-bottom: 12px;\n    height: 48px;\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    line-height: 1.5;\n}\n\n.card-price {\n    margin-bottom: 16px;\n}\n\n.price-symbol {\n    color: #3b82f6;\n    font-size: 16px;\n    font-weight: 600;\n}\n\n.price-number {\n    color: #3b82f6;\n    font-size: 24px;\n    font-weight: 700;\n}\n\n.card-actions {\n    display: flex;\n    justify-content: center;\n}\n\n.cart-btn {\n    background: #3b82f6;\n    border-color: #3b82f6;\n    color: white;\n    border-radius: 20px;\n    padding: 8px 20px;\n    font-weight: 500;\n    transition: all 0.3s ease;\n}\n\n.cart-btn:hover {\n    background: #1e40af;\n    border-color: #1e40af;\n    transform: translateY(-2px);\n}\n\n/* 分页区域 */\n.pagination-section {\n    display: flex;\n    justify-content: center;\n    padding: 40px 0;\n}\n\n.custom-pagination >>> .el-pager li {\n    background: white;\n    border: 1px solid #e5e7eb;\n    border-radius: 8px;\n    margin: 0 4px;\n    transition: all 0.3s ease;\n}\n\n.custom-pagination >>> .el-pager li:hover {\n    border-color: #3b82f6;\n    color: #3b82f6;\n}\n\n.custom-pagination >>> .el-pager li.active {\n    background: #3b82f6;\n    border-color: #3b82f6;\n    color: white;\n}\n\n/* 详情弹窗 */\n.detail-dialog >>> .el-dialog {\n    border-radius: 16px;\n    overflow: hidden;\n}\n\n.detail-dialog >>> .el-dialog__header {\n    display: none;\n}\n\n.detail-dialog >>> .el-dialog__body {\n    padding: 0;\n}\n\n.detail-container {\n    display: flex;\n    min-height: 500px;\n}\n\n.detail-left {\n    flex: 1;\n    background: #f8fafc;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 40px;\n}\n\n.detail-image-container {\n    width: 100%;\n    max-width: 400px;\n}\n\n.detail-image {\n    width: 100%;\n    height: 400px;\n    border-radius: 12px;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.detail-right {\n    flex: 1;\n    padding: 40px;\n    display: flex;\n    flex-direction: column;\n}\n\n.detail-header {\n    margin-bottom: 30px;\n}\n\n.detail-title {\n    font-size: 28px;\n    font-weight: 700;\n    color: #1e293b;\n    margin-bottom: 16px;\n    line-height: 1.3;\n}\n\n.detail-price {\n    margin-bottom: 20px;\n}\n\n.detail-price .price-symbol {\n    color: #3b82f6;\n    font-size: 24px;\n    font-weight: 600;\n}\n\n.detail-price .price-number {\n    color: #3b82f6;\n    font-size: 36px;\n    font-weight: 700;\n}\n\n.detail-info {\n    margin-bottom: 30px;\n}\n\n.info-card {\n    background: #f8fafc;\n    border-radius: 12px;\n    padding: 20px;\n    border: 1px solid #e2e8f0;\n}\n\n.info-item {\n    display: flex;\n    align-items: center;\n    margin-bottom: 16px;\n}\n\n.info-item:last-child {\n    margin-bottom: 0;\n}\n\n.info-icon {\n    width: 40px;\n    height: 40px;\n    background: #3b82f6;\n    color: white;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-right: 16px;\n    font-size: 16px;\n}\n\n.info-content {\n    flex: 1;\n}\n\n.info-label {\n    display: block;\n    font-size: 14px;\n    color: #64748b;\n    margin-bottom: 4px;\n}\n\n.info-value {\n    font-size: 16px;\n    font-weight: 600;\n    color: #1e293b;\n}\n\n.detail-description {\n    margin-bottom: 40px;\n    flex: 1;\n}\n\n.desc-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #1e293b;\n    margin-bottom: 12px;\n}\n\n.desc-content {\n    font-size: 15px;\n    color: #475569;\n    line-height: 1.7;\n    margin: 0;\n}\n\n.detail-actions {\n    display: flex;\n    gap: 16px;\n}\n\n.action-btn {\n    flex: 1;\n    height: 50px;\n    font-size: 16px;\n    font-weight: 600;\n    border-radius: 12px;\n    transition: all 0.3s ease;\n}\n\n.cart-action {\n    background: #3b82f6;\n    border-color: #3b82f6;\n}\n\n.cart-action:hover {\n    background: #1e40af;\n    border-color: #1e40af;\n    transform: translateY(-2px);\n}\n\n.buy-action {\n    background: #ef4444;\n    border-color: #ef4444;\n}\n\n.buy-action:hover {\n    background: #dc2626;\n    border-color: #dc2626;\n    transform: translateY(-2px);\n}\n\n/* 订单弹窗 */\n.order-dialog >>> .el-dialog {\n    border-radius: 16px;\n}\n\n.order-dialog >>> .el-dialog__header {\n    background: #f8fafc;\n    border-bottom: 1px solid #e2e8f0;\n}\n\n.order-form {\n    padding: 20px 0;\n}\n\n.form-input >>> .el-input__inner,\n.form-textarea >>> .el-textarea__inner {\n    border-radius: 8px;\n    border: 1px solid #e2e8f0;\n    transition: all 0.3s ease;\n}\n\n.form-input >>> .el-input__inner:focus,\n.form-textarea >>> .el-textarea__inner:focus {\n    border-color: #3b82f6;\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.dialog-footer {\n    display: flex;\n    justify-content: flex-end;\n    gap: 12px;\n}\n\n.cancel-btn {\n    border-radius: 8px;\n    padding: 10px 20px;\n}\n\n.confirm-btn {\n    background: #3b82f6;\n    border-color: #3b82f6;\n    border-radius: 8px;\n    padding: 10px 20px;\n}\n\n.confirm-btn:hover {\n    background: #1e40af;\n    border-color: #1e40af;\n}\n\n/* 动画效果 */\n@keyframes fadeInUp {\n    from {\n        opacity: 0;\n        transform: translateY(30px);\n    }\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n/* 响应式设计 */\n@media (max-width: 1024px) {\n    .sidebar-container {\n        width: 240px;\n    }\n    \n    .sidebar-container.sidebar-collapsed {\n        width: 70px;\n    }\n    \n    .goods-grid {\n        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n    }\n}\n\n@media (max-width: 768px) {\n    .main-content {\n        flex-direction: column;\n    }\n    \n    .sidebar-container {\n        width: 100%;\n        height: auto;\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    }\n    \n    .sidebar-container.sidebar-collapsed {\n        width: 100%;\n        height: 60px;\n        overflow: hidden;\n    }\n    \n    .category-sidebar {\n        padding: 10px 0;\n    }\n    \n    .sidebar-collapsed .category-menu {\n        display: none;\n    }\n    \n    .content-area {\n        margin-left: 0;\n    }\n    \n    .content-area.content-expanded {\n        margin-left: 0;\n    }\n    \n    .goods-grid {\n        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n        gap: 16px;\n    }\n    \n    .detail-container {\n        flex-direction: column;\n    }\n    \n    .detail-left {\n        padding: 20px;\n    }\n    \n    .detail-right {\n        padding: 20px;\n    }\n}\n\n@media (max-width: 480px) {\n    .content-section {\n        padding: 20px 16px;\n    }\n    \n    .goods-grid {\n        grid-template-columns: 1fr;\n    }\n    \n    .search-container {\n        padding: 0 16px;\n    }\n}\n</style>"], "mappings": ";;AAgTA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAAA;MACAL,IAAA;MAAA;;MAEA;MACAM,UAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;MAAA;MACAC,WAAA;MAAA;MACAC,gBAAA;MAAA;;MAEAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MAAA;;MAEA;MACAC,kBAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,SAAA;QACAC,OAAA;QACAC,SAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;IACA,KAAAC,cAAA;EACA;EACAC,cAAA;IACA;IACA,SAAAtB,WAAA;MACAuB,YAAA,MAAAvB,WAAA;IACA;;IAEA;IACA,SAAAD,cAAA;MACA,KAAAA,cAAA,CAAAyB,MAAA,SAAAzB,cAAA,CAAAyB,MAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,eAAAC,IAAA;MACA,MAAAC,CAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,MAAAG,IAAA,GAAAF,CAAA,CAAAG,WAAA;MACA,MAAAC,KAAA,GAAAC,MAAA,CAAAL,CAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,MAAAC,GAAA,GAAAH,MAAA,CAAAL,CAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,MAAAG,KAAA,GAAAL,MAAA,CAAAL,CAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,MAAAK,OAAA,GAAAP,MAAA,CAAAL,CAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,MAAAO,OAAA,GAAAT,MAAA,CAAAL,CAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAAL,IAAA,IAAAE,KAAA,IAAAI,GAAA,IAAAE,KAAA,IAAAE,OAAA,IAAAE,OAAA;IACA;IAEAtB,KAAA5B,OAAA;MAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MAEA,KAAAM,YAAA;MACA,KAAA8C,QAAA,CAAAC,GAAA;QACAC,MAAA;UACAtD,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAJ,IAAA,OAAAA,IAAA;UACA0D,UAAA,OAAAnD;QACA;MACA,GAAAoD,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAA3D,SAAA,GAAA0D,GAAA,CAAA3D,IAAA,EAAA6D,IAAA;UACA,KAAAzD,KAAA,GAAAuD,GAAA,CAAA3D,IAAA,EAAAI,KAAA;QACA;UACA,KAAA0D,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAC,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAJ,KAAA,YAAAG,GAAA;QACA,KAAAJ,QAAA,CAAAC,KAAA;MACA,GAAAK,OAAA;QACA,KAAA5D,YAAA;MACA;IACA;IAEA6D,WAAAC,IAAA;MAAA;MACA,KAAAzD,YAAA,GAAAyD,IAAA;MACA,KAAA1D,aAAA;IACA;IAEA;IACA2D,eAAAC,KAAA;MACA,KAAAlD,SAAA;QACAC,OAAA,EAAAiD,KAAA,CAAAC,EAAA;QACAjD,SAAA,EAAAgD,KAAA,CAAAzE,IAAA;QACA0B,UAAA,EAAA+C,KAAA,CAAAE,OAAA;QACA/C,QAAA;QACAC,UAAA;MACA;MACA,KAAAR,WAAA;MACA,KAAAC,gBAAA;MACA,KAAAF,kBAAA;IACA;IAEA;IACAwD,cAAAH,KAAA;MACA,KAAAlD,SAAA;QACAC,OAAA,EAAAiD,KAAA,CAAAC,EAAA;QACAjD,SAAA,EAAAgD,KAAA,CAAAzE,IAAA;QACA0B,UAAA,EAAA+C,KAAA,CAAAE,OAAA;QACA/C,QAAA;QACAC,UAAA;MACA;MACA,KAAAR,WAAA;MACA,KAAAC,gBAAA;MACA,KAAAF,kBAAA;IACA;IAEA;IACAyD,aAAA;MACA,SAAAtD,SAAA,CAAAM,UAAA;QACA,KAAAiD,SAAA;MACA;QACA,KAAAC,SAAA;MACA;IACA;IAEA;IACAD,UAAA;MACA,KAAAvB,QAAA,CAAAyB,IAAA;QACAC,UAAA,OAAAlE,IAAA,CAAAf,IAAA;QACAkF,QAAA,OAAAnE,IAAA,CAAA2D,EAAA;QACAS,YAAA,OAAA5D,SAAA,CAAAC,OAAA,CAAA4D,QAAA;QACAC,MAAA;QACAC,YAAA;QACAC,YAAA;QAAA;QACA3D,QAAA,OAAAL,SAAA,CAAAK,QAAA;QAAA;QACA4D,YAAA,OAAAnD,cAAA,KAAAG,IAAA;MACA,GAAAmB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAE,QAAA,CAAA0B,OAAA;UACA,KAAArE,kBAAA;QACA;UACA,KAAA2C,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAC,KAAA;QACA,KAAAH,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAe,UAAA;MACA,KAAAxB,QAAA,CAAAyB,IAAA;QACAC,UAAA,OAAAlE,IAAA,CAAAf,IAAA;QACAkF,QAAA,OAAAnE,IAAA,CAAA2D,EAAA;QACAS,YAAA,OAAA5D,SAAA,CAAAC,OAAA,CAAA4D,QAAA;QACAC,MAAA;QAAA;QACAC,YAAA;QAAA;QACAC,YAAA,OAAAhE,SAAA,CAAAG,UAAA;QAAA;QACAE,QAAA,OAAAL,SAAA,CAAAK,QAAA;QAAA;QACA4D,YAAA,OAAAnD,cAAA,KAAAG,IAAA;MACA,GAAAmB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAE,QAAA,CAAA0B,OAAA;UACA,KAAA5E,aAAA;UACA,KAAAO,kBAAA;QACA;UACA,KAAA2C,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAC,KAAA;QACA,KAAAH,QAAA,CAAAC,KAAA;MACA;IACA;IAEA0B,oBAAAvF,OAAA;MAAA;MACA,KAAA4B,IAAA,CAAA5B,OAAA;IACA;IAEA;IACA6B,eAAA;MACA,KAAAxB,iBAAA;MACA,KAAA+C,QAAA,CAAAC,GAAA,4BAAAG,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAvD,UAAA,GAAAsD,GAAA,CAAA3D,IAAA;QACA;UACAmE,OAAA,CAAAJ,KAAA,YAAAJ,GAAA,CAAAK,GAAA;UACA,KAAAF,QAAA,CAAA4B,OAAA;QACA;MACA,GAAAzB,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAJ,KAAA,YAAAG,GAAA;QACA,KAAAJ,QAAA,CAAA4B,OAAA;MACA,GAAAtB,OAAA;QACA,KAAA7D,iBAAA;MACA;IACA;IAEA;IACAoF,eAAAlC,UAAA;MACA,SAAAnD,kBAAA,KAAAmD,UAAA;QACA;MACA;;MAEA;MACA,SAAAhD,cAAA;QACA,KAAAA,cAAA,CAAAyB,MAAA,SAAAzB,cAAA,CAAAyB,MAAA;MACA;MAEA,KAAA5B,kBAAA,GAAAmD,UAAA;;MAEA;MACA,MAAAmC,YAAA,GAAAnC,UAAA,qBACA,KAAApD,UAAA,CAAAwF,IAAA,CAAAC,CAAA,IAAAA,CAAA,CAAArB,EAAA,KAAAhB,UAAA,GAAA1D,IAAA;;MAEA;MACA,KAAA+D,QAAA;QACAiC,OAAA,SAAAH,YAAA;QACAI,IAAA;QACAC,QAAA;MACA;MAEA,KAAAnE,IAAA;IACA;IAEA;IACAoE,kBAAA;MACA;MACA,SAAAxF,WAAA;QACAuB,YAAA,MAAAvB,WAAA;MACA;;MAEA;MACA,KAAAA,WAAA,GAAAyF,UAAA;QACA,KAAArE,IAAA;MACA;IACA;IAEA;IACAsE,cAAA;MACA,KAAAzF,gBAAA,SAAAA,gBAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}