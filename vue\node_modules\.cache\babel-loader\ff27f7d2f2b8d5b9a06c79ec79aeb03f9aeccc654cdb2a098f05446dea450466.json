{"ast": null, "code": "export default {\n  name: \"GoodsList\",\n  data() {\n    return {\n      tableData: [],\n      // 商品数据\n      pageNum: 1,\n      // 当前页码\n      pageSize: 12,\n      // 每页12条\n      total: 0,\n      // 总数\n      name: null,\n      // 搜索关键词\n      detailVisible: false,\n      // 详情弹窗显示\n      currentGoods: null,\n      // 当前查看的商品\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      // 当前登录用户\n\n      // 订单相关\n      orderDialogVisible: false,\n      dialogTitle: '',\n      dialogButtonText: '',\n      orderForm: {\n        goodsId: null,\n        goodsName: '',\n        goodsPrice: 0,\n        sfRemark: '',\n        actionType: '' // 'cart' or 'buy'\n      }\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    // 格式化时间为年月日时分秒\n    formatDateTime(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      const hours = String(d.getHours()).padStart(2, '0');\n      const minutes = String(d.getMinutes()).padStart(2, '0');\n      const seconds = String(d.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n    load(pageNum) {\n      // 加载商品数据\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/foods/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          name: this.name\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list;\n          this.total = res.data?.total;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    showDetail(item) {\n      // 显示商品详情\n      this.currentGoods = item;\n      this.detailVisible = true;\n    },\n    // 显示购物车弹窗\n    showCartDialog(goods) {\n      this.orderForm = {\n        goodsId: goods.id,\n        goodsName: goods.name,\n        goodsPrice: goods.sfPrice,\n        sfRemark: '',\n        actionType: 'cart'\n      };\n      this.dialogTitle = '加入购物车';\n      this.dialogButtonText = '确认加入';\n      this.orderDialogVisible = true;\n    },\n    // 显示购买弹窗\n    showBuyDialog(goods) {\n      this.orderForm = {\n        goodsId: goods.id,\n        goodsName: goods.name,\n        goodsPrice: goods.sfPrice,\n        sfRemark: '',\n        actionType: 'buy'\n      };\n      this.dialogTitle = '立即购买';\n      this.dialogButtonText = '确认购买';\n      this.orderDialogVisible = true;\n    },\n    // 确认订单（购物车或购买）\n    confirmOrder() {\n      if (this.orderForm.actionType === 'cart') {\n        this.addToCart();\n      } else {\n        this.handleBuy();\n      }\n    },\n    // 加入购物车\n    addToCart() {\n      this.$request.post('/dingdan/add', {\n        sfUserName: this.user.name || '匿名用户',\n        sfUserId: this.user.id || 0,\n        sfGoodsId: this.orderForm.goodsId.toString(),\n        status: '未付款',\n        sfCartStatus: '已加入购物车',\n        sfTotalPrice: 0,\n        // 购物车状态下价格为0\n        sfRemark: this.orderForm.sfRemark,\n        // 添加备注\n        sfCreateTime: this.formatDateTime(new Date()) // 格式化时间\n      }).then(res => {\n        if (res.code === '200') {\n          this.$message.success('商品已加入购物车！');\n          this.orderDialogVisible = false;\n        } else {\n          this.$message.error(res.msg || '操作失败');\n        }\n      }).catch(() => {\n        this.$message.error('操作失败，请重试');\n      });\n    },\n    // 立即购买\n    handleBuy() {\n      this.$request.post('/dingdan/add', {\n        sfUserName: this.user.name || '匿名用户',\n        sfUserId: this.user.id || 0,\n        sfGoodsId: this.orderForm.goodsId.toString(),\n        status: '未出餐',\n        // 立即购买直接设为已付款状态\n        sfCartStatus: '',\n        // 购物车状态为空\n        sfTotalPrice: this.orderForm.goodsPrice,\n        // 商品价格放入订单价格字段\n        sfRemark: this.orderForm.sfRemark,\n        // 添加备注\n        sfCreateTime: this.formatDateTime(new Date()) // 格式化时间\n      }).then(res => {\n        if (res.code === '200') {\n          this.$message.success('订单创建成功！');\n          this.detailVisible = false;\n          this.orderDialogVisible = false;\n        } else {\n          this.$message.error(res.msg || '下单失败');\n        }\n      }).catch(() => {\n        this.$message.error('下单失败，请重试');\n      });\n    },\n    handleCurrentChange(pageNum) {\n      // 分页变化\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "detailVisible", "currentGoods", "user", "JSON", "parse", "localStorage", "getItem", "orderDialogVisible", "dialogTitle", "dialogButtonText", "orderForm", "goodsId", "goodsName", "goodsPrice", "sfRemark", "actionType", "created", "load", "methods", "formatDateTime", "date", "d", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "$request", "get", "params", "then", "res", "code", "list", "$message", "error", "msg", "showDetail", "item", "showCartDialog", "goods", "id", "sfPrice", "showBuyDialog", "confirmOrder", "addToCart", "handleBuy", "post", "sfUserName", "sfUserId", "sfGoodsId", "toString", "status", "sfCartStatus", "sfTotalPrice", "sfCreateTime", "success", "catch", "handleCurrentChange"], "sources": ["src/views/front/Home.vue"], "sourcesContent": ["<template>\r\n    <div class=\"home-container\">\r\n        <!-- 搜索栏 -->\r\n        <div class=\"search-section\">\r\n            <div class=\"search-container\">\r\n                <el-input\r\n                    placeholder=\"搜索您想要的美食...\"\r\n                    v-model=\"name\"\r\n                    class=\"search-input\"\r\n                    clearable\r\n                    size=\"large\"\r\n                    @keyup.enter.native=\"load(1)\"\r\n                >\r\n                    <el-button\r\n                        slot=\"append\"\r\n                        icon=\"el-icon-search\"\r\n                        @click=\"load(1)\"\r\n                        class=\"search-btn\"\r\n                    ></el-button>\r\n                </el-input>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 商品展示区 -->\r\n        <div class=\"content-section\">\r\n            <div class=\"section-header\">\r\n                <h2 class=\"section-title\">精选美食</h2>\r\n                <p class=\"section-subtitle\">为您精心挑选的优质美食</p>\r\n            </div>\r\n            \r\n            <div class=\"goods-grid\">\r\n                <div\r\n                    class=\"goods-card\"\r\n                    v-for=\"item in tableData\"\r\n                    :key=\"item.id\"\r\n                    @click=\"showDetail(item)\"\r\n                >\r\n                    <div class=\"card-image-container\">\r\n                        <el-image\r\n                            :src=\"item.sfImage\"\r\n                            fit=\"cover\"\r\n                            class=\"card-image\"\r\n                        ></el-image>\r\n                        <div class=\"card-overlay\">\r\n                            <el-button\r\n                                type=\"primary\"\r\n                                size=\"small\"\r\n                                circle\r\n                                icon=\"el-icon-view\"\r\n                                class=\"view-btn\"\r\n                            ></el-button>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"card-content\">\r\n                        <h3 class=\"card-title\">{{ item.name }}</h3>\r\n                        <div class=\"card-price\">\r\n                            <span class=\"price-symbol\">¥</span>\r\n                            <span class=\"price-number\">{{ item.sfPrice }}</span>\r\n                        </div>\r\n                        <div class=\"card-actions\">\r\n                            <el-button\r\n                                type=\"primary\"\r\n                                size=\"small\"\r\n                                @click.stop=\"showCartDialog(item)\"\r\n                                class=\"cart-btn\"\r\n                            >\r\n                                <i class=\"el-icon-shopping-cart-2\"></i>\r\n                                加入购物车\r\n                            </el-button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-section\">\r\n            <el-pagination\r\n                background\r\n                @current-change=\"handleCurrentChange\"\r\n                :current-page=\"pageNum\"\r\n                :page-size=\"pageSize\"\r\n                layout=\"prev, pager, next\"\r\n                :total=\"total\"\r\n                :pager-count=\"5\"\r\n                prev-text=\"上一页\"\r\n                next-text=\"下一页\"\r\n                class=\"custom-pagination\"\r\n            >\r\n            </el-pagination>\r\n        </div>\r\n\r\n        <!-- 商品详情弹窗 -->\r\n        <el-dialog\r\n            :visible.sync=\"detailVisible\"\r\n            width=\"70%\"\r\n            top=\"5vh\"\r\n            custom-class=\"detail-dialog\"\r\n            :close-on-click-modal=\"false\"\r\n        >\r\n            <div class=\"detail-container\" v-if=\"currentGoods\">\r\n                <div class=\"detail-left\">\r\n                    <div class=\"detail-image-container\">\r\n                        <el-image\r\n                            :src=\"currentGoods.sfImage\"\r\n                            fit=\"contain\"\r\n                            class=\"detail-image\"\r\n                        ></el-image>\r\n                    </div>\r\n                </div>\r\n                <div class=\"detail-right\">\r\n                    <div class=\"detail-header\">\r\n                        <h2 class=\"detail-title\">{{ currentGoods.name }}</h2>\r\n                        <div class=\"detail-price\">\r\n                            <span class=\"price-symbol\">¥</span>\r\n                            <span class=\"price-number\">{{ currentGoods.sfPrice }}</span>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div class=\"detail-info\">\r\n                        <div class=\"info-card\">\r\n                            <div class=\"info-item\">\r\n                                <i class=\"el-icon-goods info-icon\"></i>\r\n                                <div class=\"info-content\">\r\n                                    <span class=\"info-label\">商品类型</span>\r\n                                    <span class=\"info-value\">{{ currentGoods.foodtyope }}</span>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"info-item\">\r\n                                <i class=\"el-icon-box info-icon\"></i>\r\n                                <div class=\"info-content\">\r\n                                    <span class=\"info-label\">库存状态</span>\r\n                                    <span class=\"info-value\">{{ currentGoods.amount }}件</span>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"info-item\">\r\n                                <i class=\"el-icon-check info-icon\"></i>\r\n                                <div class=\"info-content\">\r\n                                    <span class=\"info-label\">上架状态</span>\r\n                                    <span class=\"info-value\">{{ currentGoods.fstatus }}</span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div class=\"detail-description\">\r\n                        <h3 class=\"desc-title\">商品描述</h3>\r\n                        <p class=\"desc-content\">{{ currentGoods.sfDescription }}</p>\r\n                    </div>\r\n                    \r\n                    <div class=\"detail-actions\">\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            size=\"large\"\r\n                            @click=\"showCartDialog(currentGoods)\"\r\n                            class=\"action-btn cart-action\"\r\n                        >\r\n                            <i class=\"el-icon-shopping-cart-2\"></i>\r\n                            加入购物车\r\n                        </el-button>\r\n                        <el-button\r\n                            type=\"danger\"\r\n                            size=\"large\"\r\n                            @click=\"showBuyDialog(currentGoods)\"\r\n                            class=\"action-btn buy-action\"\r\n                        >\r\n                            <i class=\"el-icon-lightning\"></i>\r\n                            立即购买\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 购物车/购买备注弹窗 -->\r\n        <el-dialog\r\n            :title=\"dialogTitle\"\r\n            :visible.sync=\"orderDialogVisible\"\r\n            width=\"40%\"\r\n            :close-on-click-modal=\"false\"\r\n            custom-class=\"order-dialog\"\r\n        >\r\n            <div class=\"order-form\">\r\n                <el-form :model=\"orderForm\" label-width=\"80px\">\r\n                    <el-form-item label=\"商品名称\">\r\n                        <el-input v-model=\"orderForm.goodsName\" disabled class=\"form-input\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"商品价格\">\r\n                        <el-input v-model=\"orderForm.goodsPrice\" disabled class=\"form-input\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"订单备注\" prop=\"sfRemark\">\r\n                        <el-input\r\n                            type=\"textarea\"\r\n                            v-model=\"orderForm.sfRemark\"\r\n                            placeholder=\"请输入订单备注（可选）\"\r\n                            :rows=\"3\"\r\n                            maxlength=\"200\"\r\n                            show-word-limit>\r\n                        </el-input>\r\n                    </el-form-item>\r\n                </el-form>\r\n            </div>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"orderDialogVisible = false\" class=\"cancel-btn\">取消</el-button>\r\n                <el-button type=\"primary\" @click=\"confirmOrder\" class=\"confirm-btn\">{{ dialogButtonText }}</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"GoodsList\",\r\n    data() {\r\n        return {\r\n            tableData: [],  // 商品数据\r\n            pageNum: 1,     // 当前页码\r\n            pageSize: 12,   // 每页12条\r\n            total: 0,       // 总数\r\n            name: null,     // 搜索关键词\r\n            detailVisible: false, // 详情弹窗显示\r\n            currentGoods: null,   // 当前查看的商品\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'), // 当前登录用户\r\n\r\n            // 订单相关\r\n            orderDialogVisible: false,\r\n            dialogTitle: '',\r\n            dialogButtonText: '',\r\n            orderForm: {\r\n                goodsId: null,\r\n                goodsName: '',\r\n                goodsPrice: 0,\r\n                sfRemark: '',\r\n                actionType: '' // 'cart' or 'buy'\r\n            }\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)\r\n    },\r\n    methods: {\r\n        // 格式化时间为年月日时分秒\r\n        formatDateTime(date) {\r\n            const d = new Date(date);\r\n            const year = d.getFullYear();\r\n            const month = String(d.getMonth() + 1).padStart(2, '0');\r\n            const day = String(d.getDate()).padStart(2, '0');\r\n            const hours = String(d.getHours()).padStart(2, '0');\r\n            const minutes = String(d.getMinutes()).padStart(2, '0');\r\n            const seconds = String(d.getSeconds()).padStart(2, '0');\r\n            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n        },\r\n\r\n        load(pageNum) {  // 加载商品数据\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/foods/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n                    name: this.name,\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list\r\n                    this.total = res.data?.total\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            })\r\n        },\r\n\r\n        showDetail(item) {  // 显示商品详情\r\n            this.currentGoods = item\r\n            this.detailVisible = true\r\n        },\r\n\r\n        // 显示购物车弹窗\r\n        showCartDialog(goods) {\r\n            this.orderForm = {\r\n                goodsId: goods.id,\r\n                goodsName: goods.name,\r\n                goodsPrice: goods.sfPrice,\r\n                sfRemark: '',\r\n                actionType: 'cart'\r\n            }\r\n            this.dialogTitle = '加入购物车'\r\n            this.dialogButtonText = '确认加入'\r\n            this.orderDialogVisible = true\r\n        },\r\n\r\n        // 显示购买弹窗\r\n        showBuyDialog(goods) {\r\n            this.orderForm = {\r\n                goodsId: goods.id,\r\n                goodsName: goods.name,\r\n                goodsPrice: goods.sfPrice,\r\n                sfRemark: '',\r\n                actionType: 'buy'\r\n            }\r\n            this.dialogTitle = '立即购买'\r\n            this.dialogButtonText = '确认购买'\r\n            this.orderDialogVisible = true\r\n        },\r\n\r\n        // 确认订单（购物车或购买）\r\n        confirmOrder() {\r\n            if (this.orderForm.actionType === 'cart') {\r\n                this.addToCart()\r\n            } else {\r\n                this.handleBuy()\r\n            }\r\n        },\r\n\r\n        // 加入购物车\r\n        addToCart() {\r\n            this.$request.post('/dingdan/add', {\r\n                sfUserName: this.user.name || '匿名用户',\r\n                sfUserId: this.user.id || 0,\r\n                sfGoodsId: this.orderForm.goodsId.toString(),\r\n                status: '未付款',\r\n                sfCartStatus: '已加入购物车',\r\n                sfTotalPrice: 0, // 购物车状态下价格为0\r\n                sfRemark: this.orderForm.sfRemark, // 添加备注\r\n                sfCreateTime: this.formatDateTime(new Date()) // 格式化时间\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.$message.success('商品已加入购物车！')\r\n                    this.orderDialogVisible = false\r\n                } else {\r\n                    this.$message.error(res.msg || '操作失败')\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('操作失败，请重试')\r\n            })\r\n        },\r\n\r\n        // 立即购买\r\n        handleBuy() {\r\n            this.$request.post('/dingdan/add', {\r\n                sfUserName: this.user.name || '匿名用户',\r\n                sfUserId: this.user.id || 0,\r\n                sfGoodsId: this.orderForm.goodsId.toString(),\r\n                status: '未出餐', // 立即购买直接设为已付款状态\r\n                sfCartStatus: '', // 购物车状态为空\r\n                sfTotalPrice: this.orderForm.goodsPrice, // 商品价格放入订单价格字段\r\n                sfRemark: this.orderForm.sfRemark, // 添加备注\r\n                sfCreateTime: this.formatDateTime(new Date()) // 格式化时间\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.$message.success('订单创建成功！')\r\n                    this.detailVisible = false\r\n                    this.orderDialogVisible = false\r\n                } else {\r\n                    this.$message.error(res.msg || '下单失败')\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('下单失败，请重试')\r\n            })\r\n        },\r\n\r\n        handleCurrentChange(pageNum) {  // 分页变化\r\n            this.load(pageNum)\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home-container {\r\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n    min-height: 100vh;\r\n}\r\n\r\n/* 搜索区域 */\r\n.search-section {\r\n    padding: 40px 0;\r\n    background: white;\r\n}\r\n\r\n.search-container {\r\n    max-width: 600px;\r\n    margin: 0 auto;\r\n    padding: 0 20px;\r\n}\r\n\r\n.search-input {\r\n    width: 100%;\r\n}\r\n\r\n.search-input >>> .el-input__inner {\r\n    height: 50px;\r\n    border-radius: 25px;\r\n    border: 2px solid #e5e7eb;\r\n    padding-left: 20px;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.search-input >>> .el-input__inner:focus {\r\n    border-color: #3b82f6;\r\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.search-btn {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    color: white;\r\n    border-radius: 0 25px 25px 0;\r\n    padding: 0 20px;\r\n    font-size: 16px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.search-btn:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-section {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 40px 20px;\r\n}\r\n\r\n.section-header {\r\n    text-align: center;\r\n    margin-bottom: 40px;\r\n}\r\n\r\n.section-title {\r\n    font-size: 32px;\r\n    font-weight: 700;\r\n    color: #1e40af;\r\n    margin-bottom: 12px;\r\n}\r\n\r\n.section-subtitle {\r\n    font-size: 16px;\r\n    color: #64748b;\r\n    margin: 0;\r\n}\r\n\r\n/* 商品网格 */\r\n.goods-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 24px;\r\n    margin-bottom: 40px;\r\n}\r\n\r\n.goods-card {\r\n    background: white;\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    cursor: pointer;\r\n    border: 1px solid #f1f5f9;\r\n}\r\n\r\n.goods-card:hover {\r\n    transform: translateY(-8px);\r\n    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);\r\n    border-color: #3b82f6;\r\n}\r\n\r\n.card-image-container {\r\n    position: relative;\r\n    height: 200px;\r\n    overflow: hidden;\r\n}\r\n\r\n.card-image {\r\n    width: 100%;\r\n    height: 100%;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.goods-card:hover .card-image {\r\n    transform: scale(1.05);\r\n}\r\n\r\n.card-overlay {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.5);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    opacity: 0;\r\n    transition: opacity 0.3s ease;\r\n}\r\n\r\n.goods-card:hover .card-overlay {\r\n    opacity: 1;\r\n}\r\n\r\n.view-btn {\r\n    background: white;\r\n    color: #3b82f6;\r\n    border: none;\r\n    width: 50px;\r\n    height: 50px;\r\n    font-size: 20px;\r\n}\r\n\r\n.card-content {\r\n    padding: 20px;\r\n}\r\n\r\n.card-title {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #1e293b;\r\n    margin-bottom: 12px;\r\n    height: 48px;\r\n    overflow: hidden;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    line-height: 1.5;\r\n}\r\n\r\n.card-price {\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.price-symbol {\r\n    color: #3b82f6;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n\r\n.price-number {\r\n    color: #3b82f6;\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n}\r\n\r\n.card-actions {\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n\r\n.cart-btn {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    color: white;\r\n    border-radius: 20px;\r\n    padding: 8px 20px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.cart-btn:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n/* 分页区域 */\r\n.pagination-section {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 40px 0;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li {\r\n    background: white;\r\n    border: 1px solid #e5e7eb;\r\n    border-radius: 8px;\r\n    margin: 0 4px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li:hover {\r\n    border-color: #3b82f6;\r\n    color: #3b82f6;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li.active {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    color: white;\r\n}\r\n\r\n/* 详情弹窗 */\r\n.detail-dialog >>> .el-dialog {\r\n    border-radius: 16px;\r\n    overflow: hidden;\r\n}\r\n\r\n.detail-dialog >>> .el-dialog__header {\r\n    display: none;\r\n}\r\n\r\n.detail-dialog >>> .el-dialog__body {\r\n    padding: 0;\r\n}\r\n\r\n.detail-container {\r\n    display: flex;\r\n    min-height: 500px;\r\n}\r\n\r\n.detail-left {\r\n    flex: 1;\r\n    background: #f8fafc;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 40px;\r\n}\r\n\r\n.detail-image-container {\r\n    width: 100%;\r\n    max-width: 400px;\r\n}\r\n\r\n.detail-image {\r\n    width: 100%;\r\n    height: 400px;\r\n    border-radius: 12px;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.detail-right {\r\n    flex: 1;\r\n    padding: 40px;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.detail-header {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.detail-title {\r\n    font-size: 28px;\r\n    font-weight: 700;\r\n    color: #1e293b;\r\n    margin-bottom: 16px;\r\n    line-height: 1.3;\r\n}\r\n\r\n.detail-price {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.detail-price .price-symbol {\r\n    color: #3b82f6;\r\n    font-size: 24px;\r\n    font-weight: 600;\r\n}\r\n\r\n.detail-price .price-number {\r\n    color: #3b82f6;\r\n    font-size: 36px;\r\n    font-weight: 700;\r\n}\r\n\r\n.detail-info {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.info-card {\r\n    background: #f8fafc;\r\n    border-radius: 12px;\r\n    padding: 20px;\r\n    border: 1px solid #e2e8f0;\r\n}\r\n\r\n.info-item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n}\r\n\r\n.info-item:last-child {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.info-icon {\r\n    width: 40px;\r\n    height: 40px;\r\n    background: #3b82f6;\r\n    color: white;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 16px;\r\n    font-size: 16px;\r\n}\r\n\r\n.info-content {\r\n    flex: 1;\r\n}\r\n\r\n.info-label {\r\n    display: block;\r\n    font-size: 14px;\r\n    color: #64748b;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.info-value {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #1e293b;\r\n}\r\n\r\n.detail-description {\r\n    margin-bottom: 40px;\r\n    flex: 1;\r\n}\r\n\r\n.desc-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #1e293b;\r\n    margin-bottom: 12px;\r\n}\r\n\r\n.desc-content {\r\n    font-size: 15px;\r\n    color: #475569;\r\n    line-height: 1.7;\r\n    margin: 0;\r\n}\r\n\r\n.detail-actions {\r\n    display: flex;\r\n    gap: 16px;\r\n}\r\n\r\n.action-btn {\r\n    flex: 1;\r\n    height: 50px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    border-radius: 12px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.cart-action {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n}\r\n\r\n.cart-action:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.buy-action {\r\n    background: #ef4444;\r\n    border-color: #ef4444;\r\n}\r\n\r\n.buy-action:hover {\r\n    background: #dc2626;\r\n    border-color: #dc2626;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n/* 订单弹窗 */\r\n.order-dialog >>> .el-dialog {\r\n    border-radius: 16px;\r\n}\r\n\r\n.order-dialog >>> .el-dialog__header {\r\n    background: #f8fafc;\r\n    border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.order-form {\r\n    padding: 20px 0;\r\n}\r\n\r\n.form-input >>> .el-input__inner,\r\n.form-textarea >>> .el-textarea__inner {\r\n    border-radius: 8px;\r\n    border: 1px solid #e2e8f0;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.form-input >>> .el-input__inner:focus,\r\n.form-textarea >>> .el-textarea__inner:focus {\r\n    border-color: #3b82f6;\r\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.dialog-footer {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    gap: 12px;\r\n}\r\n\r\n.cancel-btn {\r\n    border-radius: 8px;\r\n    padding: 10px 20px;\r\n}\r\n\r\n.confirm-btn {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    border-radius: 8px;\r\n    padding: 10px 20px;\r\n}\r\n\r\n.confirm-btn:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .banner-title {\r\n        font-size: 32px;\r\n    }\r\n    \r\n    .banner-subtitle {\r\n        font-size: 16px;\r\n    }\r\n    \r\n    .goods-grid {\r\n        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n        gap: 16px;\r\n    }\r\n    \r\n    .detail-container {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .detail-left {\r\n        padding: 20px;\r\n    }\r\n    \r\n    .detail-right {\r\n        padding: 20px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .goods-grid {\r\n        grid-template-columns: 1fr;\r\n    }\r\n    \r\n    .search-container {\r\n        padding: 0 16px;\r\n    }\r\n    \r\n    .content-section {\r\n        padding: 20px 16px;\r\n    }\r\n}\r\n</style>"], "mappings": "AAmNA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAAA;MACAL,IAAA;MAAA;MACAM,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MAAA;;MAEA;MACAC,kBAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,SAAA;QACAC,OAAA;QACAC,SAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACA;IACAC,eAAAC,IAAA;MACA,MAAAC,CAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,MAAAG,IAAA,GAAAF,CAAA,CAAAG,WAAA;MACA,MAAAC,KAAA,GAAAC,MAAA,CAAAL,CAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,MAAAC,GAAA,GAAAH,MAAA,CAAAL,CAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,MAAAG,KAAA,GAAAL,MAAA,CAAAL,CAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,MAAAK,OAAA,GAAAP,MAAA,CAAAL,CAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,MAAAO,OAAA,GAAAT,MAAA,CAAAL,CAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAAL,IAAA,IAAAE,KAAA,IAAAI,GAAA,IAAAE,KAAA,IAAAE,OAAA,IAAAE,OAAA;IACA;IAEAlB,KAAApB,OAAA;MAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAwC,QAAA,CAAAC,GAAA;QACAC,MAAA;UACA1C,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAJ,IAAA,OAAAA;QACA;MACA,GAAA8C,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAA9C,SAAA,GAAA6C,GAAA,CAAA9C,IAAA,EAAAgD,IAAA;UACA,KAAA5C,KAAA,GAAA0C,GAAA,CAAA9C,IAAA,EAAAI,KAAA;QACA;UACA,KAAA6C,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IAEAC,WAAAC,IAAA;MAAA;MACA,KAAA/C,YAAA,GAAA+C,IAAA;MACA,KAAAhD,aAAA;IACA;IAEA;IACAiD,eAAAC,KAAA;MACA,KAAAxC,SAAA;QACAC,OAAA,EAAAuC,KAAA,CAAAC,EAAA;QACAvC,SAAA,EAAAsC,KAAA,CAAAxD,IAAA;QACAmB,UAAA,EAAAqC,KAAA,CAAAE,OAAA;QACAtC,QAAA;QACAC,UAAA;MACA;MACA,KAAAP,WAAA;MACA,KAAAC,gBAAA;MACA,KAAAF,kBAAA;IACA;IAEA;IACA8C,cAAAH,KAAA;MACA,KAAAxC,SAAA;QACAC,OAAA,EAAAuC,KAAA,CAAAC,EAAA;QACAvC,SAAA,EAAAsC,KAAA,CAAAxD,IAAA;QACAmB,UAAA,EAAAqC,KAAA,CAAAE,OAAA;QACAtC,QAAA;QACAC,UAAA;MACA;MACA,KAAAP,WAAA;MACA,KAAAC,gBAAA;MACA,KAAAF,kBAAA;IACA;IAEA;IACA+C,aAAA;MACA,SAAA5C,SAAA,CAAAK,UAAA;QACA,KAAAwC,SAAA;MACA;QACA,KAAAC,SAAA;MACA;IACA;IAEA;IACAD,UAAA;MACA,KAAAlB,QAAA,CAAAoB,IAAA;QACAC,UAAA,OAAAxD,IAAA,CAAAR,IAAA;QACAiE,QAAA,OAAAzD,IAAA,CAAAiD,EAAA;QACAS,SAAA,OAAAlD,SAAA,CAAAC,OAAA,CAAAkD,QAAA;QACAC,MAAA;QACAC,YAAA;QACAC,YAAA;QAAA;QACAlD,QAAA,OAAAJ,SAAA,CAAAI,QAAA;QAAA;QACAmD,YAAA,OAAA9C,cAAA,KAAAG,IAAA;MACA,GAAAkB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAE,QAAA,CAAAsB,OAAA;UACA,KAAA3D,kBAAA;QACA;UACA,KAAAqC,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAqB,KAAA;QACA,KAAAvB,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAW,UAAA;MACA,KAAAnB,QAAA,CAAAoB,IAAA;QACAC,UAAA,OAAAxD,IAAA,CAAAR,IAAA;QACAiE,QAAA,OAAAzD,IAAA,CAAAiD,EAAA;QACAS,SAAA,OAAAlD,SAAA,CAAAC,OAAA,CAAAkD,QAAA;QACAC,MAAA;QAAA;QACAC,YAAA;QAAA;QACAC,YAAA,OAAAtD,SAAA,CAAAG,UAAA;QAAA;QACAC,QAAA,OAAAJ,SAAA,CAAAI,QAAA;QAAA;QACAmD,YAAA,OAAA9C,cAAA,KAAAG,IAAA;MACA,GAAAkB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAE,QAAA,CAAAsB,OAAA;UACA,KAAAlE,aAAA;UACA,KAAAO,kBAAA;QACA;UACA,KAAAqC,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAqB,KAAA;QACA,KAAAvB,QAAA,CAAAC,KAAA;MACA;IACA;IAEAuB,oBAAAvE,OAAA;MAAA;MACA,KAAAoB,IAAA,CAAApB,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}