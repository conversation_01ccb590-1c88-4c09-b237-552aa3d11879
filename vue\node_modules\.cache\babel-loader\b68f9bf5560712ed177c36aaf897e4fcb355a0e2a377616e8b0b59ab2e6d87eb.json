{"ast": null, "code": "export default {\n  name: \"Leave<PERSON><PERSON>\",\n  data() {\n    return {\n      successVisible: false,\n      loading: false,\n      form: {\n        sfUserId: '',\n        sfQuestion: '',\n        reply: '',\n        sfImage: '',\n        sfLeaveTime: '',\n        sfReplyTime: ''\n      },\n      rules: {\n        sfQuestion: [{\n          required: true,\n          message: '请输入评论问题',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  methods: {\n    save() {\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          this.loading = true;\n          this.$request({\n            url: '/leavemess/add',\n            method: 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              this.successVisible = true;\n              this.form = {\n                sfUserId: '',\n                sfQuestion: '',\n                reply: '',\n                sfImage: '',\n                sfLeaveTime: '',\n                sfReplyTime: ''\n              };\n              this.$refs.formRef.resetFields();\n            } else {\n              this.$message.error(res.msg);\n            }\n          }).catch(() => {\n            this.$message.error('请求失败');\n          }).finally(() => {\n            this.loading = false;\n          });\n        }\n      });\n    },\n    handleImageSuccess(response) {\n      if (response.code === '200') {\n        this.form.sfImage = response.data.url || response.data;\n        this.$message.success('图片上传成功');\n      } else {\n        this.$message.error('图片上传失败');\n      }\n    },\n    beforeUpload(file) {\n      const isImage = file.type.startsWith('image/');\n      const isLt2M = file.size / 1024 / 1024 < 2;\n      if (!isImage) {\n        this.$message.error('上传图片只能是 JPG/PNG 格式!');\n      }\n      if (!isLt2M) {\n        this.$message.error('上传图片大小不能超过 2MB!');\n      }\n      return isImage && isLt2M;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "successVisible", "loading", "form", "sfUserId", "sfQuestion", "reply", "sfImage", "sfLeaveTime", "sfReplyTime", "rules", "required", "message", "trigger", "methods", "save", "$refs", "formRef", "validate", "valid", "$request", "url", "method", "then", "res", "code", "resetFields", "$message", "error", "msg", "catch", "finally", "handleImageSuccess", "response", "success", "beforeUpload", "file", "isImage", "type", "startsWith", "isLt2M", "size"], "sources": ["src/views/front/Leavemess.vue"], "sourcesContent": ["<template>\r\n    <div class=\"leavemess-container\">\r\n        <!-- 评论表单区域 -->\r\n        <el-form\r\n            :model=\"form\"\r\n            label-width=\"100px\"\r\n            class=\"form-container\"\r\n            :rules=\"rules\"\r\n            ref=\"formRef\">\r\n            <!-- 问题输入框 -->\r\n            <el-form-item label=\"问题\" prop=\"sfQuestion\">\r\n                <el-input\r\n                    v-model=\"form.sfQuestion\"\r\n                    placeholder=\"请输入评论问题\"\r\n                    type=\"textarea\"\r\n                    :rows=\"10\"\r\n                    class=\"input-field large-input\">\r\n                </el-input>\r\n            </el-form-item>\r\n\r\n            <!-- 图片上传 -->\r\n            <el-form-item label=\"图片\" prop=\"sfImage\">\r\n                <el-upload\r\n                    class=\"avatar-uploader\"\r\n                    :action=\"$baseUrl + '/files/upload'\"\r\n                    :show-file-list=\"false\"\r\n                    :on-success=\"handleImageSuccess\"\r\n                    :before-upload=\"beforeUpload\">\r\n                    <el-button type=\"primary\" class=\"upload-btn\">点击上传图片</el-button>\r\n                </el-upload>\r\n                <!-- 显示上传成功后的图片 -->\r\n                <el-image\r\n                    v-if=\"form.sfImage\"\r\n                    class=\"uploaded-image\"\r\n                    :src=\"form.sfImage\"\r\n                    :preview-src-list=\"[form.sfImage]\">\r\n                </el-image>\r\n            </el-form-item>\r\n\r\n            <!-- 提交按钮 -->\r\n            <div class=\"form-footer\">\r\n                <el-button type=\"primary\" class=\"submit-btn\" @click=\"save\" :loading=\"loading\">提交评论</el-button>\r\n            </div>\r\n        </el-form>\r\n\r\n        <!-- 提交成功提示 -->\r\n        <el-dialog\r\n            :visible.sync=\"successVisible\"\r\n            title=\"评论提交成功\"\r\n            width=\"30%\"\r\n            :close-on-click-modal=\"false\"\r\n            destroy-on-close>\r\n            <p>您的评论已成功提交，我们会尽快处理。</p>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button type=\"primary\" @click=\"successVisible = false\">确认</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Leavemess\",\r\n    data() {\r\n        return {\r\n            successVisible: false,\r\n            loading: false,\r\n            form: {\r\n                sfUserId: '',\r\n                sfQuestion: '',\r\n                reply: '',\r\n                sfImage: '',\r\n                sfLeaveTime: '',\r\n                sfReplyTime: '',\r\n            },\r\n            rules: {\r\n                sfQuestion: [\r\n                    { required: true, message: '请输入评论问题', trigger: 'blur' },\r\n                ],\r\n            },\r\n        }\r\n    },\r\n    methods: {\r\n        save() {\r\n            this.$refs.formRef.validate((valid) => {\r\n                if (valid) {\r\n                    this.loading = true;\r\n                    this.$request({\r\n                        url: '/leavemess/add',\r\n                        method: 'POST',\r\n                        data: this.form\r\n                    }).then(res => {\r\n                        if (res.code === '200') {\r\n                            this.successVisible = true;\r\n                            this.form = {\r\n                                sfUserId: '',\r\n                                sfQuestion: '',\r\n                                reply: '',\r\n                                sfImage: '',\r\n                                sfLeaveTime: '',\r\n                                sfReplyTime: '',\r\n                            }\r\n                            this.$refs.formRef.resetFields();\r\n                        } else {\r\n                            this.$message.error(res.msg);\r\n                        }\r\n                    }).catch(() => {\r\n                        this.$message.error('请求失败');\r\n                    }).finally(() => {\r\n                        this.loading = false;\r\n                    })\r\n                }\r\n            })\r\n        },\r\n        handleImageSuccess(response) {\r\n            if (response.code === '200') {\r\n                this.form.sfImage = response.data.url || response.data;\r\n                this.$message.success('图片上传成功');\r\n            } else {\r\n                this.$message.error('图片上传失败');\r\n            }\r\n        },\r\n        beforeUpload(file) {\r\n            const isImage = file.type.startsWith('image/');\r\n            const isLt2M = file.size / 1024 / 1024 < 2;\r\n\r\n            if (!isImage) {\r\n                this.$message.error('上传图片只能是 JPG/PNG 格式!');\r\n            }\r\n            if (!isLt2M) {\r\n                this.$message.error('上传图片大小不能超过 2MB!');\r\n            }\r\n            return isImage && isLt2M;\r\n        },\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.leavemess-container {\r\n    background-color: #f8f9fa;\r\n    min-height: 100vh;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: flex-start;\r\n    padding: 20px;\r\n    font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\r\n}\r\n\r\n.form-container {\r\n    background: #ffffff;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n    border-radius: 12px;\r\n    width: 100%;\r\n    max-width: 720px;\r\n    padding: 24px;\r\n    transition: box-shadow 0.3s, transform 0.3s;\r\n    border: 1px solid #ebebeb;\r\n}\r\n\r\n.form-container:hover {\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.el-form-item__label {\r\n    color: #333;\r\n    font-weight: 500;\r\n    font-size: 14px;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.input-field {\r\n    border-radius: 8px;\r\n    background: #fdfdfd;\r\n    border: 1px solid #dcdfe6;\r\n    transition: border-color 0.3s, box-shadow 0.3s;\r\n    font-size: 14px;\r\n    padding: 8px 12px;\r\n}\r\n\r\n.input-field:focus {\r\n    border-color: #409eff;\r\n    box-shadow: 0 0 5px rgba(64, 158, 255, 0.4);\r\n    outline: none;\r\n}\r\n\r\n.large-input {\r\n    min-height: 120px;\r\n    resize: none;\r\n    line-height: 1.6;\r\n}\r\n\r\n.upload-btn {\r\n    display: inline-block;\r\n    background-color: #409eff;\r\n    border: 1px solid #409eff;\r\n    color: #fff;\r\n    font-size: 14px;\r\n    border-radius: 6px;\r\n    padding: 8px 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s;\r\n}\r\n\r\n.upload-btn:hover {\r\n    background-color: #66b1ff;\r\n    border-color: #66b1ff;\r\n    box-shadow: 0 2px 6px rgba(102, 177, 255, 0.3);\r\n}\r\n\r\n.uploaded-image {\r\n    display: block;\r\n    margin-top: 10px;\r\n    border-radius: 8px;\r\n    border: 1px solid #ebebeb;\r\n    width: 100px;\r\n    height: 100px;\r\n    object-fit: cover;\r\n    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.submit-btn {\r\n    background: linear-gradient(90deg, #ff7e5f, #feb47b);\r\n    border: none;\r\n    color: #fff;\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    border-radius: 8px;\r\n    padding: 10px 20px;\r\n    cursor: pointer;\r\n    transition: all 0.3s;\r\n    width: 140px;\r\n    margin-left: auto;\r\n    display: block;\r\n}\r\n\r\n.submit-btn:hover {\r\n    background: linear-gradient(90deg, #feb47b, #ff7e5f);\r\n    box-shadow: 0 4px 10px rgba(255, 126, 95, 0.4);\r\n}\r\n\r\n.dialog-footer {\r\n    text-align: right;\r\n    padding-top: 10px;\r\n}\r\n\r\n.form-footer {\r\n    text-align: right;\r\n    margin-top: 20px;\r\n}\r\n</style>\r\n"], "mappings": "AA6DA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,cAAA;MACAC,OAAA;MACAC,IAAA;QACAC,QAAA;QACAC,UAAA;QACAC,KAAA;QACAC,OAAA;QACAC,WAAA;QACAC,WAAA;MACA;MACAC,KAAA;QACAL,UAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA;IACAC,KAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAjB,OAAA;UACA,KAAAkB,QAAA;YACAC,GAAA;YACAC,MAAA;YACAtB,IAAA,OAAAG;UACA,GAAAoB,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA,KAAAxB,cAAA;cACA,KAAAE,IAAA;gBACAC,QAAA;gBACAC,UAAA;gBACAC,KAAA;gBACAC,OAAA;gBACAC,WAAA;gBACAC,WAAA;cACA;cACA,KAAAO,KAAA,CAAAC,OAAA,CAAAS,WAAA;YACA;cACA,KAAAC,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;YACA;UACA,GAAAC,KAAA;YACA,KAAAH,QAAA,CAAAC,KAAA;UACA,GAAAG,OAAA;YACA,KAAA7B,OAAA;UACA;QACA;MACA;IACA;IACA8B,mBAAAC,QAAA;MACA,IAAAA,QAAA,CAAAR,IAAA;QACA,KAAAtB,IAAA,CAAAI,OAAA,GAAA0B,QAAA,CAAAjC,IAAA,CAAAqB,GAAA,IAAAY,QAAA,CAAAjC,IAAA;QACA,KAAA2B,QAAA,CAAAO,OAAA;MACA;QACA,KAAAP,QAAA,CAAAC,KAAA;MACA;IACA;IACAO,aAAAC,IAAA;MACA,MAAAC,OAAA,GAAAD,IAAA,CAAAE,IAAA,CAAAC,UAAA;MACA,MAAAC,MAAA,GAAAJ,IAAA,CAAAK,IAAA;MAEA,KAAAJ,OAAA;QACA,KAAAV,QAAA,CAAAC,KAAA;MACA;MACA,KAAAY,MAAA;QACA,KAAAb,QAAA,CAAAC,KAAA;MACA;MACA,OAAAS,OAAA,IAAAG,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}