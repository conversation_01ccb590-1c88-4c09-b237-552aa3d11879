{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: \"Blogs\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前的页码\n      pageSize: 12,\n      // 每页显示的个数\n      total: 0,\n      title: null,\n      // 搜索标题关键字\n      fromVisible: false,\n      form: {},\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      rules: {},\n      ids: []\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    // 新增博客\n    handleAdd() {\n      this.form = {}; // 清空数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    // 编辑博客\n    handleEdit(row) {\n      this.form = JSON.parse(JSON.stringify(row)); // 深拷贝数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    // 保存操作\n    save() {\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          this.$request({\n            url: this.form.id ? '/blogs/update' : '/blogs/add',\n            method: this.form.id ? 'PUT' : 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              this.$message.success('保存成功');\n              this.load(1);\n              this.fromVisible = false;\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    },\n    // 删除博客\n    del(id) {\n      this.$confirm('您确定删除吗？', '确认删除', {\n        type: \"warning\"\n      }).then(() => {\n        this.$request.delete(`/blogs/delete/${id}`).then(res => {\n          if (res.code === '200') {\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {});\n    },\n    // 批量删除\n    delBatch() {\n      if (!this.ids.length) {\n        this.$message.warning('请选择数据');\n        return;\n      }\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {\n        type: \"warning\"\n      }).then(() => {\n        this.$request.delete('/blogs/delete/batch', {\n          data: this.ids\n        }).then(res => {\n          if (res.code === '200') {\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {});\n    },\n    // 加载博客数据\n    load(pageNum) {\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/blogs/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          title: this.title // 使用title作为查询参数\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list;\n          this.total = res.data?.total;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    // 重置查询条件\n    reset() {\n      this.title = null;\n      this.load(1);\n    },\n    // 分页处理\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    },\n    // 浏览次数增加\n    incrementViews(id) {\n      this.$request.put(`/blogs/incrementViews/${id}`).then(res => {\n        if (res.code === '200') {\n          this.load(this.pageNum);\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    // 进入博客详情\n    goToDetail(id) {\n      this.incrementViews(id);\n      this.$router.push({\n        name: 'BlogsDetails',\n        query: {\n          id\n        }\n      });\n    },\n    // 图片上传成功回调\n    handleAvatarSuccess(response, file, fileList) {\n      if (response.code === '200') {\n        this.form.blogimg = response.data;\n      } else {\n        this.$message.error(response.msg);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "title", "fromVisible", "form", "user", "JSON", "parse", "localStorage", "getItem", "rules", "ids", "created", "load", "methods", "handleAdd", "handleEdit", "row", "stringify", "save", "$refs", "formRef", "validate", "valid", "$request", "url", "id", "method", "then", "res", "code", "$message", "success", "error", "msg", "del", "$confirm", "type", "delete", "catch", "delBatch", "length", "warning", "get", "params", "list", "reset", "handleCurrentChange", "incrementViews", "put", "goToDetail", "$router", "push", "query", "handleAvatarSuccess", "response", "file", "fileList", "blogimg"], "sources": ["src/views/front/Blogs.vue"], "sourcesContent": ["<template>\r\n    <div class=\"blogs-container\">\r\n        <!-- 搜索框 -->\r\n        <div class=\"search-bar\">\r\n            <el-input\r\n                placeholder=\"请输入博客标题查询\"\r\n                style=\"width: 400px; height: 50px;\"\r\n                v-model=\"title\"\r\n                size=\"large\"\r\n                class=\"search-input\"\r\n                @keyup.enter.native=\"load(1)\"\r\n            ></el-input>\r\n            <el-button\r\n                type=\"primary\"\r\n                style=\"height: 50px; width: 100px; border-radius: 25px; font-size: 16px;\"\r\n                size=\"large\"\r\n                @click=\"load(1)\"\r\n            >\r\n                查询\r\n            </el-button>\r\n            <el-button\r\n                type=\"default\"\r\n                style=\"height: 50px; width: 100px; border-radius: 25px; font-size: 16px;\"\r\n                size=\"large\"\r\n                @click=\"reset\"\r\n            >\r\n                重置\r\n            </el-button>\r\n        </div>\r\n\r\n        <!-- 网格布局 -->\r\n        <div class=\"grid-container\">\r\n            <el-row :gutter=\"30\">\r\n                <el-col v-for=\"(item, index) in tableData\" :key=\"index\" :span=\"6\">\r\n                    <el-card :body-style=\"{ padding: '20px' }\" class=\"blog-card\">\r\n                        <div class=\"card-content\">\r\n                            <el-image\r\n                                class=\"blog-image\"\r\n                                :src=\"item.blogimg\"\r\n                                fit=\"cover\"\r\n                                @click=\"goToDetail(item.id)\"\r\n                                preview\r\n                            ></el-image>\r\n                            <div\r\n                                class=\"card-title\"\r\n                                @click=\"goToDetail(item.id)\"\r\n                            >\r\n                                {{ item.title }}\r\n                            </div>\r\n                            <div class=\"card-info\">\r\n                                <span>{{ item.createdat }}</span>\r\n                                &nbsp;|&nbsp;\r\n                                <span>{{ item.views }} 次浏览</span>\r\n                            </div>\r\n                        </div>\r\n                    </el-card>\r\n                </el-col>\r\n            </el-row>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n            <el-pagination\r\n                background\r\n                @current-change=\"handleCurrentChange\"\r\n                :current-page=\"pageNum\"\r\n                :page-sizes=\"[12]\"\r\n                :page-size=\"pageSize\"\r\n                layout=\"total, prev, pager, next\"\r\n                :total=\"total\"\r\n                class=\"pagination\"\r\n            ></el-pagination>\r\n        </div>\r\n\r\n        <!-- 新增/编辑博客弹窗 -->\r\n        <el-dialog\r\n            title=\"博客表\"\r\n            :visible.sync=\"fromVisible\"\r\n            width=\"50%\"\r\n            :close-on-click-modal=\"false\"\r\n            destroy-on-close\r\n        >\r\n            <el-form\r\n                :model=\"form\"\r\n                label-width=\"120px\"\r\n                style=\"padding-right: 60px\"\r\n                :rules=\"rules\"\r\n                ref=\"formRef\"\r\n            >\r\n                <el-form-item label=\"博客标题\" prop=\"title\">\r\n                    <el-input v-model=\"form.title\" placeholder=\"博客标题\" size=\"large\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"博客内容\" prop=\"content\">\r\n                    <el-input v-model=\"form.content\" placeholder=\"博客内容\" size=\"large\" type=\"textarea\" :rows=\"4\"></el-input>\r\n                </el-form-item>\r\n\r\n                <!-- 图片上传 -->\r\n                <el-form-item label=\"博客图片\" prop=\"blogimg\">\r\n                    <el-upload\r\n                        class=\"avatar-uploader\"\r\n                        :action=\"$baseUrl + '/files/upload'\"\r\n                        :headers=\"{ token: user.token }\"\r\n                        list-type=\"picture\"\r\n                        :on-success=\"handleAvatarSuccess\"\r\n                    >\r\n                        <el-button type=\"primary\" size=\"large\">上传博客图片</el-button>\r\n                    </el-upload>\r\n                    <el-image\r\n                        v-if=\"form.blogimg\"\r\n                        style=\"width: 200px; height: 200px; margin-top: 15px; border-radius: 10px;\"\r\n                        :src=\"form.blogimg\"\r\n                        fit=\"cover\"\r\n                        preview\r\n                    ></el-image>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"博客状态\" prop=\"status\">\r\n                    <el-input v-model=\"form.status\" placeholder=\"博客状态\" size=\"large\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"类别名字\" prop=\"categoryname\">\r\n                    <el-input v-model=\"form.categoryname\" placeholder=\"类别名字\" size=\"large\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"博客标签\" prop=\"tags\">\r\n                    <el-input v-model=\"form.tags\" placeholder=\"博客标签\" size=\"large\"></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"浏览次数\" prop=\"views\">\r\n                    <el-input v-model=\"form.views\" placeholder=\"浏览次数\" size=\"large\"></el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"fromVisible = false\" size=\"large\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"save\" size=\"large\">确 定</el-button>\r\n            </div>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \"Blogs\",\r\n    data() {\r\n        return {\r\n            tableData: [],   // 所有的数据\r\n            pageNum: 1,      // 当前的页码\r\n            pageSize: 12,    // 每页显示的个数\r\n            total: 0,\r\n            title: null,     // 搜索标题关键字\r\n            fromVisible: false,\r\n            form: {},\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n            rules: {},\r\n            ids: []\r\n        };\r\n    },\r\n    created() {\r\n        this.load(1);\r\n    },\r\n    methods: {\r\n        // 新增博客\r\n        handleAdd() {\r\n            this.form = {};  // 清空数据\r\n            this.fromVisible = true;  // 打开弹窗\r\n        },\r\n        // 编辑博客\r\n        handleEdit(row) {\r\n            this.form = JSON.parse(JSON.stringify(row));  // 深拷贝数据\r\n            this.fromVisible = true;  // 打开弹窗\r\n        },\r\n        // 保存操作\r\n        save() {\r\n            this.$refs.formRef.validate((valid) => {\r\n                if (valid) {\r\n                    this.$request({\r\n                        url: this.form.id ? '/blogs/update' : '/blogs/add',\r\n                        method: this.form.id ? 'PUT' : 'POST',\r\n                        data: this.form\r\n                    }).then(res => {\r\n                        if (res.code === '200') {\r\n                            this.$message.success('保存成功');\r\n                            this.load(1);\r\n                            this.fromVisible = false;\r\n                        } else {\r\n                            this.$message.error(res.msg);\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        },\r\n        // 删除博客\r\n        del(id) {\r\n            this.$confirm('您确定删除吗？', '确认删除', { type: \"warning\" })\r\n                .then(() => {\r\n                    this.$request.delete(`/blogs/delete/${id}`)\r\n                        .then(res => {\r\n                            if (res.code === '200') {\r\n                                this.$message.success('操作成功');\r\n                                this.load(1);\r\n                            } else {\r\n                                this.$message.error(res.msg);\r\n                            }\r\n                        });\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        // 批量删除\r\n        delBatch() {\r\n            if (!this.ids.length) {\r\n                this.$message.warning('请选择数据');\r\n                return;\r\n            }\r\n            this.$confirm('您确定批量删除这些数据吗？', '确认删除', { type: \"warning\" })\r\n                .then(() => {\r\n                    this.$request.delete('/blogs/delete/batch', { data: this.ids })\r\n                        .then(res => {\r\n                            if (res.code === '200') {\r\n                                this.$message.success('操作成功');\r\n                                this.load(1);\r\n                            } else {\r\n                                this.$message.error(res.msg);\r\n                            }\r\n                        });\r\n                })\r\n                .catch(() => {});\r\n        },\r\n        // 加载博客数据\r\n        load(pageNum) {\r\n            if (pageNum) this.pageNum = pageNum;\r\n            this.$request.get('/blogs/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n                    title: this.title,  // 使用title作为查询参数\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list;\r\n                    this.total = res.data?.total;\r\n                } else {\r\n                    this.$message.error(res.msg);\r\n                }\r\n            });\r\n        },\r\n        // 重置查询条件\r\n        reset() {\r\n            this.title = null;\r\n            this.load(1);\r\n        },\r\n        // 分页处理\r\n        handleCurrentChange(pageNum) {\r\n            this.load(pageNum);\r\n        },\r\n        // 浏览次数增加\r\n        incrementViews(id) {\r\n            this.$request.put(`/blogs/incrementViews/${id}`).then(res => {\r\n                if (res.code === '200') {\r\n                    this.load(this.pageNum);\r\n                } else {\r\n                    this.$message.error(res.msg);\r\n                }\r\n            });\r\n        },\r\n        // 进入博客详情\r\n        goToDetail(id) {\r\n            this.incrementViews(id);\r\n            this.$router.push({ name: 'BlogsDetails', query: { id } });\r\n        },\r\n        // 图片上传成功回调\r\n        handleAvatarSuccess(response, file, fileList) {\r\n            if (response.code === '200') {\r\n                this.form.blogimg = response.data;\r\n            } else {\r\n                this.$message.error(response.msg);\r\n            }\r\n        },\r\n    }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.blogs-container {\r\n    margin: 30px auto;\r\n    max-width: 1400px;\r\n    background-color: #fff;\r\n    border-radius: 12px;\r\n    padding: 30px;\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.search-bar {\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.search-input {\r\n    margin-right: 15px;\r\n    border: 1px solid #dcdfe6;\r\n    border-radius: 25px;\r\n    font-size: 16px;\r\n    padding: 0 20px;\r\n}\r\n\r\n.el-button {\r\n    border-radius: 25px;\r\n    font-size: 16px;\r\n    padding: 0 25px;\r\n    height: 50px;\r\n    margin-left: 15px;\r\n}\r\n\r\n.grid-container {\r\n    margin-top: 30px;\r\n}\r\n\r\n.blog-card {\r\n    margin-bottom: 30px;\r\n    transition: all 0.3s;\r\n    border-radius: 12px;\r\n}\r\n\r\n.blog-card:hover {\r\n    transform: translateY(-8px);\r\n    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-content {\r\n    text-align: center;\r\n}\r\n\r\n.blog-image {\r\n    width: 100%;\r\n    height: 220px;\r\n    object-fit: cover;\r\n    border-radius: 10px;\r\n    cursor: pointer;\r\n}\r\n\r\n.card-title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin: 15px 0;\r\n    cursor: pointer;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n.card-info {\r\n    font-size: 14px;\r\n    color: #999;\r\n    margin-top: 10px;\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n\r\n.pagination-container {\r\n    margin-top: 40px;\r\n    text-align: center;\r\n}\r\n\r\n.pagination {\r\n    display: inline-block;\r\n    margin-top: 20px;\r\n}\r\n\r\n.el-pagination__item.is-active {\r\n    background-color: #409eff;\r\n    color: white;\r\n    border-radius: 25px;\r\n    font-size: 16px;\r\n    padding: 0 15px;\r\n    height: 40px;\r\n    line-height: 40px;\r\n}\r\n\r\n.el-pagination__item {\r\n    font-size: 16px;\r\n    padding: 0 15px;\r\n    height: 40px;\r\n    line-height: 40px;\r\n    margin: 0 5px;\r\n    border-radius: 25px;\r\n}\r\n\r\n.el-pagination__item:hover {\r\n    background-color: #e0f7ff;\r\n    border-radius: 25px;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n    width: 120px;\r\n}\r\n</style>"], "mappings": ";AA0IA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MACAC,KAAA;MAAA;MACAC,WAAA;MACAC,IAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,KAAA;MACAC,GAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACA;IACAC,UAAA;MACA,KAAAX,IAAA;MACA,KAAAD,WAAA;IACA;IACA;IACAa,WAAAC,GAAA;MACA,KAAAb,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAY,SAAA,CAAAD,GAAA;MACA,KAAAd,WAAA;IACA;IACA;IACAgB,KAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,QAAA;YACAC,GAAA,OAAArB,IAAA,CAAAsB,EAAA;YACAC,MAAA,OAAAvB,IAAA,CAAAsB,EAAA;YACA7B,IAAA,OAAAO;UACA,GAAAwB,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA,KAAAnB,IAAA;cACA,KAAAV,WAAA;YACA;cACA,KAAA4B,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC,IAAAT,EAAA;MACA,KAAAU,QAAA;QAAAC,IAAA;MAAA,GACAT,IAAA;QACA,KAAAJ,QAAA,CAAAc,MAAA,kBAAAZ,EAAA,IACAE,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAnB,IAAA;UACA;YACA,KAAAkB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GACAK,KAAA;IACA;IACA;IACAC,SAAA;MACA,UAAA7B,GAAA,CAAA8B,MAAA;QACA,KAAAV,QAAA,CAAAW,OAAA;QACA;MACA;MACA,KAAAN,QAAA;QAAAC,IAAA;MAAA,GACAT,IAAA;QACA,KAAAJ,QAAA,CAAAc,MAAA;UAAAzC,IAAA,OAAAc;QAAA,GACAiB,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAnB,IAAA;UACA;YACA,KAAAkB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GACAK,KAAA;IACA;IACA;IACA1B,KAAAd,OAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAyB,QAAA,CAAAmB,GAAA;QACAC,MAAA;UACA7C,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAE,KAAA,OAAAA,KAAA;QACA;MACA,GAAA0B,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAhC,SAAA,GAAA+B,GAAA,CAAAhC,IAAA,EAAAgD,IAAA;UACA,KAAA5C,KAAA,GAAA4B,GAAA,CAAAhC,IAAA,EAAAI,KAAA;QACA;UACA,KAAA8B,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IACA;IACAY,MAAA;MACA,KAAA5C,KAAA;MACA,KAAAW,IAAA;IACA;IACA;IACAkC,oBAAAhD,OAAA;MACA,KAAAc,IAAA,CAAAd,OAAA;IACA;IACA;IACAiD,eAAAtB,EAAA;MACA,KAAAF,QAAA,CAAAyB,GAAA,0BAAAvB,EAAA,IAAAE,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAjB,IAAA,MAAAd,OAAA;QACA;UACA,KAAAgC,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IACA;IACAgB,WAAAxB,EAAA;MACA,KAAAsB,cAAA,CAAAtB,EAAA;MACA,KAAAyB,OAAA,CAAAC,IAAA;QAAAxD,IAAA;QAAAyD,KAAA;UAAA3B;QAAA;MAAA;IACA;IACA;IACA4B,oBAAAC,QAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,IAAAF,QAAA,CAAAzB,IAAA;QACA,KAAA1B,IAAA,CAAAsD,OAAA,GAAAH,QAAA,CAAA1D,IAAA;MACA;QACA,KAAAkC,QAAA,CAAAE,KAAA,CAAAsB,QAAA,CAAArB,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}