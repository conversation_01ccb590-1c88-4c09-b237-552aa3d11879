{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parsePercent, linearMap } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { normalizeArcAngles } from 'zrender/lib/core/PathProxy.js';\nimport { makeInner } from '../../util/model.js';\nvar PI2 = Math.PI * 2;\nvar RADIAN = Math.PI / 180;\nfunction getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nexport function getBasicPieLayout(seriesModel, api) {\n  var viewRect = getViewRect(seriesModel, api);\n  // center can be string or number when coordinateSystem is specified\n  var center = seriesModel.get('center');\n  var radius = seriesModel.get('radius');\n  if (!zrUtil.isArray(radius)) {\n    radius = [0, radius];\n  }\n  var width = parsePercent(viewRect.width, api.getWidth());\n  var height = parsePercent(viewRect.height, api.getHeight());\n  var size = Math.min(width, height);\n  var r0 = parsePercent(radius[0], size / 2);\n  var r = parsePercent(radius[1], size / 2);\n  var cx;\n  var cy;\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys) {\n    // percentage is not allowed when coordinate system is specified\n    var point = coordSys.dataToPoint(center);\n    cx = point[0] || 0;\n    cy = point[1] || 0;\n  } else {\n    if (!zrUtil.isArray(center)) {\n      center = [center, center];\n    }\n    cx = parsePercent(center[0], width) + viewRect.x;\n    cy = parsePercent(center[1], height) + viewRect.y;\n  }\n  return {\n    cx: cx,\n    cy: cy,\n    r0: r0,\n    r: r\n  };\n}\nexport default function pieLayout(seriesType, ecModel, api) {\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var viewRect = getViewRect(seriesModel, api);\n    var _a = getBasicPieLayout(seriesModel, api),\n      cx = _a.cx,\n      cy = _a.cy,\n      r = _a.r,\n      r0 = _a.r0;\n    var startAngle = -seriesModel.get('startAngle') * RADIAN;\n    var endAngle = seriesModel.get('endAngle');\n    var padAngle = seriesModel.get('padAngle') * RADIAN;\n    endAngle = endAngle === 'auto' ? startAngle - PI2 : -endAngle * RADIAN;\n    var minAngle = seriesModel.get('minAngle') * RADIAN;\n    var minAndPadAngle = minAngle + padAngle;\n    var validDataCount = 0;\n    data.each(valueDim, function (value) {\n      !isNaN(value) && validDataCount++;\n    });\n    var sum = data.getSum(valueDim);\n    // Sum may be 0\n    var unitRadian = Math.PI / (sum || validDataCount) * 2;\n    var clockwise = seriesModel.get('clockwise');\n    var roseType = seriesModel.get('roseType');\n    var stillShowZeroSum = seriesModel.get('stillShowZeroSum');\n    // [0...max]\n    var extent = data.getDataExtent(valueDim);\n    extent[0] = 0;\n    var dir = clockwise ? 1 : -1;\n    var angles = [startAngle, endAngle];\n    var halfPadAngle = dir * padAngle / 2;\n    normalizeArcAngles(angles, !clockwise);\n    startAngle = angles[0], endAngle = angles[1];\n    var layoutData = getSeriesLayoutData(seriesModel);\n    layoutData.startAngle = startAngle;\n    layoutData.endAngle = endAngle;\n    layoutData.clockwise = clockwise;\n    var angleRange = Math.abs(endAngle - startAngle);\n    // In the case some sector angle is smaller than minAngle\n    var restAngle = angleRange;\n    var valueSumLargerThanMinAngle = 0;\n    var currentAngle = startAngle;\n    data.setLayout({\n      viewRect: viewRect,\n      r: r\n    });\n    data.each(valueDim, function (value, idx) {\n      var angle;\n      if (isNaN(value)) {\n        data.setItemLayout(idx, {\n          angle: NaN,\n          startAngle: NaN,\n          endAngle: NaN,\n          clockwise: clockwise,\n          cx: cx,\n          cy: cy,\n          r0: r0,\n          r: roseType ? NaN : r\n        });\n        return;\n      }\n      // FIXME 兼容 2.0 但是 roseType 是 area 的时候才是这样？\n      if (roseType !== 'area') {\n        angle = sum === 0 && stillShowZeroSum ? unitRadian : value * unitRadian;\n      } else {\n        angle = angleRange / validDataCount;\n      }\n      if (angle < minAndPadAngle) {\n        angle = minAndPadAngle;\n        restAngle -= minAndPadAngle;\n      } else {\n        valueSumLargerThanMinAngle += value;\n      }\n      var endAngle = currentAngle + dir * angle;\n      // calculate display angle\n      var actualStartAngle = 0;\n      var actualEndAngle = 0;\n      if (padAngle > angle) {\n        actualStartAngle = currentAngle + dir * angle / 2;\n        actualEndAngle = actualStartAngle;\n      } else {\n        actualStartAngle = currentAngle + halfPadAngle;\n        actualEndAngle = endAngle - halfPadAngle;\n      }\n      data.setItemLayout(idx, {\n        angle: angle,\n        startAngle: actualStartAngle,\n        endAngle: actualEndAngle,\n        clockwise: clockwise,\n        cx: cx,\n        cy: cy,\n        r0: r0,\n        r: roseType ? linearMap(value, extent, [r0, r]) : r\n      });\n      currentAngle = endAngle;\n    });\n    // Some sector is constrained by minAngle and padAngle\n    // Rest sectors needs recalculate angle\n    if (restAngle < PI2 && validDataCount) {\n      // Average the angle if rest angle is not enough after all angles is\n      // Constrained by minAngle and padAngle\n      if (restAngle <= 1e-3) {\n        var angle_1 = angleRange / validDataCount;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_1 = data.getItemLayout(idx);\n            layout_1.angle = angle_1;\n            var actualStartAngle = 0;\n            var actualEndAngle = 0;\n            if (angle_1 < padAngle) {\n              actualStartAngle = startAngle + dir * (idx + 1 / 2) * angle_1;\n              actualEndAngle = actualStartAngle;\n            } else {\n              actualStartAngle = startAngle + dir * idx * angle_1 + halfPadAngle;\n              actualEndAngle = startAngle + dir * (idx + 1) * angle_1 - halfPadAngle;\n            }\n            layout_1.startAngle = actualStartAngle;\n            layout_1.endAngle = actualEndAngle;\n          }\n        });\n      } else {\n        unitRadian = restAngle / valueSumLargerThanMinAngle;\n        currentAngle = startAngle;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_2 = data.getItemLayout(idx);\n            var angle = layout_2.angle === minAndPadAngle ? minAndPadAngle : value * unitRadian;\n            var actualStartAngle = 0;\n            var actualEndAngle = 0;\n            if (angle < padAngle) {\n              actualStartAngle = currentAngle + dir * angle / 2;\n              actualEndAngle = actualStartAngle;\n            } else {\n              actualStartAngle = currentAngle + halfPadAngle;\n              actualEndAngle = currentAngle + dir * angle - halfPadAngle;\n            }\n            layout_2.startAngle = actualStartAngle;\n            layout_2.endAngle = actualEndAngle;\n            currentAngle += dir * angle;\n          }\n        });\n      }\n    }\n  });\n}\nexport var getSeriesLayoutData = makeInner();", "map": {"version": 3, "names": ["parsePercent", "linearMap", "layout", "zrUtil", "normalizeArcAngles", "makeInner", "PI2", "Math", "PI", "RADIAN", "getViewRect", "seriesModel", "api", "getLayoutRect", "getBoxLayoutParams", "width", "getWidth", "height", "getHeight", "getBasicPieLayout", "viewRect", "center", "get", "radius", "isArray", "size", "min", "r0", "r", "cx", "cy", "coordSys", "coordinateSystem", "point", "dataToPoint", "x", "y", "pieLayout", "seriesType", "ecModel", "eachSeriesByType", "data", "getData", "valueDim", "mapDimension", "_a", "startAngle", "endAngle", "padAngle", "minAngle", "minAndPadAngle", "validDataCount", "each", "value", "isNaN", "sum", "getSum", "unitRadian", "clockwise", "roseType", "stillShowZeroSum", "extent", "getDataExtent", "dir", "angles", "halfPadAngle", "layoutData", "getSeriesLayoutData", "angleRange", "abs", "restAngle", "valueSumLargerThanMinAngle", "currentAngle", "setLayout", "idx", "angle", "setItemLayout", "NaN", "actualStartAngle", "actualEndAngle", "angle_1", "layout_1", "getItemLayout", "layout_2"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/chart/pie/pieLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parsePercent, linearMap } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { normalizeArcAngles } from 'zrender/lib/core/PathProxy.js';\nimport { makeInner } from '../../util/model.js';\nvar PI2 = Math.PI * 2;\nvar RADIAN = Math.PI / 180;\nfunction getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nexport function getBasicPieLayout(seriesModel, api) {\n  var viewRect = getViewRect(seriesModel, api);\n  // center can be string or number when coordinateSystem is specified\n  var center = seriesModel.get('center');\n  var radius = seriesModel.get('radius');\n  if (!zrUtil.isArray(radius)) {\n    radius = [0, radius];\n  }\n  var width = parsePercent(viewRect.width, api.getWidth());\n  var height = parsePercent(viewRect.height, api.getHeight());\n  var size = Math.min(width, height);\n  var r0 = parsePercent(radius[0], size / 2);\n  var r = parsePercent(radius[1], size / 2);\n  var cx;\n  var cy;\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys) {\n    // percentage is not allowed when coordinate system is specified\n    var point = coordSys.dataToPoint(center);\n    cx = point[0] || 0;\n    cy = point[1] || 0;\n  } else {\n    if (!zrUtil.isArray(center)) {\n      center = [center, center];\n    }\n    cx = parsePercent(center[0], width) + viewRect.x;\n    cy = parsePercent(center[1], height) + viewRect.y;\n  }\n  return {\n    cx: cx,\n    cy: cy,\n    r0: r0,\n    r: r\n  };\n}\nexport default function pieLayout(seriesType, ecModel, api) {\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var viewRect = getViewRect(seriesModel, api);\n    var _a = getBasicPieLayout(seriesModel, api),\n      cx = _a.cx,\n      cy = _a.cy,\n      r = _a.r,\n      r0 = _a.r0;\n    var startAngle = -seriesModel.get('startAngle') * RADIAN;\n    var endAngle = seriesModel.get('endAngle');\n    var padAngle = seriesModel.get('padAngle') * RADIAN;\n    endAngle = endAngle === 'auto' ? startAngle - PI2 : -endAngle * RADIAN;\n    var minAngle = seriesModel.get('minAngle') * RADIAN;\n    var minAndPadAngle = minAngle + padAngle;\n    var validDataCount = 0;\n    data.each(valueDim, function (value) {\n      !isNaN(value) && validDataCount++;\n    });\n    var sum = data.getSum(valueDim);\n    // Sum may be 0\n    var unitRadian = Math.PI / (sum || validDataCount) * 2;\n    var clockwise = seriesModel.get('clockwise');\n    var roseType = seriesModel.get('roseType');\n    var stillShowZeroSum = seriesModel.get('stillShowZeroSum');\n    // [0...max]\n    var extent = data.getDataExtent(valueDim);\n    extent[0] = 0;\n    var dir = clockwise ? 1 : -1;\n    var angles = [startAngle, endAngle];\n    var halfPadAngle = dir * padAngle / 2;\n    normalizeArcAngles(angles, !clockwise);\n    startAngle = angles[0], endAngle = angles[1];\n    var layoutData = getSeriesLayoutData(seriesModel);\n    layoutData.startAngle = startAngle;\n    layoutData.endAngle = endAngle;\n    layoutData.clockwise = clockwise;\n    var angleRange = Math.abs(endAngle - startAngle);\n    // In the case some sector angle is smaller than minAngle\n    var restAngle = angleRange;\n    var valueSumLargerThanMinAngle = 0;\n    var currentAngle = startAngle;\n    data.setLayout({\n      viewRect: viewRect,\n      r: r\n    });\n    data.each(valueDim, function (value, idx) {\n      var angle;\n      if (isNaN(value)) {\n        data.setItemLayout(idx, {\n          angle: NaN,\n          startAngle: NaN,\n          endAngle: NaN,\n          clockwise: clockwise,\n          cx: cx,\n          cy: cy,\n          r0: r0,\n          r: roseType ? NaN : r\n        });\n        return;\n      }\n      // FIXME 兼容 2.0 但是 roseType 是 area 的时候才是这样？\n      if (roseType !== 'area') {\n        angle = sum === 0 && stillShowZeroSum ? unitRadian : value * unitRadian;\n      } else {\n        angle = angleRange / validDataCount;\n      }\n      if (angle < minAndPadAngle) {\n        angle = minAndPadAngle;\n        restAngle -= minAndPadAngle;\n      } else {\n        valueSumLargerThanMinAngle += value;\n      }\n      var endAngle = currentAngle + dir * angle;\n      // calculate display angle\n      var actualStartAngle = 0;\n      var actualEndAngle = 0;\n      if (padAngle > angle) {\n        actualStartAngle = currentAngle + dir * angle / 2;\n        actualEndAngle = actualStartAngle;\n      } else {\n        actualStartAngle = currentAngle + halfPadAngle;\n        actualEndAngle = endAngle - halfPadAngle;\n      }\n      data.setItemLayout(idx, {\n        angle: angle,\n        startAngle: actualStartAngle,\n        endAngle: actualEndAngle,\n        clockwise: clockwise,\n        cx: cx,\n        cy: cy,\n        r0: r0,\n        r: roseType ? linearMap(value, extent, [r0, r]) : r\n      });\n      currentAngle = endAngle;\n    });\n    // Some sector is constrained by minAngle and padAngle\n    // Rest sectors needs recalculate angle\n    if (restAngle < PI2 && validDataCount) {\n      // Average the angle if rest angle is not enough after all angles is\n      // Constrained by minAngle and padAngle\n      if (restAngle <= 1e-3) {\n        var angle_1 = angleRange / validDataCount;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_1 = data.getItemLayout(idx);\n            layout_1.angle = angle_1;\n            var actualStartAngle = 0;\n            var actualEndAngle = 0;\n            if (angle_1 < padAngle) {\n              actualStartAngle = startAngle + dir * (idx + 1 / 2) * angle_1;\n              actualEndAngle = actualStartAngle;\n            } else {\n              actualStartAngle = startAngle + dir * idx * angle_1 + halfPadAngle;\n              actualEndAngle = startAngle + dir * (idx + 1) * angle_1 - halfPadAngle;\n            }\n            layout_1.startAngle = actualStartAngle;\n            layout_1.endAngle = actualEndAngle;\n          }\n        });\n      } else {\n        unitRadian = restAngle / valueSumLargerThanMinAngle;\n        currentAngle = startAngle;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_2 = data.getItemLayout(idx);\n            var angle = layout_2.angle === minAndPadAngle ? minAndPadAngle : value * unitRadian;\n            var actualStartAngle = 0;\n            var actualEndAngle = 0;\n            if (angle < padAngle) {\n              actualStartAngle = currentAngle + dir * angle / 2;\n              actualEndAngle = actualStartAngle;\n            } else {\n              actualStartAngle = currentAngle + halfPadAngle;\n              actualEndAngle = currentAngle + dir * angle - halfPadAngle;\n            }\n            layout_2.startAngle = actualStartAngle;\n            layout_2.endAngle = actualEndAngle;\n            currentAngle += dir * angle;\n          }\n        });\n      }\n    }\n  });\n}\nexport var getSeriesLayoutData = makeInner();"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAY,EAAEC,SAAS,QAAQ,sBAAsB;AAC9D,OAAO,KAAKC,MAAM,MAAM,sBAAsB;AAC9C,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,GAAG,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;AACrB,IAAIC,MAAM,GAAGF,IAAI,CAACC,EAAE,GAAG,GAAG;AAC1B,SAASE,WAAWA,CAACC,WAAW,EAAEC,GAAG,EAAE;EACrC,OAAOV,MAAM,CAACW,aAAa,CAACF,WAAW,CAACG,kBAAkB,CAAC,CAAC,EAAE;IAC5DC,KAAK,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC;IACrBC,MAAM,EAAEL,GAAG,CAACM,SAAS,CAAC;EACxB,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,iBAAiBA,CAACR,WAAW,EAAEC,GAAG,EAAE;EAClD,IAAIQ,QAAQ,GAAGV,WAAW,CAACC,WAAW,EAAEC,GAAG,CAAC;EAC5C;EACA,IAAIS,MAAM,GAAGV,WAAW,CAACW,GAAG,CAAC,QAAQ,CAAC;EACtC,IAAIC,MAAM,GAAGZ,WAAW,CAACW,GAAG,CAAC,QAAQ,CAAC;EACtC,IAAI,CAACnB,MAAM,CAACqB,OAAO,CAACD,MAAM,CAAC,EAAE;IAC3BA,MAAM,GAAG,CAAC,CAAC,EAAEA,MAAM,CAAC;EACtB;EACA,IAAIR,KAAK,GAAGf,YAAY,CAACoB,QAAQ,CAACL,KAAK,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC,CAAC;EACxD,IAAIC,MAAM,GAAGjB,YAAY,CAACoB,QAAQ,CAACH,MAAM,EAAEL,GAAG,CAACM,SAAS,CAAC,CAAC,CAAC;EAC3D,IAAIO,IAAI,GAAGlB,IAAI,CAACmB,GAAG,CAACX,KAAK,EAAEE,MAAM,CAAC;EAClC,IAAIU,EAAE,GAAG3B,YAAY,CAACuB,MAAM,CAAC,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,CAAC;EAC1C,IAAIG,CAAC,GAAG5B,YAAY,CAACuB,MAAM,CAAC,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,CAAC;EACzC,IAAII,EAAE;EACN,IAAIC,EAAE;EACN,IAAIC,QAAQ,GAAGpB,WAAW,CAACqB,gBAAgB;EAC3C,IAAID,QAAQ,EAAE;IACZ;IACA,IAAIE,KAAK,GAAGF,QAAQ,CAACG,WAAW,CAACb,MAAM,CAAC;IACxCQ,EAAE,GAAGI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IAClBH,EAAE,GAAGG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;EACpB,CAAC,MAAM;IACL,IAAI,CAAC9B,MAAM,CAACqB,OAAO,CAACH,MAAM,CAAC,EAAE;MAC3BA,MAAM,GAAG,CAACA,MAAM,EAAEA,MAAM,CAAC;IAC3B;IACAQ,EAAE,GAAG7B,YAAY,CAACqB,MAAM,CAAC,CAAC,CAAC,EAAEN,KAAK,CAAC,GAAGK,QAAQ,CAACe,CAAC;IAChDL,EAAE,GAAG9B,YAAY,CAACqB,MAAM,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAAC,GAAGG,QAAQ,CAACgB,CAAC;EACnD;EACA,OAAO;IACLP,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA,EAAE;IACNH,EAAE,EAAEA,EAAE;IACNC,CAAC,EAAEA;EACL,CAAC;AACH;AACA,eAAe,SAASS,SAASA,CAACC,UAAU,EAAEC,OAAO,EAAE3B,GAAG,EAAE;EAC1D2B,OAAO,CAACC,gBAAgB,CAACF,UAAU,EAAE,UAAU3B,WAAW,EAAE;IAC1D,IAAI8B,IAAI,GAAG9B,WAAW,CAAC+B,OAAO,CAAC,CAAC;IAChC,IAAIC,QAAQ,GAAGF,IAAI,CAACG,YAAY,CAAC,OAAO,CAAC;IACzC,IAAIxB,QAAQ,GAAGV,WAAW,CAACC,WAAW,EAAEC,GAAG,CAAC;IAC5C,IAAIiC,EAAE,GAAG1B,iBAAiB,CAACR,WAAW,EAAEC,GAAG,CAAC;MAC1CiB,EAAE,GAAGgB,EAAE,CAAChB,EAAE;MACVC,EAAE,GAAGe,EAAE,CAACf,EAAE;MACVF,CAAC,GAAGiB,EAAE,CAACjB,CAAC;MACRD,EAAE,GAAGkB,EAAE,CAAClB,EAAE;IACZ,IAAImB,UAAU,GAAG,CAACnC,WAAW,CAACW,GAAG,CAAC,YAAY,CAAC,GAAGb,MAAM;IACxD,IAAIsC,QAAQ,GAAGpC,WAAW,CAACW,GAAG,CAAC,UAAU,CAAC;IAC1C,IAAI0B,QAAQ,GAAGrC,WAAW,CAACW,GAAG,CAAC,UAAU,CAAC,GAAGb,MAAM;IACnDsC,QAAQ,GAAGA,QAAQ,KAAK,MAAM,GAAGD,UAAU,GAAGxC,GAAG,GAAG,CAACyC,QAAQ,GAAGtC,MAAM;IACtE,IAAIwC,QAAQ,GAAGtC,WAAW,CAACW,GAAG,CAAC,UAAU,CAAC,GAAGb,MAAM;IACnD,IAAIyC,cAAc,GAAGD,QAAQ,GAAGD,QAAQ;IACxC,IAAIG,cAAc,GAAG,CAAC;IACtBV,IAAI,CAACW,IAAI,CAACT,QAAQ,EAAE,UAAUU,KAAK,EAAE;MACnC,CAACC,KAAK,CAACD,KAAK,CAAC,IAAIF,cAAc,EAAE;IACnC,CAAC,CAAC;IACF,IAAII,GAAG,GAAGd,IAAI,CAACe,MAAM,CAACb,QAAQ,CAAC;IAC/B;IACA,IAAIc,UAAU,GAAGlD,IAAI,CAACC,EAAE,IAAI+C,GAAG,IAAIJ,cAAc,CAAC,GAAG,CAAC;IACtD,IAAIO,SAAS,GAAG/C,WAAW,CAACW,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAIqC,QAAQ,GAAGhD,WAAW,CAACW,GAAG,CAAC,UAAU,CAAC;IAC1C,IAAIsC,gBAAgB,GAAGjD,WAAW,CAACW,GAAG,CAAC,kBAAkB,CAAC;IAC1D;IACA,IAAIuC,MAAM,GAAGpB,IAAI,CAACqB,aAAa,CAACnB,QAAQ,CAAC;IACzCkB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;IACb,IAAIE,GAAG,GAAGL,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5B,IAAIM,MAAM,GAAG,CAAClB,UAAU,EAAEC,QAAQ,CAAC;IACnC,IAAIkB,YAAY,GAAGF,GAAG,GAAGf,QAAQ,GAAG,CAAC;IACrC5C,kBAAkB,CAAC4D,MAAM,EAAE,CAACN,SAAS,CAAC;IACtCZ,UAAU,GAAGkB,MAAM,CAAC,CAAC,CAAC,EAAEjB,QAAQ,GAAGiB,MAAM,CAAC,CAAC,CAAC;IAC5C,IAAIE,UAAU,GAAGC,mBAAmB,CAACxD,WAAW,CAAC;IACjDuD,UAAU,CAACpB,UAAU,GAAGA,UAAU;IAClCoB,UAAU,CAACnB,QAAQ,GAAGA,QAAQ;IAC9BmB,UAAU,CAACR,SAAS,GAAGA,SAAS;IAChC,IAAIU,UAAU,GAAG7D,IAAI,CAAC8D,GAAG,CAACtB,QAAQ,GAAGD,UAAU,CAAC;IAChD;IACA,IAAIwB,SAAS,GAAGF,UAAU;IAC1B,IAAIG,0BAA0B,GAAG,CAAC;IAClC,IAAIC,YAAY,GAAG1B,UAAU;IAC7BL,IAAI,CAACgC,SAAS,CAAC;MACbrD,QAAQ,EAAEA,QAAQ;MAClBQ,CAAC,EAAEA;IACL,CAAC,CAAC;IACFa,IAAI,CAACW,IAAI,CAACT,QAAQ,EAAE,UAAUU,KAAK,EAAEqB,GAAG,EAAE;MACxC,IAAIC,KAAK;MACT,IAAIrB,KAAK,CAACD,KAAK,CAAC,EAAE;QAChBZ,IAAI,CAACmC,aAAa,CAACF,GAAG,EAAE;UACtBC,KAAK,EAAEE,GAAG;UACV/B,UAAU,EAAE+B,GAAG;UACf9B,QAAQ,EAAE8B,GAAG;UACbnB,SAAS,EAAEA,SAAS;UACpB7B,EAAE,EAAEA,EAAE;UACNC,EAAE,EAAEA,EAAE;UACNH,EAAE,EAAEA,EAAE;UACNC,CAAC,EAAE+B,QAAQ,GAAGkB,GAAG,GAAGjD;QACtB,CAAC,CAAC;QACF;MACF;MACA;MACA,IAAI+B,QAAQ,KAAK,MAAM,EAAE;QACvBgB,KAAK,GAAGpB,GAAG,KAAK,CAAC,IAAIK,gBAAgB,GAAGH,UAAU,GAAGJ,KAAK,GAAGI,UAAU;MACzE,CAAC,MAAM;QACLkB,KAAK,GAAGP,UAAU,GAAGjB,cAAc;MACrC;MACA,IAAIwB,KAAK,GAAGzB,cAAc,EAAE;QAC1ByB,KAAK,GAAGzB,cAAc;QACtBoB,SAAS,IAAIpB,cAAc;MAC7B,CAAC,MAAM;QACLqB,0BAA0B,IAAIlB,KAAK;MACrC;MACA,IAAIN,QAAQ,GAAGyB,YAAY,GAAGT,GAAG,GAAGY,KAAK;MACzC;MACA,IAAIG,gBAAgB,GAAG,CAAC;MACxB,IAAIC,cAAc,GAAG,CAAC;MACtB,IAAI/B,QAAQ,GAAG2B,KAAK,EAAE;QACpBG,gBAAgB,GAAGN,YAAY,GAAGT,GAAG,GAAGY,KAAK,GAAG,CAAC;QACjDI,cAAc,GAAGD,gBAAgB;MACnC,CAAC,MAAM;QACLA,gBAAgB,GAAGN,YAAY,GAAGP,YAAY;QAC9Cc,cAAc,GAAGhC,QAAQ,GAAGkB,YAAY;MAC1C;MACAxB,IAAI,CAACmC,aAAa,CAACF,GAAG,EAAE;QACtBC,KAAK,EAAEA,KAAK;QACZ7B,UAAU,EAAEgC,gBAAgB;QAC5B/B,QAAQ,EAAEgC,cAAc;QACxBrB,SAAS,EAAEA,SAAS;QACpB7B,EAAE,EAAEA,EAAE;QACNC,EAAE,EAAEA,EAAE;QACNH,EAAE,EAAEA,EAAE;QACNC,CAAC,EAAE+B,QAAQ,GAAG1D,SAAS,CAACoD,KAAK,EAAEQ,MAAM,EAAE,CAAClC,EAAE,EAAEC,CAAC,CAAC,CAAC,GAAGA;MACpD,CAAC,CAAC;MACF4C,YAAY,GAAGzB,QAAQ;IACzB,CAAC,CAAC;IACF;IACA;IACA,IAAIuB,SAAS,GAAGhE,GAAG,IAAI6C,cAAc,EAAE;MACrC;MACA;MACA,IAAImB,SAAS,IAAI,IAAI,EAAE;QACrB,IAAIU,OAAO,GAAGZ,UAAU,GAAGjB,cAAc;QACzCV,IAAI,CAACW,IAAI,CAACT,QAAQ,EAAE,UAAUU,KAAK,EAAEqB,GAAG,EAAE;UACxC,IAAI,CAACpB,KAAK,CAACD,KAAK,CAAC,EAAE;YACjB,IAAI4B,QAAQ,GAAGxC,IAAI,CAACyC,aAAa,CAACR,GAAG,CAAC;YACtCO,QAAQ,CAACN,KAAK,GAAGK,OAAO;YACxB,IAAIF,gBAAgB,GAAG,CAAC;YACxB,IAAIC,cAAc,GAAG,CAAC;YACtB,IAAIC,OAAO,GAAGhC,QAAQ,EAAE;cACtB8B,gBAAgB,GAAGhC,UAAU,GAAGiB,GAAG,IAAIW,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGM,OAAO;cAC7DD,cAAc,GAAGD,gBAAgB;YACnC,CAAC,MAAM;cACLA,gBAAgB,GAAGhC,UAAU,GAAGiB,GAAG,GAAGW,GAAG,GAAGM,OAAO,GAAGf,YAAY;cAClEc,cAAc,GAAGjC,UAAU,GAAGiB,GAAG,IAAIW,GAAG,GAAG,CAAC,CAAC,GAAGM,OAAO,GAAGf,YAAY;YACxE;YACAgB,QAAQ,CAACnC,UAAU,GAAGgC,gBAAgB;YACtCG,QAAQ,CAAClC,QAAQ,GAAGgC,cAAc;UACpC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLtB,UAAU,GAAGa,SAAS,GAAGC,0BAA0B;QACnDC,YAAY,GAAG1B,UAAU;QACzBL,IAAI,CAACW,IAAI,CAACT,QAAQ,EAAE,UAAUU,KAAK,EAAEqB,GAAG,EAAE;UACxC,IAAI,CAACpB,KAAK,CAACD,KAAK,CAAC,EAAE;YACjB,IAAI8B,QAAQ,GAAG1C,IAAI,CAACyC,aAAa,CAACR,GAAG,CAAC;YACtC,IAAIC,KAAK,GAAGQ,QAAQ,CAACR,KAAK,KAAKzB,cAAc,GAAGA,cAAc,GAAGG,KAAK,GAAGI,UAAU;YACnF,IAAIqB,gBAAgB,GAAG,CAAC;YACxB,IAAIC,cAAc,GAAG,CAAC;YACtB,IAAIJ,KAAK,GAAG3B,QAAQ,EAAE;cACpB8B,gBAAgB,GAAGN,YAAY,GAAGT,GAAG,GAAGY,KAAK,GAAG,CAAC;cACjDI,cAAc,GAAGD,gBAAgB;YACnC,CAAC,MAAM;cACLA,gBAAgB,GAAGN,YAAY,GAAGP,YAAY;cAC9Cc,cAAc,GAAGP,YAAY,GAAGT,GAAG,GAAGY,KAAK,GAAGV,YAAY;YAC5D;YACAkB,QAAQ,CAACrC,UAAU,GAAGgC,gBAAgB;YACtCK,QAAQ,CAACpC,QAAQ,GAAGgC,cAAc;YAClCP,YAAY,IAAIT,GAAG,GAAGY,KAAK;UAC7B;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;AACJ;AACA,OAAO,IAAIR,mBAAmB,GAAG9D,SAAS,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}