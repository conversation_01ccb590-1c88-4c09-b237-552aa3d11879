{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as clazzUtil from '../util/clazz.js';\nvar Scale = /** @class */function () {\n  function Scale(setting) {\n    this._setting = setting || {};\n    this._extent = [Infinity, -Infinity];\n  }\n  Scale.prototype.getSetting = function (name) {\n    return this._setting[name];\n  };\n  /**\r\n   * Set extent from data\r\n   */\n  Scale.prototype.unionExtent = function (other) {\n    var extent = this._extent;\n    other[0] < extent[0] && (extent[0] = other[0]);\n    other[1] > extent[1] && (extent[1] = other[1]);\n    // not setExtent because in log axis it may transformed to power\n    // this.setExtent(extent[0], extent[1]);\n  };\n  /**\r\n   * Set extent from data\r\n   */\n  Scale.prototype.unionExtentFromData = function (data, dim) {\n    this.unionExtent(data.getApproximateExtent(dim));\n  };\n  /**\r\n   * Get extent\r\n   *\r\n   * Extent is always in increase order.\r\n   */\n  Scale.prototype.getExtent = function () {\n    return this._extent.slice();\n  };\n  /**\r\n   * Set extent\r\n   */\n  Scale.prototype.setExtent = function (start, end) {\n    var thisExtent = this._extent;\n    if (!isNaN(start)) {\n      thisExtent[0] = start;\n    }\n    if (!isNaN(end)) {\n      thisExtent[1] = end;\n    }\n  };\n  /**\r\n   * If value is in extent range\r\n   */\n  Scale.prototype.isInExtentRange = function (value) {\n    return this._extent[0] <= value && this._extent[1] >= value;\n  };\n  /**\r\n   * When axis extent depends on data and no data exists,\r\n   * axis ticks should not be drawn, which is named 'blank'.\r\n   */\n  Scale.prototype.isBlank = function () {\n    return this._isBlank;\n  };\n  /**\r\n   * When axis extent depends on data and no data exists,\r\n   * axis ticks should not be drawn, which is named 'blank'.\r\n   */\n  Scale.prototype.setBlank = function (isBlank) {\n    this._isBlank = isBlank;\n  };\n  return Scale;\n}();\nclazzUtil.enableClassManagement(Scale);\nexport default Scale;", "map": {"version": 3, "names": ["clazzUtil", "Scale", "setting", "_setting", "_extent", "Infinity", "prototype", "getSetting", "name", "unionExtent", "other", "extent", "unionExtentFromData", "data", "dim", "getApproximateExtent", "getExtent", "slice", "setExtent", "start", "end", "thisExtent", "isNaN", "isInExtentRange", "value", "isBlank", "_isBlank", "setBlank", "enableClassManagement"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/scale/Scale.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as clazzUtil from '../util/clazz.js';\nvar Scale = /** @class */function () {\n  function Scale(setting) {\n    this._setting = setting || {};\n    this._extent = [Infinity, -Infinity];\n  }\n  Scale.prototype.getSetting = function (name) {\n    return this._setting[name];\n  };\n  /**\r\n   * Set extent from data\r\n   */\n  Scale.prototype.unionExtent = function (other) {\n    var extent = this._extent;\n    other[0] < extent[0] && (extent[0] = other[0]);\n    other[1] > extent[1] && (extent[1] = other[1]);\n    // not setExtent because in log axis it may transformed to power\n    // this.setExtent(extent[0], extent[1]);\n  };\n  /**\r\n   * Set extent from data\r\n   */\n  Scale.prototype.unionExtentFromData = function (data, dim) {\n    this.unionExtent(data.getApproximateExtent(dim));\n  };\n  /**\r\n   * Get extent\r\n   *\r\n   * Extent is always in increase order.\r\n   */\n  Scale.prototype.getExtent = function () {\n    return this._extent.slice();\n  };\n  /**\r\n   * Set extent\r\n   */\n  Scale.prototype.setExtent = function (start, end) {\n    var thisExtent = this._extent;\n    if (!isNaN(start)) {\n      thisExtent[0] = start;\n    }\n    if (!isNaN(end)) {\n      thisExtent[1] = end;\n    }\n  };\n  /**\r\n   * If value is in extent range\r\n   */\n  Scale.prototype.isInExtentRange = function (value) {\n    return this._extent[0] <= value && this._extent[1] >= value;\n  };\n  /**\r\n   * When axis extent depends on data and no data exists,\r\n   * axis ticks should not be drawn, which is named 'blank'.\r\n   */\n  Scale.prototype.isBlank = function () {\n    return this._isBlank;\n  };\n  /**\r\n   * When axis extent depends on data and no data exists,\r\n   * axis ticks should not be drawn, which is named 'blank'.\r\n   */\n  Scale.prototype.setBlank = function (isBlank) {\n    this._isBlank = isBlank;\n  };\n  return Scale;\n}();\nclazzUtil.enableClassManagement(Scale);\nexport default Scale;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,SAAS,MAAM,kBAAkB;AAC7C,IAAIC,KAAK,GAAG,aAAa,YAAY;EACnC,SAASA,KAAKA,CAACC,OAAO,EAAE;IACtB,IAAI,CAACC,QAAQ,GAAGD,OAAO,IAAI,CAAC,CAAC;IAC7B,IAAI,CAACE,OAAO,GAAG,CAACC,QAAQ,EAAE,CAACA,QAAQ,CAAC;EACtC;EACAJ,KAAK,CAACK,SAAS,CAACC,UAAU,GAAG,UAAUC,IAAI,EAAE;IAC3C,OAAO,IAAI,CAACL,QAAQ,CAACK,IAAI,CAAC;EAC5B,CAAC;EACD;AACF;AACA;EACEP,KAAK,CAACK,SAAS,CAACG,WAAW,GAAG,UAAUC,KAAK,EAAE;IAC7C,IAAIC,MAAM,GAAG,IAAI,CAACP,OAAO;IACzBM,KAAK,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9CA,KAAK,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9C;IACA;EACF,CAAC;EACD;AACF;AACA;EACET,KAAK,CAACK,SAAS,CAACM,mBAAmB,GAAG,UAAUC,IAAI,EAAEC,GAAG,EAAE;IACzD,IAAI,CAACL,WAAW,CAACI,IAAI,CAACE,oBAAoB,CAACD,GAAG,CAAC,CAAC;EAClD,CAAC;EACD;AACF;AACA;AACA;AACA;EACEb,KAAK,CAACK,SAAS,CAACU,SAAS,GAAG,YAAY;IACtC,OAAO,IAAI,CAACZ,OAAO,CAACa,KAAK,CAAC,CAAC;EAC7B,CAAC;EACD;AACF;AACA;EACEhB,KAAK,CAACK,SAAS,CAACY,SAAS,GAAG,UAAUC,KAAK,EAAEC,GAAG,EAAE;IAChD,IAAIC,UAAU,GAAG,IAAI,CAACjB,OAAO;IAC7B,IAAI,CAACkB,KAAK,CAACH,KAAK,CAAC,EAAE;MACjBE,UAAU,CAAC,CAAC,CAAC,GAAGF,KAAK;IACvB;IACA,IAAI,CAACG,KAAK,CAACF,GAAG,CAAC,EAAE;MACfC,UAAU,CAAC,CAAC,CAAC,GAAGD,GAAG;IACrB;EACF,CAAC;EACD;AACF;AACA;EACEnB,KAAK,CAACK,SAAS,CAACiB,eAAe,GAAG,UAAUC,KAAK,EAAE;IACjD,OAAO,IAAI,CAACpB,OAAO,CAAC,CAAC,CAAC,IAAIoB,KAAK,IAAI,IAAI,CAACpB,OAAO,CAAC,CAAC,CAAC,IAAIoB,KAAK;EAC7D,CAAC;EACD;AACF;AACA;AACA;EACEvB,KAAK,CAACK,SAAS,CAACmB,OAAO,GAAG,YAAY;IACpC,OAAO,IAAI,CAACC,QAAQ;EACtB,CAAC;EACD;AACF;AACA;AACA;EACEzB,KAAK,CAACK,SAAS,CAACqB,QAAQ,GAAG,UAAUF,OAAO,EAAE;IAC5C,IAAI,CAACC,QAAQ,GAAGD,OAAO;EACzB,CAAC;EACD,OAAOxB,KAAK;AACd,CAAC,CAAC,CAAC;AACHD,SAAS,CAAC4B,qBAAqB,CAAC3B,KAAK,CAAC;AACtC,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}