{"ast": null, "code": "import \"core-js/modules/esnext.iterator.map.js\";\nexport default {\n  name: \"Notice\",\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      pageNum: 1,\n      // 当前的页码\n      pageSize: 10,\n      // 每页显示的个数\n      total: 0,\n      title: null,\n      fromVisible: false,\n      form: {},\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      rules: {\n        title: [{\n          required: true,\n          message: '请输入标题',\n          trigger: 'blur'\n        }],\n        content: [{\n          required: true,\n          message: '请输入内容',\n          trigger: 'blur'\n        }]\n      },\n      ids: []\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    handleAdd() {\n      // 新增数据\n      this.form = {}; // 新增数据的时候清空数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    handleEdit(row) {\n      // 编辑数据\n      this.form = JSON.parse(JSON.stringify(row)); // 给form对象赋值  注意要深拷贝数据\n      this.fromVisible = true; // 打开弹窗\n    },\n    save() {\n      // 保存按钮触发的逻辑  它会触发新增或者更新\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          this.$request({\n            url: this.form.id ? '/notice/update' : '/notice/add',\n            method: this.form.id ? 'PUT' : 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              // 表示成功保存\n              this.$message.success('保存成功');\n              this.load(1);\n              this.fromVisible = false;\n            } else {\n              this.$message.error(res.msg); // 弹出错误的信息\n            }\n          });\n        }\n      });\n    },\n    del(id) {\n      // 单个删除\n      this.$confirm('您确定删除吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/notice/delete/' + id).then(res => {\n          if (res.code === '200') {\n            // 表示操作成功\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg); // 弹出错误的信息\n          }\n        });\n      }).catch(() => {});\n    },\n    handleSelectionChange(rows) {\n      // 当前选中的所有的行数据\n      this.ids = rows.map(v => v.id); //  [1,2]\n    },\n    delBatch() {\n      // 批量删除\n      if (!this.ids.length) {\n        this.$message.warning('请选择数据');\n        return;\n      }\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/notice/delete/batch', {\n          data: this.ids\n        }).then(res => {\n          if (res.code === '200') {\n            // 表示操作成功\n            this.$message.success('操作成功');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg); // 弹出错误的信息\n          }\n        });\n      }).catch(() => {});\n    },\n    load(pageNum) {\n      // 分页查询\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/notice/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          title: this.title\n        }\n      }).then(res => {\n        this.tableData = res.data?.list;\n        this.total = res.data?.total;\n      });\n    },\n    reset() {\n      this.title = null;\n      this.load(1);\n    },\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "tableData", "pageNum", "pageSize", "total", "title", "fromVisible", "form", "user", "JSON", "parse", "localStorage", "getItem", "rules", "required", "message", "trigger", "content", "ids", "created", "load", "methods", "handleAdd", "handleEdit", "row", "stringify", "save", "$refs", "formRef", "validate", "valid", "$request", "url", "id", "method", "then", "res", "code", "$message", "success", "error", "msg", "del", "$confirm", "type", "response", "delete", "catch", "handleSelectionChange", "rows", "map", "v", "delBatch", "length", "warning", "get", "params", "list", "reset", "handleCurrentChange"], "sources": ["src/views/manager/Notice.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"search\">\r\n      <el-input placeholder=\"请输入标题查询\" style=\"width: 200px\" v-model=\"title\"></el-input>\r\n      <el-button type=\"info\" plain style=\"margin-left: 10px\" @click=\"load(1)\">查询</el-button>\r\n      <el-button type=\"warning\" plain style=\"margin-left: 10px\" @click=\"reset\">重置</el-button>\r\n    </div>\r\n\r\n    <div class=\"operation\">\r\n      <el-button type=\"primary\" plain @click=\"handleAdd\">新增</el-button>\r\n      <el-button type=\"danger\" plain @click=\"delBatch\">批量删除</el-button>\r\n    </div>\r\n\r\n    <div class=\"table\">\r\n      <el-table :data=\"tableData\" stripe  @selection-change=\"handleSelectionChange\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\"></el-table-column>\r\n        <el-table-column prop=\"id\" label=\"序号\" width=\"80\" align=\"center\" sortable></el-table-column>\r\n        <el-table-column prop=\"title\" label=\"标题\" show-overflow-tooltip></el-table-column>\r\n        <el-table-column prop=\"content\" label=\"内容\" show-overflow-tooltip></el-table-column>\r\n        <el-table-column prop=\"time\" label=\"创建时间\"></el-table-column>\r\n        <el-table-column prop=\"user\" label=\"创建人\"></el-table-column>\r\n\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\">\r\n          <template v-slot=\"scope\">\r\n            <el-button plain type=\"primary\" @click=\"handleEdit(scope.row)\" size=\"mini\">编辑</el-button>\r\n            <el-button plain type=\"danger\" size=\"mini\" @click=del(scope.row.id)>删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"pagination\">\r\n        <el-pagination\r\n            background\r\n            @current-change=\"handleCurrentChange\"\r\n            :current-page=\"pageNum\"\r\n            :page-sizes=\"[5, 10, 20]\"\r\n            :page-size=\"pageSize\"\r\n            layout=\"total, prev, pager, next\"\r\n            :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n\r\n\r\n    <el-dialog title=\"信息\" :visible.sync=\"fromVisible\" width=\"40%\" :close-on-click-modal=\"false\" destroy-on-close>\r\n      <el-form label-width=\"100px\" style=\"padding-right: 50px\" :model=\"form\" :rules=\"rules\" ref=\"formRef\">\r\n        <el-form-item prop=\"title\" label=\"标题\">\r\n          <el-input v-model=\"form.title\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item prop=\"content\" label=\"内容\">\r\n          <el-input type=\"textarea\" :rows=\"5\" v-model=\"form.content\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"fromVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Notice\",\r\n  data() {\r\n    return {\r\n      tableData: [],  // 所有的数据\r\n      pageNum: 1,   // 当前的页码\r\n      pageSize: 10,  // 每页显示的个数\r\n      total: 0,\r\n      title: null,\r\n      fromVisible: false,\r\n      form: {},\r\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n      rules: {\r\n        title: [\r\n          {required: true, message: '请输入标题', trigger: 'blur'},\r\n        ],\r\n        content: [\r\n          {required: true, message: '请输入内容', trigger: 'blur'},\r\n        ]\r\n      },\r\n      ids: []\r\n    }\r\n  },\r\n  created() {\r\n    this.load(1)\r\n  },\r\n  methods: {\r\n    handleAdd() {   // 新增数据\r\n      this.form = {}  // 新增数据的时候清空数据\r\n      this.fromVisible = true   // 打开弹窗\r\n    },\r\n    handleEdit(row) {   // 编辑数据\r\n      this.form = JSON.parse(JSON.stringify(row))  // 给form对象赋值  注意要深拷贝数据\r\n      this.fromVisible = true   // 打开弹窗\r\n    },\r\n    save() {   // 保存按钮触发的逻辑  它会触发新增或者更新\r\n      this.$refs.formRef.validate((valid) => {\r\n        if (valid) {\r\n          this.$request({\r\n            url: this.form.id ? '/notice/update' : '/notice/add',\r\n            method: this.form.id ? 'PUT' : 'POST',\r\n            data: this.form\r\n          }).then(res => {\r\n            if (res.code === '200') {  // 表示成功保存\r\n              this.$message.success('保存成功')\r\n              this.load(1)\r\n              this.fromVisible = false\r\n            } else {\r\n              this.$message.error(res.msg)  // 弹出错误的信息\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    del(id) {   // 单个删除\r\n      this.$confirm('您确定删除吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n        this.$request.delete('/notice/delete/' + id).then(res => {\r\n          if (res.code === '200') {   // 表示操作成功\r\n            this.$message.success('操作成功')\r\n            this.load(1)\r\n          } else {\r\n            this.$message.error(res.msg)  // 弹出错误的信息\r\n          }\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleSelectionChange(rows) {   // 当前选中的所有的行数据\r\n      this.ids = rows.map(v => v.id)   //  [1,2]\r\n    },\r\n    delBatch() {   // 批量删除\r\n      if (!this.ids.length) {\r\n        this.$message.warning('请选择数据')\r\n        return\r\n      }\r\n      this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n        this.$request.delete('/notice/delete/batch', {data: this.ids}).then(res => {\r\n          if (res.code === '200') {   // 表示操作成功\r\n            this.$message.success('操作成功')\r\n            this.load(1)\r\n          } else {\r\n            this.$message.error(res.msg)  // 弹出错误的信息\r\n          }\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    load(pageNum) {  // 分页查询\r\n      if (pageNum) this.pageNum = pageNum\r\n      this.$request.get('/notice/selectPage', {\r\n        params: {\r\n          pageNum: this.pageNum,\r\n          pageSize: this.pageSize,\r\n          title: this.title,\r\n        }\r\n      }).then(res => {\r\n        this.tableData = res.data?.list\r\n        this.total = res.data?.total\r\n      })\r\n    },\r\n    reset() {\r\n      this.title = null\r\n      this.load(1)\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.load(pageNum)\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";AAgEA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MACAC,KAAA;MACAC,WAAA;MACAC,IAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,KAAA;QACAR,KAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,OAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAE,GAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAC,UAAA;MAAA;MACA,KAAAf,IAAA;MACA,KAAAD,WAAA;IACA;IACAiB,WAAAC,GAAA;MAAA;MACA,KAAAjB,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAgB,SAAA,CAAAD,GAAA;MACA,KAAAlB,WAAA;IACA;IACAoB,KAAA;MAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,KAAAC,QAAA;YACAC,GAAA,OAAAzB,IAAA,CAAA0B,EAAA;YACAC,MAAA,OAAA3B,IAAA,CAAA0B,EAAA;YACAjC,IAAA,OAAAO;UACA,GAAA4B,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cAAA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA,KAAAnB,IAAA;cACA,KAAAd,WAAA;YACA;cACA,KAAAgC,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;YACA;UACA;QACA;MACA;IACA;IACAC,IAAAT,EAAA;MAAA;MACA,KAAAU,QAAA;QAAAC,IAAA;MAAA,GAAAT,IAAA,CAAAU,QAAA;QACA,KAAAd,QAAA,CAAAe,MAAA,qBAAAb,EAAA,EAAAE,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAnB,IAAA;UACA;YACA,KAAAkB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAM,KAAA,QACA;IACA;IACAC,sBAAAC,IAAA;MAAA;MACA,KAAA/B,GAAA,GAAA+B,IAAA,CAAAC,GAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAlB,EAAA;IACA;IACAmB,SAAA;MAAA;MACA,UAAAlC,GAAA,CAAAmC,MAAA;QACA,KAAAf,QAAA,CAAAgB,OAAA;QACA;MACA;MACA,KAAAX,QAAA;QAAAC,IAAA;MAAA,GAAAT,IAAA,CAAAU,QAAA;QACA,KAAAd,QAAA,CAAAe,MAAA;UAAA9C,IAAA,OAAAkB;QAAA,GAAAiB,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAnB,IAAA;UACA;YACA,KAAAkB,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAM,KAAA,QACA;IACA;IACA3B,KAAAlB,OAAA;MAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAA6B,QAAA,CAAAwB,GAAA;QACAC,MAAA;UACAtD,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA,QAAA;UACAE,KAAA,OAAAA;QACA;MACA,GAAA8B,IAAA,CAAAC,GAAA;QACA,KAAAnC,SAAA,GAAAmC,GAAA,CAAApC,IAAA,EAAAyD,IAAA;QACA,KAAArD,KAAA,GAAAgC,GAAA,CAAApC,IAAA,EAAAI,KAAA;MACA;IACA;IACAsD,MAAA;MACA,KAAArD,KAAA;MACA,KAAAe,IAAA;IACA;IACAuC,oBAAAzD,OAAA;MACA,KAAAkB,IAAA,CAAAlB,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}