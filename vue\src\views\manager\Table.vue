<template>
  <div>
    <!-- 搜索区域 -->
    <div class="search">
      <el-input placeholder="请输入餐桌号查询" style="width: 200px" v-model="searchForm.tableNumber"></el-input>
      <el-select v-model="searchForm.area" placeholder="请选择区域" clearable style="width: 150px; margin-left: 10px">
        <el-option label="大厅" value="大厅"></el-option>
        <el-option label="包间" value="包间"></el-option>
        <el-option label="靠窗" value="靠窗"></el-option>
      </el-select>
      <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 150px; margin-left: 10px">
        <el-option label="空闲" value="空闲"></el-option>
        <el-option label="使用中" value="使用中"></el-option>
        <el-option label="清洁中" value="清洁中"></el-option>
        <el-option label="维修中" value="维修中"></el-option>
      </el-select>
      <el-button type="info" plain style="margin-left: 10px" @click="load">查询</el-button>
      <el-button type="warning" plain style="margin-left: 10px" @click="reset">重置</el-button>
    </div>

    <!-- 操作区域 -->
    <div class="operation">
      <el-button type="primary" plain @click="handleAdd">新增餐桌</el-button>
      <el-button type="success" plain @click="syncTableStatus">同步状态</el-button>
      <el-button type="danger" plain @click="delBatch">批量删除</el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table">
      <el-table :data="tableData" stripe @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"></el-table-column>
        <el-table-column prop="id" label="序号" width="70" align="center" sortable></el-table-column>
        <el-table-column prop="tableNumber" label="餐桌号" width="120"></el-table-column>
        <el-table-column prop="seats" label="座位数" width="100">
          <template v-slot="scope">
            {{ scope.row.seats }}人
          </template>
        </el-table-column>
        <el-table-column prop="area" label="区域" width="120"></el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template v-slot="scope">
            <el-tag
              :type="getStatusTagType(scope.row.status)"
              size="small">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="occupyStatus" label="占用状态" width="120">
          <template v-slot="scope">
            <el-tag
              :type="scope.row.occupyStatus === '占用中' ? 'danger' : 'success'"
              size="small">
              {{ scope.row.occupyStatus || '空闲' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template v-slot="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="220">
          <template v-slot="scope">
            <el-button size="mini" type="primary" plain @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="warning" plain @click="handleStatusChange(scope.row)">状态</el-button>
            <el-button size="mini" type="danger" plain @click="del(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="total">
        </el-pagination>
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible" 
      width="500px"
      :close-on-click-modal="false">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="餐桌号" prop="tableNumber">
          <el-input v-model="form.tableNumber" placeholder="请输入餐桌号"></el-input>
        </el-form-item>
        <el-form-item label="座位数" prop="seats">
          <el-input-number v-model="form.seats" :min="1" :max="20" placeholder="请输入座位数"></el-input-number>
        </el-form-item>
        <el-form-item label="区域" prop="area">
          <el-select v-model="form.area" placeholder="请选择区域" style="width: 100%">
            <el-option label="大厅" value="大厅"></el-option>
            <el-option label="包间" value="包间"></el-option>
            <el-option label="靠窗" value="靠窗"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="空闲" value="空闲"></el-option>
            <el-option label="使用中" value="使用中"></el-option>
            <el-option label="清洁中" value="清洁中"></el-option>
            <el-option label="维修中" value="维修中"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 状态修改对话框 -->
    <el-dialog 
      title="修改餐桌状态" 
      :visible.sync="statusDialogVisible" 
      width="400px">
      <el-form :model="statusForm" label-width="100px">
        <el-form-item label="餐桌号">
          <el-input v-model="statusForm.tableNumber" readonly></el-input>
        </el-form-item>
        <el-form-item label="当前状态">
          <el-tag :type="getStatusTagType(statusForm.currentStatus)">
            {{ statusForm.currentStatus }}
          </el-tag>
        </el-form-item>
        <el-form-item label="新状态" prop="newStatus">
          <el-select v-model="statusForm.newStatus" placeholder="请选择新状态" style="width: 100%">
            <el-option label="空闲" value="空闲"></el-option>
            <el-option label="使用中" value="使用中"></el-option>
            <el-option label="清洁中" value="清洁中"></el-option>
            <el-option label="维修中" value="维修中"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <span class="dialog-footer">
          <el-button @click="statusDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleStatusSave">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'Table',
  data() {
    return {
      // 搜索表单
      searchForm: {
        tableNumber: '',
        area: '',
        status: ''
      },
      // 表格数据
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 多选
      ids: [],
      // 对话框
      dialogVisible: false,
      dialogTitle: '',
      form: {
        id: null,
        tableNumber: '',
        seats: 4,
        area: '',
        status: '空闲'
      },
      // 状态修改对话框
      statusDialogVisible: false,
      statusForm: {
        id: null,
        tableNumber: '',
        currentStatus: '',
        newStatus: ''
      },
      // 表单验证规则
      rules: {
        tableNumber: [
          { required: true, message: '请输入餐桌号', trigger: 'blur' }
        ],
        seats: [
          { required: true, message: '请输入座位数', trigger: 'blur' }
        ],
        area: [
          { required: true, message: '请选择区域', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.load()
  },
  methods: {
    // 加载数据
    load(pageNum) {
      if (pageNum) this.currentPage = pageNum
      request.get('/table/selectPage', {
        params: {
          ...this.searchForm,
          pageNum: this.currentPage,
          pageSize: this.pageSize
        }
      }).then(res => {
        this.tableData = res.data.list
        this.total = res.data.total
        // 加载餐桌占用状态
        this.loadTableStatusDetail()
      })
    },
    // 重置搜索
    reset() {
      this.searchForm = {
        tableNumber: '',
        area: '',
        status: ''
      }
      this.load(1)
    },
    // 多选
    handleSelectionChange(val) {
      this.ids = val.map(v => v.id)
    },
    // 批量删除
    delBatch() {
      if (!this.ids.length) {
        this.$message.warning('请选择数据')
        return
      }
      this.$confirm('确定批量删除所选餐桌吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request.delete('/table/delete/batch', { data: this.ids }).then(res => {
          this.$message.success('批量删除成功')
          this.load()
        })
      })
    },
    // 获取餐桌状态详情（包含占用信息）
    loadTableStatusDetail() {
      request.get('/table/statusDetail').then(res => {
        // 将占用状态信息合并到表格数据中
        const statusMap = {}
        res.data.forEach(item => {
          statusMap[item.id] = item.occupyStatus
        })
        this.tableData.forEach(row => {
          row.occupyStatus = statusMap[row.id] || '空闲'
        })
      })
    },
    // 新增
    handleAdd() {
      this.dialogTitle = '新增餐桌'
      this.dialogVisible = true
      this.form = {
        id: null,
        tableNumber: '',
        seats: 4,
        area: '',
        status: '空闲'
      }
    },
    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑餐桌'
      this.dialogVisible = true
      this.form = { ...row }
    },
    // 保存
    handleSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          const url = this.form.id ? '/table/update' : '/table/add'
          const method = this.form.id ? 'put' : 'post'
          
          request[method](url, this.form).then(res => {
            this.$message.success('操作成功')
            this.dialogVisible = false
            this.load()
          })
        }
      })
    },
    // 删除
    del(id) {
      this.$confirm('确定删除该餐桌吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request.delete('/table/delete/' + id).then(res => {
          this.$message.success('删除成功')
          this.load()
        })
      })
    },
    // 状态修改
    handleStatusChange(row) {
      this.statusForm = {
        id: row.id,
        tableNumber: row.tableNumber,
        currentStatus: row.status,
        newStatus: row.status
      }
      this.statusDialogVisible = true
    },
    // 保存状态修改
    handleStatusSave() {
      if (this.statusForm.newStatus === this.statusForm.currentStatus) {
        this.$message.warning('状态未发生变化')
        return
      }
      
      request.put('/table/updateStatus', {
        id: this.statusForm.id,
        status: this.statusForm.newStatus
      }).then(res => {
        this.$message.success('状态修改成功')
        this.statusDialogVisible = false
        this.load()
      })
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.load(1)
    },
    handleCurrentChange(val) {
      this.load(val)
    },
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '空闲': 'success',
        '使用中': 'danger',
        '清洁中': 'warning',
        '维修中': 'info'
      }
      return statusMap[status] || 'info'
    },
    // 格式化时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      return date.toLocaleString()
    },
    // 同步餐桌状态
    syncTableStatus() {
      this.$confirm('确定要同步所有餐桌状态吗？系统将根据订单数据自动更新餐桌状态。', '同步状态确认', {
        confirmButtonText: '确定同步',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request.post('/table/syncStatus').then(res => {
          this.$message.success(res.data || '餐桌状态同步完成')
          this.load() // 重新加载数据
        }).catch(err => {
          this.$message.error('同步失败：' + (err.response?.data?.msg || err.message))
        })
      }).catch(() => {
        this.$message.info('已取消同步')
      })
    }
  }
}
</script>

