{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.find.js\";\nimport \"core-js/modules/esnext.iterator.for-each.js\";\nimport \"core-js/modules/esnext.iterator.map.js\";\nimport \"core-js/modules/esnext.iterator.reduce.js\";\nimport TableSelect from '@/components/TableSelect.vue';\nexport default {\n  name: \"CartPage\",\n  components: {\n    TableSelect\n  },\n  data() {\n    return {\n      tableData: [],\n      // 所有的数据\n      filteredTableData: [],\n      // 过滤后的数据\n      pageNum: 1,\n      // 当前的页码\n      pageSize: 10,\n      // 每页显示的个数\n      total: 0,\n      fromVisible: false,\n      form: {},\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      rules: {\n        sfUserName: [{\n          required: true,\n          message: '请输入用户名',\n          trigger: 'blur'\n        }],\n        sfUserId: [{\n          required: true,\n          message: '请输入用户ID',\n          trigger: 'blur'\n        }],\n        status: [{\n          required: true,\n          message: '请选择订单状态',\n          trigger: 'change'\n        }],\n        sfOrderNumber: [{\n          required: true,\n          message: '请输入订单编号',\n          trigger: 'blur'\n        }],\n        sfCreateTime: [{\n          required: true,\n          message: '请选择下单时间',\n          trigger: 'change'\n        }],\n        sfTotalPrice: [{\n          required: true,\n          message: '请输入订单价格',\n          trigger: 'blur'\n        }],\n        sfCartStatus: [{\n          required: true,\n          message: '请选择购物车状态',\n          trigger: 'change'\n        }]\n      },\n      ids: [],\n      selectedItems: [],\n      // 餐桌选择相关\n      tableDialogVisible: false,\n      selectedTable: null,\n      currentPayItem: null,\n      // 当前要支付的单个商品\n      goodsPriceMap: {},\n      // 存储商品ID和价格的映射\n      goodsNameMap: {},\n      // 存储商品ID和名称的映射\n      submitting: false // 防重复提交\n    };\n  },\n  computed: {\n    totalAmount() {\n      return this.selectedItems.reduce((total, itemId) => {\n        const item = this.filteredTableData.find(item => item.id === itemId);\n        return total + (item ? parseFloat(item.sfTotalPrice) : 0);\n      }, 0).toFixed(2);\n    }\n  },\n  created() {\n    this.load(1); // 加载购物车数据\n  },\n  methods: {\n    toggleSelection(item) {\n      const index = this.selectedItems.indexOf(item.id);\n      if (index > -1) {\n        this.selectedItems.splice(index, 1);\n      } else {\n        this.selectedItems.push(item.id);\n      }\n      this.ids = this.selectedItems;\n    },\n    // 批量支付选中的商品（购物车结算）\n    payAllSelected() {\n      if (this.selectedItems.length === 0) {\n        this.$message.warning('请选择要支付的商品');\n        return;\n      }\n\n      // 打开餐桌选择对话框\n      this.currentPayItem = null; // 清除单个商品支付状态\n      this.selectedTable = null;\n      this.tableDialogVisible = true;\n    },\n    // 处理餐桌选择\n    handleTableSelected(table) {\n      this.selectedTable = table;\n    },\n    // 确认下单（选择餐桌后）\n    confirmCheckout() {\n      if (!this.selectedTable) {\n        this.$message.warning('请选择餐桌');\n        return;\n      }\n\n      // 防重复提交\n      if (this.submitting) {\n        this.$message.warning('正在处理中，请勿重复提交');\n        return;\n      }\n\n      // 判断是单个商品支付还是批量支付\n      const isSinglePay = this.currentPayItem !== null;\n      const confirmMessage = isSinglePay ? `确认要在${this.selectedTable.tableNumber}号桌支付该商品吗？金额：¥${this.currentPayItem.sfTotalPrice}` : `确认要在${this.selectedTable.tableNumber}号桌结算购物车吗？总金额：¥${this.totalAmount}`;\n      const confirmTitle = isSinglePay ? '单个商品支付确认' : '购物车结算确认';\n      this.$confirm(confirmMessage, confirmTitle, {\n        confirmButtonText: '确定支付',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.submitting = true;\n\n        // 准备请求参数\n        const requestData = {\n          tableNumber: this.selectedTable.tableNumber\n        };\n        if (isSinglePay) {\n          // 单个商品支付\n          requestData.remark = `单个商品结算：${this.currentPayItem.foodName || '商品ID:' + this.currentPayItem.foodId}`;\n        } else {\n          // 批量支付\n          requestData.remark = `购物车批量结算，共${this.selectedItems.length}件商品`;\n        }\n\n        // 购物车结算，会将所有购物车商品转为订单\n        this.$request.post('/cart/checkout', requestData).then(res => {\n          if (res.code === '200') {\n            const successMessage = isSinglePay ? `商品支付成功，订单已生成！餐桌：${this.selectedTable.tableNumber}号` : `购物车结算成功，订单已生成！餐桌：${this.selectedTable.tableNumber}号`;\n            this.$message.success(successMessage);\n\n            // 重置状态\n            this.selectedItems = [];\n            this.ids = [];\n            this.currentPayItem = null;\n            this.tableDialogVisible = false;\n            this.selectedTable = null;\n            this.load(1); // 重新加载购物车\n          } else {\n            this.$message.error(res.msg || '支付失败');\n          }\n        }).catch(err => {\n          console.error('支付失败:', err);\n          if (err.response && err.response.data && err.response.data.msg) {\n            this.$message.error(err.response.data.msg);\n          } else {\n            this.$message.error('支付失败，请重试');\n          }\n        }).finally(() => {\n          this.submitting = false;\n        });\n      }).catch(() => {\n        this.$message.info('已取消支付');\n      });\n    },\n    // 加载商品价格和名称\n    loadGoodsPrices() {\n      this.$request.get('/foods/selectAll').then(res => {\n        if (res.code === '200') {\n          this.goodsPriceMap = {};\n          this.goodsNameMap = {};\n          res.data?.forEach(item => {\n            // 使用字符串和数字两种格式作为键，确保能匹配到\n            this.goodsPriceMap[item.id] = item.sfPrice;\n            this.goodsPriceMap[item.id.toString()] = item.sfPrice;\n            this.goodsNameMap[item.id] = item.name;\n            this.goodsNameMap[item.id.toString()] = item.name;\n          });\n          // 重新过滤数据以更新价格和名称\n          this.filterTableData();\n        }\n      }).catch(err => {\n        this.$message.error('加载商品信息失败');\n        console.error(err);\n      });\n    },\n    // 处理购物车数据，购物车API已返回完整数据，无需过滤\n    filterTableData() {\n      this.filteredTableData = this.tableData.map(item => {\n        // 购物车API返回的数据结构已包含商品详情和小计\n        return {\n          ...item,\n          // 为了兼容现有模板，映射字段名\n          sfUserName: item.userId ? '当前用户' : '匿名用户',\n          sfProductIds: item.foodId,\n          sfTotalPrice: item.subtotal,\n          sfCreateTime: item.createTime,\n          status: '未付款',\n          // 购物车商品默认未付款状态\n          sfCartStatus: '已加入购物车'\n        };\n      });\n      this.total = this.filteredTableData.length;\n    },\n    // 立即支付操作（购物车结算）\n    handlePay(row) {\n      // 设置当前要支付的商品，然后打开餐桌选择对话框\n      this.currentPayItem = row;\n      this.selectedItems = [row.id]; // 设置为当前商品\n      this.selectedTable = null;\n      this.tableDialogVisible = true;\n    },\n    handleAdd() {\n      this.form = {\n        status: '未付款',\n        sfCartStatus: '已加入购物车',\n        sfCreateTime: new Date(),\n        sfTotalPrice: 0\n      };\n      this.fromVisible = true;\n    },\n    handleEdit(row) {\n      this.form = JSON.parse(JSON.stringify(row));\n      // 如果是购物车订单，自动同步商品价格\n      if (this.form.sfCartStatus === '已加入购物车' && this.goodsPriceMap[this.form.sfProductIds]) {\n        this.form.sfTotalPrice = this.goodsPriceMap[this.form.sfProductIds];\n      }\n      this.fromVisible = true;\n    },\n    // 更新商品数量\n    updateQuantity(item, newQuantity) {\n      if (newQuantity < 1 || newQuantity > 99) {\n        return;\n      }\n      this.$request.put('/cart/update', {\n        foodId: item.foodId,\n        quantity: newQuantity\n      }).then(res => {\n        if (res.code === '200') {\n          this.$message.success('数量更新成功');\n          this.load(this.pageNum); // 重新加载数据\n        } else {\n          this.$message.error(res.msg || '更新失败');\n        }\n      }).catch(() => {\n        this.$message.error('更新失败，请重试');\n      });\n    },\n    save() {\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          // 格式化下单时间\n          if (this.form.sfCreateTime instanceof Date) {\n            this.form.sfCreateTime = this.formatDateTime(this.form.sfCreateTime);\n          }\n\n          // 如果是购物车订单，确保价格与商品一致\n          if (this.form.sfCartStatus === '已加入购物车' && this.goodsPriceMap[this.form.sfProductIds]) {\n            this.form.sfTotalPrice = this.goodsPriceMap[this.form.sfProductIds];\n          }\n          this.$request({\n            url: this.form.id ? '/dingdan/update' : '/dingdan/add',\n            method: this.form.id ? 'PUT' : 'POST',\n            data: this.form\n          }).then(res => {\n            if (res.code === '200') {\n              this.$message.success('保存成功');\n              this.load(1);\n              this.fromVisible = false;\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    },\n    formatDateTime(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      const seconds = String(date.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n    del(id) {\n      // 根据购物车项ID找到对应的商品ID\n      const item = this.filteredTableData.find(item => item.id === id);\n      if (!item) {\n        this.$message.error('商品不存在');\n        return;\n      }\n      this.$confirm('您确定要从购物车中移除该商品吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/cart/remove', {\n          params: {\n            foodId: item.foodId\n          }\n        }).then(res => {\n          if (res.code === '200') {\n            this.$message.success('商品已从购物车中移除');\n            this.load(1);\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {});\n    },\n    handleSelectionChange(rows) {\n      this.ids = rows.map(v => v.id);\n    },\n    delBatch() {\n      if (!this.ids.length) {\n        this.$message.warning('请选择数据');\n        return;\n      }\n      this.$confirm('您确定要清空购物车吗？', '确认删除', {\n        type: \"warning\"\n      }).then(response => {\n        this.$request.delete('/cart/clear').then(res => {\n          if (res.code === '200') {\n            this.$message.success('购物车已清空');\n            this.selectedItems = [];\n            this.ids = [];\n            this.load(1);\n          } else {\n            this.$message.error(res.msg);\n          }\n        });\n      }).catch(() => {});\n    },\n    load(pageNum) {\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/cart/list').then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data || [];\n          this.filterTableData();\n        } else {\n          this.$message.error(res.msg);\n        }\n      }).catch(err => {\n        this.$message.error('加载购物车数据失败');\n        console.error(err);\n      });\n    },\n    handleCurrentChange(pageNum) {\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["TableSelect", "name", "components", "data", "tableData", "filteredTableData", "pageNum", "pageSize", "total", "fromVisible", "form", "user", "JSON", "parse", "localStorage", "getItem", "rules", "sfUserName", "required", "message", "trigger", "sfUserId", "status", "sfOrderNumber", "sfCreateTime", "sfTotalPrice", "sfCartStatus", "ids", "selectedItems", "tableDialogVisible", "selectedTable", "currentPayItem", "goodsPriceMap", "goodsNameMap", "submitting", "computed", "totalAmount", "reduce", "itemId", "item", "find", "id", "parseFloat", "toFixed", "created", "load", "methods", "toggleSelection", "index", "indexOf", "splice", "push", "payAllSelected", "length", "$message", "warning", "handleTableSelected", "table", "confirm<PERSON>heckout", "isSinglePay", "confirmMessage", "tableNumber", "confirmTitle", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "requestData", "remark", "foodName", "foodId", "$request", "post", "res", "code", "successMessage", "success", "error", "msg", "catch", "err", "console", "response", "finally", "info", "loadGoodsPrices", "get", "for<PERSON>ach", "sfPrice", "toString", "filterTableData", "map", "userId", "sfProductIds", "subtotal", "createTime", "handlePay", "row", "handleAdd", "Date", "handleEdit", "stringify", "updateQuantity", "newQuantity", "put", "quantity", "save", "$refs", "formRef", "validate", "valid", "formatDateTime", "url", "method", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "del", "delete", "params", "handleSelectionChange", "rows", "v", "delBatch", "handleCurrentChange"], "sources": ["src/views/front/Dingdan2.vue"], "sourcesContent": ["<template>\r\n    <div class=\"cart-container\">\r\n        <!-- 购物车统计 -->\r\n        <div class=\"content-section\">\r\n            <div class=\"cart-summary\" v-if=\"filteredTableData.length > 0\">\r\n                <div class=\"summary-card\">\r\n                    <div class=\"summary-item\">\r\n                        <i class=\"el-icon-shopping-cart-2 summary-icon\"></i>\r\n                        <div class=\"summary-content\">\r\n                            <div class=\"summary-label\">商品数量</div>\r\n                            <div class=\"summary-value\">{{ filteredTableData.length }} 件</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"summary-item\">\r\n                        <i class=\"el-icon-money summary-icon\"></i>\r\n                        <div class=\"summary-content\">\r\n                            <div class=\"summary-label\">总金额</div>\r\n                            <div class=\"summary-value\">¥{{ totalAmount }}</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"summary-actions\">\r\n                        <el-button \r\n                            type=\"primary\" \r\n                            size=\"large\"\r\n                            @click=\"payAllSelected\"\r\n                            :disabled=\"selectedItems.length === 0\"\r\n                            class=\"pay-all-btn\">\r\n                            <i class=\"el-icon-wallet\"></i>\r\n                            批量支付 ({{ selectedItems.length }})\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 操作按钮 -->\r\n            <div class=\"operation-section\">\r\n                <el-button \r\n                    type=\"danger\" \r\n                    size=\"medium\"\r\n                    @click=\"delBatch\"\r\n                    :disabled=\"!ids.length\"\r\n                    class=\"batch-delete-btn\">\r\n                    <i class=\"el-icon-delete\"></i>\r\n                    批量删除 ({{ ids.length }})\r\n                </el-button>\r\n            </div>\r\n\r\n            <!-- 购物车列表 -->\r\n            <div class=\"cart-list\">\r\n                <div v-if=\"filteredTableData.length === 0\" class=\"empty-state\">\r\n                    <i class=\"el-icon-shopping-cart-full\"></i>\r\n                    <h3>购物车空空如也</h3>\r\n                    <p>快去挑选您喜欢的商品吧</p>\r\n                    <el-button type=\"primary\" @click=\"$router.push('/front/home')\" class=\"go-shopping-btn\">\r\n                        去购物\r\n                    </el-button>\r\n                </div>\r\n                \r\n                <div v-else class=\"cart-grid\">\r\n                    <div \r\n                        v-for=\"item in filteredTableData\" \r\n                        :key=\"item.id\"\r\n                        class=\"cart-item\"\r\n                        :class=\"{ 'selected': selectedItems.includes(item.id) }\"\r\n                        @click=\"toggleSelection(item)\">\r\n                        \r\n                        <div class=\"item-header\">\r\n                            <div class=\"item-info\">\r\n                                <div class=\"item-name\">{{ item.foodName || '商品ID: ' + item.foodId }}</div>\r\n                                <div class=\"item-time\">{{ item.sfCreateTime }}</div>\r\n                            </div>\r\n                            <div class=\"item-status\">\r\n                                <el-tag \r\n                                    :type=\"item.status === '未付款' ? 'danger' : 'success'\"\r\n                                    size=\"medium\"\r\n                                    class=\"status-tag\">\r\n                                    {{ item.status }}\r\n                                </el-tag>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"item-content\">\r\n                            <div class=\"item-details\">\r\n                                <div class=\"detail-item\">\r\n                                    <i class=\"el-icon-user detail-icon\"></i>\r\n                                    <span class=\"detail-label\">用户：</span>\r\n                                    <span class=\"detail-value\">{{ item.sfUserName }}</span>\r\n                                </div>\r\n                                <div class=\"detail-item\" v-if=\"item.sfRemark\">\r\n                                    <i class=\"el-icon-chat-line-square detail-icon\"></i>\r\n                                    <span class=\"detail-label\">备注：</span>\r\n                                    <span class=\"detail-value\">{{ item.sfRemark }}</span>\r\n                                </div>\r\n                                <div class=\"detail-item\">\r\n                                    <i class=\"el-icon-shopping-bag-2 detail-icon\"></i>\r\n                                    <span class=\"detail-label\">数量：</span>\r\n                                    <div class=\"quantity-controls\">\r\n                                        <el-button \r\n                                            size=\"mini\" \r\n                                            icon=\"el-icon-minus\" \r\n                                            @click.stop=\"updateQuantity(item, item.quantity - 1)\"\r\n                                            :disabled=\"item.quantity <= 1\"\r\n                                            class=\"quantity-btn\">\r\n                                        </el-button>\r\n                                        <span class=\"quantity-value\">{{ item.quantity }}</span>\r\n                                        <el-button \r\n                                            size=\"mini\" \r\n                                            icon=\"el-icon-plus\" \r\n                                            @click.stop=\"updateQuantity(item, item.quantity + 1)\"\r\n                                            :disabled=\"item.quantity >= 99\"\r\n                                            class=\"quantity-btn\">\r\n                                        </el-button>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            \r\n                            <div class=\"item-price\">\r\n                                <div class=\"price-label\">商品价格</div>\r\n                                <div class=\"price-value\">¥{{ item.sfTotalPrice }}</div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"item-actions\">\r\n                            <el-button\r\n                                v-if=\"item.status === '未付款' && item.sfCartStatus === '已加入购物车'\"\r\n                                type=\"success\"\r\n                                size=\"small\"\r\n                                @click.stop=\"handlePay(item)\"\r\n                                class=\"action-btn pay-btn\">\r\n                                <i class=\"el-icon-wallet\"></i>\r\n                                立即支付\r\n                            </el-button>\r\n                            <el-button\r\n                                type=\"danger\"\r\n                                size=\"small\"\r\n                                @click.stop=\"del(item.id)\"\r\n                                class=\"action-btn\">\r\n                                <i class=\"el-icon-delete\"></i>\r\n                                删除\r\n                            </el-button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 分页 -->\r\n            <div class=\"pagination-section\">\r\n                <el-pagination\r\n                    background\r\n                    @current-change=\"handleCurrentChange\"\r\n                    :current-page=\"pageNum\"\r\n                    :page-size=\"pageSize\"\r\n                    layout=\"prev, pager, next\"\r\n                    :total=\"total\"\r\n                    class=\"custom-pagination\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 餐桌选择对话框 -->\r\n        <el-dialog \r\n            title=\"选择餐桌\" \r\n            :visible.sync=\"tableDialogVisible\" \r\n            width=\"800px\"\r\n            :close-on-click-modal=\"false\">\r\n            <div class=\"table-selection-container\">\r\n                <div class=\"table-selection-header\">\r\n                    <div class=\"selection-info\">\r\n                        <p v-if=\"currentPayItem\">\r\n                            单个商品支付：{{ currentPayItem.foodName || '商品ID: ' + currentPayItem.foodId }}，\r\n                            金额：<span class=\"total-price\">¥{{ currentPayItem.sfTotalPrice }}</span>\r\n                        </p>\r\n                        <p v-else>\r\n                            批量支付：共{{ selectedItems.length }}件商品，\r\n                            总金额：<span class=\"total-price\">¥{{ totalAmount }}</span>\r\n                        </p>\r\n                        <p v-if=\"selectedTable\">已选择：{{ selectedTable.tableNumber }}号桌 ({{ selectedTable.seats }}人座，{{ selectedTable.area }})</p>\r\n                    </div>\r\n                </div>\r\n                \r\n                <!-- 餐桌选择组件 -->\r\n                <table-select \r\n                    v-model=\"selectedTable\" \r\n                    @table-selected=\"handleTableSelected\">\r\n                </table-select>\r\n            </div>\r\n            \r\n            <template slot=\"footer\">\r\n                <span class=\"dialog-footer\">\r\n                    <el-button @click=\"tableDialogVisible = false\">取消</el-button>\r\n                    <el-button \r\n                        type=\"primary\" \r\n                        @click=\"confirmCheckout\"\r\n                        :disabled=\"!selectedTable\">\r\n                        确认下单\r\n                    </el-button>\r\n                </span>\r\n            </template>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport TableSelect from '@/components/TableSelect.vue'\r\n\r\nexport default {\r\n    name: \"CartPage\",\r\n    components: {\r\n        TableSelect\r\n    },\r\n    data() {\r\n        return {\r\n            tableData: [],  // 所有的数据\r\n            filteredTableData: [], // 过滤后的数据\r\n            pageNum: 1,   // 当前的页码\r\n            pageSize: 10,  // 每页显示的个数\r\n            total: 0,\r\n\r\n            fromVisible: false,\r\n            form: {},\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n            rules: {\r\n                sfUserName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],\r\n                sfUserId: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],\r\n                status: [{ required: true, message: '请选择订单状态', trigger: 'change' }],\r\n                sfOrderNumber: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],\r\n                sfCreateTime: [{ required: true, message: '请选择下单时间', trigger: 'change' }],\r\n                sfTotalPrice: [{ required: true, message: '请输入订单价格', trigger: 'blur' }],\r\n                sfCartStatus: [{ required: true, message: '请选择购物车状态', trigger: 'change' }],\r\n            },\r\n            ids: [],\r\n            selectedItems: [],\r\n            // 餐桌选择相关\r\n            tableDialogVisible: false,\r\n            selectedTable: null,\r\n            currentPayItem: null, // 当前要支付的单个商品\r\n            goodsPriceMap: {}, // 存储商品ID和价格的映射\r\n            goodsNameMap: {}, // 存储商品ID和名称的映射\r\n            submitting: false // 防重复提交\r\n        }\r\n    },\r\n    computed: {\r\n        totalAmount() {\r\n            return this.selectedItems.reduce((total, itemId) => {\r\n                const item = this.filteredTableData.find(item => item.id === itemId)\r\n                return total + (item ? parseFloat(item.sfTotalPrice) : 0)\r\n            }, 0).toFixed(2)\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1) // 加载购物车数据\r\n    },\r\n    methods: {\r\n        toggleSelection(item) {\r\n            const index = this.selectedItems.indexOf(item.id)\r\n            if (index > -1) {\r\n                this.selectedItems.splice(index, 1)\r\n            } else {\r\n                this.selectedItems.push(item.id)\r\n            }\r\n            this.ids = this.selectedItems\r\n        },\r\n\r\n        // 批量支付选中的商品（购物车结算）\r\n        payAllSelected() {\r\n            if (this.selectedItems.length === 0) {\r\n                this.$message.warning('请选择要支付的商品')\r\n                return\r\n            }\r\n            \r\n            // 打开餐桌选择对话框\r\n            this.currentPayItem = null // 清除单个商品支付状态\r\n            this.selectedTable = null\r\n            this.tableDialogVisible = true\r\n        },\r\n\r\n        // 处理餐桌选择\r\n        handleTableSelected(table) {\r\n            this.selectedTable = table\r\n        },\r\n\r\n        // 确认下单（选择餐桌后）\r\n        confirmCheckout() {\r\n            if (!this.selectedTable) {\r\n                this.$message.warning('请选择餐桌')\r\n                return\r\n            }\r\n\r\n            // 防重复提交\r\n            if (this.submitting) {\r\n                this.$message.warning('正在处理中，请勿重复提交')\r\n                return\r\n            }\r\n\r\n            // 判断是单个商品支付还是批量支付\r\n            const isSinglePay = this.currentPayItem !== null\r\n            const confirmMessage = isSinglePay \r\n                ? `确认要在${this.selectedTable.tableNumber}号桌支付该商品吗？金额：¥${this.currentPayItem.sfTotalPrice}`\r\n                : `确认要在${this.selectedTable.tableNumber}号桌结算购物车吗？总金额：¥${this.totalAmount}`\r\n            const confirmTitle = isSinglePay ? '单个商品支付确认' : '购物车结算确认'\r\n\r\n            this.$confirm(confirmMessage, confirmTitle, {\r\n                confirmButtonText: '确定支付',\r\n                cancelButtonText: '取消',\r\n                type: 'warning'\r\n            }).then(() => {\r\n                this.submitting = true\r\n                \r\n                // 准备请求参数\r\n                const requestData = {\r\n                    tableNumber: this.selectedTable.tableNumber\r\n                }\r\n                \r\n                if (isSinglePay) {\r\n                    // 单个商品支付\r\n                    requestData.remark = `单个商品结算：${this.currentPayItem.foodName || '商品ID:' + this.currentPayItem.foodId}`\r\n                } else {\r\n                    // 批量支付\r\n                    requestData.remark = `购物车批量结算，共${this.selectedItems.length}件商品`\r\n                }\r\n\r\n                // 购物车结算，会将所有购物车商品转为订单\r\n                this.$request.post('/cart/checkout', requestData).then(res => {\r\n                    if (res.code === '200') {\r\n                        const successMessage = isSinglePay \r\n                            ? `商品支付成功，订单已生成！餐桌：${this.selectedTable.tableNumber}号`\r\n                            : `购物车结算成功，订单已生成！餐桌：${this.selectedTable.tableNumber}号`\r\n                        this.$message.success(successMessage)\r\n                        \r\n                        // 重置状态\r\n                        this.selectedItems = []\r\n                        this.ids = []\r\n                        this.currentPayItem = null\r\n                        this.tableDialogVisible = false\r\n                        this.selectedTable = null\r\n                        this.load(1) // 重新加载购物车\r\n                    } else {\r\n                        this.$message.error(res.msg || '支付失败')\r\n                    }\r\n                }).catch(err => {\r\n                    console.error('支付失败:', err)\r\n                    if (err.response && err.response.data && err.response.data.msg) {\r\n                        this.$message.error(err.response.data.msg)\r\n                    } else {\r\n                        this.$message.error('支付失败，请重试')\r\n                    }\r\n                }).finally(() => {\r\n                    this.submitting = false\r\n                })\r\n            }).catch(() => {\r\n                this.$message.info('已取消支付')\r\n            })\r\n        },\r\n\r\n        // 加载商品价格和名称\r\n        loadGoodsPrices() {\r\n            this.$request.get('/foods/selectAll').then(res => {\r\n                if (res.code === '200') {\r\n                    this.goodsPriceMap = {}\r\n                    this.goodsNameMap = {}\r\n                    res.data?.forEach(item => {\r\n                        // 使用字符串和数字两种格式作为键，确保能匹配到\r\n                        this.goodsPriceMap[item.id] = item.sfPrice\r\n                        this.goodsPriceMap[item.id.toString()] = item.sfPrice\r\n                        this.goodsNameMap[item.id] = item.name\r\n                        this.goodsNameMap[item.id.toString()] = item.name\r\n                    })\r\n                    // 重新过滤数据以更新价格和名称\r\n                    this.filterTableData()\r\n                }\r\n            }).catch(err => {\r\n                this.$message.error('加载商品信息失败')\r\n                console.error(err)\r\n            })\r\n        },\r\n\r\n        // 处理购物车数据，购物车API已返回完整数据，无需过滤\r\n        filterTableData() {\r\n            this.filteredTableData = this.tableData.map(item => {\r\n                // 购物车API返回的数据结构已包含商品详情和小计\r\n                return {\r\n                    ...item,\r\n                    // 为了兼容现有模板，映射字段名\r\n                    sfUserName: item.userId ? '当前用户' : '匿名用户',\r\n                    sfProductIds: item.foodId,\r\n                    sfTotalPrice: item.subtotal,\r\n                    sfCreateTime: item.createTime,\r\n                    status: '未付款', // 购物车商品默认未付款状态\r\n                    sfCartStatus: '已加入购物车'\r\n                }\r\n            })\r\n            this.total = this.filteredTableData.length\r\n        },\r\n\r\n        // 立即支付操作（购物车结算）\r\n        handlePay(row) {\r\n            // 设置当前要支付的商品，然后打开餐桌选择对话框\r\n            this.currentPayItem = row\r\n            this.selectedItems = [row.id] // 设置为当前商品\r\n            this.selectedTable = null\r\n            this.tableDialogVisible = true\r\n        },\r\n\r\n        handleAdd() {\r\n            this.form = {\r\n                status: '未付款',\r\n                sfCartStatus: '已加入购物车',\r\n                sfCreateTime: new Date(),\r\n                sfTotalPrice: 0\r\n            }\r\n            this.fromVisible = true\r\n        },\r\n\r\n        handleEdit(row) {\r\n            this.form = JSON.parse(JSON.stringify(row))\r\n            // 如果是购物车订单，自动同步商品价格\r\n            if (this.form.sfCartStatus === '已加入购物车' && this.goodsPriceMap[this.form.sfProductIds]) {\r\n                this.form.sfTotalPrice = this.goodsPriceMap[this.form.sfProductIds]\r\n            }\r\n            this.fromVisible = true\r\n        },\r\n\r\n        // 更新商品数量\r\n        updateQuantity(item, newQuantity) {\r\n            if (newQuantity < 1 || newQuantity > 99) {\r\n                return\r\n            }\r\n            \r\n            this.$request.put('/cart/update', {\r\n                foodId: item.foodId,\r\n                quantity: newQuantity\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.$message.success('数量更新成功')\r\n                    this.load(this.pageNum) // 重新加载数据\r\n                } else {\r\n                    this.$message.error(res.msg || '更新失败')\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('更新失败，请重试')\r\n            })\r\n        },\r\n\r\n        save() {\r\n            this.$refs.formRef.validate((valid) => {\r\n                if (valid) {\r\n                                    // 格式化下单时间\r\n                if (this.form.sfCreateTime instanceof Date) {\r\n                    this.form.sfCreateTime = this.formatDateTime(this.form.sfCreateTime)\r\n                }\r\n\r\n                    // 如果是购物车订单，确保价格与商品一致\r\n                    if (this.form.sfCartStatus === '已加入购物车' && this.goodsPriceMap[this.form.sfProductIds]) {\r\n                        this.form.sfTotalPrice = this.goodsPriceMap[this.form.sfProductIds]\r\n                    }\r\n\r\n                    this.$request({\r\n                        url: this.form.id ? '/dingdan/update' : '/dingdan/add',\r\n                        method: this.form.id ? 'PUT' : 'POST',\r\n                        data: this.form\r\n                    }).then(res => {\r\n                        if (res.code === '200') {\r\n                            this.$message.success('保存成功')\r\n                            this.load(1)\r\n                            this.fromVisible = false\r\n                        } else {\r\n                            this.$message.error(res.msg)\r\n                        }\r\n                    })\r\n                }\r\n            })\r\n        },\r\n\r\n        formatDateTime(date) {\r\n            const year = date.getFullYear()\r\n            const month = String(date.getMonth() + 1).padStart(2, '0')\r\n            const day = String(date.getDate()).padStart(2, '0')\r\n            const hours = String(date.getHours()).padStart(2, '0')\r\n            const minutes = String(date.getMinutes()).padStart(2, '0')\r\n            const seconds = String(date.getSeconds()).padStart(2, '0')\r\n\r\n            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n        },\r\n\r\n        del(id) {\r\n            // 根据购物车项ID找到对应的商品ID\r\n            const item = this.filteredTableData.find(item => item.id === id)\r\n            if (!item) {\r\n                this.$message.error('商品不存在')\r\n                return\r\n            }\r\n            \r\n            this.$confirm('您确定要从购物车中移除该商品吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n                this.$request.delete('/cart/remove', {\r\n                    params: { foodId: item.foodId }\r\n                }).then(res => {\r\n                    if (res.code === '200') {\r\n                        this.$message.success('商品已从购物车中移除')\r\n                        this.load(1)\r\n                    } else {\r\n                        this.$message.error(res.msg)\r\n                    }\r\n                })\r\n            }).catch(() => {})\r\n        },\r\n\r\n        handleSelectionChange(rows) {\r\n            this.ids = rows.map(v => v.id)\r\n        },\r\n\r\n        delBatch() {\r\n            if (!this.ids.length) {\r\n                this.$message.warning('请选择数据')\r\n                return\r\n            }\r\n            this.$confirm('您确定要清空购物车吗？', '确认删除', {type: \"warning\"}).then(response => {\r\n                this.$request.delete('/cart/clear').then(res => {\r\n                    if (res.code === '200') {\r\n                        this.$message.success('购物车已清空')\r\n                        this.selectedItems = []\r\n                        this.ids = []\r\n                        this.load(1)\r\n                    } else {\r\n                        this.$message.error(res.msg)\r\n                    }\r\n                })\r\n            }).catch(() => {})\r\n        },\r\n\r\n        load(pageNum) {\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/cart/list').then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data || []\r\n                    this.filterTableData()\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            }).catch(err => {\r\n                this.$message.error('加载购物车数据失败')\r\n                console.error(err)\r\n            })\r\n        },\r\n\r\n\r\n\r\n        handleCurrentChange(pageNum) {\r\n            this.load(pageNum)\r\n        },\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.cart-container {\r\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n    min-height: 100vh;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-section {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 40px 20px;\r\n}\r\n\r\n\r\n\r\n/* 购物车统计 */\r\n.cart-summary {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.summary-card {\r\n    background: white;\r\n    border-radius: 16px;\r\n    padding: 24px;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    gap: 20px;\r\n}\r\n\r\n.summary-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.summary-icon {\r\n    width: 48px;\r\n    height: 48px;\r\n    background: #3b82f6;\r\n    color: white;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 20px;\r\n}\r\n\r\n.summary-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.summary-label {\r\n    font-size: 14px;\r\n    color: #64748b;\r\n}\r\n\r\n.summary-value {\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    color: #1e293b;\r\n}\r\n\r\n.summary-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n}\r\n\r\n.pay-all-btn {\r\n    background: #10b981;\r\n    border-color: #10b981;\r\n    border-radius: 12px;\r\n    padding: 12px 24px;\r\n    font-weight: 600;\r\n    font-size: 16px;\r\n}\r\n\r\n.pay-all-btn:hover {\r\n    background: #059669;\r\n    border-color: #059669;\r\n}\r\n\r\n.pay-all-btn:disabled {\r\n    background: #e5e7eb;\r\n    border-color: #e5e7eb;\r\n    color: #9ca3af;\r\n}\r\n\r\n/* 操作区域 */\r\n.operation-section {\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.batch-delete-btn {\r\n    background: #ef4444;\r\n    border-color: #ef4444;\r\n    border-radius: 8px;\r\n    font-weight: 500;\r\n}\r\n\r\n.batch-delete-btn:hover {\r\n    background: #dc2626;\r\n    border-color: #dc2626;\r\n}\r\n\r\n.batch-delete-btn:disabled {\r\n    background: #e5e7eb;\r\n    border-color: #e5e7eb;\r\n    color: #9ca3af;\r\n}\r\n\r\n/* 购物车列表 */\r\n.cart-list {\r\n    margin-bottom: 40px;\r\n}\r\n\r\n.empty-state {\r\n    text-align: center;\r\n    padding: 80px 20px;\r\n    color: #64748b;\r\n}\r\n\r\n.empty-state i {\r\n    font-size: 64px;\r\n    margin-bottom: 16px;\r\n    color: #cbd5e1;\r\n}\r\n\r\n.empty-state h3 {\r\n    font-size: 20px;\r\n    margin-bottom: 8px;\r\n    color: #475569;\r\n}\r\n\r\n.empty-state p {\r\n    margin-bottom: 24px;\r\n}\r\n\r\n.go-shopping-btn {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    border-radius: 25px;\r\n    padding: 12px 32px;\r\n    font-weight: 600;\r\n}\r\n\r\n.go-shopping-btn:hover {\r\n    background: #1e40af;\r\n    border-color: #1e40af;\r\n}\r\n\r\n.cart-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\r\n    gap: 24px;\r\n}\r\n\r\n.cart-item {\r\n    background: white;\r\n    border-radius: 16px;\r\n    padding: 24px;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n    border: 2px solid transparent;\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n}\r\n\r\n.cart-item:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);\r\n    border-color: #3b82f6;\r\n}\r\n\r\n.cart-item.selected {\r\n    border-color: #3b82f6;\r\n    background: #f0f9ff;\r\n}\r\n\r\n.item-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.item-info {\r\n    flex: 1;\r\n}\r\n\r\n.item-name {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #1e293b;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.item-time {\r\n    font-size: 14px;\r\n    color: #64748b;\r\n}\r\n\r\n.item-status {\r\n    flex-shrink: 0;\r\n}\r\n\r\n.status-tag {\r\n    font-weight: 500;\r\n    border-radius: 12px;\r\n    padding: 4px 12px;\r\n}\r\n\r\n.item-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.item-details {\r\n    flex: 1;\r\n}\r\n\r\n.detail-item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 8px;\r\n    font-size: 14px;\r\n}\r\n\r\n.detail-item:last-child {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.detail-icon {\r\n    width: 16px;\r\n    color: #3b82f6;\r\n    margin-right: 8px;\r\n}\r\n\r\n.detail-label {\r\n    color: #64748b;\r\n    margin-right: 8px;\r\n}\r\n\r\n.detail-value {\r\n    color: #1e293b;\r\n    font-weight: 500;\r\n}\r\n\r\n.item-price {\r\n    text-align: right;\r\n    flex-shrink: 0;\r\n    margin-left: 20px;\r\n}\r\n\r\n.price-label {\r\n    font-size: 12px;\r\n    color: #64748b;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.price-value {\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    color: #10b981;\r\n}\r\n\r\n.item-actions {\r\n    display: flex;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n    border-radius: 8px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.pay-btn {\r\n    background: #10b981;\r\n    border-color: #10b981;\r\n}\r\n\r\n.pay-btn:hover {\r\n    background: #059669;\r\n    border-color: #059669;\r\n}\r\n\r\n/* 数量控制 */\r\n.quantity-controls {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.quantity-btn {\r\n    width: 24px;\r\n    height: 24px;\r\n    padding: 0;\r\n    border-radius: 50%;\r\n    border: 1px solid #e5e7eb;\r\n    background: white;\r\n    color: #6b7280;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.quantity-btn:hover:not(:disabled) {\r\n    border-color: #3b82f6;\r\n    color: #3b82f6;\r\n    background: #f0f9ff;\r\n}\r\n\r\n.quantity-btn:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n}\r\n\r\n.quantity-value {\r\n    font-weight: 600;\r\n    color: #1f2937;\r\n    min-width: 20px;\r\n    text-align: center;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-section {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 20px 0;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li {\r\n    background: white;\r\n    border: 1px solid #e5e7eb;\r\n    border-radius: 8px;\r\n    margin: 0 4px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li:hover {\r\n    border-color: #3b82f6;\r\n    color: #3b82f6;\r\n}\r\n\r\n.custom-pagination >>> .el-pager li.active {\r\n    background: #3b82f6;\r\n    border-color: #3b82f6;\r\n    color: white;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .cart-grid {\r\n        grid-template-columns: 1fr;\r\n    }\r\n    \r\n\r\n    \r\n    .summary-card {\r\n        flex-direction: column;\r\n        text-align: center;\r\n    }\r\n    \r\n    .item-content {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .item-price {\r\n        margin-left: 0;\r\n        margin-top: 16px;\r\n        text-align: left;\r\n    }\r\n}\r\n\r\n/* 餐桌选择对话框样式 */\r\n.table-selection-container {\r\n    padding: 20px 0;\r\n}\r\n\r\n.table-selection-header {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.selection-info p {\r\n    margin: 8px 0;\r\n    font-size: 16px;\r\n}\r\n\r\n.total-price {\r\n    color: #e6a23c;\r\n    font-weight: bold;\r\n    font-size: 18px;\r\n}\r\n\r\n.dialog-footer {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    gap: 10px;\r\n}\r\n</style>"], "mappings": ";;;;;;AA2MA,OAAAA,WAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAEAC,WAAA;MACAC,IAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,KAAA;QACAC,UAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,QAAA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAE,MAAA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAG,aAAA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAI,YAAA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAK,YAAA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAM,YAAA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAO,GAAA;MACAC,aAAA;MACA;MACAC,kBAAA;MACAC,aAAA;MACAC,cAAA;MAAA;MACAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACAC,YAAA;MACA,YAAAR,aAAA,CAAAS,MAAA,EAAA7B,KAAA,EAAA8B,MAAA;QACA,MAAAC,IAAA,QAAAlC,iBAAA,CAAAmC,IAAA,CAAAD,IAAA,IAAAA,IAAA,CAAAE,EAAA,KAAAH,MAAA;QACA,OAAA9B,KAAA,IAAA+B,IAAA,GAAAG,UAAA,CAAAH,IAAA,CAAAd,YAAA;MACA,MAAAkB,OAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAC,gBAAAR,IAAA;MACA,MAAAS,KAAA,QAAApB,aAAA,CAAAqB,OAAA,CAAAV,IAAA,CAAAE,EAAA;MACA,IAAAO,KAAA;QACA,KAAApB,aAAA,CAAAsB,MAAA,CAAAF,KAAA;MACA;QACA,KAAApB,aAAA,CAAAuB,IAAA,CAAAZ,IAAA,CAAAE,EAAA;MACA;MACA,KAAAd,GAAA,QAAAC,aAAA;IACA;IAEA;IACAwB,eAAA;MACA,SAAAxB,aAAA,CAAAyB,MAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAxB,cAAA;MACA,KAAAD,aAAA;MACA,KAAAD,kBAAA;IACA;IAEA;IACA2B,oBAAAC,KAAA;MACA,KAAA3B,aAAA,GAAA2B,KAAA;IACA;IAEA;IACAC,gBAAA;MACA,UAAA5B,aAAA;QACA,KAAAwB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,SAAArB,UAAA;QACA,KAAAoB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,MAAAI,WAAA,QAAA5B,cAAA;MACA,MAAA6B,cAAA,GAAAD,WAAA,GACA,YAAA7B,aAAA,CAAA+B,WAAA,qBAAA9B,cAAA,CAAAN,YAAA,KACA,YAAAK,aAAA,CAAA+B,WAAA,sBAAAzB,WAAA;MACA,MAAA0B,YAAA,GAAAH,WAAA;MAEA,KAAAI,QAAA,CAAAH,cAAA,EAAAE,YAAA;QACAE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA,KAAAjC,UAAA;;QAEA;QACA,MAAAkC,WAAA;UACAP,WAAA,OAAA/B,aAAA,CAAA+B;QACA;QAEA,IAAAF,WAAA;UACA;UACAS,WAAA,CAAAC,MAAA,kBAAAtC,cAAA,CAAAuC,QAAA,mBAAAvC,cAAA,CAAAwC,MAAA;QACA;UACA;UACAH,WAAA,CAAAC,MAAA,oBAAAzC,aAAA,CAAAyB,MAAA;QACA;;QAEA;QACA,KAAAmB,QAAA,CAAAC,IAAA,mBAAAL,WAAA,EAAAD,IAAA,CAAAO,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,MAAAC,cAAA,GAAAjB,WAAA,GACA,wBAAA7B,aAAA,CAAA+B,WAAA,MACA,yBAAA/B,aAAA,CAAA+B,WAAA;YACA,KAAAP,QAAA,CAAAuB,OAAA,CAAAD,cAAA;;YAEA;YACA,KAAAhD,aAAA;YACA,KAAAD,GAAA;YACA,KAAAI,cAAA;YACA,KAAAF,kBAAA;YACA,KAAAC,aAAA;YACA,KAAAe,IAAA;UACA;YACA,KAAAS,QAAA,CAAAwB,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA,GAAAC,KAAA,CAAAC,GAAA;UACAC,OAAA,CAAAJ,KAAA,UAAAG,GAAA;UACA,IAAAA,GAAA,CAAAE,QAAA,IAAAF,GAAA,CAAAE,QAAA,CAAAhF,IAAA,IAAA8E,GAAA,CAAAE,QAAA,CAAAhF,IAAA,CAAA4E,GAAA;YACA,KAAAzB,QAAA,CAAAwB,KAAA,CAAAG,GAAA,CAAAE,QAAA,CAAAhF,IAAA,CAAA4E,GAAA;UACA;YACA,KAAAzB,QAAA,CAAAwB,KAAA;UACA;QACA,GAAAM,OAAA;UACA,KAAAlD,UAAA;QACA;MACA,GAAA8C,KAAA;QACA,KAAA1B,QAAA,CAAA+B,IAAA;MACA;IACA;IAEA;IACAC,gBAAA;MACA,KAAAd,QAAA,CAAAe,GAAA,qBAAApB,IAAA,CAAAO,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAA3C,aAAA;UACA,KAAAC,YAAA;UACAyC,GAAA,CAAAvE,IAAA,EAAAqF,OAAA,CAAAjD,IAAA;YACA;YACA,KAAAP,aAAA,CAAAO,IAAA,CAAAE,EAAA,IAAAF,IAAA,CAAAkD,OAAA;YACA,KAAAzD,aAAA,CAAAO,IAAA,CAAAE,EAAA,CAAAiD,QAAA,MAAAnD,IAAA,CAAAkD,OAAA;YACA,KAAAxD,YAAA,CAAAM,IAAA,CAAAE,EAAA,IAAAF,IAAA,CAAAtC,IAAA;YACA,KAAAgC,YAAA,CAAAM,IAAA,CAAAE,EAAA,CAAAiD,QAAA,MAAAnD,IAAA,CAAAtC,IAAA;UACA;UACA;UACA,KAAA0F,eAAA;QACA;MACA,GAAAX,KAAA,CAAAC,GAAA;QACA,KAAA3B,QAAA,CAAAwB,KAAA;QACAI,OAAA,CAAAJ,KAAA,CAAAG,GAAA;MACA;IACA;IAEA;IACAU,gBAAA;MACA,KAAAtF,iBAAA,QAAAD,SAAA,CAAAwF,GAAA,CAAArD,IAAA;QACA;QACA;UACA,GAAAA,IAAA;UACA;UACAtB,UAAA,EAAAsB,IAAA,CAAAsD,MAAA;UACAC,YAAA,EAAAvD,IAAA,CAAAgC,MAAA;UACA9C,YAAA,EAAAc,IAAA,CAAAwD,QAAA;UACAvE,YAAA,EAAAe,IAAA,CAAAyD,UAAA;UACA1E,MAAA;UAAA;UACAI,YAAA;QACA;MACA;MACA,KAAAlB,KAAA,QAAAH,iBAAA,CAAAgD,MAAA;IACA;IAEA;IACA4C,UAAAC,GAAA;MACA;MACA,KAAAnE,cAAA,GAAAmE,GAAA;MACA,KAAAtE,aAAA,IAAAsE,GAAA,CAAAzD,EAAA;MACA,KAAAX,aAAA;MACA,KAAAD,kBAAA;IACA;IAEAsE,UAAA;MACA,KAAAzF,IAAA;QACAY,MAAA;QACAI,YAAA;QACAF,YAAA,MAAA4E,IAAA;QACA3E,YAAA;MACA;MACA,KAAAhB,WAAA;IACA;IAEA4F,WAAAH,GAAA;MACA,KAAAxF,IAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAA0F,SAAA,CAAAJ,GAAA;MACA;MACA,SAAAxF,IAAA,CAAAgB,YAAA,sBAAAM,aAAA,MAAAtB,IAAA,CAAAoF,YAAA;QACA,KAAApF,IAAA,CAAAe,YAAA,QAAAO,aAAA,MAAAtB,IAAA,CAAAoF,YAAA;MACA;MACA,KAAArF,WAAA;IACA;IAEA;IACA8F,eAAAhE,IAAA,EAAAiE,WAAA;MACA,IAAAA,WAAA,QAAAA,WAAA;QACA;MACA;MAEA,KAAAhC,QAAA,CAAAiC,GAAA;QACAlC,MAAA,EAAAhC,IAAA,CAAAgC,MAAA;QACAmC,QAAA,EAAAF;MACA,GAAArC,IAAA,CAAAO,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAArB,QAAA,CAAAuB,OAAA;UACA,KAAAhC,IAAA,MAAAvC,OAAA;QACA;UACA,KAAAgD,QAAA,CAAAwB,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAC,KAAA;QACA,KAAA1B,QAAA,CAAAwB,KAAA;MACA;IACA;IAEA6B,KAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,SAAArG,IAAA,CAAAc,YAAA,YAAA4E,IAAA;YACA,KAAA1F,IAAA,CAAAc,YAAA,QAAAwF,cAAA,MAAAtG,IAAA,CAAAc,YAAA;UACA;;UAEA;UACA,SAAAd,IAAA,CAAAgB,YAAA,sBAAAM,aAAA,MAAAtB,IAAA,CAAAoF,YAAA;YACA,KAAApF,IAAA,CAAAe,YAAA,QAAAO,aAAA,MAAAtB,IAAA,CAAAoF,YAAA;UACA;UAEA,KAAAtB,QAAA;YACAyC,GAAA,OAAAvG,IAAA,CAAA+B,EAAA;YACAyE,MAAA,OAAAxG,IAAA,CAAA+B,EAAA;YACAtC,IAAA,OAAAO;UACA,GAAAyD,IAAA,CAAAO,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA,KAAArB,QAAA,CAAAuB,OAAA;cACA,KAAAhC,IAAA;cACA,KAAApC,WAAA;YACA;cACA,KAAA6C,QAAA,CAAAwB,KAAA,CAAAJ,GAAA,CAAAK,GAAA;YACA;UACA;QACA;MACA;IACA;IAEAiC,eAAAG,IAAA;MACA,MAAAC,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,MAAAC,KAAA,GAAAC,MAAA,CAAAJ,IAAA,CAAAK,QAAA,QAAAC,QAAA;MACA,MAAAC,GAAA,GAAAH,MAAA,CAAAJ,IAAA,CAAAQ,OAAA,IAAAF,QAAA;MACA,MAAAG,KAAA,GAAAL,MAAA,CAAAJ,IAAA,CAAAU,QAAA,IAAAJ,QAAA;MACA,MAAAK,OAAA,GAAAP,MAAA,CAAAJ,IAAA,CAAAY,UAAA,IAAAN,QAAA;MACA,MAAAO,OAAA,GAAAT,MAAA,CAAAJ,IAAA,CAAAc,UAAA,IAAAR,QAAA;MAEA,UAAAL,IAAA,IAAAE,KAAA,IAAAI,GAAA,IAAAE,KAAA,IAAAE,OAAA,IAAAE,OAAA;IACA;IAEAE,IAAAzF,EAAA;MACA;MACA,MAAAF,IAAA,QAAAlC,iBAAA,CAAAmC,IAAA,CAAAD,IAAA,IAAAA,IAAA,CAAAE,EAAA,KAAAA,EAAA;MACA,KAAAF,IAAA;QACA,KAAAe,QAAA,CAAAwB,KAAA;QACA;MACA;MAEA,KAAAf,QAAA;QAAAG,IAAA;MAAA,GAAAC,IAAA,CAAAgB,QAAA;QACA,KAAAX,QAAA,CAAA2D,MAAA;UACAC,MAAA;YAAA7D,MAAA,EAAAhC,IAAA,CAAAgC;UAAA;QACA,GAAAJ,IAAA,CAAAO,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAArB,QAAA,CAAAuB,OAAA;YACA,KAAAhC,IAAA;UACA;YACA,KAAAS,QAAA,CAAAwB,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAC,KAAA;IACA;IAEAqD,sBAAAC,IAAA;MACA,KAAA3G,GAAA,GAAA2G,IAAA,CAAA1C,GAAA,CAAA2C,CAAA,IAAAA,CAAA,CAAA9F,EAAA;IACA;IAEA+F,SAAA;MACA,UAAA7G,GAAA,CAAA0B,MAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAQ,QAAA;QAAAG,IAAA;MAAA,GAAAC,IAAA,CAAAgB,QAAA;QACA,KAAAX,QAAA,CAAA2D,MAAA,gBAAAhE,IAAA,CAAAO,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAArB,QAAA,CAAAuB,OAAA;YACA,KAAAjD,aAAA;YACA,KAAAD,GAAA;YACA,KAAAkB,IAAA;UACA;YACA,KAAAS,QAAA,CAAAwB,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA;MACA,GAAAC,KAAA;IACA;IAEAnC,KAAAvC,OAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAkE,QAAA,CAAAe,GAAA,eAAApB,IAAA,CAAAO,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAvE,SAAA,GAAAsE,GAAA,CAAAvE,IAAA;UACA,KAAAwF,eAAA;QACA;UACA,KAAArC,QAAA,CAAAwB,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAC,KAAA,CAAAC,GAAA;QACA,KAAA3B,QAAA,CAAAwB,KAAA;QACAI,OAAA,CAAAJ,KAAA,CAAAG,GAAA;MACA;IACA;IAIAwD,oBAAAnI,OAAA;MACA,KAAAuC,IAAA,CAAAvC,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}