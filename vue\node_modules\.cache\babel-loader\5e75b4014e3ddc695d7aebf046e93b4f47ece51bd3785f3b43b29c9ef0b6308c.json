{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport CandlestickView from './CandlestickView.js';\nimport CandlestickSeriesModel from './CandlestickSeries.js';\nimport preprocessor from './preprocessor.js';\nimport candlestickVisual from './candlestickVisual.js';\nimport candlestickLayout from './candlestickLayout.js';\nexport function install(registers) {\n  registers.registerChartView(CandlestickView);\n  registers.registerSeriesModel(CandlestickSeriesModel);\n  registers.registerPreprocessor(preprocessor);\n  registers.registerVisual(candlestickVisual);\n  registers.registerLayout(candlestickLayout);\n}", "map": {"version": 3, "names": ["CandlestickView", "CandlestickSeriesModel", "preprocessor", "candlestickVisual", "candlestickLayout", "install", "registers", "registerChartView", "registerSeriesModel", "registerPreprocessor", "registerVisual", "registerLayout"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/chart/candlestick/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport CandlestickView from './CandlestickView.js';\nimport CandlestickSeriesModel from './CandlestickSeries.js';\nimport preprocessor from './preprocessor.js';\nimport candlestickVisual from './candlestickVisual.js';\nimport candlestickLayout from './candlestickLayout.js';\nexport function install(registers) {\n  registers.registerChartView(CandlestickView);\n  registers.registerSeriesModel(CandlestickSeriesModel);\n  registers.registerPreprocessor(preprocessor);\n  registers.registerVisual(candlestickVisual);\n  registers.registerLayout(candlestickLayout);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,eAAe,MAAM,sBAAsB;AAClD,OAAOC,sBAAsB,MAAM,wBAAwB;AAC3D,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,iBAAiB,CAACP,eAAe,CAAC;EAC5CM,SAAS,CAACE,mBAAmB,CAACP,sBAAsB,CAAC;EACrDK,SAAS,CAACG,oBAAoB,CAACP,YAAY,CAAC;EAC5CI,SAAS,CAACI,cAAc,CAACP,iBAAiB,CAAC;EAC3CG,SAAS,CAACK,cAAc,CAACP,iBAAiB,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}