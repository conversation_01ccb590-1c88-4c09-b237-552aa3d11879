{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { groupData, SINGLE_REFERRING } from '../../util/model.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nvar DATA_NAME_INDEX = 2;\nvar ThemeRiverSeriesModel = /** @class */function (_super) {\n  __extends(ThemeRiverSeriesModel, _super);\n  function ThemeRiverSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ThemeRiverSeriesModel.type;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  ThemeRiverSeriesModel.prototype.init = function (option) {\n    // eslint-disable-next-line\n    _super.prototype.init.apply(this, arguments);\n    // Put this function here is for the sake of consistency of code style.\n    // Enable legend selection for each data item\n    // Use a function instead of direct access because data reference may changed\n    this.legendVisualProvider = new LegendVisualProvider(zrUtil.bind(this.getData, this), zrUtil.bind(this.getRawData, this));\n  };\n  /**\r\n   * If there is no value of a certain point in the time for some event,set it value to 0.\r\n   *\r\n   * @param {Array} data  initial data in the option\r\n   * @return {Array}\r\n   */\n  ThemeRiverSeriesModel.prototype.fixData = function (data) {\n    var rawDataLength = data.length;\n    /**\r\n     * Make sure every layer data get the same keys.\r\n     * The value index tells which layer has visited.\r\n     * {\r\n     *  2014/01/01: -1\r\n     * }\r\n     */\n    var timeValueKeys = {};\n    // grouped data by name\n    var groupResult = groupData(data, function (item) {\n      if (!timeValueKeys.hasOwnProperty(item[0] + '')) {\n        timeValueKeys[item[0] + ''] = -1;\n      }\n      return item[2];\n    });\n    var layerData = [];\n    groupResult.buckets.each(function (items, key) {\n      layerData.push({\n        name: key,\n        dataList: items\n      });\n    });\n    var layerNum = layerData.length;\n    for (var k = 0; k < layerNum; ++k) {\n      var name_1 = layerData[k].name;\n      for (var j = 0; j < layerData[k].dataList.length; ++j) {\n        var timeValue = layerData[k].dataList[j][0] + '';\n        timeValueKeys[timeValue] = k;\n      }\n      for (var timeValue in timeValueKeys) {\n        if (timeValueKeys.hasOwnProperty(timeValue) && timeValueKeys[timeValue] !== k) {\n          timeValueKeys[timeValue] = k;\n          data[rawDataLength] = [timeValue, 0, name_1];\n          rawDataLength++;\n        }\n      }\n    }\n    return data;\n  };\n  /**\r\n   * @override\r\n   * @param  option  the initial option that user gave\r\n   * @param  ecModel  the model object for themeRiver option\r\n   */\n  ThemeRiverSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    var singleAxisModel = this.getReferringComponents('singleAxis', SINGLE_REFERRING).models[0];\n    var axisType = singleAxisModel.get('type');\n    // filter the data item with the value of label is undefined\n    var filterData = zrUtil.filter(option.data, function (dataItem) {\n      return dataItem[2] !== undefined;\n    });\n    // ??? TODO design a stage to transfer data for themeRiver and lines?\n    var data = this.fixData(filterData || []);\n    var nameList = [];\n    var nameMap = this.nameMap = zrUtil.createHashMap();\n    var count = 0;\n    for (var i = 0; i < data.length; ++i) {\n      nameList.push(data[i][DATA_NAME_INDEX]);\n      if (!nameMap.get(data[i][DATA_NAME_INDEX])) {\n        nameMap.set(data[i][DATA_NAME_INDEX], count);\n        count++;\n      }\n    }\n    var dimensions = prepareSeriesDataSchema(data, {\n      coordDimensions: ['single'],\n      dimensionsDefine: [{\n        name: 'time',\n        type: getDimensionTypeByAxis(axisType)\n      }, {\n        name: 'value',\n        type: 'float'\n      }, {\n        name: 'name',\n        type: 'ordinal'\n      }],\n      encodeDefine: {\n        single: 0,\n        value: 1,\n        itemName: 2\n      }\n    }).dimensions;\n    var list = new SeriesData(dimensions, this);\n    list.initData(data);\n    return list;\n  };\n  /**\r\n   * The raw data is divided into multiple layers and each layer\r\n   *     has same name.\r\n   */\n  ThemeRiverSeriesModel.prototype.getLayerSeries = function () {\n    var data = this.getData();\n    var lenCount = data.count();\n    var indexArr = [];\n    for (var i = 0; i < lenCount; ++i) {\n      indexArr[i] = i;\n    }\n    var timeDim = data.mapDimension('single');\n    // data group by name\n    var groupResult = groupData(indexArr, function (index) {\n      return data.get('name', index);\n    });\n    var layerSeries = [];\n    groupResult.buckets.each(function (items, key) {\n      items.sort(function (index1, index2) {\n        return data.get(timeDim, index1) - data.get(timeDim, index2);\n      });\n      layerSeries.push({\n        name: key,\n        indices: items\n      });\n    });\n    return layerSeries;\n  };\n  /**\r\n   * Get data indices for show tooltip content\r\n   */\n  ThemeRiverSeriesModel.prototype.getAxisTooltipData = function (dim, value, baseAxis) {\n    if (!zrUtil.isArray(dim)) {\n      dim = dim ? [dim] : [];\n    }\n    var data = this.getData();\n    var layerSeries = this.getLayerSeries();\n    var indices = [];\n    var layerNum = layerSeries.length;\n    var nestestValue;\n    for (var i = 0; i < layerNum; ++i) {\n      var minDist = Number.MAX_VALUE;\n      var nearestIdx = -1;\n      var pointNum = layerSeries[i].indices.length;\n      for (var j = 0; j < pointNum; ++j) {\n        var theValue = data.get(dim[0], layerSeries[i].indices[j]);\n        var dist = Math.abs(theValue - value);\n        if (dist <= minDist) {\n          nestestValue = theValue;\n          minDist = dist;\n          nearestIdx = layerSeries[i].indices[j];\n        }\n      }\n      indices.push(nearestIdx);\n    }\n    return {\n      dataIndices: indices,\n      nestestValue: nestestValue\n    };\n  };\n  ThemeRiverSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var name = data.getName(dataIndex);\n    var value = data.get(data.mapDimension('value'), dataIndex);\n    return createTooltipMarkup('nameValue', {\n      name: name,\n      value: value\n    });\n  };\n  ThemeRiverSeriesModel.type = 'series.themeRiver';\n  ThemeRiverSeriesModel.dependencies = ['singleAxis'];\n  ThemeRiverSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    colorBy: 'data',\n    coordinateSystem: 'singleAxis',\n    // gap in axis's orthogonal orientation\n    boundaryGap: ['10%', '10%'],\n    // legendHoverLink: true,\n    singleAxisIndex: 0,\n    animationEasing: 'linear',\n    label: {\n      margin: 4,\n      show: true,\n      position: 'left',\n      fontSize: 11\n    },\n    emphasis: {\n      label: {\n        show: true\n      }\n    }\n  };\n  return ThemeRiverSeriesModel;\n}(SeriesModel);\nexport default ThemeRiverSeriesModel;", "map": {"version": 3, "names": ["__extends", "SeriesModel", "prepareSeriesDataSchema", "getDimensionTypeByAxis", "SeriesData", "zrUtil", "groupData", "SINGLE_REFERRING", "LegendVisualProvider", "createTooltipMarkup", "DATA_NAME_INDEX", "ThemeRiverSeriesModel", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "option", "legend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bind", "getData", "getRawData", "fixData", "data", "rawDataLength", "length", "timeV<PERSON>ue<PERSON>eys", "groupResult", "item", "hasOwnProperty", "layerData", "buckets", "each", "items", "key", "push", "name", "dataList", "layerNum", "k", "name_1", "j", "timeValue", "getInitialData", "ecModel", "singleAxisModel", "getReferringComponents", "models", "axisType", "get", "filterData", "filter", "dataItem", "undefined", "nameList", "nameMap", "createHashMap", "count", "i", "set", "dimensions", "coordDimensions", "dimensionsDefine", "encodeDefine", "single", "value", "itemName", "list", "initData", "getLayerSeries", "lenCount", "indexArr", "timeDim", "mapDimension", "index", "layerSeries", "sort", "index1", "index2", "indices", "getAxisTooltipData", "dim", "baseAxis", "isArray", "nestest<PERSON><PERSON>ue", "minDist", "Number", "MAX_VALUE", "nearestIdx", "pointNum", "theValue", "dist", "Math", "abs", "dataIndices", "formatTooltip", "dataIndex", "multipleSeries", "dataType", "getName", "dependencies", "defaultOption", "z", "colorBy", "coordinateSystem", "boundaryGap", "singleAxisIndex", "animationEasing", "label", "margin", "show", "position", "fontSize", "emphasis"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/chart/themeRiver/ThemeRiverSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { groupData, SINGLE_REFERRING } from '../../util/model.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nvar DATA_NAME_INDEX = 2;\nvar ThemeRiverSeriesModel = /** @class */function (_super) {\n  __extends(ThemeRiverSeriesModel, _super);\n  function ThemeRiverSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ThemeRiverSeriesModel.type;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  ThemeRiverSeriesModel.prototype.init = function (option) {\n    // eslint-disable-next-line\n    _super.prototype.init.apply(this, arguments);\n    // Put this function here is for the sake of consistency of code style.\n    // Enable legend selection for each data item\n    // Use a function instead of direct access because data reference may changed\n    this.legendVisualProvider = new LegendVisualProvider(zrUtil.bind(this.getData, this), zrUtil.bind(this.getRawData, this));\n  };\n  /**\r\n   * If there is no value of a certain point in the time for some event,set it value to 0.\r\n   *\r\n   * @param {Array} data  initial data in the option\r\n   * @return {Array}\r\n   */\n  ThemeRiverSeriesModel.prototype.fixData = function (data) {\n    var rawDataLength = data.length;\n    /**\r\n     * Make sure every layer data get the same keys.\r\n     * The value index tells which layer has visited.\r\n     * {\r\n     *  2014/01/01: -1\r\n     * }\r\n     */\n    var timeValueKeys = {};\n    // grouped data by name\n    var groupResult = groupData(data, function (item) {\n      if (!timeValueKeys.hasOwnProperty(item[0] + '')) {\n        timeValueKeys[item[0] + ''] = -1;\n      }\n      return item[2];\n    });\n    var layerData = [];\n    groupResult.buckets.each(function (items, key) {\n      layerData.push({\n        name: key,\n        dataList: items\n      });\n    });\n    var layerNum = layerData.length;\n    for (var k = 0; k < layerNum; ++k) {\n      var name_1 = layerData[k].name;\n      for (var j = 0; j < layerData[k].dataList.length; ++j) {\n        var timeValue = layerData[k].dataList[j][0] + '';\n        timeValueKeys[timeValue] = k;\n      }\n      for (var timeValue in timeValueKeys) {\n        if (timeValueKeys.hasOwnProperty(timeValue) && timeValueKeys[timeValue] !== k) {\n          timeValueKeys[timeValue] = k;\n          data[rawDataLength] = [timeValue, 0, name_1];\n          rawDataLength++;\n        }\n      }\n    }\n    return data;\n  };\n  /**\r\n   * @override\r\n   * @param  option  the initial option that user gave\r\n   * @param  ecModel  the model object for themeRiver option\r\n   */\n  ThemeRiverSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    var singleAxisModel = this.getReferringComponents('singleAxis', SINGLE_REFERRING).models[0];\n    var axisType = singleAxisModel.get('type');\n    // filter the data item with the value of label is undefined\n    var filterData = zrUtil.filter(option.data, function (dataItem) {\n      return dataItem[2] !== undefined;\n    });\n    // ??? TODO design a stage to transfer data for themeRiver and lines?\n    var data = this.fixData(filterData || []);\n    var nameList = [];\n    var nameMap = this.nameMap = zrUtil.createHashMap();\n    var count = 0;\n    for (var i = 0; i < data.length; ++i) {\n      nameList.push(data[i][DATA_NAME_INDEX]);\n      if (!nameMap.get(data[i][DATA_NAME_INDEX])) {\n        nameMap.set(data[i][DATA_NAME_INDEX], count);\n        count++;\n      }\n    }\n    var dimensions = prepareSeriesDataSchema(data, {\n      coordDimensions: ['single'],\n      dimensionsDefine: [{\n        name: 'time',\n        type: getDimensionTypeByAxis(axisType)\n      }, {\n        name: 'value',\n        type: 'float'\n      }, {\n        name: 'name',\n        type: 'ordinal'\n      }],\n      encodeDefine: {\n        single: 0,\n        value: 1,\n        itemName: 2\n      }\n    }).dimensions;\n    var list = new SeriesData(dimensions, this);\n    list.initData(data);\n    return list;\n  };\n  /**\r\n   * The raw data is divided into multiple layers and each layer\r\n   *     has same name.\r\n   */\n  ThemeRiverSeriesModel.prototype.getLayerSeries = function () {\n    var data = this.getData();\n    var lenCount = data.count();\n    var indexArr = [];\n    for (var i = 0; i < lenCount; ++i) {\n      indexArr[i] = i;\n    }\n    var timeDim = data.mapDimension('single');\n    // data group by name\n    var groupResult = groupData(indexArr, function (index) {\n      return data.get('name', index);\n    });\n    var layerSeries = [];\n    groupResult.buckets.each(function (items, key) {\n      items.sort(function (index1, index2) {\n        return data.get(timeDim, index1) - data.get(timeDim, index2);\n      });\n      layerSeries.push({\n        name: key,\n        indices: items\n      });\n    });\n    return layerSeries;\n  };\n  /**\r\n   * Get data indices for show tooltip content\r\n   */\n  ThemeRiverSeriesModel.prototype.getAxisTooltipData = function (dim, value, baseAxis) {\n    if (!zrUtil.isArray(dim)) {\n      dim = dim ? [dim] : [];\n    }\n    var data = this.getData();\n    var layerSeries = this.getLayerSeries();\n    var indices = [];\n    var layerNum = layerSeries.length;\n    var nestestValue;\n    for (var i = 0; i < layerNum; ++i) {\n      var minDist = Number.MAX_VALUE;\n      var nearestIdx = -1;\n      var pointNum = layerSeries[i].indices.length;\n      for (var j = 0; j < pointNum; ++j) {\n        var theValue = data.get(dim[0], layerSeries[i].indices[j]);\n        var dist = Math.abs(theValue - value);\n        if (dist <= minDist) {\n          nestestValue = theValue;\n          minDist = dist;\n          nearestIdx = layerSeries[i].indices[j];\n        }\n      }\n      indices.push(nearestIdx);\n    }\n    return {\n      dataIndices: indices,\n      nestestValue: nestestValue\n    };\n  };\n  ThemeRiverSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var name = data.getName(dataIndex);\n    var value = data.get(data.mapDimension('value'), dataIndex);\n    return createTooltipMarkup('nameValue', {\n      name: name,\n      value: value\n    });\n  };\n  ThemeRiverSeriesModel.type = 'series.themeRiver';\n  ThemeRiverSeriesModel.dependencies = ['singleAxis'];\n  ThemeRiverSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    colorBy: 'data',\n    coordinateSystem: 'singleAxis',\n    // gap in axis's orthogonal orientation\n    boundaryGap: ['10%', '10%'],\n    // legendHoverLink: true,\n    singleAxisIndex: 0,\n    animationEasing: 'linear',\n    label: {\n      margin: 4,\n      show: true,\n      position: 'left',\n      fontSize: 11\n    },\n    emphasis: {\n      label: {\n        show: true\n      }\n    }\n  };\n  return ThemeRiverSeriesModel;\n}(SeriesModel);\nexport default ThemeRiverSeriesModel;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,uBAAuB,MAAM,uCAAuC;AAC3E,SAASC,sBAAsB,QAAQ,sCAAsC;AAC7E,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,SAAS,EAAEC,gBAAgB,QAAQ,qBAAqB;AACjE,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,IAAIC,eAAe,GAAG,CAAC;AACvB,IAAIC,qBAAqB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACzDZ,SAAS,CAACW,qBAAqB,EAAEC,MAAM,CAAC;EACxC,SAASD,qBAAqBA,CAAA,EAAG;IAC/B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,qBAAqB,CAACK,IAAI;IACvC,OAAOH,KAAK;EACd;EACA;AACF;AACA;EACEF,qBAAqB,CAACM,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAE;IACvD;IACAP,MAAM,CAACK,SAAS,CAACC,IAAI,CAACJ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC5C;IACA;IACA;IACA,IAAI,CAACK,oBAAoB,GAAG,IAAIZ,oBAAoB,CAACH,MAAM,CAACgB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC,EAAEjB,MAAM,CAACgB,IAAI,CAAC,IAAI,CAACE,UAAU,EAAE,IAAI,CAAC,CAAC;EAC3H,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEZ,qBAAqB,CAACM,SAAS,CAACO,OAAO,GAAG,UAAUC,IAAI,EAAE;IACxD,IAAIC,aAAa,GAAGD,IAAI,CAACE,MAAM;IAC/B;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAIC,aAAa,GAAG,CAAC,CAAC;IACtB;IACA,IAAIC,WAAW,GAAGvB,SAAS,CAACmB,IAAI,EAAE,UAAUK,IAAI,EAAE;MAChD,IAAI,CAACF,aAAa,CAACG,cAAc,CAACD,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE;QAC/CF,aAAa,CAACE,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;MAClC;MACA,OAAOA,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC;IACF,IAAIE,SAAS,GAAG,EAAE;IAClBH,WAAW,CAACI,OAAO,CAACC,IAAI,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;MAC7CJ,SAAS,CAACK,IAAI,CAAC;QACbC,IAAI,EAAEF,GAAG;QACTG,QAAQ,EAAEJ;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIK,QAAQ,GAAGR,SAAS,CAACL,MAAM;IAC/B,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAE,EAAEC,CAAC,EAAE;MACjC,IAAIC,MAAM,GAAGV,SAAS,CAACS,CAAC,CAAC,CAACH,IAAI;MAC9B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,SAAS,CAACS,CAAC,CAAC,CAACF,QAAQ,CAACZ,MAAM,EAAE,EAAEgB,CAAC,EAAE;QACrD,IAAIC,SAAS,GAAGZ,SAAS,CAACS,CAAC,CAAC,CAACF,QAAQ,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QAChDf,aAAa,CAACgB,SAAS,CAAC,GAAGH,CAAC;MAC9B;MACA,KAAK,IAAIG,SAAS,IAAIhB,aAAa,EAAE;QACnC,IAAIA,aAAa,CAACG,cAAc,CAACa,SAAS,CAAC,IAAIhB,aAAa,CAACgB,SAAS,CAAC,KAAKH,CAAC,EAAE;UAC7Eb,aAAa,CAACgB,SAAS,CAAC,GAAGH,CAAC;UAC5BhB,IAAI,CAACC,aAAa,CAAC,GAAG,CAACkB,SAAS,EAAE,CAAC,EAAEF,MAAM,CAAC;UAC5ChB,aAAa,EAAE;QACjB;MACF;IACF;IACA,OAAOD,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;EACEd,qBAAqB,CAACM,SAAS,CAAC4B,cAAc,GAAG,UAAU1B,MAAM,EAAE2B,OAAO,EAAE;IAC1E,IAAIC,eAAe,GAAG,IAAI,CAACC,sBAAsB,CAAC,YAAY,EAAEzC,gBAAgB,CAAC,CAAC0C,MAAM,CAAC,CAAC,CAAC;IAC3F,IAAIC,QAAQ,GAAGH,eAAe,CAACI,GAAG,CAAC,MAAM,CAAC;IAC1C;IACA,IAAIC,UAAU,GAAG/C,MAAM,CAACgD,MAAM,CAAClC,MAAM,CAACM,IAAI,EAAE,UAAU6B,QAAQ,EAAE;MAC9D,OAAOA,QAAQ,CAAC,CAAC,CAAC,KAAKC,SAAS;IAClC,CAAC,CAAC;IACF;IACA,IAAI9B,IAAI,GAAG,IAAI,CAACD,OAAO,CAAC4B,UAAU,IAAI,EAAE,CAAC;IACzC,IAAII,QAAQ,GAAG,EAAE;IACjB,IAAIC,OAAO,GAAG,IAAI,CAACA,OAAO,GAAGpD,MAAM,CAACqD,aAAa,CAAC,CAAC;IACnD,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnC,IAAI,CAACE,MAAM,EAAE,EAAEiC,CAAC,EAAE;MACpCJ,QAAQ,CAACnB,IAAI,CAACZ,IAAI,CAACmC,CAAC,CAAC,CAAClD,eAAe,CAAC,CAAC;MACvC,IAAI,CAAC+C,OAAO,CAACN,GAAG,CAAC1B,IAAI,CAACmC,CAAC,CAAC,CAAClD,eAAe,CAAC,CAAC,EAAE;QAC1C+C,OAAO,CAACI,GAAG,CAACpC,IAAI,CAACmC,CAAC,CAAC,CAAClD,eAAe,CAAC,EAAEiD,KAAK,CAAC;QAC5CA,KAAK,EAAE;MACT;IACF;IACA,IAAIG,UAAU,GAAG5D,uBAAuB,CAACuB,IAAI,EAAE;MAC7CsC,eAAe,EAAE,CAAC,QAAQ,CAAC;MAC3BC,gBAAgB,EAAE,CAAC;QACjB1B,IAAI,EAAE,MAAM;QACZtB,IAAI,EAAEb,sBAAsB,CAAC+C,QAAQ;MACvC,CAAC,EAAE;QACDZ,IAAI,EAAE,OAAO;QACbtB,IAAI,EAAE;MACR,CAAC,EAAE;QACDsB,IAAI,EAAE,MAAM;QACZtB,IAAI,EAAE;MACR,CAAC,CAAC;MACFiD,YAAY,EAAE;QACZC,MAAM,EAAE,CAAC;QACTC,KAAK,EAAE,CAAC;QACRC,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC,CAACN,UAAU;IACb,IAAIO,IAAI,GAAG,IAAIjE,UAAU,CAAC0D,UAAU,EAAE,IAAI,CAAC;IAC3CO,IAAI,CAACC,QAAQ,CAAC7C,IAAI,CAAC;IACnB,OAAO4C,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;EACE1D,qBAAqB,CAACM,SAAS,CAACsD,cAAc,GAAG,YAAY;IAC3D,IAAI9C,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IACzB,IAAIkD,QAAQ,GAAG/C,IAAI,CAACkC,KAAK,CAAC,CAAC;IAC3B,IAAIc,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,QAAQ,EAAE,EAAEZ,CAAC,EAAE;MACjCa,QAAQ,CAACb,CAAC,CAAC,GAAGA,CAAC;IACjB;IACA,IAAIc,OAAO,GAAGjD,IAAI,CAACkD,YAAY,CAAC,QAAQ,CAAC;IACzC;IACA,IAAI9C,WAAW,GAAGvB,SAAS,CAACmE,QAAQ,EAAE,UAAUG,KAAK,EAAE;MACrD,OAAOnD,IAAI,CAAC0B,GAAG,CAAC,MAAM,EAAEyB,KAAK,CAAC;IAChC,CAAC,CAAC;IACF,IAAIC,WAAW,GAAG,EAAE;IACpBhD,WAAW,CAACI,OAAO,CAACC,IAAI,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;MAC7CD,KAAK,CAAC2C,IAAI,CAAC,UAAUC,MAAM,EAAEC,MAAM,EAAE;QACnC,OAAOvD,IAAI,CAAC0B,GAAG,CAACuB,OAAO,EAAEK,MAAM,CAAC,GAAGtD,IAAI,CAAC0B,GAAG,CAACuB,OAAO,EAAEM,MAAM,CAAC;MAC9D,CAAC,CAAC;MACFH,WAAW,CAACxC,IAAI,CAAC;QACfC,IAAI,EAAEF,GAAG;QACT6C,OAAO,EAAE9C;MACX,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO0C,WAAW;EACpB,CAAC;EACD;AACF;AACA;EACElE,qBAAqB,CAACM,SAAS,CAACiE,kBAAkB,GAAG,UAAUC,GAAG,EAAEhB,KAAK,EAAEiB,QAAQ,EAAE;IACnF,IAAI,CAAC/E,MAAM,CAACgF,OAAO,CAACF,GAAG,CAAC,EAAE;MACxBA,GAAG,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC,GAAG,EAAE;IACxB;IACA,IAAI1D,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IACzB,IAAIuD,WAAW,GAAG,IAAI,CAACN,cAAc,CAAC,CAAC;IACvC,IAAIU,OAAO,GAAG,EAAE;IAChB,IAAIzC,QAAQ,GAAGqC,WAAW,CAAClD,MAAM;IACjC,IAAI2D,YAAY;IAChB,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,QAAQ,EAAE,EAAEoB,CAAC,EAAE;MACjC,IAAI2B,OAAO,GAAGC,MAAM,CAACC,SAAS;MAC9B,IAAIC,UAAU,GAAG,CAAC,CAAC;MACnB,IAAIC,QAAQ,GAAGd,WAAW,CAACjB,CAAC,CAAC,CAACqB,OAAO,CAACtD,MAAM;MAC5C,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,QAAQ,EAAE,EAAEhD,CAAC,EAAE;QACjC,IAAIiD,QAAQ,GAAGnE,IAAI,CAAC0B,GAAG,CAACgC,GAAG,CAAC,CAAC,CAAC,EAAEN,WAAW,CAACjB,CAAC,CAAC,CAACqB,OAAO,CAACtC,CAAC,CAAC,CAAC;QAC1D,IAAIkD,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACH,QAAQ,GAAGzB,KAAK,CAAC;QACrC,IAAI0B,IAAI,IAAIN,OAAO,EAAE;UACnBD,YAAY,GAAGM,QAAQ;UACvBL,OAAO,GAAGM,IAAI;UACdH,UAAU,GAAGb,WAAW,CAACjB,CAAC,CAAC,CAACqB,OAAO,CAACtC,CAAC,CAAC;QACxC;MACF;MACAsC,OAAO,CAAC5C,IAAI,CAACqD,UAAU,CAAC;IAC1B;IACA,OAAO;MACLM,WAAW,EAAEf,OAAO;MACpBK,YAAY,EAAEA;IAChB,CAAC;EACH,CAAC;EACD3E,qBAAqB,CAACM,SAAS,CAACgF,aAAa,GAAG,UAAUC,SAAS,EAAEC,cAAc,EAAEC,QAAQ,EAAE;IAC7F,IAAI3E,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;IACzB,IAAIgB,IAAI,GAAGb,IAAI,CAAC4E,OAAO,CAACH,SAAS,CAAC;IAClC,IAAI/B,KAAK,GAAG1C,IAAI,CAAC0B,GAAG,CAAC1B,IAAI,CAACkD,YAAY,CAAC,OAAO,CAAC,EAAEuB,SAAS,CAAC;IAC3D,OAAOzF,mBAAmB,CAAC,WAAW,EAAE;MACtC6B,IAAI,EAAEA,IAAI;MACV6B,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC;EACDxD,qBAAqB,CAACK,IAAI,GAAG,mBAAmB;EAChDL,qBAAqB,CAAC2F,YAAY,GAAG,CAAC,YAAY,CAAC;EACnD3F,qBAAqB,CAAC4F,aAAa,GAAG;IACpC;IACAC,CAAC,EAAE,CAAC;IACJC,OAAO,EAAE,MAAM;IACfC,gBAAgB,EAAE,YAAY;IAC9B;IACAC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;IAC3B;IACAC,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAE,QAAQ;IACzBC,KAAK,EAAE;MACLC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE;IACZ,CAAC;IACDC,QAAQ,EAAE;MACRL,KAAK,EAAE;QACLE,IAAI,EAAE;MACR;IACF;EACF,CAAC;EACD,OAAOrG,qBAAqB;AAC9B,CAAC,CAACV,WAAW,CAAC;AACd,eAAeU,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}