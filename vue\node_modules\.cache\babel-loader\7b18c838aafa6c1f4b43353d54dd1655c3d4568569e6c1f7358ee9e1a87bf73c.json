{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { registerPreprocessor, registerProcessor, registerPostInit, registerPostUpdate, registerAction, registerCoordinateSystem, registerLayout, registerVisual, registerTransform, registerLoading, registerMap, registerUpdateLifecycle, PRIORITY } from './core/echarts.js';\nimport ComponentView from './view/Component.js';\nimport ChartView from './view/Chart.js';\nimport ComponentModel from './model/Component.js';\nimport SeriesModel from './model/Series.js';\nimport { isFunction, indexOf, isArray, each } from 'zrender/lib/core/util.js';\nimport { registerImpl } from './core/impl.js';\nimport { registerPainter } from 'zrender/lib/zrender.js';\nvar extensions = [];\nvar extensionRegisters = {\n  registerPreprocessor: registerPreprocessor,\n  registerProcessor: registerProcessor,\n  registerPostInit: registerPostInit,\n  registerPostUpdate: registerPostUpdate,\n  registerUpdateLifecycle: registerUpdateLifecycle,\n  registerAction: registerAction,\n  registerCoordinateSystem: registerCoordinateSystem,\n  registerLayout: registerLayout,\n  registerVisual: registerVisual,\n  registerTransform: registerTransform,\n  registerLoading: registerLoading,\n  registerMap: registerMap,\n  registerImpl: registerImpl,\n  PRIORITY: PRIORITY,\n  ComponentModel: ComponentModel,\n  ComponentView: ComponentView,\n  SeriesModel: SeriesModel,\n  ChartView: ChartView,\n  // TODO Use ComponentModel and SeriesModel instead of Constructor\n  registerComponentModel: function (ComponentModelClass) {\n    ComponentModel.registerClass(ComponentModelClass);\n  },\n  registerComponentView: function (ComponentViewClass) {\n    ComponentView.registerClass(ComponentViewClass);\n  },\n  registerSeriesModel: function (SeriesModelClass) {\n    SeriesModel.registerClass(SeriesModelClass);\n  },\n  registerChartView: function (ChartViewClass) {\n    ChartView.registerClass(ChartViewClass);\n  },\n  registerSubTypeDefaulter: function (componentType, defaulter) {\n    ComponentModel.registerSubTypeDefaulter(componentType, defaulter);\n  },\n  registerPainter: function (painterType, PainterCtor) {\n    registerPainter(painterType, PainterCtor);\n  }\n};\nexport function use(ext) {\n  if (isArray(ext)) {\n    // use([ChartLine, ChartBar]);\n    each(ext, function (singleExt) {\n      use(singleExt);\n    });\n    return;\n  }\n  if (indexOf(extensions, ext) >= 0) {\n    return;\n  }\n  extensions.push(ext);\n  if (isFunction(ext)) {\n    ext = {\n      install: ext\n    };\n  }\n  ext.install(extensionRegisters);\n}", "map": {"version": 3, "names": ["registerPreprocessor", "registerProcessor", "registerPostInit", "registerPostUpdate", "registerAction", "registerCoordinateSystem", "registerLayout", "registerVisual", "registerTransform", "registerLoading", "registerMap", "registerUpdateLifecycle", "PRIORITY", "ComponentView", "ChartView", "ComponentModel", "SeriesModel", "isFunction", "indexOf", "isArray", "each", "registerImpl", "registerPainter", "extensions", "extensionRegisters", "registerComponentModel", "ComponentModelClass", "registerClass", "registerComponentView", "ComponentViewClass", "registerSeriesModel", "SeriesModelClass", "registerChartView", "ChartViewClass", "registerSubTypeDefaulter", "componentType", "defaulter", "painterType", "Painter<PERSON><PERSON>", "use", "ext", "singleExt", "push", "install"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/extension.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { registerPreprocessor, registerProcessor, registerPostInit, registerPostUpdate, registerAction, registerCoordinateSystem, registerLayout, registerVisual, registerTransform, registerLoading, registerMap, registerUpdateLifecycle, PRIORITY } from './core/echarts.js';\nimport ComponentView from './view/Component.js';\nimport ChartView from './view/Chart.js';\nimport ComponentModel from './model/Component.js';\nimport SeriesModel from './model/Series.js';\nimport { isFunction, indexOf, isArray, each } from 'zrender/lib/core/util.js';\nimport { registerImpl } from './core/impl.js';\nimport { registerPainter } from 'zrender/lib/zrender.js';\nvar extensions = [];\nvar extensionRegisters = {\n  registerPreprocessor: registerPreprocessor,\n  registerProcessor: registerProcessor,\n  registerPostInit: registerPostInit,\n  registerPostUpdate: registerPostUpdate,\n  registerUpdateLifecycle: registerUpdateLifecycle,\n  registerAction: registerAction,\n  registerCoordinateSystem: registerCoordinateSystem,\n  registerLayout: registerLayout,\n  registerVisual: registerVisual,\n  registerTransform: registerTransform,\n  registerLoading: registerLoading,\n  registerMap: registerMap,\n  registerImpl: registerImpl,\n  PRIORITY: PRIORITY,\n  ComponentModel: ComponentModel,\n  ComponentView: ComponentView,\n  SeriesModel: SeriesModel,\n  ChartView: ChartView,\n  // TODO Use ComponentModel and SeriesModel instead of Constructor\n  registerComponentModel: function (ComponentModelClass) {\n    ComponentModel.registerClass(ComponentModelClass);\n  },\n  registerComponentView: function (ComponentViewClass) {\n    ComponentView.registerClass(ComponentViewClass);\n  },\n  registerSeriesModel: function (SeriesModelClass) {\n    SeriesModel.registerClass(SeriesModelClass);\n  },\n  registerChartView: function (ChartViewClass) {\n    ChartView.registerClass(ChartViewClass);\n  },\n  registerSubTypeDefaulter: function (componentType, defaulter) {\n    ComponentModel.registerSubTypeDefaulter(componentType, defaulter);\n  },\n  registerPainter: function (painterType, PainterCtor) {\n    registerPainter(painterType, PainterCtor);\n  }\n};\nexport function use(ext) {\n  if (isArray(ext)) {\n    // use([ChartLine, ChartBar]);\n    each(ext, function (singleExt) {\n      use(singleExt);\n    });\n    return;\n  }\n  if (indexOf(extensions, ext) >= 0) {\n    return;\n  }\n  extensions.push(ext);\n  if (isFunction(ext)) {\n    ext = {\n      install: ext\n    };\n  }\n  ext.install(extensionRegisters);\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,oBAAoB,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,wBAAwB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,WAAW,EAAEC,uBAAuB,EAAEC,QAAQ,QAAQ,mBAAmB;AAC/Q,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,SAASC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,IAAI,QAAQ,0BAA0B;AAC7E,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,IAAIC,UAAU,GAAG,EAAE;AACnB,IAAIC,kBAAkB,GAAG;EACvBxB,oBAAoB,EAAEA,oBAAoB;EAC1CC,iBAAiB,EAAEA,iBAAiB;EACpCC,gBAAgB,EAAEA,gBAAgB;EAClCC,kBAAkB,EAAEA,kBAAkB;EACtCQ,uBAAuB,EAAEA,uBAAuB;EAChDP,cAAc,EAAEA,cAAc;EAC9BC,wBAAwB,EAAEA,wBAAwB;EAClDC,cAAc,EAAEA,cAAc;EAC9BC,cAAc,EAAEA,cAAc;EAC9BC,iBAAiB,EAAEA,iBAAiB;EACpCC,eAAe,EAAEA,eAAe;EAChCC,WAAW,EAAEA,WAAW;EACxBW,YAAY,EAAEA,YAAY;EAC1BT,QAAQ,EAAEA,QAAQ;EAClBG,cAAc,EAAEA,cAAc;EAC9BF,aAAa,EAAEA,aAAa;EAC5BG,WAAW,EAAEA,WAAW;EACxBF,SAAS,EAAEA,SAAS;EACpB;EACAW,sBAAsB,EAAE,SAAAA,CAAUC,mBAAmB,EAAE;IACrDX,cAAc,CAACY,aAAa,CAACD,mBAAmB,CAAC;EACnD,CAAC;EACDE,qBAAqB,EAAE,SAAAA,CAAUC,kBAAkB,EAAE;IACnDhB,aAAa,CAACc,aAAa,CAACE,kBAAkB,CAAC;EACjD,CAAC;EACDC,mBAAmB,EAAE,SAAAA,CAAUC,gBAAgB,EAAE;IAC/Cf,WAAW,CAACW,aAAa,CAACI,gBAAgB,CAAC;EAC7C,CAAC;EACDC,iBAAiB,EAAE,SAAAA,CAAUC,cAAc,EAAE;IAC3CnB,SAAS,CAACa,aAAa,CAACM,cAAc,CAAC;EACzC,CAAC;EACDC,wBAAwB,EAAE,SAAAA,CAAUC,aAAa,EAAEC,SAAS,EAAE;IAC5DrB,cAAc,CAACmB,wBAAwB,CAACC,aAAa,EAAEC,SAAS,CAAC;EACnE,CAAC;EACDd,eAAe,EAAE,SAAAA,CAAUe,WAAW,EAAEC,WAAW,EAAE;IACnDhB,eAAe,CAACe,WAAW,EAAEC,WAAW,CAAC;EAC3C;AACF,CAAC;AACD,OAAO,SAASC,GAAGA,CAACC,GAAG,EAAE;EACvB,IAAIrB,OAAO,CAACqB,GAAG,CAAC,EAAE;IAChB;IACApB,IAAI,CAACoB,GAAG,EAAE,UAAUC,SAAS,EAAE;MAC7BF,GAAG,CAACE,SAAS,CAAC;IAChB,CAAC,CAAC;IACF;EACF;EACA,IAAIvB,OAAO,CAACK,UAAU,EAAEiB,GAAG,CAAC,IAAI,CAAC,EAAE;IACjC;EACF;EACAjB,UAAU,CAACmB,IAAI,CAACF,GAAG,CAAC;EACpB,IAAIvB,UAAU,CAACuB,GAAG,CAAC,EAAE;IACnBA,GAAG,GAAG;MACJG,OAAO,EAAEH;IACX,CAAC;EACH;EACAA,GAAG,CAACG,OAAO,CAACnB,kBAAkB,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}