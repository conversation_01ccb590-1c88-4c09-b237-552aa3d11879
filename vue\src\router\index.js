import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

// 解决导航栏或者底部导航tabBar中的vue-router在3.0版本以上频繁点击菜单报错的问题。
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push (location) {
  return originalPush.call(this, location).catch(err => err)
}

const routes = [
  {
    path: '/',
    name: 'Manager',
    component: () => import('../views/Manager.vue'),
    redirect: '/front/home',
    children: [
      { path: '403', name: 'NoAuth', meta: { name: '无权限' }, component: () => import('../views/manager/403') },
      { path: 'home', name: 'Home', meta: { name: '系统首页' }, component: () => import('../views/manager/Home') },
      { path: 'admin', name: 'Admin', meta: { name: '管理员信息' }, component: () => import('../views/manager/Admin') },
      { path: 'business', name: 'Business', meta: { name: '商家信息' }, component: () => import('../views/manager/Business') },
      { path: 'user', name: 'User', meta: { name: '用户信息' }, component: () => import('../views/manager/User') },
      { path: 'adminPerson', name: 'AdminPerson', meta: { name: '个人信息' }, component: () => import('../views/manager/AdminPerson') },
      { path: 'businessPerson', name: 'BusinessPerson', meta: { name: '个人信息' }, component: () => import('../views/manager/BusinessPerson') },
      { path: 'password', name: 'Password', meta: { name: '修改密码' }, component: () => import('../views/manager/Password') },
      { path: 'notice', name: 'Notice', meta: { name: '公告信息' }, component: () => import('../views/manager/Notice') },
      { path: 'category', name: 'Category', meta: { name: '分类管理' }, component: () => import('../views/manager/Category') },
      { path: 'foods', name: 'Foods', meta: { name: '食物信息' }, component: () => import('../views/manager/Foods') },
      { path: 'table', name: 'Table', meta: { name: '餐桌管理' }, component: () => import('../views/manager/Table') },
      { path: 'dingdan', name: 'Dingdan', meta: { name: '订单信息' }, component: () => import('../views/manager/Dingdan') },
      { path: 'complaint', name: 'Complaint', meta: { name: '投诉管理' }, component: () => import('../views/manager/Complaint')},
      { path: 'leavemess', name: 'Leavemess', meta: { name: '咨询评论' }, component: () => import('../views/manager/Leavemess') },
      { path: 'pinglun', name: 'Pinglun', meta: { name: '评论管理' }, component: () => import('../views/manager/Pinglun')},
      { path: 'blogs', name: 'Blogs', meta: { name: '博客管理' }, component: () => import('../views/manager/Blogs')},
      { path: 'freemovies', name: 'Freemovies', meta: { name: '点餐推荐' }, component: () => import('../views/manager/Freemovies') },
      { path: 'complaint', name: 'Complaint', meta: { name: '点餐投诉管理' }, component: () => import('../views/manager/Complaint') },


    ]
  },
  {
    path: '/front',
    name: 'Front',
    component: () => import('../views/Front.vue'),
    children: [
      { path: 'home', name: 'Home', meta: { name: '系统首页' }, component: () => import('../views/front/Home') },
      { path: 'person', name: 'Person', meta: { name: '个人信息' }, component: () => import('../views/front/Person') },
      { path: 'business', name: 'Business', meta: { name: '商家店铺' }, component: () => import('../views/front/Business') },
      { path: 'foods', name: 'Foods', meta: { name: '食物信息' }, component: () => import('../views/front/Foods') },
      { path: 'dingdan', name: 'Dingdan', meta: { name: '订单信息' }, component: () => import('../views/front/Dingdan') },
      { path: 'dingdan2', name: 'Dingdan2', meta: { name: '购物车信息' }, component: () => import('../views/front/Dingdan2') },
      { path: 'complaint', name: 'Complaint', meta: { name: '我要投诉' }, component: () => import('../views/front/Complaint') },
      { path: 'response', name: 'Response', meta: { name: '投诉反馈' }, component: () => import('../views/front/Response') },
      { path: 'leavemess', name: 'Leavemess', meta: { name: '咨询评论' }, component: () => import('../views/front/Leavemess') },
      { path: 'replyLeavemess', name: 'ReplyLeavemess', meta: { name: '评论回复' }, component: () => import('../views/front/ReplyLeavemess') },
      { path: 'blogs', name: 'Blogs', meta: { name: '博客管理' }, component: () => import('../views/front/Blogs') },
      { path: 'blogsDetails', name: 'BlogsDetails', meta: { name: '博客详情' }, component: () => import('../views/front/BlogsDetails') },
      { path: 'myBlogs', name: 'MyBlogs', meta: { name: '我要发帖' }, component: () => import('../views/front/MyBlogs') },
      { path: 'response', name: 'Response', meta: { name: '投诉反馈' }, component: () => import('../views/front/Response') },
      { path: 'freemovies', name: 'Freemovies', meta: { name: '点餐推荐' }, component: () => import('../views/front/Freemovies') },
      { path: 'complaint', name: 'Complaint', meta: { name: '我要点餐投诉' }, component: () => import('../views/front/Complaint') },
      { path: 'replyLeavemess', name: 'ReplyLeavemess', meta: { name: '评论回复' }, component: () => import('../views/front/ReplyLeavemess') },
      { path: 'notice', name: 'Notice', meta: { name: '公告信息' }, component: () => import('../views/front/Notice') },


    ]
  },
  { path: '/login', name: 'Login', meta: { name: '登录' }, component: () => import('../views/Login.vue') },
  { path: '/register', name: 'Register', meta: { name: '注册' }, component: () => import('../views/Register.vue') },
  { path: '*', name: 'NotFound', meta: { name: '无法访问' }, component: () => import('../views/404.vue') },
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 注：不需要前台的项目，可以注释掉该路由守卫
// 路由守卫
// router.beforeEach((to ,from, next) => {
//   let user = JSON.parse(localStorage.getItem("xm-user") || '{}');
//   if (to.path === '/') {
//     if (user.role) {
//       if (user.role === 'USER') {
//         next('/front/home')
//       } else {
//         next('/home')
//       }
//     } else {
//       next('/login')
//     }
//   } else {
//     next()
//   }
// })

export default router
