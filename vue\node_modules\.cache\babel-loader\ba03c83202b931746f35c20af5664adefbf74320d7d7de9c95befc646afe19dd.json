{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nimport { AxisModelCommonMixin } from '../axisModelCommonMixin.js';\nimport { mixin } from 'zrender/lib/core/util.js';\nvar SingleAxisModel = /** @class */function (_super) {\n  __extends(SingleAxisModel, _super);\n  function SingleAxisModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SingleAxisModel.type;\n    return _this;\n  }\n  SingleAxisModel.prototype.getCoordSysModel = function () {\n    return this;\n  };\n  SingleAxisModel.type = 'singleAxis';\n  SingleAxisModel.layoutMode = 'box';\n  SingleAxisModel.defaultOption = {\n    left: '5%',\n    top: '5%',\n    right: '5%',\n    bottom: '5%',\n    type: 'value',\n    position: 'bottom',\n    orient: 'horizontal',\n    axisLine: {\n      show: true,\n      lineStyle: {\n        width: 1,\n        type: 'solid'\n      }\n    },\n    // Single coordinate system and single axis is the,\n    // which is used as the parent tooltip model.\n    // same model, so we set default tooltip show as true.\n    tooltip: {\n      show: true\n    },\n    axisTick: {\n      show: true,\n      length: 6,\n      lineStyle: {\n        width: 1\n      }\n    },\n    axisLabel: {\n      show: true,\n      interval: 'auto'\n    },\n    splitLine: {\n      show: true,\n      lineStyle: {\n        type: 'dashed',\n        opacity: 0.2\n      }\n    }\n  };\n  return SingleAxisModel;\n}(ComponentModel);\nmixin(SingleAxisModel, AxisModelCommonMixin.prototype);\nexport default SingleAxisModel;", "map": {"version": 3, "names": ["__extends", "ComponentModel", "AxisModelCommonMixin", "mixin", "SingleAxisModel", "_super", "_this", "apply", "arguments", "type", "prototype", "getCoordSysModel", "layoutMode", "defaultOption", "left", "top", "right", "bottom", "position", "orient", "axisLine", "show", "lineStyle", "width", "tooltip", "axisTick", "length", "axisLabel", "interval", "splitLine", "opacity"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/coord/single/AxisModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nimport { AxisModelCommonMixin } from '../axisModelCommonMixin.js';\nimport { mixin } from 'zrender/lib/core/util.js';\nvar SingleAxisModel = /** @class */function (_super) {\n  __extends(SingleAxisModel, _super);\n  function SingleAxisModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SingleAxisModel.type;\n    return _this;\n  }\n  SingleAxisModel.prototype.getCoordSysModel = function () {\n    return this;\n  };\n  SingleAxisModel.type = 'singleAxis';\n  SingleAxisModel.layoutMode = 'box';\n  SingleAxisModel.defaultOption = {\n    left: '5%',\n    top: '5%',\n    right: '5%',\n    bottom: '5%',\n    type: 'value',\n    position: 'bottom',\n    orient: 'horizontal',\n    axisLine: {\n      show: true,\n      lineStyle: {\n        width: 1,\n        type: 'solid'\n      }\n    },\n    // Single coordinate system and single axis is the,\n    // which is used as the parent tooltip model.\n    // same model, so we set default tooltip show as true.\n    tooltip: {\n      show: true\n    },\n    axisTick: {\n      show: true,\n      length: 6,\n      lineStyle: {\n        width: 1\n      }\n    },\n    axisLabel: {\n      show: true,\n      interval: 'auto'\n    },\n    splitLine: {\n      show: true,\n      lineStyle: {\n        type: 'dashed',\n        opacity: 0.2\n      }\n    }\n  };\n  return SingleAxisModel;\n}(ComponentModel);\nmixin(SingleAxisModel, AxisModelCommonMixin.prototype);\nexport default SingleAxisModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,cAAc,MAAM,0BAA0B;AACrD,SAASC,oBAAoB,QAAQ,4BAA4B;AACjE,SAASC,KAAK,QAAQ,0BAA0B;AAChD,IAAIC,eAAe,GAAG,aAAa,UAAUC,MAAM,EAAE;EACnDL,SAAS,CAACI,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAAA,EAAG;IACzB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,eAAe,CAACK,IAAI;IACjC,OAAOH,KAAK;EACd;EACAF,eAAe,CAACM,SAAS,CAACC,gBAAgB,GAAG,YAAY;IACvD,OAAO,IAAI;EACb,CAAC;EACDP,eAAe,CAACK,IAAI,GAAG,YAAY;EACnCL,eAAe,CAACQ,UAAU,GAAG,KAAK;EAClCR,eAAe,CAACS,aAAa,GAAG;IAC9BC,IAAI,EAAE,IAAI;IACVC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZR,IAAI,EAAE,OAAO;IACbS,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,YAAY;IACpBC,QAAQ,EAAE;MACRC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC;QACRd,IAAI,EAAE;MACR;IACF,CAAC;IACD;IACA;IACA;IACAe,OAAO,EAAE;MACPH,IAAI,EAAE;IACR,CAAC;IACDI,QAAQ,EAAE;MACRJ,IAAI,EAAE,IAAI;MACVK,MAAM,EAAE,CAAC;MACTJ,SAAS,EAAE;QACTC,KAAK,EAAE;MACT;IACF,CAAC;IACDI,SAAS,EAAE;MACTN,IAAI,EAAE,IAAI;MACVO,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACTR,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE;QACTb,IAAI,EAAE,QAAQ;QACdqB,OAAO,EAAE;MACX;IACF;EACF,CAAC;EACD,OAAO1B,eAAe;AACxB,CAAC,CAACH,cAAc,CAAC;AACjBE,KAAK,CAACC,eAAe,EAAEF,oBAAoB,CAACQ,SAAS,CAAC;AACtD,eAAeN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}