{"ast": null, "code": "export function createTextNode(text) {\n  return document.createTextNode(text);\n}\nexport function createComment(text) {\n  return document.createComment(text);\n}\nexport function insertBefore(parentNode, newNode, referenceNode) {\n  parentNode.insertBefore(newNode, referenceNode);\n}\nexport function removeChild(node, child) {\n  node.removeChild(child);\n}\nexport function appendChild(node, child) {\n  node.appendChild(child);\n}\nexport function parentNode(node) {\n  return node.parentNode;\n}\nexport function nextSibling(node) {\n  return node.nextSibling;\n}\nexport function tagName(elm) {\n  return elm.tagName;\n}\nexport function setTextContent(node, text) {\n  node.textContent = text;\n}\nexport function getTextContent(node) {\n  return node.textContent;\n}\nexport function isElement(node) {\n  return node.nodeType === 1;\n}\nexport function isText(node) {\n  return node.nodeType === 3;\n}\nexport function isComment(node) {\n  return node.nodeType === 8;\n}", "map": {"version": 3, "names": ["createTextNode", "text", "document", "createComment", "insertBefore", "parentNode", "newNode", "referenceNode", "<PERSON><PERSON><PERSON><PERSON>", "node", "child", "append<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "tagName", "elm", "setTextContent", "textContent", "getTextContent", "isElement", "nodeType", "isText", "isComment"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/zrender/lib/svg/domapi.js"], "sourcesContent": ["export function createTextNode(text) {\n    return document.createTextNode(text);\n}\nexport function createComment(text) {\n    return document.createComment(text);\n}\nexport function insertBefore(parentNode, newNode, referenceNode) {\n    parentNode.insertBefore(newNode, referenceNode);\n}\nexport function removeChild(node, child) {\n    node.removeChild(child);\n}\nexport function appendChild(node, child) {\n    node.appendChild(child);\n}\nexport function parentNode(node) {\n    return node.parentNode;\n}\nexport function nextSibling(node) {\n    return node.nextSibling;\n}\nexport function tagName(elm) {\n    return elm.tagName;\n}\nexport function setTextContent(node, text) {\n    node.textContent = text;\n}\nexport function getTextContent(node) {\n    return node.textContent;\n}\nexport function isElement(node) {\n    return node.nodeType === 1;\n}\nexport function isText(node) {\n    return node.nodeType === 3;\n}\nexport function isComment(node) {\n    return node.nodeType === 8;\n}\n"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAACC,IAAI,EAAE;EACjC,OAAOC,QAAQ,CAACF,cAAc,CAACC,IAAI,CAAC;AACxC;AACA,OAAO,SAASE,aAAaA,CAACF,IAAI,EAAE;EAChC,OAAOC,QAAQ,CAACC,aAAa,CAACF,IAAI,CAAC;AACvC;AACA,OAAO,SAASG,YAAYA,CAACC,UAAU,EAAEC,OAAO,EAAEC,aAAa,EAAE;EAC7DF,UAAU,CAACD,YAAY,CAACE,OAAO,EAAEC,aAAa,CAAC;AACnD;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACrCD,IAAI,CAACD,WAAW,CAACE,KAAK,CAAC;AAC3B;AACA,OAAO,SAASC,WAAWA,CAACF,IAAI,EAAEC,KAAK,EAAE;EACrCD,IAAI,CAACE,WAAW,CAACD,KAAK,CAAC;AAC3B;AACA,OAAO,SAASL,UAAUA,CAACI,IAAI,EAAE;EAC7B,OAAOA,IAAI,CAACJ,UAAU;AAC1B;AACA,OAAO,SAASO,WAAWA,CAACH,IAAI,EAAE;EAC9B,OAAOA,IAAI,CAACG,WAAW;AAC3B;AACA,OAAO,SAASC,OAAOA,CAACC,GAAG,EAAE;EACzB,OAAOA,GAAG,CAACD,OAAO;AACtB;AACA,OAAO,SAASE,cAAcA,CAACN,IAAI,EAAER,IAAI,EAAE;EACvCQ,IAAI,CAACO,WAAW,GAAGf,IAAI;AAC3B;AACA,OAAO,SAASgB,cAAcA,CAACR,IAAI,EAAE;EACjC,OAAOA,IAAI,CAACO,WAAW;AAC3B;AACA,OAAO,SAASE,SAASA,CAACT,IAAI,EAAE;EAC5B,OAAOA,IAAI,CAACU,QAAQ,KAAK,CAAC;AAC9B;AACA,OAAO,SAASC,MAAMA,CAACX,IAAI,EAAE;EACzB,OAAOA,IAAI,CAACU,QAAQ,KAAK,CAAC;AAC9B;AACA,OAAO,SAASE,SAASA,CAACZ,IAAI,EAAE;EAC5B,OAAOA,IAAI,CAACU,QAAQ,KAAK,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}