{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getPrecisionSafe, round } from '../util/number.js';\nimport IntervalScale from '../scale/Interval.js';\nimport { getScaleExtent } from './axisHelper.js';\nimport { warn } from '../util/log.js';\nimport { increaseInterval, isValueNice } from '../scale/helper.js';\nvar mathLog = Math.log;\nexport function alignScaleTicks(scale, axisModel, alignToScale) {\n  var intervalScaleProto = IntervalScale.prototype;\n  // NOTE: There is a precondition for log scale  here:\n  // In log scale we store _interval and _extent of exponent value.\n  // So if we use the method of InternalScale to set/get these data.\n  // It process the exponent value, which is linear and what we want here.\n  var alignToTicks = intervalScaleProto.getTicks.call(alignToScale);\n  var alignToNicedTicks = intervalScaleProto.getTicks.call(alignToScale, true);\n  var alignToSplitNumber = alignToTicks.length - 1;\n  var alignToInterval = intervalScaleProto.getInterval.call(alignToScale);\n  var scaleExtent = getScaleExtent(scale, axisModel);\n  var rawExtent = scaleExtent.extent;\n  var isMinFixed = scaleExtent.fixMin;\n  var isMaxFixed = scaleExtent.fixMax;\n  if (scale.type === 'log') {\n    var logBase = mathLog(scale.base);\n    rawExtent = [mathLog(rawExtent[0]) / logBase, mathLog(rawExtent[1]) / logBase];\n  }\n  scale.setExtent(rawExtent[0], rawExtent[1]);\n  scale.calcNiceExtent({\n    splitNumber: alignToSplitNumber,\n    fixMin: isMinFixed,\n    fixMax: isMaxFixed\n  });\n  var extent = intervalScaleProto.getExtent.call(scale);\n  // Need to update the rawExtent.\n  // Because value in rawExtent may be not parsed. e.g. 'dataMin', 'dataMax'\n  if (isMinFixed) {\n    rawExtent[0] = extent[0];\n  }\n  if (isMaxFixed) {\n    rawExtent[1] = extent[1];\n  }\n  var interval = intervalScaleProto.getInterval.call(scale);\n  var min = rawExtent[0];\n  var max = rawExtent[1];\n  if (isMinFixed && isMaxFixed) {\n    // User set min, max, divide to get new interval\n    interval = (max - min) / alignToSplitNumber;\n  } else if (isMinFixed) {\n    max = rawExtent[0] + interval * alignToSplitNumber;\n    // User set min, expand extent on the other side\n    while (max < rawExtent[1] && isFinite(max) && isFinite(rawExtent[1])) {\n      interval = increaseInterval(interval);\n      max = rawExtent[0] + interval * alignToSplitNumber;\n    }\n  } else if (isMaxFixed) {\n    // User set max, expand extent on the other side\n    min = rawExtent[1] - interval * alignToSplitNumber;\n    while (min > rawExtent[0] && isFinite(min) && isFinite(rawExtent[0])) {\n      interval = increaseInterval(interval);\n      min = rawExtent[1] - interval * alignToSplitNumber;\n    }\n  } else {\n    var nicedSplitNumber = scale.getTicks().length - 1;\n    if (nicedSplitNumber > alignToSplitNumber) {\n      interval = increaseInterval(interval);\n    }\n    var range = interval * alignToSplitNumber;\n    max = Math.ceil(rawExtent[1] / interval) * interval;\n    min = round(max - range);\n    // Not change the result that crossing zero.\n    if (min < 0 && rawExtent[0] >= 0) {\n      min = 0;\n      max = round(range);\n    } else if (max > 0 && rawExtent[1] <= 0) {\n      max = 0;\n      min = -round(range);\n    }\n  }\n  // Adjust min, max based on the extent of alignTo. When min or max is set in alignTo scale\n  var t0 = (alignToTicks[0].value - alignToNicedTicks[0].value) / alignToInterval;\n  var t1 = (alignToTicks[alignToSplitNumber].value - alignToNicedTicks[alignToSplitNumber].value) / alignToInterval;\n  // NOTE: Must in setExtent -> setInterval -> setNiceExtent order.\n  intervalScaleProto.setExtent.call(scale, min + interval * t0, max + interval * t1);\n  intervalScaleProto.setInterval.call(scale, interval);\n  if (t0 || t1) {\n    intervalScaleProto.setNiceExtent.call(scale, min + interval, max - interval);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    var ticks = intervalScaleProto.getTicks.call(scale);\n    if (ticks[1] && (!isValueNice(interval) || getPrecisionSafe(ticks[1].value) > getPrecisionSafe(interval))) {\n      warn(\n      // eslint-disable-next-line\n      \"The ticks may be not readable when set min: \" + axisModel.get('min') + \", max: \" + axisModel.get('max') + \" and alignTicks: true\");\n    }\n  }\n}", "map": {"version": 3, "names": ["getPrecisionSafe", "round", "IntervalScale", "getScaleExtent", "warn", "increaseInterval", "isValueNice", "mathLog", "Math", "log", "alignScaleTicks", "scale", "axisModel", "alignToScale", "intervalScaleProto", "prototype", "alignToTicks", "getTicks", "call", "alignToNicedTicks", "alignToSplitNumber", "length", "alignToInterval", "getInterval", "scaleExtent", "rawExtent", "extent", "isMinFixed", "fixMin", "isMaxFixed", "fixMax", "type", "logBase", "base", "setExtent", "calcNiceExtent", "splitNumber", "getExtent", "interval", "min", "max", "isFinite", "nicedSplitNumber", "range", "ceil", "t0", "value", "t1", "setInterval", "setNiceExtent", "process", "env", "NODE_ENV", "ticks", "get"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/coord/axisAlignTicks.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getPrecisionSafe, round } from '../util/number.js';\nimport IntervalScale from '../scale/Interval.js';\nimport { getScaleExtent } from './axisHelper.js';\nimport { warn } from '../util/log.js';\nimport { increaseInterval, isValueNice } from '../scale/helper.js';\nvar mathLog = Math.log;\nexport function alignScaleTicks(scale, axisModel, alignToScale) {\n  var intervalScaleProto = IntervalScale.prototype;\n  // NOTE: There is a precondition for log scale  here:\n  // In log scale we store _interval and _extent of exponent value.\n  // So if we use the method of InternalScale to set/get these data.\n  // It process the exponent value, which is linear and what we want here.\n  var alignToTicks = intervalScaleProto.getTicks.call(alignToScale);\n  var alignToNicedTicks = intervalScaleProto.getTicks.call(alignToScale, true);\n  var alignToSplitNumber = alignToTicks.length - 1;\n  var alignToInterval = intervalScaleProto.getInterval.call(alignToScale);\n  var scaleExtent = getScaleExtent(scale, axisModel);\n  var rawExtent = scaleExtent.extent;\n  var isMinFixed = scaleExtent.fixMin;\n  var isMaxFixed = scaleExtent.fixMax;\n  if (scale.type === 'log') {\n    var logBase = mathLog(scale.base);\n    rawExtent = [mathLog(rawExtent[0]) / logBase, mathLog(rawExtent[1]) / logBase];\n  }\n  scale.setExtent(rawExtent[0], rawExtent[1]);\n  scale.calcNiceExtent({\n    splitNumber: alignToSplitNumber,\n    fixMin: isMinFixed,\n    fixMax: isMaxFixed\n  });\n  var extent = intervalScaleProto.getExtent.call(scale);\n  // Need to update the rawExtent.\n  // Because value in rawExtent may be not parsed. e.g. 'dataMin', 'dataMax'\n  if (isMinFixed) {\n    rawExtent[0] = extent[0];\n  }\n  if (isMaxFixed) {\n    rawExtent[1] = extent[1];\n  }\n  var interval = intervalScaleProto.getInterval.call(scale);\n  var min = rawExtent[0];\n  var max = rawExtent[1];\n  if (isMinFixed && isMaxFixed) {\n    // User set min, max, divide to get new interval\n    interval = (max - min) / alignToSplitNumber;\n  } else if (isMinFixed) {\n    max = rawExtent[0] + interval * alignToSplitNumber;\n    // User set min, expand extent on the other side\n    while (max < rawExtent[1] && isFinite(max) && isFinite(rawExtent[1])) {\n      interval = increaseInterval(interval);\n      max = rawExtent[0] + interval * alignToSplitNumber;\n    }\n  } else if (isMaxFixed) {\n    // User set max, expand extent on the other side\n    min = rawExtent[1] - interval * alignToSplitNumber;\n    while (min > rawExtent[0] && isFinite(min) && isFinite(rawExtent[0])) {\n      interval = increaseInterval(interval);\n      min = rawExtent[1] - interval * alignToSplitNumber;\n    }\n  } else {\n    var nicedSplitNumber = scale.getTicks().length - 1;\n    if (nicedSplitNumber > alignToSplitNumber) {\n      interval = increaseInterval(interval);\n    }\n    var range = interval * alignToSplitNumber;\n    max = Math.ceil(rawExtent[1] / interval) * interval;\n    min = round(max - range);\n    // Not change the result that crossing zero.\n    if (min < 0 && rawExtent[0] >= 0) {\n      min = 0;\n      max = round(range);\n    } else if (max > 0 && rawExtent[1] <= 0) {\n      max = 0;\n      min = -round(range);\n    }\n  }\n  // Adjust min, max based on the extent of alignTo. When min or max is set in alignTo scale\n  var t0 = (alignToTicks[0].value - alignToNicedTicks[0].value) / alignToInterval;\n  var t1 = (alignToTicks[alignToSplitNumber].value - alignToNicedTicks[alignToSplitNumber].value) / alignToInterval;\n  // NOTE: Must in setExtent -> setInterval -> setNiceExtent order.\n  intervalScaleProto.setExtent.call(scale, min + interval * t0, max + interval * t1);\n  intervalScaleProto.setInterval.call(scale, interval);\n  if (t0 || t1) {\n    intervalScaleProto.setNiceExtent.call(scale, min + interval, max - interval);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    var ticks = intervalScaleProto.getTicks.call(scale);\n    if (ticks[1] && (!isValueNice(interval) || getPrecisionSafe(ticks[1].value) > getPrecisionSafe(interval))) {\n      warn(\n      // eslint-disable-next-line\n      \"The ticks may be not readable when set min: \" + axisModel.get('min') + \", max: \" + axisModel.get('max') + \" and alignTicks: true\");\n    }\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,EAAEC,KAAK,QAAQ,mBAAmB;AAC3D,OAAOC,aAAa,MAAM,sBAAsB;AAChD,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,oBAAoB;AAClE,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAEC,SAAS,EAAEC,YAAY,EAAE;EAC9D,IAAIC,kBAAkB,GAAGZ,aAAa,CAACa,SAAS;EAChD;EACA;EACA;EACA;EACA,IAAIC,YAAY,GAAGF,kBAAkB,CAACG,QAAQ,CAACC,IAAI,CAACL,YAAY,CAAC;EACjE,IAAIM,iBAAiB,GAAGL,kBAAkB,CAACG,QAAQ,CAACC,IAAI,CAACL,YAAY,EAAE,IAAI,CAAC;EAC5E,IAAIO,kBAAkB,GAAGJ,YAAY,CAACK,MAAM,GAAG,CAAC;EAChD,IAAIC,eAAe,GAAGR,kBAAkB,CAACS,WAAW,CAACL,IAAI,CAACL,YAAY,CAAC;EACvE,IAAIW,WAAW,GAAGrB,cAAc,CAACQ,KAAK,EAAEC,SAAS,CAAC;EAClD,IAAIa,SAAS,GAAGD,WAAW,CAACE,MAAM;EAClC,IAAIC,UAAU,GAAGH,WAAW,CAACI,MAAM;EACnC,IAAIC,UAAU,GAAGL,WAAW,CAACM,MAAM;EACnC,IAAInB,KAAK,CAACoB,IAAI,KAAK,KAAK,EAAE;IACxB,IAAIC,OAAO,GAAGzB,OAAO,CAACI,KAAK,CAACsB,IAAI,CAAC;IACjCR,SAAS,GAAG,CAAClB,OAAO,CAACkB,SAAS,CAAC,CAAC,CAAC,CAAC,GAAGO,OAAO,EAAEzB,OAAO,CAACkB,SAAS,CAAC,CAAC,CAAC,CAAC,GAAGO,OAAO,CAAC;EAChF;EACArB,KAAK,CAACuB,SAAS,CAACT,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;EAC3Cd,KAAK,CAACwB,cAAc,CAAC;IACnBC,WAAW,EAAEhB,kBAAkB;IAC/BQ,MAAM,EAAED,UAAU;IAClBG,MAAM,EAAED;EACV,CAAC,CAAC;EACF,IAAIH,MAAM,GAAGZ,kBAAkB,CAACuB,SAAS,CAACnB,IAAI,CAACP,KAAK,CAAC;EACrD;EACA;EACA,IAAIgB,UAAU,EAAE;IACdF,SAAS,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIG,UAAU,EAAE;IACdJ,SAAS,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIY,QAAQ,GAAGxB,kBAAkB,CAACS,WAAW,CAACL,IAAI,CAACP,KAAK,CAAC;EACzD,IAAI4B,GAAG,GAAGd,SAAS,CAAC,CAAC,CAAC;EACtB,IAAIe,GAAG,GAAGf,SAAS,CAAC,CAAC,CAAC;EACtB,IAAIE,UAAU,IAAIE,UAAU,EAAE;IAC5B;IACAS,QAAQ,GAAG,CAACE,GAAG,GAAGD,GAAG,IAAInB,kBAAkB;EAC7C,CAAC,MAAM,IAAIO,UAAU,EAAE;IACrBa,GAAG,GAAGf,SAAS,CAAC,CAAC,CAAC,GAAGa,QAAQ,GAAGlB,kBAAkB;IAClD;IACA,OAAOoB,GAAG,GAAGf,SAAS,CAAC,CAAC,CAAC,IAAIgB,QAAQ,CAACD,GAAG,CAAC,IAAIC,QAAQ,CAAChB,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;MACpEa,QAAQ,GAAGjC,gBAAgB,CAACiC,QAAQ,CAAC;MACrCE,GAAG,GAAGf,SAAS,CAAC,CAAC,CAAC,GAAGa,QAAQ,GAAGlB,kBAAkB;IACpD;EACF,CAAC,MAAM,IAAIS,UAAU,EAAE;IACrB;IACAU,GAAG,GAAGd,SAAS,CAAC,CAAC,CAAC,GAAGa,QAAQ,GAAGlB,kBAAkB;IAClD,OAAOmB,GAAG,GAAGd,SAAS,CAAC,CAAC,CAAC,IAAIgB,QAAQ,CAACF,GAAG,CAAC,IAAIE,QAAQ,CAAChB,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;MACpEa,QAAQ,GAAGjC,gBAAgB,CAACiC,QAAQ,CAAC;MACrCC,GAAG,GAAGd,SAAS,CAAC,CAAC,CAAC,GAAGa,QAAQ,GAAGlB,kBAAkB;IACpD;EACF,CAAC,MAAM;IACL,IAAIsB,gBAAgB,GAAG/B,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACI,MAAM,GAAG,CAAC;IAClD,IAAIqB,gBAAgB,GAAGtB,kBAAkB,EAAE;MACzCkB,QAAQ,GAAGjC,gBAAgB,CAACiC,QAAQ,CAAC;IACvC;IACA,IAAIK,KAAK,GAAGL,QAAQ,GAAGlB,kBAAkB;IACzCoB,GAAG,GAAGhC,IAAI,CAACoC,IAAI,CAACnB,SAAS,CAAC,CAAC,CAAC,GAAGa,QAAQ,CAAC,GAAGA,QAAQ;IACnDC,GAAG,GAAGtC,KAAK,CAACuC,GAAG,GAAGG,KAAK,CAAC;IACxB;IACA,IAAIJ,GAAG,GAAG,CAAC,IAAId,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MAChCc,GAAG,GAAG,CAAC;MACPC,GAAG,GAAGvC,KAAK,CAAC0C,KAAK,CAAC;IACpB,CAAC,MAAM,IAAIH,GAAG,GAAG,CAAC,IAAIf,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MACvCe,GAAG,GAAG,CAAC;MACPD,GAAG,GAAG,CAACtC,KAAK,CAAC0C,KAAK,CAAC;IACrB;EACF;EACA;EACA,IAAIE,EAAE,GAAG,CAAC7B,YAAY,CAAC,CAAC,CAAC,CAAC8B,KAAK,GAAG3B,iBAAiB,CAAC,CAAC,CAAC,CAAC2B,KAAK,IAAIxB,eAAe;EAC/E,IAAIyB,EAAE,GAAG,CAAC/B,YAAY,CAACI,kBAAkB,CAAC,CAAC0B,KAAK,GAAG3B,iBAAiB,CAACC,kBAAkB,CAAC,CAAC0B,KAAK,IAAIxB,eAAe;EACjH;EACAR,kBAAkB,CAACoB,SAAS,CAAChB,IAAI,CAACP,KAAK,EAAE4B,GAAG,GAAGD,QAAQ,GAAGO,EAAE,EAAEL,GAAG,GAAGF,QAAQ,GAAGS,EAAE,CAAC;EAClFjC,kBAAkB,CAACkC,WAAW,CAAC9B,IAAI,CAACP,KAAK,EAAE2B,QAAQ,CAAC;EACpD,IAAIO,EAAE,IAAIE,EAAE,EAAE;IACZjC,kBAAkB,CAACmC,aAAa,CAAC/B,IAAI,CAACP,KAAK,EAAE4B,GAAG,GAAGD,QAAQ,EAAEE,GAAG,GAAGF,QAAQ,CAAC;EAC9E;EACA,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIC,KAAK,GAAGvC,kBAAkB,CAACG,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC;IACnD,IAAI0C,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC/C,WAAW,CAACgC,QAAQ,CAAC,IAAItC,gBAAgB,CAACqD,KAAK,CAAC,CAAC,CAAC,CAACP,KAAK,CAAC,GAAG9C,gBAAgB,CAACsC,QAAQ,CAAC,CAAC,EAAE;MACzGlC,IAAI;MACJ;MACA,8CAA8C,GAAGQ,SAAS,CAAC0C,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG1C,SAAS,CAAC0C,GAAG,CAAC,KAAK,CAAC,GAAG,uBAAuB,CAAC;IACrI;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}