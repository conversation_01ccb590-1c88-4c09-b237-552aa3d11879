{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport axios from 'axios';\nimport router from \"@/router\";\n\n// 创建可一个新的axios对象\nconst request = axios.create({\n  baseURL: process.env.VUE_APP_BASEURL,\n  // 后端的接口地址  ip:port\n  timeout: 30000 // 30s请求超时\n});\n\n// request 拦截器\n// 可以自请求发送前对请求做一些处理\n// 比如统一加token，对请求参数统一加密\nrequest.interceptors.request.use(config => {\n  config.headers['Content-Type'] = 'application/json;charset=utf-8'; // 设置请求头格式\n  let user = JSON.parse(localStorage.getItem(\"xm-user\") || '{}'); // 获取缓存的用户信息\n  config.headers['token'] = user.token; // 设置请求头\n\n  return config;\n}, error => {\n  console.error('request error: ' + error); // for debug\n  return Promise.reject(error);\n});\n\n// response 拦截器\n// 可以在接口响应后统一处理结果\nrequest.interceptors.response.use(response => {\n  let res = response.data;\n\n  // 兼容服务端返回的字符串数据\n  if (typeof res === 'string') {\n    res = res ? JSON.parse(res) : res;\n  }\n  if (res.code === '401') {\n    router.push('/login');\n  }\n  return res;\n}, error => {\n  console.error('response error: ' + error); // for debug\n  return Promise.reject(error);\n});\nexport default request;", "map": {"version": 3, "names": ["axios", "router", "request", "create", "baseURL", "process", "env", "VUE_APP_BASEURL", "timeout", "interceptors", "use", "config", "headers", "user", "JSON", "parse", "localStorage", "getItem", "token", "error", "console", "Promise", "reject", "response", "res", "data", "code", "push"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\r\nimport router from \"@/router\";\r\n\r\n// 创建可一个新的axios对象\r\nconst request = axios.create({\r\n    baseURL: process.env.VUE_APP_BASEURL,   // 后端的接口地址  ip:port\r\n    timeout: 30000                          // 30s请求超时\r\n})\r\n\r\n// request 拦截器\r\n// 可以自请求发送前对请求做一些处理\r\n// 比如统一加token，对请求参数统一加密\r\nrequest.interceptors.request.use(config => {\r\n    config.headers['Content-Type'] = 'application/json;charset=utf-8';        // 设置请求头格式\r\n    let user = JSON.parse(localStorage.getItem(\"xm-user\") || '{}')  // 获取缓存的用户信息\r\n    config.headers['token'] = user.token  // 设置请求头\r\n\r\n    return config\r\n}, error => {\r\n    console.error('request error: ' + error) // for debug\r\n    return Promise.reject(error)\r\n});\r\n\r\n// response 拦截器\r\n// 可以在接口响应后统一处理结果\r\nrequest.interceptors.response.use(\r\n    response => {\r\n        let res = response.data;\r\n\r\n        // 兼容服务端返回的字符串数据\r\n        if (typeof res === 'string') {\r\n            res = res ? JSON.parse(res) : res\r\n        }\r\n        if (res.code === '401') {\r\n            router.push('/login')\r\n        }\r\n        return res;\r\n    },\r\n    error => {\r\n        console.error('response error: ' + error) // for debug\r\n        return Promise.reject(error)\r\n    }\r\n)\r\n\r\n\r\nexport default request"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;;AAE7B;AACA,MAAMC,OAAO,GAAGF,KAAK,CAACG,MAAM,CAAC;EACzBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,eAAe;EAAI;EACxCC,OAAO,EAAE,KAAK,CAA0B;AAC5C,CAAC,CAAC;;AAEF;AACA;AACA;AACAN,OAAO,CAACO,YAAY,CAACP,OAAO,CAACQ,GAAG,CAACC,MAAM,IAAI;EACvCA,MAAM,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,gCAAgC,CAAC,CAAQ;EAC1E,IAAIC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,EAAE;EAChEN,MAAM,CAACC,OAAO,CAAC,OAAO,CAAC,GAAGC,IAAI,CAACK,KAAK,EAAE;;EAEtC,OAAOP,MAAM;AACjB,CAAC,EAAEQ,KAAK,IAAI;EACRC,OAAO,CAACD,KAAK,CAAC,iBAAiB,GAAGA,KAAK,CAAC,EAAC;EACzC,OAAOE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAChC,CAAC,CAAC;;AAEF;AACA;AACAjB,OAAO,CAACO,YAAY,CAACc,QAAQ,CAACb,GAAG,CAC7Ba,QAAQ,IAAI;EACR,IAAIC,GAAG,GAAGD,QAAQ,CAACE,IAAI;;EAEvB;EACA,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IACzBA,GAAG,GAAGA,GAAG,GAAGV,IAAI,CAACC,KAAK,CAACS,GAAG,CAAC,GAAGA,GAAG;EACrC;EACA,IAAIA,GAAG,CAACE,IAAI,KAAK,KAAK,EAAE;IACpBzB,MAAM,CAAC0B,IAAI,CAAC,QAAQ,CAAC;EACzB;EACA,OAAOH,GAAG;AACd,CAAC,EACDL,KAAK,IAAI;EACLC,OAAO,CAACD,KAAK,CAAC,kBAAkB,GAAGA,KAAK,CAAC,EAAC;EAC1C,OAAOE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAChC,CACJ,CAAC;AAGD,eAAejB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}