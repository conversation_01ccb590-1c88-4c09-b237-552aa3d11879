{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n// 导入各个功能组件\nimport MyBlogsComponent from './MyBlogs.vue';\nimport ComplaintComponent from './Complaint.vue';\nimport ResponseComponent from './Response.vue';\nimport LeavemessComponent from './Leavemess.vue';\nimport ReplyLeavemessComponent from './ReplyLeavemess.vue';\nexport default {\n  name: 'PersonCenter',\n  components: {\n    MyBlogsComponent,\n    ComplaintComponent,\n    ResponseComponent,\n    LeavemessComponent,\n    ReplyLeavemessComponent\n  },\n  data() {\n    const validatePassword = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请确认密码'));\n      } else if (value !== this.user.newPassword) {\n        callback(new Error('确认密码错误'));\n      } else {\n        callback();\n      }\n    };\n    const validateEmail = (rule, value, callback) => {\n      if (value && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)) {\n        callback(new Error('请输入正确的邮箱格式'));\n      } else {\n        callback();\n      }\n    };\n    const validatePhone = (rule, value, callback) => {\n      if (value && !/^1[3-9]\\d{9}$/.test(value)) {\n        callback(new Error('请输入正确的手机号格式'));\n      } else {\n        callback();\n      }\n    };\n    return {\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      originalUser: {},\n      // 保存原始用户信息\n      activeMenu: 'profile',\n      // 默认显示个人信息\n      dialogVisible: false,\n      rules: {\n        username: [{\n          required: true,\n          message: '请输入用户名',\n          trigger: 'blur'\n        }],\n        phone: [{\n          required: true,\n          message: '请输入手机号',\n          trigger: 'blur'\n        }, {\n          validator: validatePhone,\n          trigger: 'blur'\n        }],\n        verifyCode: [{\n          required: true,\n          message: '请输入短信验证码',\n          trigger: 'blur'\n        }, {\n          len: 6,\n          message: '验证码长度为6位',\n          trigger: 'blur'\n        }],\n        newPassword: [{\n          required: true,\n          message: '请输入新密码',\n          trigger: 'blur'\n        }, {\n          min: 6,\n          message: '密码长度至少6位',\n          trigger: 'blur'\n        }],\n        confirmPass: [{\n          validator: validatePassword,\n          required: true,\n          trigger: 'blur'\n        }]\n      },\n      profileRules: {\n        name: [{\n          required: true,\n          message: '请输入昵称',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          max: 20,\n          message: '昵称长度在2到20个字符',\n          trigger: 'blur'\n        }],\n        email: [{\n          validator: validateEmail,\n          trigger: 'blur'\n        }],\n        phone: [{\n          validator: validatePhone,\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created() {\n    // 保存原始用户信息\n    this.originalUser = JSON.parse(JSON.stringify(this.user));\n\n    // 根据URL参数设置活跃菜单\n    const section = this.$route.query.section;\n    if (section) {\n      this.activeMenu = section;\n    }\n  },\n  watch: {\n    '$route'(to, from) {\n      // 监听路由变化，更新活跃菜单\n      const section = to.query.section;\n      if (section) {\n        this.activeMenu = section;\n      }\n    }\n  },\n  methods: {\n    handleMenuSelect(index) {\n      this.activeMenu = index;\n      // 更新URL参数，但不刷新页面\n      this.$router.push({\n        path: '/front/person',\n        query: {\n          section: index\n        }\n      });\n    },\n    updateUser() {\n      this.user = JSON.parse(localStorage.getItem('xm-user') || '{}');\n      this.originalUser = JSON.parse(JSON.stringify(this.user));\n      this.$emit('update:user');\n    },\n    getRoleText(role) {\n      const roleMap = {\n        'ADMIN': '管理员',\n        'BUSINESS': '商家用户',\n        'USER': '普通用户'\n      };\n      return roleMap[role] || '用户';\n    },\n    update() {\n      this.$refs.profileFormRef.validate(valid => {\n        if (valid) {\n          // 保存当前的用户信息到数据库\n          this.$request.put('/user/update', this.user).then(res => {\n            if (res.code === '200') {\n              // 成功更新\n              this.$message.success('保存成功');\n              // 更新浏览器缓存里的用户信息\n              localStorage.setItem('xm-user', JSON.stringify(this.user));\n              // 更新原始用户信息\n              this.originalUser = JSON.parse(JSON.stringify(this.user));\n              // 触发父级的数据更新\n              this.$emit('update:user');\n            } else {\n              this.$message.error(res.msg);\n            }\n          }).catch(() => {\n            this.$message.error('保存失败，请重试');\n          });\n        }\n      });\n    },\n    resetForm() {\n      // 重置为原始用户信息\n      this.user = JSON.parse(JSON.stringify(this.originalUser));\n      this.$message.info('已重置为原始信息');\n    },\n    handleAvatarSuccess(response, file, fileList) {\n      if (response.code === '200') {\n        // 把user的头像属性换成上传的图片的链接\n        this.$set(this.user, 'avatar', response.data);\n        this.$message.success('头像上传成功');\n      } else {\n        this.$message.error('头像上传失败');\n      }\n    },\n    // 修改密码\n    updatePassword() {\n      // 清空相关字段，保留用户名和手机号\n      this.user.verifyCode = '';\n      this.user.newPassword = '';\n      this.user.confirmPass = '';\n      this.dialogVisible = true;\n    },\n    save() {\n      this.$refs.formRef.validate(valid => {\n        if (valid) {\n          // 添加用户角色信息\n          const requestData = {\n            ...this.user,\n            role: 'USER' // 前端用户角色固定为USER\n          };\n          this.$request.put('/updatePassword', requestData).then(res => {\n            if (res.code === '200') {\n              // 成功更新\n              this.$message.success('修改密码成功，请重新登录');\n              this.dialogVisible = false;\n              // 清除本地存储并跳转到登录页\n              localStorage.removeItem('xm-user');\n              this.$router.push('/login');\n            } else {\n              this.$message.error(res.msg);\n            }\n          }).catch(() => {\n            this.$message.error('修改密码失败，请重试');\n          });\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["MyBlogsComponent", "ComplaintComponent", "ResponseComponent", "LeavemessComponent", "ReplyLeavemessComponent", "name", "components", "data", "validatePassword", "rule", "value", "callback", "Error", "user", "newPassword", "validateEmail", "test", "validatePhone", "JSON", "parse", "localStorage", "getItem", "originalUser", "activeMenu", "dialogVisible", "rules", "username", "required", "message", "trigger", "phone", "validator", "verifyCode", "len", "min", "confirmPass", "profileRules", "max", "email", "created", "stringify", "section", "$route", "query", "watch", "to", "from", "methods", "handleMenuSelect", "index", "$router", "push", "path", "updateUser", "$emit", "getRoleText", "role", "roleMap", "update", "$refs", "profileFormRef", "validate", "valid", "$request", "put", "then", "res", "code", "$message", "success", "setItem", "error", "msg", "catch", "resetForm", "info", "handleAvatarSuccess", "response", "file", "fileList", "$set", "updatePassword", "save", "formRef", "requestData", "removeItem"], "sources": ["src/views/front/Person.vue"], "sourcesContent": ["<template>\r\n  <div class=\"person-container\">\r\n    <!-- 主体内容 -->\r\n    <div class=\"main-content\">\r\n      <!-- 侧边栏 -->\r\n      <div class=\"person-sidebar\">\r\n        <div class=\"sidebar-content\">\r\n          <div class=\"user-profile-summary\">\r\n            <div class=\"avatar-container\">\r\n              <img v-if=\"user.avatar\" :src=\"user.avatar\" class=\"user-avatar\" />\r\n              <div v-else class=\"avatar-placeholder\">\r\n                <i class=\"el-icon-user\"></i>\r\n              </div>\r\n            </div>\r\n            <div class=\"user-info\">\r\n              <h3 class=\"user-name\">{{ user.name || user.username || '用户' }}</h3>\r\n              <p class=\"user-role\">{{ getRoleText(user.role) }}</p>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"menu-section\">\r\n            <el-menu\r\n              :default-active=\"activeMenu\"\r\n              class=\"sidebar-menu\"\r\n              @select=\"handleMenuSelect\">\r\n              <el-menu-item index=\"profile\" class=\"menu-item\">\r\n                <i class=\"el-icon-user menu-icon\"></i>\r\n                <span class=\"menu-text\">个人信息</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"myBlogs\" class=\"menu-item\">\r\n                <i class=\"el-icon-edit menu-icon\"></i>\r\n                <span class=\"menu-text\">我要发帖</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"complaint\" class=\"menu-item\">\r\n                <i class=\"el-icon-warning menu-icon\"></i>\r\n                <span class=\"menu-text\">填写点餐投诉</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"response\" class=\"menu-item\">\r\n                <i class=\"el-icon-message menu-icon\"></i>\r\n                <span class=\"menu-text\">点餐投诉反馈</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"leavemess\" class=\"menu-item\">\r\n                <i class=\"el-icon-chat-line-square menu-icon\"></i>\r\n                <span class=\"menu-text\">咨询留言</span>\r\n              </el-menu-item>\r\n              <el-menu-item index=\"replyLeavemess\" class=\"menu-item\">\r\n                <i class=\"el-icon-chat-dot-square menu-icon\"></i>\r\n                <span class=\"menu-text\">留言回复</span>\r\n              </el-menu-item>\r\n            </el-menu>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主内容区域 -->\r\n      <div class=\"person-content\">\r\n        <!-- 个人信息页面 -->\r\n        <div v-if=\"activeMenu === 'profile'\" class=\"content-section\">\r\n          <div class=\"section-header\">\r\n            <div class=\"header-info\">\r\n              <h2 class=\"section-title\">个人信息</h2>\r\n              <p class=\"section-subtitle\">管理您的个人资料和账户设置</p>\r\n            </div>\r\n            <el-button type=\"primary\" @click=\"updatePassword\" class=\"header-btn\">\r\n              <i class=\"el-icon-lock\"></i>\r\n              修改密码\r\n            </el-button>\r\n          </div>\r\n          \r\n          <div class=\"profile-content\">\r\n            <div class=\"profile-card\">\r\n              <div class=\"avatar-section\">\r\n                <div class=\"avatar-upload-container\">\r\n                  <el-upload\r\n                    class=\"avatar-uploader\"\r\n                    :action=\"$baseUrl + '/files/upload'\"\r\n                    :show-file-list=\"false\"\r\n                    :on-success=\"handleAvatarSuccess\"\r\n                  >\r\n                    <div class=\"avatar-wrapper\">\r\n                      <img v-if=\"user.avatar\" :src=\"user.avatar\" class=\"profile-avatar\" />\r\n                      <div v-else class=\"avatar-placeholder-large\">\r\n                        <i class=\"el-icon-user\"></i>\r\n                      </div>\r\n                      <div class=\"avatar-overlay\">\r\n                        <i class=\"el-icon-camera\"></i>\r\n                        <span>更换头像</span>\r\n                      </div>\r\n                    </div>\r\n                  </el-upload>\r\n                </div>\r\n                <div class=\"avatar-tips\">\r\n                  <p>点击上传头像</p>\r\n                  <p class=\"tip-text\">支持JPG、PNG格式，建议尺寸200x200像素</p>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"form-section\">\r\n                <el-form :model=\"user\" label-width=\"100px\" class=\"profile-form\" :rules=\"profileRules\" ref=\"profileFormRef\">\r\n                  <div class=\"form-group\">\r\n                    <h4 class=\"group-title\">基本信息</h4>\r\n                    <el-form-item label=\"用户名\" prop=\"username\">\r\n                      <el-input v-model=\"user.username\" placeholder=\"用户名\" disabled class=\"form-input\">\r\n                        <i slot=\"prefix\" class=\"el-icon-user\"></i>\r\n                      </el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"昵称\" prop=\"name\">\r\n                      <el-input v-model=\"user.name\" placeholder=\"请输入昵称\" class=\"form-input\">\r\n                        <i slot=\"prefix\" class=\"el-icon-edit\"></i>\r\n                      </el-input>\r\n                    </el-form-item>\r\n                  </div>\r\n\r\n                  <div class=\"form-group\">\r\n                    <h4 class=\"group-title\">联系方式</h4>\r\n                    <el-form-item label=\"手机号\" prop=\"phone\">\r\n                      <el-input v-model=\"user.phone\" placeholder=\"请输入手机号\" class=\"form-input\">\r\n                        <i slot=\"prefix\" class=\"el-icon-phone\"></i>\r\n                      </el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"邮箱\" prop=\"email\">\r\n                      <el-input v-model=\"user.email\" placeholder=\"请输入邮箱地址\" class=\"form-input\">\r\n                        <i slot=\"prefix\" class=\"el-icon-message\"></i>\r\n                      </el-input>\r\n                    </el-form-item>\r\n                  </div>\r\n\r\n                  <div class=\"form-actions\">\r\n                    <el-button type=\"primary\" @click=\"update\" class=\"save-btn\" size=\"large\">\r\n                      <i class=\"el-icon-check\"></i>\r\n                      保存信息\r\n                    </el-button>\r\n                    <el-button @click=\"resetForm\" class=\"reset-btn\" size=\"large\">\r\n                      <i class=\"el-icon-refresh\"></i>\r\n                      重置\r\n                    </el-button>\r\n                  </div>\r\n                </el-form>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 我要发帖页面 -->\r\n        <div v-else-if=\"activeMenu === 'myBlogs'\" class=\"content-section\">\r\n          <div class=\"section-header\">\r\n            <div class=\"header-info\">\r\n              <h2 class=\"section-title\">我要发帖</h2>\r\n              <p class=\"section-subtitle\">分享您的想法和经验</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"section-content\">\r\n            <MyBlogsComponent @update:user=\"updateUser\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 填写点餐投诉页面 -->\r\n        <div v-else-if=\"activeMenu === 'complaint'\" class=\"content-section\">\r\n          <div class=\"section-header\">\r\n            <div class=\"header-info\">\r\n              <h2 class=\"section-title\">填写点餐投诉</h2>\r\n              <p class=\"section-subtitle\">提交您的投诉和建议</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"section-content\">\r\n            <ComplaintComponent @update:user=\"updateUser\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 点餐投诉反馈页面 -->\r\n        <div v-else-if=\"activeMenu === 'response'\" class=\"content-section\">\r\n          <div class=\"section-header\">\r\n            <div class=\"header-info\">\r\n              <h2 class=\"section-title\">点餐投诉反馈</h2>\r\n              <p class=\"section-subtitle\">查看投诉处理结果</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"section-content\">\r\n            <ResponseComponent @update:user=\"updateUser\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 咨询留言页面 -->\r\n        <div v-else-if=\"activeMenu === 'leavemess'\" class=\"content-section\">\r\n          <div class=\"section-header\">\r\n            <div class=\"header-info\">\r\n              <h2 class=\"section-title\">咨询留言</h2>\r\n              <p class=\"section-subtitle\">发表您的咨询和留言</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"section-content\">\r\n            <LeavemessComponent @update:user=\"updateUser\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 留言回复页面 -->\r\n        <div v-else-if=\"activeMenu === 'replyLeavemess'\" class=\"content-section\">\r\n          <div class=\"section-header\">\r\n            <div class=\"header-info\">\r\n              <h2 class=\"section-title\">留言回复</h2>\r\n              <p class=\"section-subtitle\">查看和管理留言回复</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"section-content\">\r\n            <ReplyLeavemessComponent @update:user=\"updateUser\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <el-dialog \r\n      title=\"修改密码\" \r\n      :visible.sync=\"dialogVisible\" \r\n      width=\"40%\" \r\n      :close-on-click-modal=\"false\" \r\n      destroy-on-close\r\n      custom-class=\"password-dialog\">\r\n      <div class=\"password-form-container\">\r\n        <el-form :model=\"user\" label-width=\"100px\" :rules=\"rules\" ref=\"formRef\" class=\"password-form\">\r\n          <el-form-item label=\"用户名\" prop=\"username\">\r\n            <el-input \r\n              v-model=\"user.username\" \r\n              placeholder=\"请输入用户名\"\r\n              class=\"form-input\"\r\n              :disabled=\"true\">\r\n              <i slot=\"prefix\" class=\"el-icon-user\"></i>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"手机号\" prop=\"phone\">\r\n            <el-input \r\n              v-model=\"user.phone\" \r\n              placeholder=\"请输入手机号\"\r\n              class=\"form-input\">\r\n              <i slot=\"prefix\" class=\"el-icon-phone\"></i>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"短信验证码\" prop=\"verifyCode\">\r\n            <SmsCode \r\n              v-model=\"user.verifyCode\"\r\n              :phone-number=\"user.phone\"\r\n              placeholder=\"请输入验证码\"\r\n              style=\"width: 100%;\">\r\n            </SmsCode>\r\n          </el-form-item>\r\n          <el-form-item label=\"新密码\" prop=\"newPassword\">\r\n            <el-input \r\n              show-password \r\n              v-model=\"user.newPassword\" \r\n              placeholder=\"请输入新密码\"\r\n              class=\"form-input\">\r\n              <i slot=\"prefix\" class=\"el-icon-key\"></i>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"确认密码\" prop=\"confirmPass\">\r\n            <el-input \r\n              show-password \r\n              v-model=\"user.confirmPass\" \r\n              placeholder=\"请再次输入新密码\"\r\n              class=\"form-input\">\r\n              <i slot=\"prefix\" class=\"el-icon-key\"></i>\r\n            </el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\" size=\"large\" class=\"cancel-btn\">\r\n          取 消\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"save\" size=\"large\" class=\"confirm-btn\">\r\n          确 定\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 导入各个功能组件\r\nimport MyBlogsComponent from './MyBlogs.vue'\r\nimport ComplaintComponent from './Complaint.vue'\r\nimport ResponseComponent from './Response.vue'\r\nimport LeavemessComponent from './Leavemess.vue'\r\nimport ReplyLeavemessComponent from './ReplyLeavemess.vue'\r\n\r\nexport default {\r\n  name: 'PersonCenter',\r\n  components: {\r\n    MyBlogsComponent,\r\n    ComplaintComponent,\r\n    ResponseComponent,\r\n    LeavemessComponent,\r\n    ReplyLeavemessComponent\r\n  },\r\n  data() {\r\n    const validatePassword = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请确认密码'))\r\n      } else if (value !== this.user.newPassword) {\r\n        callback(new Error('确认密码错误'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n    const validateEmail = (rule, value, callback) => {\r\n      if (value && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)) {\r\n        callback(new Error('请输入正确的邮箱格式'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n    const validatePhone = (rule, value, callback) => {\r\n      if (value && !/^1[3-9]\\d{9}$/.test(value)) {\r\n        callback(new Error('请输入正确的手机号格式'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n    \r\n    return {\r\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n      originalUser: {}, // 保存原始用户信息\r\n      activeMenu: 'profile', // 默认显示个人信息\r\n      dialogVisible: false,\r\n      rules: {\r\n        username: [\r\n          { required: true, message: '请输入用户名', trigger: 'blur' },\r\n        ],\r\n        phone: [\r\n          { required: true, message: '请输入手机号', trigger: 'blur' },\r\n          { validator: validatePhone, trigger: 'blur' }\r\n        ],\r\n        verifyCode: [\r\n          { required: true, message: '请输入短信验证码', trigger: 'blur' },\r\n          { len: 6, message: '验证码长度为6位', trigger: 'blur' }\r\n        ],\r\n        newPassword: [\r\n          { required: true, message: '请输入新密码', trigger: 'blur' },\r\n          { min: 6, message: '密码长度至少6位', trigger: 'blur' }\r\n        ],\r\n        confirmPass: [\r\n          { validator: validatePassword, required: true, trigger: 'blur' },\r\n        ],\r\n      },\r\n      profileRules: {\r\n        name: [\r\n          { required: true, message: '请输入昵称', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '昵称长度在2到20个字符', trigger: 'blur' }\r\n        ],\r\n        email: [\r\n          { validator: validateEmail, trigger: 'blur' }\r\n        ],\r\n        phone: [\r\n          { validator: validatePhone, trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 保存原始用户信息\r\n    this.originalUser = JSON.parse(JSON.stringify(this.user))\r\n    \r\n    // 根据URL参数设置活跃菜单\r\n    const section = this.$route.query.section\r\n    if (section) {\r\n      this.activeMenu = section\r\n    }\r\n  },\r\n  watch: {\r\n    '$route'(to, from) {\r\n      // 监听路由变化，更新活跃菜单\r\n      const section = to.query.section\r\n      if (section) {\r\n        this.activeMenu = section\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    handleMenuSelect(index) {\r\n      this.activeMenu = index\r\n      // 更新URL参数，但不刷新页面\r\n      this.$router.push({ path: '/front/person', query: { section: index } })\r\n    },\r\n    \r\n    updateUser() {\r\n      this.user = JSON.parse(localStorage.getItem('xm-user') || '{}')\r\n      this.originalUser = JSON.parse(JSON.stringify(this.user))\r\n      this.$emit('update:user')\r\n    },\r\n    \r\n    getRoleText(role) {\r\n      const roleMap = {\r\n        'ADMIN': '管理员',\r\n        'BUSINESS': '商家用户',\r\n        'USER': '普通用户'\r\n      }\r\n      return roleMap[role] || '用户'\r\n    },\r\n    \r\n    update() {\r\n      this.$refs.profileFormRef.validate((valid) => {\r\n        if (valid) {\r\n          // 保存当前的用户信息到数据库\r\n          this.$request.put('/user/update', this.user).then(res => {\r\n            if (res.code === '200') {\r\n              // 成功更新\r\n              this.$message.success('保存成功')\r\n              // 更新浏览器缓存里的用户信息\r\n              localStorage.setItem('xm-user', JSON.stringify(this.user))\r\n              // 更新原始用户信息\r\n              this.originalUser = JSON.parse(JSON.stringify(this.user))\r\n              // 触发父级的数据更新\r\n              this.$emit('update:user')\r\n            } else {\r\n              this.$message.error(res.msg)\r\n            }\r\n          }).catch(() => {\r\n            this.$message.error('保存失败，请重试')\r\n          })\r\n        }\r\n      })\r\n    },\r\n    \r\n    resetForm() {\r\n      // 重置为原始用户信息\r\n      this.user = JSON.parse(JSON.stringify(this.originalUser))\r\n      this.$message.info('已重置为原始信息')\r\n    },\r\n    \r\n    handleAvatarSuccess(response, file, fileList) {\r\n      if (response.code === '200') {\r\n        // 把user的头像属性换成上传的图片的链接\r\n        this.$set(this.user, 'avatar', response.data)\r\n        this.$message.success('头像上传成功')\r\n      } else {\r\n        this.$message.error('头像上传失败')\r\n      }\r\n    },\r\n    \r\n    // 修改密码\r\n    updatePassword() {\r\n      // 清空相关字段，保留用户名和手机号\r\n      this.user.verifyCode = ''\r\n      this.user.newPassword = ''\r\n      this.user.confirmPass = ''\r\n      this.dialogVisible = true\r\n    },\r\n    \r\n    save() {\r\n      this.$refs.formRef.validate((valid) => {\r\n        if (valid) {\r\n          // 添加用户角色信息\r\n          const requestData = {\r\n            ...this.user,\r\n            role: 'USER'  // 前端用户角色固定为USER\r\n          }\r\n          \r\n          this.$request.put('/updatePassword', requestData).then(res => {\r\n            if (res.code === '200') {\r\n              // 成功更新\r\n              this.$message.success('修改密码成功，请重新登录')\r\n              this.dialogVisible = false\r\n              // 清除本地存储并跳转到登录页\r\n              localStorage.removeItem('xm-user')\r\n              this.$router.push('/login')\r\n            } else {\r\n              this.$message.error(res.msg)\r\n            }\r\n          }).catch(() => {\r\n            this.$message.error('修改密码失败，请重试')\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.person-container {\r\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 主体内容 */\r\n.main-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 40px 20px;\r\n  display: flex;\r\n  gap: 30px;\r\n  align-items: flex-start;\r\n}\r\n\r\n/* 侧边栏 */\r\n.person-sidebar {\r\n  width: 280px;\r\n  flex-shrink: 0;\r\n  animation: slideInLeft 0.8s ease-out;\r\n}\r\n\r\n.sidebar-content {\r\n  background: white;\r\n  border-radius: 16px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  overflow: hidden;\r\n  border: 2px solid #f1f5f9;\r\n}\r\n\r\n.user-profile-summary {\r\n  padding: 30px 20px;\r\n  text-align: center;\r\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.avatar-container {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.user-avatar {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 3px solid #3b82f6;\r\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.avatar-placeholder {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  background: #e5e7eb;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto;\r\n  color: #9ca3af;\r\n  font-size: 32px;\r\n  border: 3px solid #d1d5db;\r\n}\r\n\r\n.user-info {\r\n  margin-top: 12px;\r\n}\r\n\r\n.user-name {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #1e293b;\r\n  margin: 0 0 4px 0;\r\n}\r\n\r\n.user-role {\r\n  font-size: 14px;\r\n  color: #64748b;\r\n  margin: 0;\r\n}\r\n\r\n.menu-section {\r\n  padding: 20px 0;\r\n}\r\n\r\n.sidebar-menu {\r\n  border: none;\r\n  background: transparent;\r\n}\r\n\r\n.menu-item {\r\n  margin: 0 16px 8px 16px;\r\n  border-radius: 12px;\r\n  height: 48px;\r\n  line-height: 48px;\r\n  transition: all 0.3s ease;\r\n  border: none;\r\n}\r\n\r\n.menu-item:hover {\r\n  background: #f0f9ff;\r\n  color: #3b82f6;\r\n}\r\n\r\n.menu-item.is-active {\r\n  background: #3b82f6;\r\n  color: white;\r\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.menu-icon {\r\n  font-size: 18px;\r\n  margin-right: 12px;\r\n  width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.menu-text {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 主内容区域 */\r\n.person-content {\r\n  flex: 1;\r\n  animation: fadeInRight 0.8s ease-out;\r\n}\r\n\r\n.content-section {\r\n  background: white;\r\n  border-radius: 16px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 2px solid #f1f5f9;\r\n  overflow: hidden;\r\n}\r\n\r\n.section-header {\r\n  padding: 30px 40px;\r\n  border-bottom: 1px solid #e2e8f0;\r\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.header-info {\r\n  flex: 1;\r\n}\r\n\r\n.section-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #1e293b;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.section-subtitle {\r\n  font-size: 14px;\r\n  color: #64748b;\r\n  margin: 0;\r\n}\r\n\r\n.header-btn {\r\n  background: #3b82f6;\r\n  border-color: #3b82f6;\r\n  border-radius: 12px;\r\n  padding: 0 20px;\r\n  font-weight: 500;\r\n}\r\n\r\n.header-btn:hover {\r\n  background: #1e40af;\r\n  border-color: #1e40af;\r\n}\r\n\r\n.section-content {\r\n  padding: 40px;\r\n}\r\n\r\n/* 个人信息页面 */\r\n.profile-content {\r\n  padding: 40px;\r\n}\r\n\r\n.profile-card {\r\n  display: flex;\r\n  gap: 40px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.avatar-section {\r\n  flex-shrink: 0;\r\n  text-align: center;\r\n  width: 200px;\r\n}\r\n\r\n.avatar-upload-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.avatar-uploader {\r\n  display: inline-block;\r\n}\r\n\r\n.avatar-wrapper {\r\n  position: relative;\r\n  width: 150px;\r\n  height: 150px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.avatar-wrapper:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.profile-avatar {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  border: 4px solid #3b82f6;\r\n  border-radius: 50%;\r\n}\r\n\r\n.avatar-placeholder-large {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f1f5f9;\r\n  border: 4px solid #e5e7eb;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #9ca3af;\r\n  font-size: 48px;\r\n}\r\n\r\n.avatar-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(59, 130, 246, 0.8);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n  color: white;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.avatar-wrapper:hover .avatar-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.avatar-overlay i {\r\n  font-size: 24px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.avatar-tips {\r\n  text-align: center;\r\n}\r\n\r\n.avatar-tips p {\r\n  margin: 0 0 4px 0;\r\n  font-size: 14px;\r\n  color: #374151;\r\n}\r\n\r\n.tip-text {\r\n  font-size: 12px;\r\n  color: #9ca3af;\r\n}\r\n\r\n.form-section {\r\n  flex: 1;\r\n}\r\n\r\n.profile-form {\r\n  max-width: 500px;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: #f8fafc;\r\n  border-radius: 12px;\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.group-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #1e293b;\r\n  margin: 0 0 20px 0;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #3b82f6;\r\n}\r\n\r\n.form-input >>> .el-input__inner {\r\n  border-radius: 12px;\r\n  border: 2px solid #e5e7eb;\r\n  padding-left: 40px;\r\n  height: 48px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.form-input >>> .el-input__inner:focus {\r\n  border-color: #3b82f6;\r\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.form-input >>> .el-input__inner:disabled {\r\n  background: #f9fafb;\r\n  color: #6b7280;\r\n}\r\n\r\n.form-input >>> .el-input__prefix {\r\n  color: #3b82f6;\r\n  font-size: 16px;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n  justify-content: center;\r\n  margin-top: 40px;\r\n}\r\n\r\n.save-btn {\r\n  background: #3b82f6;\r\n  border-color: #3b82f6;\r\n  border-radius: 12px;\r\n  padding: 0 32px;\r\n  font-weight: 600;\r\n}\r\n\r\n.save-btn:hover {\r\n  background: #1e40af;\r\n  border-color: #1e40af;\r\n}\r\n\r\n.reset-btn {\r\n  border-radius: 12px;\r\n  padding: 0 32px;\r\n  border: 2px solid #e5e7eb;\r\n  color: #64748b;\r\n  font-weight: 500;\r\n}\r\n\r\n.reset-btn:hover {\r\n  border-color: #3b82f6;\r\n  color: #3b82f6;\r\n}\r\n\r\n/* 修改密码弹窗 */\r\n.password-dialog >>> .el-dialog {\r\n  border-radius: 16px;\r\n}\r\n\r\n.password-dialog >>> .el-dialog__header {\r\n  background: #f8fafc;\r\n  border-bottom: 1px solid #e2e8f0;\r\n  border-radius: 16px 16px 0 0;\r\n}\r\n\r\n.password-form-container {\r\n  padding: 20px 0;\r\n}\r\n\r\n.password-form .form-input >>> .el-input__inner {\r\n  border-radius: 12px;\r\n  border: 2px solid #e5e7eb;\r\n  padding-left: 40px;\r\n  height: 48px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.password-form .form-input >>> .el-input__inner:focus {\r\n  border-color: #3b82f6;\r\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.password-form .form-input >>> .el-input__inner:disabled {\r\n  background: #f9fafb;\r\n  color: #6b7280;\r\n  border-color: #e5e7eb;\r\n}\r\n\r\n.password-form .form-input >>> .el-input__prefix {\r\n  color: #3b82f6;\r\n  font-size: 16px;\r\n}\r\n\r\n.dialog-footer {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 16px;\r\n  padding: 20px 0 0;\r\n}\r\n\r\n.cancel-btn {\r\n  border-radius: 12px;\r\n  padding: 0 32px;\r\n  border: 2px solid #e5e7eb;\r\n  color: #64748b;\r\n  font-weight: 500;\r\n}\r\n\r\n.cancel-btn:hover {\r\n  border-color: #3b82f6;\r\n  color: #3b82f6;\r\n}\r\n\r\n.confirm-btn {\r\n  background: #3b82f6;\r\n  border-color: #3b82f6;\r\n  border-radius: 12px;\r\n  padding: 0 32px;\r\n  font-weight: 600;\r\n}\r\n\r\n.confirm-btn:hover {\r\n  background: #1e40af;\r\n  border-color: #1e40af;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes slideInLeft {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(-30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes fadeInRight {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1024px) {\r\n  .main-content {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n  }\r\n  \r\n  .person-sidebar {\r\n    width: 100%;\r\n  }\r\n  \r\n  .profile-card {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    text-align: center;\r\n  }\r\n  \r\n  .avatar-section {\r\n    width: auto;\r\n  }\r\n  \r\n  .form-section {\r\n    width: 100%;\r\n    max-width: 500px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    padding: 20px 15px;\r\n  }\r\n  \r\n  .section-header {\r\n    padding: 20px 25px;\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 15px;\r\n  }\r\n  \r\n  .section-content,\r\n  .profile-content {\r\n    padding: 25px;\r\n  }\r\n  \r\n  .user-profile-summary {\r\n    padding: 20px 15px;\r\n  }\r\n  \r\n  .menu-section {\r\n    padding: 15px 0;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .save-btn,\r\n  .reset-btn {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>"], "mappings": ";AAsRA;AACA,OAAAA,gBAAA;AACA,OAAAC,kBAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,kBAAA;AACA,OAAAC,uBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,gBAAA;IACAC,kBAAA;IACAC,iBAAA;IACAC,kBAAA;IACAC;EACA;EACAG,KAAA;IACA,MAAAC,gBAAA,GAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAD,KAAA;QACAC,QAAA,KAAAC,KAAA;MACA,WAAAF,KAAA,UAAAG,IAAA,CAAAC,WAAA;QACAH,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IACA,MAAAI,aAAA,GAAAA,CAAAN,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAD,KAAA,kCAAAM,IAAA,CAAAN,KAAA;QACAC,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IACA,MAAAM,aAAA,GAAAA,CAAAR,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAD,KAAA,qBAAAM,IAAA,CAAAN,KAAA;QACAC,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IAEA;MACAE,IAAA,EAAAK,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,YAAA;MAAA;MACAC,UAAA;MAAA;MACAC,aAAA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,KAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,SAAA,EAAAd,aAAA;UAAAY,OAAA;QAAA,EACA;QACAG,UAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAI,GAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,WAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAK,GAAA;UAAAN,OAAA;UAAAC,OAAA;QAAA,EACA;QACAM,WAAA,GACA;UAAAJ,SAAA,EAAAvB,gBAAA;UAAAmB,QAAA;UAAAE,OAAA;QAAA;MAEA;MACAO,YAAA;QACA/B,IAAA,GACA;UAAAsB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAK,GAAA;UAAAG,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAS,KAAA,GACA;UAAAP,SAAA,EAAAhB,aAAA;UAAAc,OAAA;QAAA,EACA;QACAC,KAAA,GACA;UAAAC,SAAA,EAAAd,aAAA;UAAAY,OAAA;QAAA;MAEA;IACA;EACA;EACAU,QAAA;IACA;IACA,KAAAjB,YAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAsB,SAAA,MAAA3B,IAAA;;IAEA;IACA,MAAA4B,OAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,OAAA;IACA,IAAAA,OAAA;MACA,KAAAlB,UAAA,GAAAkB,OAAA;IACA;EACA;EACAG,KAAA;IACA,QAAAF,CAAAG,EAAA,EAAAC,IAAA;MACA;MACA,MAAAL,OAAA,GAAAI,EAAA,CAAAF,KAAA,CAAAF,OAAA;MACA,IAAAA,OAAA;QACA,KAAAlB,UAAA,GAAAkB,OAAA;MACA;IACA;EACA;EACAM,OAAA;IACAC,iBAAAC,KAAA;MACA,KAAA1B,UAAA,GAAA0B,KAAA;MACA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA;QAAAT,KAAA;UAAAF,OAAA,EAAAQ;QAAA;MAAA;IACA;IAEAI,WAAA;MACA,KAAAxC,IAAA,GAAAK,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACA,KAAAC,YAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAsB,SAAA,MAAA3B,IAAA;MACA,KAAAyC,KAAA;IACA;IAEAC,YAAAC,IAAA;MACA,MAAAC,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEAE,OAAA;MACA,KAAAC,KAAA,CAAAC,cAAA,CAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAC,QAAA,CAAAC,GAAA,sBAAAnD,IAAA,EAAAoD,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA;cACAjD,YAAA,CAAAkD,OAAA,YAAApD,IAAA,CAAAsB,SAAA,MAAA3B,IAAA;cACA;cACA,KAAAS,YAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAsB,SAAA,MAAA3B,IAAA;cACA;cACA,KAAAyC,KAAA;YACA;cACA,KAAAc,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;YACA;UACA,GAAAC,KAAA;YACA,KAAAL,QAAA,CAAAG,KAAA;UACA;QACA;MACA;IACA;IAEAG,UAAA;MACA;MACA,KAAA7D,IAAA,GAAAK,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAsB,SAAA,MAAAlB,YAAA;MACA,KAAA8C,QAAA,CAAAO,IAAA;IACA;IAEAC,oBAAAC,QAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,IAAAF,QAAA,CAAAV,IAAA;QACA;QACA,KAAAa,IAAA,MAAAnE,IAAA,YAAAgE,QAAA,CAAAtE,IAAA;QACA,KAAA6D,QAAA,CAAAC,OAAA;MACA;QACA,KAAAD,QAAA,CAAAG,KAAA;MACA;IACA;IAEA;IACAU,eAAA;MACA;MACA,KAAApE,IAAA,CAAAmB,UAAA;MACA,KAAAnB,IAAA,CAAAC,WAAA;MACA,KAAAD,IAAA,CAAAsB,WAAA;MACA,KAAAX,aAAA;IACA;IAEA0D,KAAA;MACA,KAAAvB,KAAA,CAAAwB,OAAA,CAAAtB,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,MAAAsB,WAAA;YACA,QAAAvE,IAAA;YACA2C,IAAA;UACA;UAEA,KAAAO,QAAA,CAAAC,GAAA,oBAAAoB,WAAA,EAAAnB,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA;cACA,KAAAC,QAAA,CAAAC,OAAA;cACA,KAAA7C,aAAA;cACA;cACAJ,YAAA,CAAAiE,UAAA;cACA,KAAAnC,OAAA,CAAAC,IAAA;YACA;cACA,KAAAiB,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;YACA;UACA,GAAAC,KAAA;YACA,KAAAL,QAAA,CAAAG,KAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}