{"ast": null, "code": "import rules from '../rule/';\nimport { isEmptyValue } from '../util';\n/**\n *  Validates an array.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param callback The callback function.\n *  @param source The source object being validated.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction array(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, 'array') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'array');\n    if (!isEmptyValue(value, 'array')) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n}\nexport default array;", "map": {"version": 3, "names": ["rules", "isEmptyValue", "array", "rule", "value", "callback", "source", "options", "errors", "validate", "required", "hasOwnProperty", "field", "type", "range"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/async-validator/es/validator/array.js"], "sourcesContent": ["import rules from '../rule/';\nimport { isEmptyValue } from '../util';\n/**\n *  Validates an array.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param callback The callback function.\n *  @param source The source object being validated.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction array(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, 'array') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'array');\n    if (!isEmptyValue(value, 'array')) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n}\n\nexport default array;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,UAAU;AAC5B,SAASC,YAAY,QAAQ,SAAS;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACrD,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGN,IAAI,CAACO,QAAQ,IAAI,CAACP,IAAI,CAACO,QAAQ,IAAIJ,MAAM,CAACK,cAAc,CAACR,IAAI,CAACS,KAAK,CAAC;EACnF,IAAIH,QAAQ,EAAE;IACZ,IAAIR,YAAY,CAACG,KAAK,EAAE,OAAO,CAAC,IAAI,CAACD,IAAI,CAACO,QAAQ,EAAE;MAClD,OAAOL,QAAQ,CAAC,CAAC;IACnB;IACAL,KAAK,CAACU,QAAQ,CAACP,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,EAAE,OAAO,CAAC;IAC7D,IAAI,CAACN,YAAY,CAACG,KAAK,EAAE,OAAO,CAAC,EAAE;MACjCJ,KAAK,CAACa,IAAI,CAACV,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;MAChDP,KAAK,CAACc,KAAK,CAACX,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;IACnD;EACF;EACAF,QAAQ,CAACG,MAAM,CAAC;AAClB;AAEA,eAAeN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}