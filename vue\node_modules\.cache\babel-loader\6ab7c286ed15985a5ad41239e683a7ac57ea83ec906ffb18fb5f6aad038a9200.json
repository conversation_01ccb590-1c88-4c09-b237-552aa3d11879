{"ast": null, "code": "/**\n * Copyright 2004-present Facebook. All Rights Reserved.\n *\n * @providesModule UserAgent_DEPRECATED\n */\n\n/**\n *  Provides entirely client-side User Agent and OS detection. You should prefer\n *  the non-deprecated UserAgent module when possible, which exposes our\n *  authoritative server-side PHP-based detection to the client.\n *\n *  Usage is straightforward:\n *\n *    if (UserAgent_DEPRECATED.ie()) {\n *      //  IE\n *    }\n *\n *  You can also do version checks:\n *\n *    if (UserAgent_DEPRECATED.ie() >= 7) {\n *      //  IE7 or better\n *    }\n *\n *  The browser functions will return NaN if the browser does not match, so\n *  you can also do version compares the other way:\n *\n *    if (UserAgent_DEPRECATED.ie() < 7) {\n *      //  IE6 or worse\n *    }\n *\n *  Note that the version is a float and may include a minor version number,\n *  so you should always use range operators to perform comparisons, not\n *  strict equality.\n *\n *  **Note:** You should **strongly** prefer capability detection to browser\n *  version detection where it's reasonable:\n *\n *    http://www.quirksmode.org/js/support.html\n *\n *  Further, we have a large number of mature wrapper functions and classes\n *  which abstract away many browser irregularities. Check the documentation,\n *  grep for things, or <NAME_EMAIL> before writing yet\n *  another copy of \"event || window.event\".\n *\n */\n\nvar _populated = false;\n\n// Browsers\nvar _ie, _firefox, _opera, _webkit, _chrome;\n\n// Actual IE browser for compatibility mode\nvar _ie_real_version;\n\n// Platforms\nvar _osx, _windows, _linux, _android;\n\n// Architectures\nvar _win64;\n\n// Devices\nvar _iphone, _ipad, _native;\nvar _mobile;\nfunction _populate() {\n  if (_populated) {\n    return;\n  }\n  _populated = true;\n\n  // To work around buggy JS libraries that can't handle multi-digit\n  // version numbers, Opera 10's user agent string claims it's Opera\n  // 9, then later includes a Version/X.Y field:\n  //\n  // Opera/9.80 (foo) Presto/2.2.15 Version/10.10\n  var uas = navigator.userAgent;\n  var agent = /(?:MSIE.(\\d+\\.\\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\\d+\\.\\d+))|(?:Opera(?:.+Version.|.)(\\d+\\.\\d+))|(?:AppleWebKit.(\\d+(?:\\.\\d+)?))|(?:Trident\\/\\d+\\.\\d+.*rv:(\\d+\\.\\d+))/.exec(uas);\n  var os = /(Mac OS X)|(Windows)|(Linux)/.exec(uas);\n  _iphone = /\\b(iPhone|iP[ao]d)/.exec(uas);\n  _ipad = /\\b(iP[ao]d)/.exec(uas);\n  _android = /Android/i.exec(uas);\n  _native = /FBAN\\/\\w+;/i.exec(uas);\n  _mobile = /Mobile/i.exec(uas);\n\n  // Note that the IE team blog would have you believe you should be checking\n  // for 'Win64; x64'.  But MSDN then reveals that you can actually be coming\n  // from either x64 or ia64;  so ultimately, you should just check for Win64\n  // as in indicator of whether you're in 64-bit IE.  32-bit IE on 64-bit\n  // Windows will send 'WOW64' instead.\n  _win64 = !!/Win64/.exec(uas);\n  if (agent) {\n    _ie = agent[1] ? parseFloat(agent[1]) : agent[5] ? parseFloat(agent[5]) : NaN;\n    // IE compatibility mode\n    if (_ie && document && document.documentMode) {\n      _ie = document.documentMode;\n    }\n    // grab the \"true\" ie version from the trident token if available\n    var trident = /(?:Trident\\/(\\d+.\\d+))/.exec(uas);\n    _ie_real_version = trident ? parseFloat(trident[1]) + 4 : _ie;\n    _firefox = agent[2] ? parseFloat(agent[2]) : NaN;\n    _opera = agent[3] ? parseFloat(agent[3]) : NaN;\n    _webkit = agent[4] ? parseFloat(agent[4]) : NaN;\n    if (_webkit) {\n      // We do not add the regexp to the above test, because it will always\n      // match 'safari' only since 'AppleWebKit' appears before 'Chrome' in\n      // the userAgent string.\n      agent = /(?:Chrome\\/(\\d+\\.\\d+))/.exec(uas);\n      _chrome = agent && agent[1] ? parseFloat(agent[1]) : NaN;\n    } else {\n      _chrome = NaN;\n    }\n  } else {\n    _ie = _firefox = _opera = _chrome = _webkit = NaN;\n  }\n  if (os) {\n    if (os[1]) {\n      // Detect OS X version.  If no version number matches, set _osx to true.\n      // Version examples:  10, 10_6_1, 10.7\n      // Parses version number as a float, taking only first two sets of\n      // digits.  If only one set of digits is found, returns just the major\n      // version number.\n      var ver = /(?:Mac OS X (\\d+(?:[._]\\d+)?))/.exec(uas);\n      _osx = ver ? parseFloat(ver[1].replace('_', '.')) : true;\n    } else {\n      _osx = false;\n    }\n    _windows = !!os[2];\n    _linux = !!os[3];\n  } else {\n    _osx = _windows = _linux = false;\n  }\n}\nvar UserAgent_DEPRECATED = {\n  /**\n   *  Check if the UA is Internet Explorer.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  ie: function () {\n    return _populate() || _ie;\n  },\n  /**\n   * Check if we're in Internet Explorer compatibility mode.\n   *\n   * @return bool true if in compatibility mode, false if\n   * not compatibility mode or not ie\n   */\n  ieCompatibilityMode: function () {\n    return _populate() || _ie_real_version > _ie;\n  },\n  /**\n   * Whether the browser is 64-bit IE.  Really, this is kind of weak sauce;  we\n   * only need this because Skype can't handle 64-bit IE yet.  We need to remove\n   * this when we don't need it -- tracked by #601957.\n   */\n  ie64: function () {\n    return UserAgent_DEPRECATED.ie() && _win64;\n  },\n  /**\n   *  Check if the UA is Firefox.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  firefox: function () {\n    return _populate() || _firefox;\n  },\n  /**\n   *  Check if the UA is Opera.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  opera: function () {\n    return _populate() || _opera;\n  },\n  /**\n   *  Check if the UA is WebKit.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  webkit: function () {\n    return _populate() || _webkit;\n  },\n  /**\n   *  For Push\n   *  WILL BE REMOVED VERY SOON. Use UserAgent_DEPRECATED.webkit\n   */\n  safari: function () {\n    return UserAgent_DEPRECATED.webkit();\n  },\n  /**\n   *  Check if the UA is a Chrome browser.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  chrome: function () {\n    return _populate() || _chrome;\n  },\n  /**\n   *  Check if the user is running Windows.\n   *\n   *  @return bool `true' if the user's OS is Windows.\n   */\n  windows: function () {\n    return _populate() || _windows;\n  },\n  /**\n   *  Check if the user is running Mac OS X.\n   *\n   *  @return float|bool   Returns a float if a version number is detected,\n   *                       otherwise true/false.\n   */\n  osx: function () {\n    return _populate() || _osx;\n  },\n  /**\n   * Check if the user is running Linux.\n   *\n   * @return bool `true' if the user's OS is some flavor of Linux.\n   */\n  linux: function () {\n    return _populate() || _linux;\n  },\n  /**\n   * Check if the user is running on an iPhone or iPod platform.\n   *\n   * @return bool `true' if the user is running some flavor of the\n   *    iPhone OS.\n   */\n  iphone: function () {\n    return _populate() || _iphone;\n  },\n  mobile: function () {\n    return _populate() || _iphone || _ipad || _android || _mobile;\n  },\n  nativeApp: function () {\n    // webviews inside of the native apps\n    return _populate() || _native;\n  },\n  android: function () {\n    return _populate() || _android;\n  },\n  ipad: function () {\n    return _populate() || _ipad;\n  }\n};\nmodule.exports = UserAgent_DEPRECATED;", "map": {"version": 3, "names": ["_populated", "_ie", "_firefox", "_opera", "_webkit", "_chrome", "_ie_real_version", "_osx", "_windows", "_linux", "_android", "_win64", "_iphone", "_ipad", "_native", "_mobile", "_populate", "uas", "navigator", "userAgent", "agent", "exec", "os", "parseFloat", "NaN", "document", "documentMode", "trident", "ver", "replace", "UserAgent_DEPRECATED", "ie", "ieCompatibilityMode", "ie64", "firefox", "opera", "webkit", "safari", "chrome", "windows", "osx", "linux", "iphone", "mobile", "nativeApp", "android", "ipad", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/normalize-wheel/src/UserAgent_DEPRECATED.js"], "sourcesContent": ["/**\n * Copyright 2004-present Facebook. All Rights Reserved.\n *\n * @providesModule UserAgent_DEPRECATED\n */\n\n/**\n *  Provides entirely client-side User Agent and OS detection. You should prefer\n *  the non-deprecated UserAgent module when possible, which exposes our\n *  authoritative server-side PHP-based detection to the client.\n *\n *  Usage is straightforward:\n *\n *    if (UserAgent_DEPRECATED.ie()) {\n *      //  IE\n *    }\n *\n *  You can also do version checks:\n *\n *    if (UserAgent_DEPRECATED.ie() >= 7) {\n *      //  IE7 or better\n *    }\n *\n *  The browser functions will return NaN if the browser does not match, so\n *  you can also do version compares the other way:\n *\n *    if (UserAgent_DEPRECATED.ie() < 7) {\n *      //  IE6 or worse\n *    }\n *\n *  Note that the version is a float and may include a minor version number,\n *  so you should always use range operators to perform comparisons, not\n *  strict equality.\n *\n *  **Note:** You should **strongly** prefer capability detection to browser\n *  version detection where it's reasonable:\n *\n *    http://www.quirksmode.org/js/support.html\n *\n *  Further, we have a large number of mature wrapper functions and classes\n *  which abstract away many browser irregularities. Check the documentation,\n *  grep for things, or <NAME_EMAIL> before writing yet\n *  another copy of \"event || window.event\".\n *\n */\n\nvar _populated = false;\n\n// Browsers\nvar _ie, _firefox, _opera, _webkit, _chrome;\n\n// Actual IE browser for compatibility mode\nvar _ie_real_version;\n\n// Platforms\nvar _osx, _windows, _linux, _android;\n\n// Architectures\nvar _win64;\n\n// Devices\nvar _iphone, _ipad, _native;\n\nvar _mobile;\n\nfunction _populate() {\n  if (_populated) {\n    return;\n  }\n\n  _populated = true;\n\n  // To work around buggy JS libraries that can't handle multi-digit\n  // version numbers, Opera 10's user agent string claims it's Opera\n  // 9, then later includes a Version/X.Y field:\n  //\n  // Opera/9.80 (foo) Presto/2.2.15 Version/10.10\n  var uas = navigator.userAgent;\n  var agent = /(?:MSIE.(\\d+\\.\\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\\d+\\.\\d+))|(?:Opera(?:.+Version.|.)(\\d+\\.\\d+))|(?:AppleWebKit.(\\d+(?:\\.\\d+)?))|(?:Trident\\/\\d+\\.\\d+.*rv:(\\d+\\.\\d+))/.exec(uas);\n  var os    = /(Mac OS X)|(Windows)|(Linux)/.exec(uas);\n\n  _iphone = /\\b(iPhone|iP[ao]d)/.exec(uas);\n  _ipad = /\\b(iP[ao]d)/.exec(uas);\n  _android = /Android/i.exec(uas);\n  _native = /FBAN\\/\\w+;/i.exec(uas);\n  _mobile = /Mobile/i.exec(uas);\n\n  // Note that the IE team blog would have you believe you should be checking\n  // for 'Win64; x64'.  But MSDN then reveals that you can actually be coming\n  // from either x64 or ia64;  so ultimately, you should just check for Win64\n  // as in indicator of whether you're in 64-bit IE.  32-bit IE on 64-bit\n  // Windows will send 'WOW64' instead.\n  _win64 = !!(/Win64/.exec(uas));\n\n  if (agent) {\n    _ie = agent[1] ? parseFloat(agent[1]) : (\n          agent[5] ? parseFloat(agent[5]) : NaN);\n    // IE compatibility mode\n    if (_ie && document && document.documentMode) {\n      _ie = document.documentMode;\n    }\n    // grab the \"true\" ie version from the trident token if available\n    var trident = /(?:Trident\\/(\\d+.\\d+))/.exec(uas);\n    _ie_real_version = trident ? parseFloat(trident[1]) + 4 : _ie;\n\n    _firefox = agent[2] ? parseFloat(agent[2]) : NaN;\n    _opera   = agent[3] ? parseFloat(agent[3]) : NaN;\n    _webkit  = agent[4] ? parseFloat(agent[4]) : NaN;\n    if (_webkit) {\n      // We do not add the regexp to the above test, because it will always\n      // match 'safari' only since 'AppleWebKit' appears before 'Chrome' in\n      // the userAgent string.\n      agent = /(?:Chrome\\/(\\d+\\.\\d+))/.exec(uas);\n      _chrome = agent && agent[1] ? parseFloat(agent[1]) : NaN;\n    } else {\n      _chrome = NaN;\n    }\n  } else {\n    _ie = _firefox = _opera = _chrome = _webkit = NaN;\n  }\n\n  if (os) {\n    if (os[1]) {\n      // Detect OS X version.  If no version number matches, set _osx to true.\n      // Version examples:  10, 10_6_1, 10.7\n      // Parses version number as a float, taking only first two sets of\n      // digits.  If only one set of digits is found, returns just the major\n      // version number.\n      var ver = /(?:Mac OS X (\\d+(?:[._]\\d+)?))/.exec(uas);\n\n      _osx = ver ? parseFloat(ver[1].replace('_', '.')) : true;\n    } else {\n      _osx = false;\n    }\n    _windows = !!os[2];\n    _linux   = !!os[3];\n  } else {\n    _osx = _windows = _linux = false;\n  }\n}\n\nvar UserAgent_DEPRECATED = {\n\n  /**\n   *  Check if the UA is Internet Explorer.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  ie: function() {\n    return _populate() || _ie;\n  },\n\n  /**\n   * Check if we're in Internet Explorer compatibility mode.\n   *\n   * @return bool true if in compatibility mode, false if\n   * not compatibility mode or not ie\n   */\n  ieCompatibilityMode: function() {\n    return _populate() || (_ie_real_version > _ie);\n  },\n\n\n  /**\n   * Whether the browser is 64-bit IE.  Really, this is kind of weak sauce;  we\n   * only need this because Skype can't handle 64-bit IE yet.  We need to remove\n   * this when we don't need it -- tracked by #601957.\n   */\n  ie64: function() {\n    return UserAgent_DEPRECATED.ie() && _win64;\n  },\n\n  /**\n   *  Check if the UA is Firefox.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  firefox: function() {\n    return _populate() || _firefox;\n  },\n\n\n  /**\n   *  Check if the UA is Opera.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  opera: function() {\n    return _populate() || _opera;\n  },\n\n\n  /**\n   *  Check if the UA is WebKit.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  webkit: function() {\n    return _populate() || _webkit;\n  },\n\n  /**\n   *  For Push\n   *  WILL BE REMOVED VERY SOON. Use UserAgent_DEPRECATED.webkit\n   */\n  safari: function() {\n    return UserAgent_DEPRECATED.webkit();\n  },\n\n  /**\n   *  Check if the UA is a Chrome browser.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  chrome : function() {\n    return _populate() || _chrome;\n  },\n\n\n  /**\n   *  Check if the user is running Windows.\n   *\n   *  @return bool `true' if the user's OS is Windows.\n   */\n  windows: function() {\n    return _populate() || _windows;\n  },\n\n\n  /**\n   *  Check if the user is running Mac OS X.\n   *\n   *  @return float|bool   Returns a float if a version number is detected,\n   *                       otherwise true/false.\n   */\n  osx: function() {\n    return _populate() || _osx;\n  },\n\n  /**\n   * Check if the user is running Linux.\n   *\n   * @return bool `true' if the user's OS is some flavor of Linux.\n   */\n  linux: function() {\n    return _populate() || _linux;\n  },\n\n  /**\n   * Check if the user is running on an iPhone or iPod platform.\n   *\n   * @return bool `true' if the user is running some flavor of the\n   *    iPhone OS.\n   */\n  iphone: function() {\n    return _populate() || _iphone;\n  },\n\n  mobile: function() {\n    return _populate() || (_iphone || _ipad || _android || _mobile);\n  },\n\n  nativeApp: function() {\n    // webviews inside of the native apps\n    return _populate() || _native;\n  },\n\n  android: function() {\n    return _populate() || _android;\n  },\n\n  ipad: function() {\n    return _populate() || _ipad;\n  }\n};\n\nmodule.exports = UserAgent_DEPRECATED;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIA,UAAU,GAAG,KAAK;;AAEtB;AACA,IAAIC,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO;;AAE3C;AACA,IAAIC,gBAAgB;;AAEpB;AACA,IAAIC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ;;AAEpC;AACA,IAAIC,MAAM;;AAEV;AACA,IAAIC,OAAO,EAAEC,KAAK,EAAEC,OAAO;AAE3B,IAAIC,OAAO;AAEX,SAASC,SAASA,CAAA,EAAG;EACnB,IAAIhB,UAAU,EAAE;IACd;EACF;EAEAA,UAAU,GAAG,IAAI;;EAEjB;EACA;EACA;EACA;EACA;EACA,IAAIiB,GAAG,GAAGC,SAAS,CAACC,SAAS;EAC7B,IAAIC,KAAK,GAAG,gLAAgL,CAACC,IAAI,CAACJ,GAAG,CAAC;EACtM,IAAIK,EAAE,GAAM,8BAA8B,CAACD,IAAI,CAACJ,GAAG,CAAC;EAEpDL,OAAO,GAAG,oBAAoB,CAACS,IAAI,CAACJ,GAAG,CAAC;EACxCJ,KAAK,GAAG,aAAa,CAACQ,IAAI,CAACJ,GAAG,CAAC;EAC/BP,QAAQ,GAAG,UAAU,CAACW,IAAI,CAACJ,GAAG,CAAC;EAC/BH,OAAO,GAAG,aAAa,CAACO,IAAI,CAACJ,GAAG,CAAC;EACjCF,OAAO,GAAG,SAAS,CAACM,IAAI,CAACJ,GAAG,CAAC;;EAE7B;EACA;EACA;EACA;EACA;EACAN,MAAM,GAAG,CAAC,CAAE,OAAO,CAACU,IAAI,CAACJ,GAAG,CAAE;EAE9B,IAAIG,KAAK,EAAE;IACTnB,GAAG,GAAGmB,KAAK,CAAC,CAAC,CAAC,GAAGG,UAAU,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAC/BA,KAAK,CAAC,CAAC,CAAC,GAAGG,UAAU,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGI,GAAI;IAC5C;IACA,IAAIvB,GAAG,IAAIwB,QAAQ,IAAIA,QAAQ,CAACC,YAAY,EAAE;MAC5CzB,GAAG,GAAGwB,QAAQ,CAACC,YAAY;IAC7B;IACA;IACA,IAAIC,OAAO,GAAG,wBAAwB,CAACN,IAAI,CAACJ,GAAG,CAAC;IAChDX,gBAAgB,GAAGqB,OAAO,GAAGJ,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG1B,GAAG;IAE7DC,QAAQ,GAAGkB,KAAK,CAAC,CAAC,CAAC,GAAGG,UAAU,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGI,GAAG;IAChDrB,MAAM,GAAKiB,KAAK,CAAC,CAAC,CAAC,GAAGG,UAAU,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGI,GAAG;IAChDpB,OAAO,GAAIgB,KAAK,CAAC,CAAC,CAAC,GAAGG,UAAU,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGI,GAAG;IAChD,IAAIpB,OAAO,EAAE;MACX;MACA;MACA;MACAgB,KAAK,GAAG,wBAAwB,CAACC,IAAI,CAACJ,GAAG,CAAC;MAC1CZ,OAAO,GAAGe,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGG,UAAU,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGI,GAAG;IAC1D,CAAC,MAAM;MACLnB,OAAO,GAAGmB,GAAG;IACf;EACF,CAAC,MAAM;IACLvB,GAAG,GAAGC,QAAQ,GAAGC,MAAM,GAAGE,OAAO,GAAGD,OAAO,GAAGoB,GAAG;EACnD;EAEA,IAAIF,EAAE,EAAE;IACN,IAAIA,EAAE,CAAC,CAAC,CAAC,EAAE;MACT;MACA;MACA;MACA;MACA;MACA,IAAIM,GAAG,GAAG,gCAAgC,CAACP,IAAI,CAACJ,GAAG,CAAC;MAEpDV,IAAI,GAAGqB,GAAG,GAAGL,UAAU,CAACK,GAAG,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI;IAC1D,CAAC,MAAM;MACLtB,IAAI,GAAG,KAAK;IACd;IACAC,QAAQ,GAAG,CAAC,CAACc,EAAE,CAAC,CAAC,CAAC;IAClBb,MAAM,GAAK,CAAC,CAACa,EAAE,CAAC,CAAC,CAAC;EACpB,CAAC,MAAM;IACLf,IAAI,GAAGC,QAAQ,GAAGC,MAAM,GAAG,KAAK;EAClC;AACF;AAEA,IAAIqB,oBAAoB,GAAG;EAEzB;AACF;AACA;AACA;AACA;AACA;EACEC,EAAE,EAAE,SAAAA,CAAA,EAAW;IACb,OAAOf,SAAS,CAAC,CAAC,IAAIf,GAAG;EAC3B,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE+B,mBAAmB,EAAE,SAAAA,CAAA,EAAW;IAC9B,OAAOhB,SAAS,CAAC,CAAC,IAAKV,gBAAgB,GAAGL,GAAI;EAChD,CAAC;EAGD;AACF;AACA;AACA;AACA;EACEgC,IAAI,EAAE,SAAAA,CAAA,EAAW;IACf,OAAOH,oBAAoB,CAACC,EAAE,CAAC,CAAC,IAAIpB,MAAM;EAC5C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEuB,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,OAAOlB,SAAS,CAAC,CAAC,IAAId,QAAQ;EAChC,CAAC;EAGD;AACF;AACA;AACA;AACA;AACA;EACEiC,KAAK,EAAE,SAAAA,CAAA,EAAW;IAChB,OAAOnB,SAAS,CAAC,CAAC,IAAIb,MAAM;EAC9B,CAAC;EAGD;AACF;AACA;AACA;AACA;AACA;EACEiC,MAAM,EAAE,SAAAA,CAAA,EAAW;IACjB,OAAOpB,SAAS,CAAC,CAAC,IAAIZ,OAAO;EAC/B,CAAC;EAED;AACF;AACA;AACA;EACEiC,MAAM,EAAE,SAAAA,CAAA,EAAW;IACjB,OAAOP,oBAAoB,CAACM,MAAM,CAAC,CAAC;EACtC,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEE,MAAM,EAAG,SAAAA,CAAA,EAAW;IAClB,OAAOtB,SAAS,CAAC,CAAC,IAAIX,OAAO;EAC/B,CAAC;EAGD;AACF;AACA;AACA;AACA;EACEkC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,OAAOvB,SAAS,CAAC,CAAC,IAAIR,QAAQ;EAChC,CAAC;EAGD;AACF;AACA;AACA;AACA;AACA;EACEgC,GAAG,EAAE,SAAAA,CAAA,EAAW;IACd,OAAOxB,SAAS,CAAC,CAAC,IAAIT,IAAI;EAC5B,CAAC;EAED;AACF;AACA;AACA;AACA;EACEkC,KAAK,EAAE,SAAAA,CAAA,EAAW;IAChB,OAAOzB,SAAS,CAAC,CAAC,IAAIP,MAAM;EAC9B,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEiC,MAAM,EAAE,SAAAA,CAAA,EAAW;IACjB,OAAO1B,SAAS,CAAC,CAAC,IAAIJ,OAAO;EAC/B,CAAC;EAED+B,MAAM,EAAE,SAAAA,CAAA,EAAW;IACjB,OAAO3B,SAAS,CAAC,CAAC,IAAKJ,OAAO,IAAIC,KAAK,IAAIH,QAAQ,IAAIK,OAAQ;EACjE,CAAC;EAED6B,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB;IACA,OAAO5B,SAAS,CAAC,CAAC,IAAIF,OAAO;EAC/B,CAAC;EAED+B,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,OAAO7B,SAAS,CAAC,CAAC,IAAIN,QAAQ;EAChC,CAAC;EAEDoC,IAAI,EAAE,SAAAA,CAAA,EAAW;IACf,OAAO9B,SAAS,CAAC,CAAC,IAAIH,KAAK;EAC7B;AACF,CAAC;AAEDkC,MAAM,CAACC,OAAO,GAAGlB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}