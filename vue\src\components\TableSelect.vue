<template>
  <div class="table-select-container">
    <!-- 区域筛选 -->
    <div class="area-filter">
      <el-radio-group v-model="selectedArea" @change="filterByArea">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button label="大厅">大厅</el-radio-button>
        <el-radio-button label="包间">包间</el-radio-button>
        <el-radio-button label="靠窗">靠窗</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 状态说明 -->
    <div class="status-legend">
      <div class="legend-item">
        <div class="legend-color available"></div>
        <span>空闲可选</span>
      </div>
      <div class="legend-item">
        <div class="legend-color occupied"></div>
        <span>占用中</span>
      </div>
      <div class="legend-item">
        <div class="legend-color maintenance"></div>
        <span>维修中</span>
      </div>
      <div class="legend-item">
        <div class="legend-color cleaning"></div>
        <span>清洁中</span>
      </div>
      <div class="legend-item">
        <div class="legend-color selected"></div>
        <span>已选择</span>
      </div>
    </div>

    <!-- 餐桌网格 -->
    <div class="table-grid" v-loading="loading" element-loading-text="加载中...">
      <div 
        v-for="table in filteredTables" 
        :key="table.id"
        :class="getTableClass(table)"
        @click="selectTable(table)"
        @mouseenter="showTooltip($event, table)"
        @mouseleave="hideTooltip">
        
        <div class="table-number">{{ table.tableNumber }}</div>
        <div class="table-seats">{{ table.seats }}人</div>
        <div class="table-area">{{ table.area }}</div>
        
        <!-- 状态图标 -->
        <div class="table-status-icon">
          <i :class="getStatusIcon(table)"></i>
        </div>
        
        <!-- 选中标记 -->
        <div v-if="isSelected(table)" class="selected-mark">
          <i class="el-icon-check"></i>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredTables.length === 0 && !loading" class="empty-state">
      <i class="el-icon-coffee-cup"></i>
      <p>暂无可用餐桌</p>
    </div>

    <!-- 悬浮提示 -->
    <div 
      ref="tooltip" 
      class="table-tooltip" 
      v-show="tooltipVisible"
      :style="tooltipStyle">
      <div class="tooltip-content">
        <div class="tooltip-title">{{ tooltipData.tableNumber }}号桌</div>
        <div class="tooltip-info">
          <p><i class="el-icon-user"></i> 座位：{{ tooltipData.seats }}人</p>
          <p><i class="el-icon-location"></i> 区域：{{ tooltipData.area }}</p>
          <p><i class="el-icon-info"></i> 状态：{{ getTableStatusText(tooltipData) }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'TableSelect',
  props: {
    value: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      tables: [], // 所有餐桌
      filteredTables: [], // 过滤后的餐桌
      selectedArea: '', // 选中的区域
      loading: false,
      // 悬浮提示相关
      tooltipVisible: false,
      tooltipData: {},
      tooltipStyle: {
        left: '0px',
        top: '0px'
      },
      // 自动刷新相关
      refreshTimer: null,
      refreshInterval: 30000 // 30秒刷新一次
    }
  },
  mounted() {
    this.loadTables()
    // 启动定时刷新餐桌状态
    this.startAutoRefresh()
  },
  beforeDestroy() {
    // 清理定时器
    this.stopAutoRefresh()
  },
  methods: {
    // 加载餐桌数据
    async loadTables() {
      this.loading = true
      try {
        // 获取餐桌详细状态（包含占用信息）
        const res = await request.get('/table/statusDetail')
        this.tables = res.data || []
        this.filterByArea()
      } catch (error) {
        this.$message.error('加载餐桌信息失败')
      } finally {
        this.loading = false
      }
    },

    // 按区域筛选
    filterByArea() {
      if (this.selectedArea) {
        this.filteredTables = this.tables.filter(table => table.area === this.selectedArea)
      } else {
        this.filteredTables = [...this.tables]
      }
    },

    // 选择餐桌
    selectTable(table) {
      if (!this.isTableSelectable(table)) {
        this.$message.warning(`${table.tableNumber}号桌当前不可选择`)
        return
      }

      this.$emit('input', table)
      this.$emit('table-selected', table)
    },

    // 判断餐桌是否可选择
    isTableSelectable(table) {
      // 只有空闲且无未完成订单的餐桌才可选择
      return table.tableStatus === '空闲' && table.occupyStatus === '空闲'
    },

    // 判断是否已选择
    isSelected(table) {
      return this.value && this.value.id === table.id
    },

    // 获取餐桌样式类
    getTableClass(table) {
      const classes = ['table-item']
      
      if (this.isSelected(table)) {
        classes.push('selected')
      } else if (this.isTableSelectable(table)) {
        classes.push('available')
      } else if (table.occupyStatus === '占用中') {
        classes.push('occupied')
      } else if (table.tableStatus === '维修中') {
        classes.push('maintenance')
      } else if (table.tableStatus === '清洁中') {
        classes.push('cleaning')
      } else {
        classes.push('unavailable')
      }
      
      return classes
    },

    // 获取状态图标
    getStatusIcon(table) {
      if (table.occupyStatus === '占用中') {
        return 'el-icon-user-solid'
      } else if (table.tableStatus === '维修中') {
        return 'el-icon-warning'
      } else if (table.tableStatus === '清洁中') {
        return 'el-icon-brush'
      } else {
        return 'el-icon-coffee-cup'
      }
    },

    // 获取餐桌状态文本
    getTableStatusText(table) {
      if (table.occupyStatus === '占用中') {
        return '占用中'
      } else {
        return table.tableStatus || '空闲'
      }
    },

    // 显示悬浮提示
    showTooltip(event, table) {
      this.tooltipData = table
      this.tooltipVisible = true
      
      // 设置提示位置
      this.$nextTick(() => {
        const rect = event.target.getBoundingClientRect()
        const tooltipRect = this.$refs.tooltip.getBoundingClientRect()
        
        let left = rect.left + rect.width / 2 - tooltipRect.width / 2
        let top = rect.top - tooltipRect.height - 10
        
        // 边界检测
        if (left < 10) left = 10
        if (left + tooltipRect.width > window.innerWidth - 10) {
          left = window.innerWidth - tooltipRect.width - 10
        }
        if (top < 10) {
          top = rect.bottom + 10
        }
        
        this.tooltipStyle = {
          left: left + 'px',
          top: top + 'px'
        }
      })
    },

    // 隐藏悬浮提示
    hideTooltip() {
      this.tooltipVisible = false
    },

    // 启动自动刷新
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.refreshTableStatus()
      }, this.refreshInterval)
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 刷新餐桌状态（静默更新，不显示loading）
    async refreshTableStatus() {
      try {
        const res = await request.get('/table/statusDetail')
        const newTables = res.data || []
        
        // 检查是否有状态变化
        const hasChanges = this.checkTableStatusChanges(this.tables, newTables)
        
        if (hasChanges) {
          this.tables = newTables
          this.filterByArea()
          
          // 如果当前选择的餐桌状态发生变化，给出提示
          if (this.value && this.value.id) {
            const currentSelectedTable = newTables.find(t => t.id === this.value.id)
            if (currentSelectedTable && !this.isTableSelectable(currentSelectedTable)) {
              this.$message.warning(`${currentSelectedTable.tableNumber}号桌状态已变更，请重新选择餐桌`)
              this.$emit('input', null)
              this.$emit('table-selected', null)
            }
          }
        }
      } catch (error) {
        // 静默处理错误，避免频繁提示
        console.error('刷新餐桌状态失败:', error)
      }
    },

    // 检查餐桌状态是否有变化
    checkTableStatusChanges(oldTables, newTables) {
      if (oldTables.length !== newTables.length) return true
      
      for (let i = 0; i < oldTables.length; i++) {
        const oldTable = oldTables[i]
        const newTable = newTables.find(t => t.id === oldTable.id)
        
        if (!newTable || 
            oldTable.tableStatus !== newTable.tableStatus || 
            oldTable.occupyStatus !== newTable.occupyStatus) {
          return true
        }
      }
      
      return false
    }
  }
}
</script>

<style scoped>
.table-select-container {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

/* 区域筛选 */
.area-filter {
  margin-bottom: 20px;
  text-align: center;
}

/* 状态说明 */
.status-legend {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #ddd;
}

.legend-color.available {
  background: #67c23a;
  border-color: #67c23a;
}

.legend-color.occupied {
  background: #f56c6c;
  border-color: #f56c6c;
}

.legend-color.maintenance {
  background: #909399;
  border-color: #909399;
}

.legend-color.cleaning {
  background: #e6a23c;
  border-color: #e6a23c;
}

.legend-color.selected {
  background: #409eff;
  border-color: #409eff;
}

/* 餐桌网格 */
.table-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  min-height: 200px;
}

/* 餐桌项 */
.table-item {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #ddd;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 可选择的餐桌 */
.table-item.available {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
}

.table-item.available:hover {
  border-color: #5daf34;
  background: linear-gradient(135deg, #e6f7ff, #d1f2ff);
}

/* 已选择的餐桌 */
.table-item.selected {
  border-color: #409eff;
  background: linear-gradient(135deg, #409eff, #66b3ff);
  color: white;
}

/* 占用中的餐桌 */
.table-item.occupied {
  border-color: #f56c6c;
  background: linear-gradient(135deg, #fef0f0, #fde2e2);
  cursor: not-allowed;
  opacity: 0.7;
}

/* 维修中的餐桌 */
.table-item.maintenance {
  border-color: #909399;
  background: linear-gradient(135deg, #f4f4f5, #e9e9eb);
  cursor: not-allowed;
  opacity: 0.7;
}

/* 清洁中的餐桌 */
.table-item.cleaning {
  border-color: #e6a23c;
  background: linear-gradient(135deg, #fdf6ec, #faecd8);
  cursor: not-allowed;
  opacity: 0.7;
}

/* 餐桌信息 */
.table-number {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.table-seats {
  font-size: 12px;
  margin-bottom: 2px;
}

.table-area {
  font-size: 10px;
  opacity: 0.8;
}

/* 状态图标 */
.table-status-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 14px;
  opacity: 0.6;
}

/* 选中标记 */
.selected-mark {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 24px;
  height: 24px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

/* 悬浮提示 */
.table-tooltip {
  position: fixed;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  pointer-events: none;
  max-width: 200px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.tooltip-title {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 16px;
}

.tooltip-info p {
  margin: 4px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tooltip-info i {
  width: 14px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
  }
  
  .table-item {
    width: 100px;
    height: 100px;
  }
  
  .table-number {
    font-size: 16px;
  }
  
  .status-legend {
    gap: 15px;
  }
  
  .legend-item {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .status-legend {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  
  .area-filter .el-radio-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style> 