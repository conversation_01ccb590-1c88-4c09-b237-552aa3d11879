{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as pathTool from 'zrender/lib/tool/path.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as vector from 'zrender/lib/core/vector.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport Transformable from 'zrender/lib/core/Transformable.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport Group from 'zrender/lib/graphic/Group.js';\nimport ZRText from 'zrender/lib/graphic/Text.js';\nimport Circle from 'zrender/lib/graphic/shape/Circle.js';\nimport Ellipse from 'zrender/lib/graphic/shape/Ellipse.js';\nimport Sector from 'zrender/lib/graphic/shape/Sector.js';\nimport Ring from 'zrender/lib/graphic/shape/Ring.js';\nimport Polygon from 'zrender/lib/graphic/shape/Polygon.js';\nimport Polyline from 'zrender/lib/graphic/shape/Polyline.js';\nimport Rect from 'zrender/lib/graphic/shape/Rect.js';\nimport Line from 'zrender/lib/graphic/shape/Line.js';\nimport BezierCurve from 'zrender/lib/graphic/shape/BezierCurve.js';\nimport Arc from 'zrender/lib/graphic/shape/Arc.js';\nimport CompoundPath from 'zrender/lib/graphic/CompoundPath.js';\nimport LinearGradient from 'zrender/lib/graphic/LinearGradient.js';\nimport RadialGradient from 'zrender/lib/graphic/RadialGradient.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport OrientedBoundingRect from 'zrender/lib/core/OrientedBoundingRect.js';\nimport Point from 'zrender/lib/core/Point.js';\nimport IncrementalDisplayable from 'zrender/lib/graphic/IncrementalDisplayable.js';\nimport * as subPixelOptimizeUtil from 'zrender/lib/graphic/helper/subPixelOptimize.js';\nimport { extend, isArrayLike, map, defaults, isString, keys, each, hasOwn, isArray } from 'zrender/lib/core/util.js';\nimport { getECData } from './innerStore.js';\nimport { updateProps, initProps, removeElement, removeElementWithFadeOut, isElementRemoved } from '../animation/basicTransition.js';\n/**\r\n * @deprecated export for compatitable reason\r\n */\nexport { updateProps, initProps, removeElement, removeElementWithFadeOut, isElementRemoved };\nvar mathMax = Math.max;\nvar mathMin = Math.min;\nvar _customShapeMap = {};\n/**\r\n * Extend shape with parameters\r\n */\nexport function extendShape(opts) {\n  return Path.extend(opts);\n}\nvar extendPathFromString = pathTool.extendFromString;\n/**\r\n * Extend path\r\n */\nexport function extendPath(pathData, opts) {\n  return extendPathFromString(pathData, opts);\n}\n/**\r\n * Register a user defined shape.\r\n * The shape class can be fetched by `getShapeClass`\r\n * This method will overwrite the registered shapes, including\r\n * the registered built-in shapes, if using the same `name`.\r\n * The shape can be used in `custom series` and\r\n * `graphic component` by declaring `{type: name}`.\r\n *\r\n * @param name\r\n * @param ShapeClass Can be generated by `extendShape`.\r\n */\nexport function registerShape(name, ShapeClass) {\n  _customShapeMap[name] = ShapeClass;\n}\n/**\r\n * Find shape class registered by `registerShape`. Usually used in\r\n * fetching user defined shape.\r\n *\r\n * [Caution]:\r\n * (1) This method **MUST NOT be used inside echarts !!!**, unless it is prepared\r\n * to use user registered shapes.\r\n * Because the built-in shape (see `getBuiltInShape`) will be registered by\r\n * `registerShape` by default. That enables users to get both built-in\r\n * shapes as well as the shapes belonging to themsleves. But users can overwrite\r\n * the built-in shapes by using names like 'circle', 'rect' via calling\r\n * `registerShape`. So the echarts inner featrues should not fetch shapes from here\r\n * in case that it is overwritten by users, except that some features, like\r\n * `custom series`, `graphic component`, do it deliberately.\r\n *\r\n * (2) In the features like `custom series`, `graphic component`, the user input\r\n * `{tpye: 'xxx'}` does not only specify shapes but also specify other graphic\r\n * elements like `'group'`, `'text'`, `'image'` or event `'path'`. Those names\r\n * are reserved names, that is, if some user registers a shape named `'image'`,\r\n * the shape will not be used. If we intending to add some more reserved names\r\n * in feature, that might bring break changes (disable some existing user shape\r\n * names). But that case probably rarely happens. So we don't make more mechanism\r\n * to resolve this issue here.\r\n *\r\n * @param name\r\n * @return The shape class. If not found, return nothing.\r\n */\nexport function getShapeClass(name) {\n  if (_customShapeMap.hasOwnProperty(name)) {\n    return _customShapeMap[name];\n  }\n}\n/**\r\n * Create a path element from path data string\r\n * @param pathData\r\n * @param opts\r\n * @param rect\r\n * @param layout 'center' or 'cover' default to be cover\r\n */\nexport function makePath(pathData, opts, rect, layout) {\n  var path = pathTool.createFromString(pathData, opts);\n  if (rect) {\n    if (layout === 'center') {\n      rect = centerGraphic(rect, path.getBoundingRect());\n    }\n    resizePath(path, rect);\n  }\n  return path;\n}\n/**\r\n * Create a image element from image url\r\n * @param imageUrl image url\r\n * @param opts options\r\n * @param rect constrain rect\r\n * @param layout 'center' or 'cover'. Default to be 'cover'\r\n */\nexport function makeImage(imageUrl, rect, layout) {\n  var zrImg = new ZRImage({\n    style: {\n      image: imageUrl,\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    },\n    onload: function (img) {\n      if (layout === 'center') {\n        var boundingRect = {\n          width: img.width,\n          height: img.height\n        };\n        zrImg.setStyle(centerGraphic(rect, boundingRect));\n      }\n    }\n  });\n  return zrImg;\n}\n/**\r\n * Get position of centered element in bounding box.\r\n *\r\n * @param  rect         element local bounding box\r\n * @param  boundingRect constraint bounding box\r\n * @return element position containing x, y, width, and height\r\n */\nfunction centerGraphic(rect, boundingRect) {\n  // Set rect to center, keep width / height ratio.\n  var aspect = boundingRect.width / boundingRect.height;\n  var width = rect.height * aspect;\n  var height;\n  if (width <= rect.width) {\n    height = rect.height;\n  } else {\n    width = rect.width;\n    height = width / aspect;\n  }\n  var cx = rect.x + rect.width / 2;\n  var cy = rect.y + rect.height / 2;\n  return {\n    x: cx - width / 2,\n    y: cy - height / 2,\n    width: width,\n    height: height\n  };\n}\nexport var mergePath = pathTool.mergePath;\n/**\r\n * Resize a path to fit the rect\r\n * @param path\r\n * @param rect\r\n */\nexport function resizePath(path, rect) {\n  if (!path.applyTransform) {\n    return;\n  }\n  var pathRect = path.getBoundingRect();\n  var m = pathRect.calculateTransform(rect);\n  path.applyTransform(m);\n}\n/**\r\n * Sub pixel optimize line for canvas\r\n */\nexport function subPixelOptimizeLine(shape, lineWidth) {\n  subPixelOptimizeUtil.subPixelOptimizeLine(shape, shape, {\n    lineWidth: lineWidth\n  });\n  return shape;\n}\n/**\r\n * Sub pixel optimize rect for canvas\r\n */\nexport function subPixelOptimizeRect(param) {\n  subPixelOptimizeUtil.subPixelOptimizeRect(param.shape, param.shape, param.style);\n  return param;\n}\n/**\r\n * Sub pixel optimize for canvas\r\n *\r\n * @param position Coordinate, such as x, y\r\n * @param lineWidth Should be nonnegative integer.\r\n * @param positiveOrNegative Default false (negative).\r\n * @return Optimized position.\r\n */\nexport var subPixelOptimize = subPixelOptimizeUtil.subPixelOptimize;\n/**\r\n * Get transform matrix of target (param target),\r\n * in coordinate of its ancestor (param ancestor)\r\n *\r\n * @param target\r\n * @param [ancestor]\r\n */\nexport function getTransform(target, ancestor) {\n  var mat = matrix.identity([]);\n  while (target && target !== ancestor) {\n    matrix.mul(mat, target.getLocalTransform(), mat);\n    target = target.parent;\n  }\n  return mat;\n}\n/**\r\n * Apply transform to an vertex.\r\n * @param target [x, y]\r\n * @param transform Can be:\r\n *      + Transform matrix: like [1, 0, 0, 1, 0, 0]\r\n *      + {position, rotation, scale}, the same as `zrender/Transformable`.\r\n * @param invert Whether use invert matrix.\r\n * @return [x, y]\r\n */\nexport function applyTransform(target, transform, invert) {\n  if (transform && !isArrayLike(transform)) {\n    transform = Transformable.getLocalTransform(transform);\n  }\n  if (invert) {\n    transform = matrix.invert([], transform);\n  }\n  return vector.applyTransform([], target, transform);\n}\n/**\r\n * @param direction 'left' 'right' 'top' 'bottom'\r\n * @param transform Transform matrix: like [1, 0, 0, 1, 0, 0]\r\n * @param invert Whether use invert matrix.\r\n * @return Transformed direction. 'left' 'right' 'top' 'bottom'\r\n */\nexport function transformDirection(direction, transform, invert) {\n  // Pick a base, ensure that transform result will not be (0, 0).\n  var hBase = transform[4] === 0 || transform[5] === 0 || transform[0] === 0 ? 1 : Math.abs(2 * transform[4] / transform[0]);\n  var vBase = transform[4] === 0 || transform[5] === 0 || transform[2] === 0 ? 1 : Math.abs(2 * transform[4] / transform[2]);\n  var vertex = [direction === 'left' ? -hBase : direction === 'right' ? hBase : 0, direction === 'top' ? -vBase : direction === 'bottom' ? vBase : 0];\n  vertex = applyTransform(vertex, transform, invert);\n  return Math.abs(vertex[0]) > Math.abs(vertex[1]) ? vertex[0] > 0 ? 'right' : 'left' : vertex[1] > 0 ? 'bottom' : 'top';\n}\nfunction isNotGroup(el) {\n  return !el.isGroup;\n}\nfunction isPath(el) {\n  return el.shape != null;\n}\n/**\r\n * Apply group transition animation from g1 to g2.\r\n * If no animatableModel, no animation.\r\n */\nexport function groupTransition(g1, g2, animatableModel) {\n  if (!g1 || !g2) {\n    return;\n  }\n  function getElMap(g) {\n    var elMap = {};\n    g.traverse(function (el) {\n      if (isNotGroup(el) && el.anid) {\n        elMap[el.anid] = el;\n      }\n    });\n    return elMap;\n  }\n  function getAnimatableProps(el) {\n    var obj = {\n      x: el.x,\n      y: el.y,\n      rotation: el.rotation\n    };\n    if (isPath(el)) {\n      obj.shape = extend({}, el.shape);\n    }\n    return obj;\n  }\n  var elMap1 = getElMap(g1);\n  g2.traverse(function (el) {\n    if (isNotGroup(el) && el.anid) {\n      var oldEl = elMap1[el.anid];\n      if (oldEl) {\n        var newProp = getAnimatableProps(el);\n        el.attr(getAnimatableProps(oldEl));\n        updateProps(el, newProp, animatableModel, getECData(el).dataIndex);\n      }\n    }\n  });\n}\nexport function clipPointsByRect(points, rect) {\n  // FIXME: This way might be incorrect when graphic clipped by a corner\n  // and when element has a border.\n  return map(points, function (point) {\n    var x = point[0];\n    x = mathMax(x, rect.x);\n    x = mathMin(x, rect.x + rect.width);\n    var y = point[1];\n    y = mathMax(y, rect.y);\n    y = mathMin(y, rect.y + rect.height);\n    return [x, y];\n  });\n}\n/**\r\n * Return a new clipped rect. If rect size are negative, return undefined.\r\n */\nexport function clipRectByRect(targetRect, rect) {\n  var x = mathMax(targetRect.x, rect.x);\n  var x2 = mathMin(targetRect.x + targetRect.width, rect.x + rect.width);\n  var y = mathMax(targetRect.y, rect.y);\n  var y2 = mathMin(targetRect.y + targetRect.height, rect.y + rect.height);\n  // If the total rect is cliped, nothing, including the border,\n  // should be painted. So return undefined.\n  if (x2 >= x && y2 >= y) {\n    return {\n      x: x,\n      y: y,\n      width: x2 - x,\n      height: y2 - y\n    };\n  }\n}\nexport function createIcon(iconStr,\n// Support 'image://' or 'path://' or direct svg path.\nopt, rect) {\n  var innerOpts = extend({\n    rectHover: true\n  }, opt);\n  var style = innerOpts.style = {\n    strokeNoScale: true\n  };\n  rect = rect || {\n    x: -1,\n    y: -1,\n    width: 2,\n    height: 2\n  };\n  if (iconStr) {\n    return iconStr.indexOf('image://') === 0 ? (style.image = iconStr.slice(8), defaults(style, rect), new ZRImage(innerOpts)) : makePath(iconStr.replace('path://', ''), innerOpts, rect, 'center');\n  }\n}\n/**\r\n * Return `true` if the given line (line `a`) and the given polygon\r\n * are intersect.\r\n * Note that we do not count colinear as intersect here because no\r\n * requirement for that. We could do that if required in future.\r\n */\nexport function linePolygonIntersect(a1x, a1y, a2x, a2y, points) {\n  for (var i = 0, p2 = points[points.length - 1]; i < points.length; i++) {\n    var p = points[i];\n    if (lineLineIntersect(a1x, a1y, a2x, a2y, p[0], p[1], p2[0], p2[1])) {\n      return true;\n    }\n    p2 = p;\n  }\n}\n/**\r\n * Return `true` if the given two lines (line `a` and line `b`)\r\n * are intersect.\r\n * Note that we do not count colinear as intersect here because no\r\n * requirement for that. We could do that if required in future.\r\n */\nexport function lineLineIntersect(a1x, a1y, a2x, a2y, b1x, b1y, b2x, b2y) {\n  // let `vec_m` to be `vec_a2 - vec_a1` and `vec_n` to be `vec_b2 - vec_b1`.\n  var mx = a2x - a1x;\n  var my = a2y - a1y;\n  var nx = b2x - b1x;\n  var ny = b2y - b1y;\n  // `vec_m` and `vec_n` are parallel iff\n  //     existing `k` such that `vec_m = k · vec_n`, equivalent to `vec_m X vec_n = 0`.\n  var nmCrossProduct = crossProduct2d(nx, ny, mx, my);\n  if (nearZero(nmCrossProduct)) {\n    return false;\n  }\n  // `vec_m` and `vec_n` are intersect iff\n  //     existing `p` and `q` in [0, 1] such that `vec_a1 + p * vec_m = vec_b1 + q * vec_n`,\n  //     such that `q = ((vec_a1 - vec_b1) X vec_m) / (vec_n X vec_m)`\n  //           and `p = ((vec_a1 - vec_b1) X vec_n) / (vec_n X vec_m)`.\n  var b1a1x = a1x - b1x;\n  var b1a1y = a1y - b1y;\n  var q = crossProduct2d(b1a1x, b1a1y, mx, my) / nmCrossProduct;\n  if (q < 0 || q > 1) {\n    return false;\n  }\n  var p = crossProduct2d(b1a1x, b1a1y, nx, ny) / nmCrossProduct;\n  if (p < 0 || p > 1) {\n    return false;\n  }\n  return true;\n}\n/**\r\n * Cross product of 2-dimension vector.\r\n */\nfunction crossProduct2d(x1, y1, x2, y2) {\n  return x1 * y2 - x2 * y1;\n}\nfunction nearZero(val) {\n  return val <= 1e-6 && val >= -1e-6;\n}\nexport function setTooltipConfig(opt) {\n  var itemTooltipOption = opt.itemTooltipOption;\n  var componentModel = opt.componentModel;\n  var itemName = opt.itemName;\n  var itemTooltipOptionObj = isString(itemTooltipOption) ? {\n    formatter: itemTooltipOption\n  } : itemTooltipOption;\n  var mainType = componentModel.mainType;\n  var componentIndex = componentModel.componentIndex;\n  var formatterParams = {\n    componentType: mainType,\n    name: itemName,\n    $vars: ['name']\n  };\n  formatterParams[mainType + 'Index'] = componentIndex;\n  var formatterParamsExtra = opt.formatterParamsExtra;\n  if (formatterParamsExtra) {\n    each(keys(formatterParamsExtra), function (key) {\n      if (!hasOwn(formatterParams, key)) {\n        formatterParams[key] = formatterParamsExtra[key];\n        formatterParams.$vars.push(key);\n      }\n    });\n  }\n  var ecData = getECData(opt.el);\n  ecData.componentMainType = mainType;\n  ecData.componentIndex = componentIndex;\n  ecData.tooltipConfig = {\n    name: itemName,\n    option: defaults({\n      content: itemName,\n      encodeHTMLContent: true,\n      formatterParams: formatterParams\n    }, itemTooltipOptionObj)\n  };\n}\nfunction traverseElement(el, cb) {\n  var stopped;\n  // TODO\n  // Polyfill for fixing zrender group traverse don't visit it's root issue.\n  if (el.isGroup) {\n    stopped = cb(el);\n  }\n  if (!stopped) {\n    el.traverse(cb);\n  }\n}\nexport function traverseElements(els, cb) {\n  if (els) {\n    if (isArray(els)) {\n      for (var i = 0; i < els.length; i++) {\n        traverseElement(els[i], cb);\n      }\n    } else {\n      traverseElement(els, cb);\n    }\n  }\n}\n// Register built-in shapes. These shapes might be overwritten\n// by users, although we do not recommend that.\nregisterShape('circle', Circle);\nregisterShape('ellipse', Ellipse);\nregisterShape('sector', Sector);\nregisterShape('ring', Ring);\nregisterShape('polygon', Polygon);\nregisterShape('polyline', Polyline);\nregisterShape('rect', Rect);\nregisterShape('line', Line);\nregisterShape('bezierCurve', BezierCurve);\nregisterShape('arc', Arc);\nexport { Group, ZRImage as Image, ZRText as Text, Circle, Ellipse, Sector, Ring, Polygon, Polyline, Rect, Line, BezierCurve, Arc, IncrementalDisplayable, CompoundPath, LinearGradient, RadialGradient, BoundingRect, OrientedBoundingRect, Point, Path };", "map": {"version": 3, "names": ["pathTool", "matrix", "vector", "Path", "Transformable", "ZRImage", "Group", "ZRText", "Circle", "Ellipse", "Sector", "Ring", "Polygon", "Polyline", "Rect", "Line", "BezierCurve", "Arc", "CompoundPath", "LinearGradient", "RadialGrad<PERSON>", "BoundingRect", "OrientedBoundingRect", "Point", "IncrementalDisplayable", "subPixelOptimizeUtil", "extend", "isArrayLike", "map", "defaults", "isString", "keys", "each", "hasOwn", "isArray", "getECData", "updateProps", "initProps", "removeElement", "removeElementWithFadeOut", "isElementRemoved", "mathMax", "Math", "max", "mathMin", "min", "_customShapeMap", "extendShape", "opts", "extendPathFromString", "extendFromString", "extendPath", "pathData", "registerShape", "name", "ShapeClass", "getShapeClass", "hasOwnProperty", "<PERSON><PERSON><PERSON>", "rect", "layout", "path", "createFromString", "centerGraphic", "getBoundingRect", "resizePath", "makeImage", "imageUrl", "zrImg", "style", "image", "x", "y", "width", "height", "onload", "img", "boundingRect", "setStyle", "aspect", "cx", "cy", "mergePath", "applyTransform", "pathRect", "m", "calculateTransform", "subPixelOptimizeLine", "shape", "lineWidth", "subPixelOptimizeRect", "param", "subPixelOptimize", "getTransform", "target", "ancestor", "mat", "identity", "mul", "getLocalTransform", "parent", "transform", "invert", "transformDirection", "direction", "hBase", "abs", "vBase", "vertex", "isNotGroup", "el", "isGroup", "isPath", "groupTransition", "g1", "g2", "animatableModel", "getElMap", "g", "elMap", "traverse", "anid", "getAnimatableProps", "obj", "rotation", "elMap1", "oldEl", "newProp", "attr", "dataIndex", "clipPointsByRect", "points", "point", "clipRectByRect", "targetRect", "x2", "y2", "createIcon", "iconStr", "opt", "innerOpts", "rectHover", "strokeNoScale", "indexOf", "slice", "replace", "linePolygonIntersect", "a1x", "a1y", "a2x", "a2y", "i", "p2", "length", "p", "lineLineIntersect", "b1x", "b1y", "b2x", "b2y", "mx", "my", "nx", "ny", "nmCrossProduct", "crossProduct2d", "nearZero", "b1a1x", "b1a1y", "q", "x1", "y1", "val", "setTooltipConfig", "itemTooltipOption", "componentModel", "itemName", "itemTooltipOptionObj", "formatter", "mainType", "componentIndex", "formatterParams", "componentType", "$vars", "formatterParamsExtra", "key", "push", "ecData", "componentMainType", "tooltipConfig", "option", "content", "encodeHTMLContent", "traverseElement", "cb", "stopped", "traverseElements", "els", "Image", "Text"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/echarts/lib/util/graphic.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as pathTool from 'zrender/lib/tool/path.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as vector from 'zrender/lib/core/vector.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport Transformable from 'zrender/lib/core/Transformable.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport Group from 'zrender/lib/graphic/Group.js';\nimport ZRText from 'zrender/lib/graphic/Text.js';\nimport Circle from 'zrender/lib/graphic/shape/Circle.js';\nimport Ellipse from 'zrender/lib/graphic/shape/Ellipse.js';\nimport Sector from 'zrender/lib/graphic/shape/Sector.js';\nimport Ring from 'zrender/lib/graphic/shape/Ring.js';\nimport Polygon from 'zrender/lib/graphic/shape/Polygon.js';\nimport Polyline from 'zrender/lib/graphic/shape/Polyline.js';\nimport Rect from 'zrender/lib/graphic/shape/Rect.js';\nimport Line from 'zrender/lib/graphic/shape/Line.js';\nimport BezierCurve from 'zrender/lib/graphic/shape/BezierCurve.js';\nimport Arc from 'zrender/lib/graphic/shape/Arc.js';\nimport CompoundPath from 'zrender/lib/graphic/CompoundPath.js';\nimport LinearGradient from 'zrender/lib/graphic/LinearGradient.js';\nimport RadialGradient from 'zrender/lib/graphic/RadialGradient.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport OrientedBoundingRect from 'zrender/lib/core/OrientedBoundingRect.js';\nimport Point from 'zrender/lib/core/Point.js';\nimport IncrementalDisplayable from 'zrender/lib/graphic/IncrementalDisplayable.js';\nimport * as subPixelOptimizeUtil from 'zrender/lib/graphic/helper/subPixelOptimize.js';\nimport { extend, isArrayLike, map, defaults, isString, keys, each, hasOwn, isArray } from 'zrender/lib/core/util.js';\nimport { getECData } from './innerStore.js';\nimport { updateProps, initProps, removeElement, removeElementWithFadeOut, isElementRemoved } from '../animation/basicTransition.js';\n/**\r\n * @deprecated export for compatitable reason\r\n */\nexport { updateProps, initProps, removeElement, removeElementWithFadeOut, isElementRemoved };\nvar mathMax = Math.max;\nvar mathMin = Math.min;\nvar _customShapeMap = {};\n/**\r\n * Extend shape with parameters\r\n */\nexport function extendShape(opts) {\n  return Path.extend(opts);\n}\nvar extendPathFromString = pathTool.extendFromString;\n/**\r\n * Extend path\r\n */\nexport function extendPath(pathData, opts) {\n  return extendPathFromString(pathData, opts);\n}\n/**\r\n * Register a user defined shape.\r\n * The shape class can be fetched by `getShapeClass`\r\n * This method will overwrite the registered shapes, including\r\n * the registered built-in shapes, if using the same `name`.\r\n * The shape can be used in `custom series` and\r\n * `graphic component` by declaring `{type: name}`.\r\n *\r\n * @param name\r\n * @param ShapeClass Can be generated by `extendShape`.\r\n */\nexport function registerShape(name, ShapeClass) {\n  _customShapeMap[name] = ShapeClass;\n}\n/**\r\n * Find shape class registered by `registerShape`. Usually used in\r\n * fetching user defined shape.\r\n *\r\n * [Caution]:\r\n * (1) This method **MUST NOT be used inside echarts !!!**, unless it is prepared\r\n * to use user registered shapes.\r\n * Because the built-in shape (see `getBuiltInShape`) will be registered by\r\n * `registerShape` by default. That enables users to get both built-in\r\n * shapes as well as the shapes belonging to themsleves. But users can overwrite\r\n * the built-in shapes by using names like 'circle', 'rect' via calling\r\n * `registerShape`. So the echarts inner featrues should not fetch shapes from here\r\n * in case that it is overwritten by users, except that some features, like\r\n * `custom series`, `graphic component`, do it deliberately.\r\n *\r\n * (2) In the features like `custom series`, `graphic component`, the user input\r\n * `{tpye: 'xxx'}` does not only specify shapes but also specify other graphic\r\n * elements like `'group'`, `'text'`, `'image'` or event `'path'`. Those names\r\n * are reserved names, that is, if some user registers a shape named `'image'`,\r\n * the shape will not be used. If we intending to add some more reserved names\r\n * in feature, that might bring break changes (disable some existing user shape\r\n * names). But that case probably rarely happens. So we don't make more mechanism\r\n * to resolve this issue here.\r\n *\r\n * @param name\r\n * @return The shape class. If not found, return nothing.\r\n */\nexport function getShapeClass(name) {\n  if (_customShapeMap.hasOwnProperty(name)) {\n    return _customShapeMap[name];\n  }\n}\n/**\r\n * Create a path element from path data string\r\n * @param pathData\r\n * @param opts\r\n * @param rect\r\n * @param layout 'center' or 'cover' default to be cover\r\n */\nexport function makePath(pathData, opts, rect, layout) {\n  var path = pathTool.createFromString(pathData, opts);\n  if (rect) {\n    if (layout === 'center') {\n      rect = centerGraphic(rect, path.getBoundingRect());\n    }\n    resizePath(path, rect);\n  }\n  return path;\n}\n/**\r\n * Create a image element from image url\r\n * @param imageUrl image url\r\n * @param opts options\r\n * @param rect constrain rect\r\n * @param layout 'center' or 'cover'. Default to be 'cover'\r\n */\nexport function makeImage(imageUrl, rect, layout) {\n  var zrImg = new ZRImage({\n    style: {\n      image: imageUrl,\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    },\n    onload: function (img) {\n      if (layout === 'center') {\n        var boundingRect = {\n          width: img.width,\n          height: img.height\n        };\n        zrImg.setStyle(centerGraphic(rect, boundingRect));\n      }\n    }\n  });\n  return zrImg;\n}\n/**\r\n * Get position of centered element in bounding box.\r\n *\r\n * @param  rect         element local bounding box\r\n * @param  boundingRect constraint bounding box\r\n * @return element position containing x, y, width, and height\r\n */\nfunction centerGraphic(rect, boundingRect) {\n  // Set rect to center, keep width / height ratio.\n  var aspect = boundingRect.width / boundingRect.height;\n  var width = rect.height * aspect;\n  var height;\n  if (width <= rect.width) {\n    height = rect.height;\n  } else {\n    width = rect.width;\n    height = width / aspect;\n  }\n  var cx = rect.x + rect.width / 2;\n  var cy = rect.y + rect.height / 2;\n  return {\n    x: cx - width / 2,\n    y: cy - height / 2,\n    width: width,\n    height: height\n  };\n}\nexport var mergePath = pathTool.mergePath;\n/**\r\n * Resize a path to fit the rect\r\n * @param path\r\n * @param rect\r\n */\nexport function resizePath(path, rect) {\n  if (!path.applyTransform) {\n    return;\n  }\n  var pathRect = path.getBoundingRect();\n  var m = pathRect.calculateTransform(rect);\n  path.applyTransform(m);\n}\n/**\r\n * Sub pixel optimize line for canvas\r\n */\nexport function subPixelOptimizeLine(shape, lineWidth) {\n  subPixelOptimizeUtil.subPixelOptimizeLine(shape, shape, {\n    lineWidth: lineWidth\n  });\n  return shape;\n}\n/**\r\n * Sub pixel optimize rect for canvas\r\n */\nexport function subPixelOptimizeRect(param) {\n  subPixelOptimizeUtil.subPixelOptimizeRect(param.shape, param.shape, param.style);\n  return param;\n}\n/**\r\n * Sub pixel optimize for canvas\r\n *\r\n * @param position Coordinate, such as x, y\r\n * @param lineWidth Should be nonnegative integer.\r\n * @param positiveOrNegative Default false (negative).\r\n * @return Optimized position.\r\n */\nexport var subPixelOptimize = subPixelOptimizeUtil.subPixelOptimize;\n/**\r\n * Get transform matrix of target (param target),\r\n * in coordinate of its ancestor (param ancestor)\r\n *\r\n * @param target\r\n * @param [ancestor]\r\n */\nexport function getTransform(target, ancestor) {\n  var mat = matrix.identity([]);\n  while (target && target !== ancestor) {\n    matrix.mul(mat, target.getLocalTransform(), mat);\n    target = target.parent;\n  }\n  return mat;\n}\n/**\r\n * Apply transform to an vertex.\r\n * @param target [x, y]\r\n * @param transform Can be:\r\n *      + Transform matrix: like [1, 0, 0, 1, 0, 0]\r\n *      + {position, rotation, scale}, the same as `zrender/Transformable`.\r\n * @param invert Whether use invert matrix.\r\n * @return [x, y]\r\n */\nexport function applyTransform(target, transform, invert) {\n  if (transform && !isArrayLike(transform)) {\n    transform = Transformable.getLocalTransform(transform);\n  }\n  if (invert) {\n    transform = matrix.invert([], transform);\n  }\n  return vector.applyTransform([], target, transform);\n}\n/**\r\n * @param direction 'left' 'right' 'top' 'bottom'\r\n * @param transform Transform matrix: like [1, 0, 0, 1, 0, 0]\r\n * @param invert Whether use invert matrix.\r\n * @return Transformed direction. 'left' 'right' 'top' 'bottom'\r\n */\nexport function transformDirection(direction, transform, invert) {\n  // Pick a base, ensure that transform result will not be (0, 0).\n  var hBase = transform[4] === 0 || transform[5] === 0 || transform[0] === 0 ? 1 : Math.abs(2 * transform[4] / transform[0]);\n  var vBase = transform[4] === 0 || transform[5] === 0 || transform[2] === 0 ? 1 : Math.abs(2 * transform[4] / transform[2]);\n  var vertex = [direction === 'left' ? -hBase : direction === 'right' ? hBase : 0, direction === 'top' ? -vBase : direction === 'bottom' ? vBase : 0];\n  vertex = applyTransform(vertex, transform, invert);\n  return Math.abs(vertex[0]) > Math.abs(vertex[1]) ? vertex[0] > 0 ? 'right' : 'left' : vertex[1] > 0 ? 'bottom' : 'top';\n}\nfunction isNotGroup(el) {\n  return !el.isGroup;\n}\nfunction isPath(el) {\n  return el.shape != null;\n}\n/**\r\n * Apply group transition animation from g1 to g2.\r\n * If no animatableModel, no animation.\r\n */\nexport function groupTransition(g1, g2, animatableModel) {\n  if (!g1 || !g2) {\n    return;\n  }\n  function getElMap(g) {\n    var elMap = {};\n    g.traverse(function (el) {\n      if (isNotGroup(el) && el.anid) {\n        elMap[el.anid] = el;\n      }\n    });\n    return elMap;\n  }\n  function getAnimatableProps(el) {\n    var obj = {\n      x: el.x,\n      y: el.y,\n      rotation: el.rotation\n    };\n    if (isPath(el)) {\n      obj.shape = extend({}, el.shape);\n    }\n    return obj;\n  }\n  var elMap1 = getElMap(g1);\n  g2.traverse(function (el) {\n    if (isNotGroup(el) && el.anid) {\n      var oldEl = elMap1[el.anid];\n      if (oldEl) {\n        var newProp = getAnimatableProps(el);\n        el.attr(getAnimatableProps(oldEl));\n        updateProps(el, newProp, animatableModel, getECData(el).dataIndex);\n      }\n    }\n  });\n}\nexport function clipPointsByRect(points, rect) {\n  // FIXME: This way might be incorrect when graphic clipped by a corner\n  // and when element has a border.\n  return map(points, function (point) {\n    var x = point[0];\n    x = mathMax(x, rect.x);\n    x = mathMin(x, rect.x + rect.width);\n    var y = point[1];\n    y = mathMax(y, rect.y);\n    y = mathMin(y, rect.y + rect.height);\n    return [x, y];\n  });\n}\n/**\r\n * Return a new clipped rect. If rect size are negative, return undefined.\r\n */\nexport function clipRectByRect(targetRect, rect) {\n  var x = mathMax(targetRect.x, rect.x);\n  var x2 = mathMin(targetRect.x + targetRect.width, rect.x + rect.width);\n  var y = mathMax(targetRect.y, rect.y);\n  var y2 = mathMin(targetRect.y + targetRect.height, rect.y + rect.height);\n  // If the total rect is cliped, nothing, including the border,\n  // should be painted. So return undefined.\n  if (x2 >= x && y2 >= y) {\n    return {\n      x: x,\n      y: y,\n      width: x2 - x,\n      height: y2 - y\n    };\n  }\n}\nexport function createIcon(iconStr,\n// Support 'image://' or 'path://' or direct svg path.\nopt, rect) {\n  var innerOpts = extend({\n    rectHover: true\n  }, opt);\n  var style = innerOpts.style = {\n    strokeNoScale: true\n  };\n  rect = rect || {\n    x: -1,\n    y: -1,\n    width: 2,\n    height: 2\n  };\n  if (iconStr) {\n    return iconStr.indexOf('image://') === 0 ? (style.image = iconStr.slice(8), defaults(style, rect), new ZRImage(innerOpts)) : makePath(iconStr.replace('path://', ''), innerOpts, rect, 'center');\n  }\n}\n/**\r\n * Return `true` if the given line (line `a`) and the given polygon\r\n * are intersect.\r\n * Note that we do not count colinear as intersect here because no\r\n * requirement for that. We could do that if required in future.\r\n */\nexport function linePolygonIntersect(a1x, a1y, a2x, a2y, points) {\n  for (var i = 0, p2 = points[points.length - 1]; i < points.length; i++) {\n    var p = points[i];\n    if (lineLineIntersect(a1x, a1y, a2x, a2y, p[0], p[1], p2[0], p2[1])) {\n      return true;\n    }\n    p2 = p;\n  }\n}\n/**\r\n * Return `true` if the given two lines (line `a` and line `b`)\r\n * are intersect.\r\n * Note that we do not count colinear as intersect here because no\r\n * requirement for that. We could do that if required in future.\r\n */\nexport function lineLineIntersect(a1x, a1y, a2x, a2y, b1x, b1y, b2x, b2y) {\n  // let `vec_m` to be `vec_a2 - vec_a1` and `vec_n` to be `vec_b2 - vec_b1`.\n  var mx = a2x - a1x;\n  var my = a2y - a1y;\n  var nx = b2x - b1x;\n  var ny = b2y - b1y;\n  // `vec_m` and `vec_n` are parallel iff\n  //     existing `k` such that `vec_m = k · vec_n`, equivalent to `vec_m X vec_n = 0`.\n  var nmCrossProduct = crossProduct2d(nx, ny, mx, my);\n  if (nearZero(nmCrossProduct)) {\n    return false;\n  }\n  // `vec_m` and `vec_n` are intersect iff\n  //     existing `p` and `q` in [0, 1] such that `vec_a1 + p * vec_m = vec_b1 + q * vec_n`,\n  //     such that `q = ((vec_a1 - vec_b1) X vec_m) / (vec_n X vec_m)`\n  //           and `p = ((vec_a1 - vec_b1) X vec_n) / (vec_n X vec_m)`.\n  var b1a1x = a1x - b1x;\n  var b1a1y = a1y - b1y;\n  var q = crossProduct2d(b1a1x, b1a1y, mx, my) / nmCrossProduct;\n  if (q < 0 || q > 1) {\n    return false;\n  }\n  var p = crossProduct2d(b1a1x, b1a1y, nx, ny) / nmCrossProduct;\n  if (p < 0 || p > 1) {\n    return false;\n  }\n  return true;\n}\n/**\r\n * Cross product of 2-dimension vector.\r\n */\nfunction crossProduct2d(x1, y1, x2, y2) {\n  return x1 * y2 - x2 * y1;\n}\nfunction nearZero(val) {\n  return val <= 1e-6 && val >= -1e-6;\n}\nexport function setTooltipConfig(opt) {\n  var itemTooltipOption = opt.itemTooltipOption;\n  var componentModel = opt.componentModel;\n  var itemName = opt.itemName;\n  var itemTooltipOptionObj = isString(itemTooltipOption) ? {\n    formatter: itemTooltipOption\n  } : itemTooltipOption;\n  var mainType = componentModel.mainType;\n  var componentIndex = componentModel.componentIndex;\n  var formatterParams = {\n    componentType: mainType,\n    name: itemName,\n    $vars: ['name']\n  };\n  formatterParams[mainType + 'Index'] = componentIndex;\n  var formatterParamsExtra = opt.formatterParamsExtra;\n  if (formatterParamsExtra) {\n    each(keys(formatterParamsExtra), function (key) {\n      if (!hasOwn(formatterParams, key)) {\n        formatterParams[key] = formatterParamsExtra[key];\n        formatterParams.$vars.push(key);\n      }\n    });\n  }\n  var ecData = getECData(opt.el);\n  ecData.componentMainType = mainType;\n  ecData.componentIndex = componentIndex;\n  ecData.tooltipConfig = {\n    name: itemName,\n    option: defaults({\n      content: itemName,\n      encodeHTMLContent: true,\n      formatterParams: formatterParams\n    }, itemTooltipOptionObj)\n  };\n}\nfunction traverseElement(el, cb) {\n  var stopped;\n  // TODO\n  // Polyfill for fixing zrender group traverse don't visit it's root issue.\n  if (el.isGroup) {\n    stopped = cb(el);\n  }\n  if (!stopped) {\n    el.traverse(cb);\n  }\n}\nexport function traverseElements(els, cb) {\n  if (els) {\n    if (isArray(els)) {\n      for (var i = 0; i < els.length; i++) {\n        traverseElement(els[i], cb);\n      }\n    } else {\n      traverseElement(els, cb);\n    }\n  }\n}\n// Register built-in shapes. These shapes might be overwritten\n// by users, although we do not recommend that.\nregisterShape('circle', Circle);\nregisterShape('ellipse', Ellipse);\nregisterShape('sector', Sector);\nregisterShape('ring', Ring);\nregisterShape('polygon', Polygon);\nregisterShape('polyline', Polyline);\nregisterShape('rect', Rect);\nregisterShape('line', Line);\nregisterShape('bezierCurve', BezierCurve);\nregisterShape('arc', Arc);\nexport { Group, ZRImage as Image, ZRText as Text, Circle, Ellipse, Sector, Ring, Polygon, Polyline, Rect, Line, BezierCurve, Arc, IncrementalDisplayable, CompoundPath, LinearGradient, RadialGradient, BoundingRect, OrientedBoundingRect, Point, Path };"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,QAAQ,MAAM,0BAA0B;AACpD,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,MAAM,MAAM,qCAAqC;AACxD,OAAOC,OAAO,MAAM,sCAAsC;AAC1D,OAAOC,MAAM,MAAM,qCAAqC;AACxD,OAAOC,IAAI,MAAM,mCAAmC;AACpD,OAAOC,OAAO,MAAM,sCAAsC;AAC1D,OAAOC,QAAQ,MAAM,uCAAuC;AAC5D,OAAOC,IAAI,MAAM,mCAAmC;AACpD,OAAOC,IAAI,MAAM,mCAAmC;AACpD,OAAOC,WAAW,MAAM,0CAA0C;AAClE,OAAOC,GAAG,MAAM,kCAAkC;AAClD,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,OAAOC,sBAAsB,MAAM,+CAA+C;AAClF,OAAO,KAAKC,oBAAoB,MAAM,gDAAgD;AACtF,SAASC,MAAM,EAAEC,WAAW,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,0BAA0B;AACpH,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,WAAW,EAAEC,SAAS,EAAEC,aAAa,EAAEC,wBAAwB,EAAEC,gBAAgB,QAAQ,iCAAiC;AACnI;AACA;AACA;AACA,SAASJ,WAAW,EAAEC,SAAS,EAAEC,aAAa,EAAEC,wBAAwB,EAAEC,gBAAgB;AAC1F,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;AACtB,IAAIC,eAAe,GAAG,CAAC,CAAC;AACxB;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAO7C,IAAI,CAACuB,MAAM,CAACsB,IAAI,CAAC;AAC1B;AACA,IAAIC,oBAAoB,GAAGjD,QAAQ,CAACkD,gBAAgB;AACpD;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAEJ,IAAI,EAAE;EACzC,OAAOC,oBAAoB,CAACG,QAAQ,EAAEJ,IAAI,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,aAAaA,CAACC,IAAI,EAAEC,UAAU,EAAE;EAC9CT,eAAe,CAACQ,IAAI,CAAC,GAAGC,UAAU;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACF,IAAI,EAAE;EAClC,IAAIR,eAAe,CAACW,cAAc,CAACH,IAAI,CAAC,EAAE;IACxC,OAAOR,eAAe,CAACQ,IAAI,CAAC;EAC9B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,QAAQA,CAACN,QAAQ,EAAEJ,IAAI,EAAEW,IAAI,EAAEC,MAAM,EAAE;EACrD,IAAIC,IAAI,GAAG7D,QAAQ,CAAC8D,gBAAgB,CAACV,QAAQ,EAAEJ,IAAI,CAAC;EACpD,IAAIW,IAAI,EAAE;IACR,IAAIC,MAAM,KAAK,QAAQ,EAAE;MACvBD,IAAI,GAAGI,aAAa,CAACJ,IAAI,EAAEE,IAAI,CAACG,eAAe,CAAC,CAAC,CAAC;IACpD;IACAC,UAAU,CAACJ,IAAI,EAAEF,IAAI,CAAC;EACxB;EACA,OAAOE,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,SAASA,CAACC,QAAQ,EAAER,IAAI,EAAEC,MAAM,EAAE;EAChD,IAAIQ,KAAK,GAAG,IAAI/D,OAAO,CAAC;IACtBgE,KAAK,EAAE;MACLC,KAAK,EAAEH,QAAQ;MACfI,CAAC,EAAEZ,IAAI,CAACY,CAAC;MACTC,CAAC,EAAEb,IAAI,CAACa,CAAC;MACTC,KAAK,EAAEd,IAAI,CAACc,KAAK;MACjBC,MAAM,EAAEf,IAAI,CAACe;IACf,CAAC;IACDC,MAAM,EAAE,SAAAA,CAAUC,GAAG,EAAE;MACrB,IAAIhB,MAAM,KAAK,QAAQ,EAAE;QACvB,IAAIiB,YAAY,GAAG;UACjBJ,KAAK,EAAEG,GAAG,CAACH,KAAK;UAChBC,MAAM,EAAEE,GAAG,CAACF;QACd,CAAC;QACDN,KAAK,CAACU,QAAQ,CAACf,aAAa,CAACJ,IAAI,EAAEkB,YAAY,CAAC,CAAC;MACnD;IACF;EACF,CAAC,CAAC;EACF,OAAOT,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,aAAaA,CAACJ,IAAI,EAAEkB,YAAY,EAAE;EACzC;EACA,IAAIE,MAAM,GAAGF,YAAY,CAACJ,KAAK,GAAGI,YAAY,CAACH,MAAM;EACrD,IAAID,KAAK,GAAGd,IAAI,CAACe,MAAM,GAAGK,MAAM;EAChC,IAAIL,MAAM;EACV,IAAID,KAAK,IAAId,IAAI,CAACc,KAAK,EAAE;IACvBC,MAAM,GAAGf,IAAI,CAACe,MAAM;EACtB,CAAC,MAAM;IACLD,KAAK,GAAGd,IAAI,CAACc,KAAK;IAClBC,MAAM,GAAGD,KAAK,GAAGM,MAAM;EACzB;EACA,IAAIC,EAAE,GAAGrB,IAAI,CAACY,CAAC,GAAGZ,IAAI,CAACc,KAAK,GAAG,CAAC;EAChC,IAAIQ,EAAE,GAAGtB,IAAI,CAACa,CAAC,GAAGb,IAAI,CAACe,MAAM,GAAG,CAAC;EACjC,OAAO;IACLH,CAAC,EAAES,EAAE,GAAGP,KAAK,GAAG,CAAC;IACjBD,CAAC,EAAES,EAAE,GAAGP,MAAM,GAAG,CAAC;IAClBD,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA;EACV,CAAC;AACH;AACA,OAAO,IAAIQ,SAAS,GAAGlF,QAAQ,CAACkF,SAAS;AACzC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASjB,UAAUA,CAACJ,IAAI,EAAEF,IAAI,EAAE;EACrC,IAAI,CAACE,IAAI,CAACsB,cAAc,EAAE;IACxB;EACF;EACA,IAAIC,QAAQ,GAAGvB,IAAI,CAACG,eAAe,CAAC,CAAC;EACrC,IAAIqB,CAAC,GAAGD,QAAQ,CAACE,kBAAkB,CAAC3B,IAAI,CAAC;EACzCE,IAAI,CAACsB,cAAc,CAACE,CAAC,CAAC;AACxB;AACA;AACA;AACA;AACA,OAAO,SAASE,oBAAoBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACrDhE,oBAAoB,CAAC8D,oBAAoB,CAACC,KAAK,EAAEA,KAAK,EAAE;IACtDC,SAAS,EAAEA;EACb,CAAC,CAAC;EACF,OAAOD,KAAK;AACd;AACA;AACA;AACA;AACA,OAAO,SAASE,oBAAoBA,CAACC,KAAK,EAAE;EAC1ClE,oBAAoB,CAACiE,oBAAoB,CAACC,KAAK,CAACH,KAAK,EAAEG,KAAK,CAACH,KAAK,EAAEG,KAAK,CAACtB,KAAK,CAAC;EAChF,OAAOsB,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAGnE,oBAAoB,CAACmE,gBAAgB;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC7C,IAAIC,GAAG,GAAG/F,MAAM,CAACgG,QAAQ,CAAC,EAAE,CAAC;EAC7B,OAAOH,MAAM,IAAIA,MAAM,KAAKC,QAAQ,EAAE;IACpC9F,MAAM,CAACiG,GAAG,CAACF,GAAG,EAAEF,MAAM,CAACK,iBAAiB,CAAC,CAAC,EAAEH,GAAG,CAAC;IAChDF,MAAM,GAAGA,MAAM,CAACM,MAAM;EACxB;EACA,OAAOJ,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASb,cAAcA,CAACW,MAAM,EAAEO,SAAS,EAAEC,MAAM,EAAE;EACxD,IAAID,SAAS,IAAI,CAAC1E,WAAW,CAAC0E,SAAS,CAAC,EAAE;IACxCA,SAAS,GAAGjG,aAAa,CAAC+F,iBAAiB,CAACE,SAAS,CAAC;EACxD;EACA,IAAIC,MAAM,EAAE;IACVD,SAAS,GAAGpG,MAAM,CAACqG,MAAM,CAAC,EAAE,EAAED,SAAS,CAAC;EAC1C;EACA,OAAOnG,MAAM,CAACiF,cAAc,CAAC,EAAE,EAAEW,MAAM,EAAEO,SAAS,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,kBAAkBA,CAACC,SAAS,EAAEH,SAAS,EAAEC,MAAM,EAAE;EAC/D;EACA,IAAIG,KAAK,GAAGJ,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG3D,IAAI,CAACgE,GAAG,CAAC,CAAC,GAAGL,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC;EAC1H,IAAIM,KAAK,GAAGN,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG3D,IAAI,CAACgE,GAAG,CAAC,CAAC,GAAGL,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC;EAC1H,IAAIO,MAAM,GAAG,CAACJ,SAAS,KAAK,MAAM,GAAG,CAACC,KAAK,GAAGD,SAAS,KAAK,OAAO,GAAGC,KAAK,GAAG,CAAC,EAAED,SAAS,KAAK,KAAK,GAAG,CAACG,KAAK,GAAGH,SAAS,KAAK,QAAQ,GAAGG,KAAK,GAAG,CAAC,CAAC;EACnJC,MAAM,GAAGzB,cAAc,CAACyB,MAAM,EAAEP,SAAS,EAAEC,MAAM,CAAC;EAClD,OAAO5D,IAAI,CAACgE,GAAG,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGlE,IAAI,CAACgE,GAAG,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,KAAK;AACxH;AACA,SAASC,UAAUA,CAACC,EAAE,EAAE;EACtB,OAAO,CAACA,EAAE,CAACC,OAAO;AACpB;AACA,SAASC,MAAMA,CAACF,EAAE,EAAE;EAClB,OAAOA,EAAE,CAACtB,KAAK,IAAI,IAAI;AACzB;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyB,eAAeA,CAACC,EAAE,EAAEC,EAAE,EAAEC,eAAe,EAAE;EACvD,IAAI,CAACF,EAAE,IAAI,CAACC,EAAE,EAAE;IACd;EACF;EACA,SAASE,QAAQA,CAACC,CAAC,EAAE;IACnB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACdD,CAAC,CAACE,QAAQ,CAAC,UAAUV,EAAE,EAAE;MACvB,IAAID,UAAU,CAACC,EAAE,CAAC,IAAIA,EAAE,CAACW,IAAI,EAAE;QAC7BF,KAAK,CAACT,EAAE,CAACW,IAAI,CAAC,GAAGX,EAAE;MACrB;IACF,CAAC,CAAC;IACF,OAAOS,KAAK;EACd;EACA,SAASG,kBAAkBA,CAACZ,EAAE,EAAE;IAC9B,IAAIa,GAAG,GAAG;MACRpD,CAAC,EAAEuC,EAAE,CAACvC,CAAC;MACPC,CAAC,EAAEsC,EAAE,CAACtC,CAAC;MACPoD,QAAQ,EAAEd,EAAE,CAACc;IACf,CAAC;IACD,IAAIZ,MAAM,CAACF,EAAE,CAAC,EAAE;MACda,GAAG,CAACnC,KAAK,GAAG9D,MAAM,CAAC,CAAC,CAAC,EAAEoF,EAAE,CAACtB,KAAK,CAAC;IAClC;IACA,OAAOmC,GAAG;EACZ;EACA,IAAIE,MAAM,GAAGR,QAAQ,CAACH,EAAE,CAAC;EACzBC,EAAE,CAACK,QAAQ,CAAC,UAAUV,EAAE,EAAE;IACxB,IAAID,UAAU,CAACC,EAAE,CAAC,IAAIA,EAAE,CAACW,IAAI,EAAE;MAC7B,IAAIK,KAAK,GAAGD,MAAM,CAACf,EAAE,CAACW,IAAI,CAAC;MAC3B,IAAIK,KAAK,EAAE;QACT,IAAIC,OAAO,GAAGL,kBAAkB,CAACZ,EAAE,CAAC;QACpCA,EAAE,CAACkB,IAAI,CAACN,kBAAkB,CAACI,KAAK,CAAC,CAAC;QAClC1F,WAAW,CAAC0E,EAAE,EAAEiB,OAAO,EAAEX,eAAe,EAAEjF,SAAS,CAAC2E,EAAE,CAAC,CAACmB,SAAS,CAAC;MACpE;IACF;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAExE,IAAI,EAAE;EAC7C;EACA;EACA,OAAO/B,GAAG,CAACuG,MAAM,EAAE,UAAUC,KAAK,EAAE;IAClC,IAAI7D,CAAC,GAAG6D,KAAK,CAAC,CAAC,CAAC;IAChB7D,CAAC,GAAG9B,OAAO,CAAC8B,CAAC,EAAEZ,IAAI,CAACY,CAAC,CAAC;IACtBA,CAAC,GAAG3B,OAAO,CAAC2B,CAAC,EAAEZ,IAAI,CAACY,CAAC,GAAGZ,IAAI,CAACc,KAAK,CAAC;IACnC,IAAID,CAAC,GAAG4D,KAAK,CAAC,CAAC,CAAC;IAChB5D,CAAC,GAAG/B,OAAO,CAAC+B,CAAC,EAAEb,IAAI,CAACa,CAAC,CAAC;IACtBA,CAAC,GAAG5B,OAAO,CAAC4B,CAAC,EAAEb,IAAI,CAACa,CAAC,GAAGb,IAAI,CAACe,MAAM,CAAC;IACpC,OAAO,CAACH,CAAC,EAAEC,CAAC,CAAC;EACf,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA,OAAO,SAAS6D,cAAcA,CAACC,UAAU,EAAE3E,IAAI,EAAE;EAC/C,IAAIY,CAAC,GAAG9B,OAAO,CAAC6F,UAAU,CAAC/D,CAAC,EAAEZ,IAAI,CAACY,CAAC,CAAC;EACrC,IAAIgE,EAAE,GAAG3F,OAAO,CAAC0F,UAAU,CAAC/D,CAAC,GAAG+D,UAAU,CAAC7D,KAAK,EAAEd,IAAI,CAACY,CAAC,GAAGZ,IAAI,CAACc,KAAK,CAAC;EACtE,IAAID,CAAC,GAAG/B,OAAO,CAAC6F,UAAU,CAAC9D,CAAC,EAAEb,IAAI,CAACa,CAAC,CAAC;EACrC,IAAIgE,EAAE,GAAG5F,OAAO,CAAC0F,UAAU,CAAC9D,CAAC,GAAG8D,UAAU,CAAC5D,MAAM,EAAEf,IAAI,CAACa,CAAC,GAAGb,IAAI,CAACe,MAAM,CAAC;EACxE;EACA;EACA,IAAI6D,EAAE,IAAIhE,CAAC,IAAIiE,EAAE,IAAIhE,CAAC,EAAE;IACtB,OAAO;MACLD,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJC,KAAK,EAAE8D,EAAE,GAAGhE,CAAC;MACbG,MAAM,EAAE8D,EAAE,GAAGhE;IACf,CAAC;EACH;AACF;AACA,OAAO,SAASiE,UAAUA,CAACC,OAAO;AAClC;AACAC,GAAG,EAAEhF,IAAI,EAAE;EACT,IAAIiF,SAAS,GAAGlH,MAAM,CAAC;IACrBmH,SAAS,EAAE;EACb,CAAC,EAAEF,GAAG,CAAC;EACP,IAAItE,KAAK,GAAGuE,SAAS,CAACvE,KAAK,GAAG;IAC5ByE,aAAa,EAAE;EACjB,CAAC;EACDnF,IAAI,GAAGA,IAAI,IAAI;IACbY,CAAC,EAAE,CAAC,CAAC;IACLC,CAAC,EAAE,CAAC,CAAC;IACLC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACD,IAAIgE,OAAO,EAAE;IACX,OAAOA,OAAO,CAACK,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI1E,KAAK,CAACC,KAAK,GAAGoE,OAAO,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEnH,QAAQ,CAACwC,KAAK,EAAEV,IAAI,CAAC,EAAE,IAAItD,OAAO,CAACuI,SAAS,CAAC,IAAIlF,QAAQ,CAACgF,OAAO,CAACO,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAEL,SAAS,EAAEjF,IAAI,EAAE,QAAQ,CAAC;EAClM;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASuF,oBAAoBA,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEnB,MAAM,EAAE;EAC/D,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGrB,MAAM,CAACA,MAAM,CAACsB,MAAM,GAAG,CAAC,CAAC,EAAEF,CAAC,GAAGpB,MAAM,CAACsB,MAAM,EAAEF,CAAC,EAAE,EAAE;IACtE,IAAIG,CAAC,GAAGvB,MAAM,CAACoB,CAAC,CAAC;IACjB,IAAII,iBAAiB,CAACR,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEI,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEF,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACnE,OAAO,IAAI;IACb;IACAA,EAAE,GAAGE,CAAC;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACR,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEM,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACxE;EACA,IAAIC,EAAE,GAAGX,GAAG,GAAGF,GAAG;EAClB,IAAIc,EAAE,GAAGX,GAAG,GAAGF,GAAG;EAClB,IAAIc,EAAE,GAAGJ,GAAG,GAAGF,GAAG;EAClB,IAAIO,EAAE,GAAGJ,GAAG,GAAGF,GAAG;EAClB;EACA;EACA,IAAIO,cAAc,GAAGC,cAAc,CAACH,EAAE,EAAEC,EAAE,EAAEH,EAAE,EAAEC,EAAE,CAAC;EACnD,IAAIK,QAAQ,CAACF,cAAc,CAAC,EAAE;IAC5B,OAAO,KAAK;EACd;EACA;EACA;EACA;EACA;EACA,IAAIG,KAAK,GAAGpB,GAAG,GAAGS,GAAG;EACrB,IAAIY,KAAK,GAAGpB,GAAG,GAAGS,GAAG;EACrB,IAAIY,CAAC,GAAGJ,cAAc,CAACE,KAAK,EAAEC,KAAK,EAAER,EAAE,EAAEC,EAAE,CAAC,GAAGG,cAAc;EAC7D,IAAIK,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;IAClB,OAAO,KAAK;EACd;EACA,IAAIf,CAAC,GAAGW,cAAc,CAACE,KAAK,EAAEC,KAAK,EAAEN,EAAE,EAAEC,EAAE,CAAC,GAAGC,cAAc;EAC7D,IAAIV,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;IAClB,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AACA;AACA;AACA;AACA,SAASW,cAAcA,CAACK,EAAE,EAAEC,EAAE,EAAEpC,EAAE,EAAEC,EAAE,EAAE;EACtC,OAAOkC,EAAE,GAAGlC,EAAE,GAAGD,EAAE,GAAGoC,EAAE;AAC1B;AACA,SAASL,QAAQA,CAACM,GAAG,EAAE;EACrB,OAAOA,GAAG,IAAI,IAAI,IAAIA,GAAG,IAAI,CAAC,IAAI;AACpC;AACA,OAAO,SAASC,gBAAgBA,CAAClC,GAAG,EAAE;EACpC,IAAImC,iBAAiB,GAAGnC,GAAG,CAACmC,iBAAiB;EAC7C,IAAIC,cAAc,GAAGpC,GAAG,CAACoC,cAAc;EACvC,IAAIC,QAAQ,GAAGrC,GAAG,CAACqC,QAAQ;EAC3B,IAAIC,oBAAoB,GAAGnJ,QAAQ,CAACgJ,iBAAiB,CAAC,GAAG;IACvDI,SAAS,EAAEJ;EACb,CAAC,GAAGA,iBAAiB;EACrB,IAAIK,QAAQ,GAAGJ,cAAc,CAACI,QAAQ;EACtC,IAAIC,cAAc,GAAGL,cAAc,CAACK,cAAc;EAClD,IAAIC,eAAe,GAAG;IACpBC,aAAa,EAAEH,QAAQ;IACvB7H,IAAI,EAAE0H,QAAQ;IACdO,KAAK,EAAE,CAAC,MAAM;EAChB,CAAC;EACDF,eAAe,CAACF,QAAQ,GAAG,OAAO,CAAC,GAAGC,cAAc;EACpD,IAAII,oBAAoB,GAAG7C,GAAG,CAAC6C,oBAAoB;EACnD,IAAIA,oBAAoB,EAAE;IACxBxJ,IAAI,CAACD,IAAI,CAACyJ,oBAAoB,CAAC,EAAE,UAAUC,GAAG,EAAE;MAC9C,IAAI,CAACxJ,MAAM,CAACoJ,eAAe,EAAEI,GAAG,CAAC,EAAE;QACjCJ,eAAe,CAACI,GAAG,CAAC,GAAGD,oBAAoB,CAACC,GAAG,CAAC;QAChDJ,eAAe,CAACE,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC;MACjC;IACF,CAAC,CAAC;EACJ;EACA,IAAIE,MAAM,GAAGxJ,SAAS,CAACwG,GAAG,CAAC7B,EAAE,CAAC;EAC9B6E,MAAM,CAACC,iBAAiB,GAAGT,QAAQ;EACnCQ,MAAM,CAACP,cAAc,GAAGA,cAAc;EACtCO,MAAM,CAACE,aAAa,GAAG;IACrBvI,IAAI,EAAE0H,QAAQ;IACdc,MAAM,EAAEjK,QAAQ,CAAC;MACfkK,OAAO,EAAEf,QAAQ;MACjBgB,iBAAiB,EAAE,IAAI;MACvBX,eAAe,EAAEA;IACnB,CAAC,EAAEJ,oBAAoB;EACzB,CAAC;AACH;AACA,SAASgB,eAAeA,CAACnF,EAAE,EAAEoF,EAAE,EAAE;EAC/B,IAAIC,OAAO;EACX;EACA;EACA,IAAIrF,EAAE,CAACC,OAAO,EAAE;IACdoF,OAAO,GAAGD,EAAE,CAACpF,EAAE,CAAC;EAClB;EACA,IAAI,CAACqF,OAAO,EAAE;IACZrF,EAAE,CAACU,QAAQ,CAAC0E,EAAE,CAAC;EACjB;AACF;AACA,OAAO,SAASE,gBAAgBA,CAACC,GAAG,EAAEH,EAAE,EAAE;EACxC,IAAIG,GAAG,EAAE;IACP,IAAInK,OAAO,CAACmK,GAAG,CAAC,EAAE;MAChB,KAAK,IAAI9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8C,GAAG,CAAC5C,MAAM,EAAEF,CAAC,EAAE,EAAE;QACnC0C,eAAe,CAACI,GAAG,CAAC9C,CAAC,CAAC,EAAE2C,EAAE,CAAC;MAC7B;IACF,CAAC,MAAM;MACLD,eAAe,CAACI,GAAG,EAAEH,EAAE,CAAC;IAC1B;EACF;AACF;AACA;AACA;AACA7I,aAAa,CAAC,QAAQ,EAAE7C,MAAM,CAAC;AAC/B6C,aAAa,CAAC,SAAS,EAAE5C,OAAO,CAAC;AACjC4C,aAAa,CAAC,QAAQ,EAAE3C,MAAM,CAAC;AAC/B2C,aAAa,CAAC,MAAM,EAAE1C,IAAI,CAAC;AAC3B0C,aAAa,CAAC,SAAS,EAAEzC,OAAO,CAAC;AACjCyC,aAAa,CAAC,UAAU,EAAExC,QAAQ,CAAC;AACnCwC,aAAa,CAAC,MAAM,EAAEvC,IAAI,CAAC;AAC3BuC,aAAa,CAAC,MAAM,EAAEtC,IAAI,CAAC;AAC3BsC,aAAa,CAAC,aAAa,EAAErC,WAAW,CAAC;AACzCqC,aAAa,CAAC,KAAK,EAAEpC,GAAG,CAAC;AACzB,SAASX,KAAK,EAAED,OAAO,IAAIiM,KAAK,EAAE/L,MAAM,IAAIgM,IAAI,EAAE/L,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAEO,sBAAsB,EAAEN,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,KAAK,EAAEpB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}