<template>
    <div>
        <!-- 搜索栏 -->
        <div class="search">
            <el-input
                placeholder="请输入留言问题查询"
                style="width: 200px"
                v-model="sfQuestion">
            </el-input>
            <el-button type="info" plain style="margin-left: 10px" @click="load(1)">查询</el-button>
            <el-button type="warning" plain style="margin-left: 10px" @click="reset">重置</el-button>
        </div>

        <!-- 操作按钮 -->
        <div class="operation">
            <el-button type="primary" plain @click="handleAdd">新增</el-button>
            <el-button type="danger" plain @click="delBatch">批量删除</el-button>
        </div>

        <!-- 数据表格 -->
        <div class="table">
            <el-table
                :data="tableData"
                stripe
                @selection-change="handleSelectionChange"
                style="width: 100%"
                :row-class-name="tableRowClassName"> <!-- 添加row-class-name -->
                <!-- 选择列 -->
                <el-table-column type="selection" width="55" align="center"></el-table-column>

                <!-- 序号列 -->
                <el-table-column prop="id" label="序号" width="70" align="center" sortable></el-table-column>

                <!-- 用户ID列 -->
                <el-table-column prop="sfUserId" label="用户ID" align="center"></el-table-column>

                <!-- 留言问题列 -->
                <el-table-column prop="sfQuestion" label="留言问题" align="center"></el-table-column>

                <!-- 留言回复列 -->
                <el-table-column prop="reply" label="留言回复" align="center">
                    <template slot-scope="scope">
                        <span v-if="scope.row.reply">{{ scope.row.reply }}</span>
                        <span v-else style="color: #f56c6c;">暂无回复</span>
                    </template>
                </el-table-column>

                <!-- 评留言图片列 -->
                <el-table-column prop="sfImage" label="留言图片" align="center">
                    <template slot-scope="scope">
                        <el-image
                            v-if="scope.row.sfImage"
                            :src="scope.row.sfImage"
                            fit="cover"
                            style="width: 50px; height: 50px; cursor: pointer;"
                            @click="previewImage(scope.row.sfImage)">
                        </el-image>
                        <span v-else>暂无图片</span>
                    </template>
                </el-table-column>

                <!-- 留言时间列 -->
                <el-table-column prop="sfLeaveTime" label="留言时间" align="center"></el-table-column>

                <!-- 回复时间列 -->
                <el-table-column prop="sfReplyTime" label="回复时间" align="center"></el-table-column>

                <!-- 操作列 -->
                <el-table-column label="操作" align="center" width="180">
                    <template slot-scope="scope">
                        <el-button size="mini" type="primary" plain @click="handleEdit(scope.row)">回复</el-button>
                        <el-button size="mini" type="danger" plain @click="del(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 无数据提示 -->
            <el-empty v-if="tableData.length === 0" description="暂无留言"></el-empty>

            <!-- 分页器 -->
            <div class="pagination">
                <el-pagination
                    background
                    @current-change="handleCurrentChange"
                    :current-page="pageNum"
                    :page-sizes="[5, 10, 20]"
                    :page-size="pageSize"
                    layout="total, prev, pager, next"
                    :total="total">
                </el-pagination>
            </div>
        </div>

        <!-- 弹窗 -->
        <el-dialog title="留言管理" :visible.sync="fromVisible" width="40%" :close-on-click-modal="false" destroy-on-close>
            <el-form :model="form" label-width="100px" style="padding-right: 50px" :rules="rules" ref="formRef">
                <el-form-item label="用户ID" prop="sfUserId">
                    <el-input v-model="form.sfUserId" placeholder="用户ID" class="input-field"></el-input>
                </el-form-item>
                <el-form-item label="留言问题" prop="sfQuestion">
                    <el-input v-model="form.sfQuestion" placeholder="留言问题" class="input-field"></el-input>
                </el-form-item>
                <el-form-item label="留言回复" prop="reply">
                    <el-input v-model="form.reply" placeholder="留言回复" class="input-field"></el-input>
                </el-form-item>
                <el-form-item label="留言图片" prop="sfImage">
                    <el-upload
                        class="avatar-uploader"
                        :action="$baseUrl + '/files/upload'"
                        :show-file-list="false"
                        :on-success="handleImageSuccess"
                        :before-upload="beforeUpload">
                        <el-button type="primary" class="upload-btn">点击上传图片</el-button>
                    </el-upload>
                    <!-- 显示上传成功后的图片 -->
                    <el-image
                        v-if="form.sfImage"
                        class="uploaded-image"
                        :src="form.sfImage"
                        :preview-src-list="[form.sfImage]">
                    </el-image>
                </el-form-item>
                <el-form-item label="留言时间" prop="sfLeaveTime">
                    <el-input v-model="form.sfLeaveTime" placeholder="留言时间" disabled></el-input>
                </el-form-item>
                <el-form-item label="回复时间" prop="sfReplyTime">
                    <el-input v-model="form.sfReplyTime" placeholder="回复时间" disabled></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="fromVisible = false">取 消</el-button>
                <el-button type="primary" @click="save">确 定</el-button>
            </div>
        </el-dialog>

        <!-- 图片预览组件（隐藏） -->
        <el-image ref="imagePreview" style="display: none;"></el-image>

        <!-- 提交成功提示 -->
        <el-dialog :visible.sync="successVisible" title="留言提交成功" width="30%" :close-on-click-modal="false" destroy-on-close>
            <p>用户留言已经回复</p>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="successVisible = false">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "Leavemess",
    data() {
        return {
            tableData: [],  // 所有的数据
            pageNum: 1,     // 当前的页码
            pageSize: 10,   // 每页显示的个数
            total: 0,
            sfQuestion: '',   // 搜索关键字，针对留言问题
            fromVisible: false,
            form: {},
            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
            rules: {
                sfUserId: [
                    { required: true, message: '请输入用户ID', trigger: 'blur' },
                ],
                sfQuestion: [
                    { required: true, message: '请输入留言问题', trigger: 'blur' },
                ],
                reply: [
                    { required: true, message: '请输入留言回复', trigger: 'blur' },
                ],
                sfImage: [
                    { type: 'url', message: '请输入有效的图片URL', trigger: 'blur' },
                ],
            },
            ids: [],
            successVisible: false, // 控制成功提交提示的显示
        }
    },
    created() {
        this.load(1)
    },
    methods: {
        /**
         * 根据行数据返回对应的类名
         * @param {Object} row - 当前行数据
         * @param {Number} index - 行索引
         * @returns {String} - 类名
         */
        tableRowClassName(row, index) {
            return !row.reply ? 'no-reply' : '';
        },
        /**
         * 新增数据
         */
        handleAdd() {
            this.form = {}  // 新增数据的时候清空数据
            this.fromVisible = true   // 打开弹窗
        },
        /**
         * 编辑数据（回复留言）
         * @param {Object} row - 当前行数据
         */
        handleEdit(row) {
            this.form = JSON.parse(JSON.stringify(row))  // 深拷贝数据
            this.fromVisible = true   // 打开弹窗
        },
        /**
         * 保存数据（新增或更新）
         */
        save() {
            this.$refs.formRef.validate((valid) => {
                if (valid) {
                    this.$request({
                        url: this.form.id ? '/leavemess/update' : '/leavemess/add',
                        method: this.form.id ? 'PUT' : 'POST',
                        data: this.form
                    }).then(res => {
                        if (res.code === '200') {  // 表示成功保存
                            this.$message.success('保存成功')
                            this.load(1)
                            this.fromVisible = false
                            this.successVisible = true // 显示提交成功弹窗
                        } else {
                            this.$message.error(res.msg)  // 弹出错误的信息
                        }
                    }).catch(() => {
                        this.$message.error('请求失败')
                    })
                }
            })
        },
        /**
         * 单个删除
         * @param {Number} id - 数据ID
         */
        del(id) {
            this.$confirm('您确定删除吗？', '确认删除', {type: "warning"}).then(() => {
                this.$request.delete('/leavemess/delete/' + id).then(res => {
                    if (res.code === '200') {   // 表示操作成功
                        this.$message.success('删除成功')
                        this.load(1)
                    } else {
                        this.$message.error(res.msg)  // 弹出错误的信息
                    }
                }).catch(() => {
                    this.$message.error('删除失败')
                })
            }).catch(() => {
                // 用户取消删除操作
            })
        },
        /**
         * 处理表格选中的数据
         * @param {Array} rows - 当前选中的所有行数据
         */
        handleSelectionChange(rows) {
            this.ids = rows.map(v => v.id)
        },
        /**
         * 批量删除
         */
        delBatch() {
            if (!this.ids.length) {
                this.$message.warning('请选择要删除的数据')
                return
            }
            this.$confirm('您确定批量删除这些数据吗？', '确认删除', {type: "warning"}).then(() => {
                this.$request.delete('/leavemess/delete/batch', { data: this.ids }).then(res => {
                    if (res.code === '200') {   // 表示操作成功
                        this.$message.success('批量删除成功')
                        this.load(1)
                        this.ids = []  // 清空已删除的ID
                    } else {
                        this.$message.error(res.msg)  // 弹出错误的信息
                    }
                }).catch(() => {
                    this.$message.error('批量删除失败')
                })
            }).catch(() => {
                // 用户取消删除操作
            })
        },
        /**
         * 加载数据（分页查询）
         * @param {Number} pageNum - 要加载的页码
         */
        load(pageNum) {
            if (pageNum) this.pageNum = pageNum
            this.$request.get('/leavemess/selectPage', {
                params: {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,
                    sfQuestion: this.sfQuestion, // 修改为 sfQuestion
                }
            }).then(res => {
                if (res.code === '200') {
                    this.tableData = res.data?.list || []
                    this.total = res.data?.total || 0
                } else {
                    this.$message.error(res.msg)
                }
            }).catch(() => {
                this.$message.error('加载数据失败')
            })
        },
        /**
         * 重置搜索条件并重新加载数据
         */
        reset() {
            this.sfQuestion = ''
            this.load(1)
        },
        /**
         * 处理分页器页码变化
         * @param {Number} pageNum - 当前选择的页码
         */
        handleCurrentChange(pageNum) {
            this.load(pageNum)
        },
        /**
         * 处理图片上传成功
         * @param {Object} response - 上传成功的响应
         * @param {File} file - 上传的文件
         * @param {Array} fileList - 文件列表
         */
        handleImageSuccess(response, file, fileList) {
            if (response.code === '200') {
                this.form.sfImage = response.data.url || response.data;  // 根据后端返回的数据结构调整
                this.$message.success('图片上传成功')
            } else {
                this.$message.error('图片上传失败')
            }
        },
        /**
         * 图片上传前的验证
         * @param {File} file - 上传的文件
         * @returns {Boolean} - 是否允许上传
         */
        beforeUpload(file) {
            const isImage = file.type.startsWith('image/')
            const isLt2M = file.size / 1024 / 1024 < 2

            if (!isImage) {
                this.$message.error('上传图片只能是 JPG/PNG 格式!')
            }
            if (!isLt2M) {
                this.$message.error('上传图片大小不能超过 2MB!')
            }
            return isImage && isLt2M
        },
        /**
         * 预览图片
         * @param {String} url - 图片URL
         */
        previewImage(url) {
            this.$refs.imagePreview.handlePreview(url)
        }
    }
}
</script>

<style scoped>
.complaint-container {
    background-color: #f4f7fc;
    min-height: 10vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.form-container {
    background-color: #ffffff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    width: 100%;
    max-width: 800px;  /* 调整宽度，适应表单 */
    padding: 40px;  /* 增加表单内边距 */
}

.input-field {
    border-radius: 6px;
    background-color: #f9f9f9;
    border: 1px solid #e4e7ed;
}

.input-field:focus {
    border-color: #409eff;
}

.upload-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: white;
    border-radius: 4px;
}

.upload-btn:hover {
    background-color: #66b1ff;
}

.uploaded-image {
    width: 100px;
    height: 100px;
    margin-top: 10px;
    border-radius: 8px;
}

.form-footer {
    text-align: right;
    margin-top: 20px;
}

.submit-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: white;
    border-radius: 4px;
    width: 120px;  /* 调整按钮宽度 */
}

.submit-btn:hover {
    background-color: #66b1ff;
}

.dialog-footer {
    text-align: right;
}

.search {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.operation {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.table {
    overflow-x: auto;
}

.pagination {
    margin-top: 20px;
    text-align: right;
}

.el-empty {
    margin-top: 20px;
}

/* 新增样式 */
.no-reply {
    background-color: #ffe6e6; /* 淡红色背景 */
}

.no-reply .el-table__cell {
    color: #f56c6c; /* 深红色文本 */
}
</style>
