<template>
    <div class="manager-container">
        <!--  头部  -->
        <div class="manager-header">
            <div class="manager-header-left">
                <img src="@/assets/imgs/logo.png" />
                <div class="title">后台管理系统</div>
            </div>

            <div class="manager-header-center">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                    <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                    <el-breadcrumb-item :to="{ path: $route.path }">{{ $route.meta.name }}</el-breadcrumb-item>
                </el-breadcrumb>
            </div>

            <div class="manager-header-right">
                <el-dropdown placement="bottom">
                    <div class="avatar">
                        <img :src="user.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" />
                        <div>{{ user.name || '管理员' }}</div>
                    </div>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item @click.native="goToPerson">
                            <i class="el-icon-user"></i> 个人信息
                        </el-dropdown-item>
                        <el-dropdown-item @click.native="$router.push('/password')">
                            <i class="el-icon-lock"></i> 修改密码
                        </el-dropdown-item>
                        <el-dropdown-item divided @click.native="logout">
                            <i class="el-icon-switch-button"></i> 退出登录
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>

        <!--  主体  -->
        <div class="manager-main">
            <!--  侧边栏  -->
            <div class="manager-main-left">
                <el-menu
                    router
                    :default-active="$route.path"
                >
                    <el-menu-item index="/home">
                        <i class="el-icon-s-home"></i>
                        <span slot="title">系统首页</span>
                    </el-menu-item>

                    <el-submenu index="info" v-if="user.role === 'ADMIN'">
                        <template slot="title">
                            <i class="el-icon-s-management"></i><span>信息管理</span>
                        </template>
                        <el-menu-item index="/notice">
                            <i class="el-icon-bell"></i>
                            <span slot="title">公告信息</span>
                        </el-menu-item>
                    </el-submenu>

                    <el-submenu index="admin" v-if="user.role === 'ADMIN'">
                        <template slot="title">
                            <i class="el-icon-user-solid"></i><span>管理员管理</span>
                        </template>
                        <el-menu-item index="/admin">
                            <i class="el-icon-s-custom"></i>
                            <span slot="title">管理员信息</span>
                        </el-menu-item>
                    </el-submenu>

                    <el-submenu index="business" v-if="user.role === 'ADMIN'">
                        <template slot="title">
                            <i class="el-icon-s-shop"></i><span>商家管理</span>
                        </template>
                        <el-menu-item index="/business">
                            <i class="el-icon-s-order"></i>
                            <span slot="title">商家信息</span>
                        </el-menu-item>
                    </el-submenu>

                    <el-submenu index="user" v-if="user.role === 'ADMIN'">
                        <template slot="title">
                            <i class="el-icon-s-custom"></i><span>用户管理</span>
                        </template>
                        <el-menu-item index="/user">
                            <i class="el-icon-user"></i>
                            <span slot="title">用户信息</span>
                        </el-menu-item>
                    </el-submenu>

                    <el-submenu index="foods" v-if="user.role === 'ADMIN' || user.role === 'BUSINESS'">
                        <template slot="title">
                            <i class="el-icon-food"></i><span>食物管理</span>
                        </template>
                        <el-menu-item index="/category" v-if="user.role === 'ADMIN'">
                            <i class="el-icon-s-grid"></i>
                            <span slot="title">分类管理</span>
                        </el-menu-item>
                        <el-menu-item index="/foods">
                            <i class="el-icon-dish"></i>
                            <span slot="title">食物信息</span>
                        </el-menu-item>
                    </el-submenu>

                    <el-submenu index="table" v-if="user.role === 'ADMIN' || user.role === 'BUSINESS'">
                        <template slot="title">
                            <i class="el-icon-s-grid"></i><span>餐桌管理</span>
                        </template>
                        <el-menu-item index="/table">
                            <i class="el-icon-office-building"></i>
                            <span slot="title">餐桌信息</span>
                        </el-menu-item>
                    </el-submenu>

                    <el-submenu index="dingdan" v-if="user.role === 'ADMIN' || user.role === 'BUSINESS'">
                        <template slot="title">
                            <i class="el-icon-s-order"></i><span>订单管理</span>
                        </template>
                        <el-menu-item index="/dingdan">
                            <i class="el-icon-document"></i>
                            <span slot="title">订单信息</span>
                        </el-menu-item>
                    </el-submenu>

                    <el-submenu index="complaint" v-if="user.role === 'ADMIN' || user.role === 'BUSINESS'">
                        <template slot="title">
                            <i class="el-icon-warning"></i>
                            <span>投诉管理</span>
                        </template>
                        <el-menu-item index="/complaint">
                            <i class="el-icon-warning-outline"></i>
                            <span>投诉信息</span>
                        </el-menu-item>
                    </el-submenu>

                    <el-submenu index="blogs" v-if="user.role === 'ADMIN' || user.role === 'BUSINESS'">
                        <template slot="title">
                            <i class="el-icon-chat-dot-round"></i>
                            <span>系统讨论</span>
                        </template>
                        <el-menu-item index="/blogs">
                            <i class="el-icon-folder-opened"></i>
                            <span>讨论管理</span>
                        </el-menu-item>
                    </el-submenu>

                    <el-submenu index="pinglun" v-if="user.role === 'ADMIN' || user.role === 'BUSINESS'">
                        <template slot="title">
                            <i class="el-icon-chat-dot-square"></i>
                            <span>评论管理</span>
                        </template>
                        <el-menu-item index="/pinglun">
                            <i class="el-icon-chat-line-square"></i>
                            <span>评论信息</span>
                        </el-menu-item>
                    </el-submenu>

                    <el-submenu index="leavemess" v-if="user.role === 'ADMIN' || user.role === 'BUSINESS'">
                        <template slot="title">
                            <i class="el-icon-message"></i>
                            <span>留言管理</span>
                        </template>
                        <el-menu-item index="/leavemess">
                            <i class="el-icon-chat-line-square"></i>
                            <span>留言信息</span>
                        </el-menu-item>
                    </el-submenu>

                    <el-submenu index="freemovies" v-if="user.role === 'ADMIN' || user.role === 'BUSINESS'">
                        <template slot="title">
                            <i class="el-icon-video-play"></i>
                            <span>推荐管理</span>
                        </template>
                        <el-menu-item index="/freemovies">
                            <i class="el-icon-star-on"></i>
                            <span>点餐推荐</span>
                        </el-menu-item>
                    </el-submenu>

                </el-menu>
            </div>

            <!--  数据表格  -->
            <div class="manager-main-right">
                <router-view @update:user="updateUser" />
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "Manager",
    data() {
        return {
            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
        }
    },
    created() {
        if (!this.user.id) {
            this.$router.push('/front/home')
        }
    },
    methods: {
        updateUser() {
            this.user = JSON.parse(localStorage.getItem('xm-user') || '{}')
        },
        goToPerson() {
            if (this.user.role === 'ADMIN') {
                this.$router.push('/adminPerson')
            }
            if (this.user.role === 'BUSINESS') {
                this.$router.push('/businessPerson')
            }
        },
        logout() {
            this.$confirm('确定要退出登录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                localStorage.removeItem('xm-user')
                this.$message.success('退出成功')
                this.$router.push('/login')
            }).catch(() => {
                // 用户取消退出
            })
        }
    }
}
</script>

<style scoped>
@import "@/assets/css/manager.css";
</style>