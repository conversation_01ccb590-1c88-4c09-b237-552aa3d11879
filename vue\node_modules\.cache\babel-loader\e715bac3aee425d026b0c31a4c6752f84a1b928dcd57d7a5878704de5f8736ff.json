{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"main-content\"\n  }, [_c(\"div\", {\n    staticStyle: {\n      width: \"60%\",\n      margin: \"30px auto\",\n      \"border-radius\": \"20px\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      height: \"100px\",\n      padding: \"0 10px\",\n      display: \"flex\",\n      \"align-items\": \"center\",\n      \"border-radius\": \"25px\",\n      \"background-color\": \"white\"\n    }\n  }, [_c(\"img\", {\n    staticStyle: {\n      height: \"60px\",\n      width: \"60px\",\n      \"border-radius\": \"50%\"\n    },\n    attrs: {\n      src: _vm.businessData.avatar,\n      alt: \"\"\n    }\n  }), _c(\"div\", {\n    staticStyle: {\n      width: \"220px\",\n      margin: \"0 30px 0 15px\",\n      \"font-size\": \"20px\",\n      \"font-weight\": \"bold\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      height: \"30px\",\n      \"line-height\": \"30px\"\n    }\n  }, [_vm._v(_vm._s(_vm.businessData.name))]), _c(\"img\", {\n    staticStyle: {\n      height: \"25px\",\n      \"margin-top\": \"5px\"\n    },\n    attrs: {\n      src: require(\"@/assets/imgs/icon.png\"),\n      alt: \"\"\n    }\n  })]), _c(\"div\", {\n    staticStyle: {\n      width: \"150px\",\n      height: \"100px\",\n      padding: \"20px\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      \"font-size\": \"16px\",\n      height: \"30px\",\n      \"line-height\": \"30px\",\n      color: \"#7F7F7FFF\"\n    }\n  }, [_vm._v(\"店铺电话\")]), _c(\"div\", {\n    staticStyle: {\n      \"font-size\": \"16px\",\n      height: \"30px\",\n      \"line-height\": \"30px\"\n    }\n  }, [_vm._v(_vm._s(_vm.businessData.phone))])]), _c(\"div\", {\n    staticStyle: {\n      width: \"150px\",\n      height: \"100px\",\n      padding: \"20px\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      \"font-size\": \"16px\",\n      height: \"30px\",\n      \"line-height\": \"30px\",\n      color: \"#7F7F7FFF\"\n    }\n  }, [_vm._v(\"店铺邮箱\")]), _c(\"div\", {\n    staticStyle: {\n      \"font-size\": \"16px\",\n      height: \"30px\",\n      \"line-height\": \"30px\"\n    }\n  }, [_vm._v(_vm._s(_vm.businessData.email))])]), _c(\"div\", {\n    staticStyle: {\n      flex: \"1\",\n      height: \"100px\",\n      padding: \"20px\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      height: \"60px\",\n      \"line-height\": \"30px\",\n      \"font-size\": \"16px\",\n      color: \"#000000FF\",\n      overflow: \"hidden\",\n      \"text-overflow\": \"ellipsis\",\n      display: \"-webkit-box\",\n      \"-webkit-line-clamp\": \"2\",\n      \"-webkit-box-orient\": \"vertical\"\n    }\n  }, [_vm._v(\" 店铺介绍：\" + _vm._s(_vm.businessData.description) + \" \")])])]), _c(\"div\", {\n    staticStyle: {\n      \"border-radius\": \"20px\",\n      padding: \"0 20px\",\n      \"background-color\": \"white\",\n      \"margin-top\": \"20px\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      \"font-size\": \"18px\",\n      color: \"#000000FF\",\n      \"line-height\": \"80px\",\n      \"border-bottom\": \"#cccccc 1px solid\"\n    }\n  }, [_vm._v(\"本店所有商品（\" + _vm._s(_vm.goodsData.length) + \"件）\")]), _c(\"div\", {\n    staticStyle: {\n      \"margin-top\": \"20px\"\n    }\n  }, [_c(\"el-row\", _vm._l(_vm.goodsData, function (item) {\n    return _c(\"el-col\", {\n      staticStyle: {\n        \"margin-bottom\": \"20px\"\n      },\n      attrs: {\n        span: 5\n      }\n    }, [_c(\"img\", {\n      staticStyle: {\n        width: \"100%\",\n        height: \"150px\",\n        \"border-radius\": \"10px\",\n        border: \"#cccccc 1px solid\"\n      },\n      attrs: {\n        src: item.img,\n        alt: \"\"\n      },\n      on: {\n        click: function ($event) {\n          return _vm.navTo(\"/front/detail?id=\" + item.id);\n        }\n      }\n    }), _c(\"div\", {\n      staticStyle: {\n        \"margin-top\": \"10px\",\n        \"font-weight\": \"500\",\n        \"font-size\": \"16px\",\n        width: \"160px\",\n        color: \"#000000FF\",\n        \"text-overflow\": \"ellipsis\",\n        overflow: \"hidden\",\n        \"white-space\": \"nowrap\"\n      }\n    }, [_vm._v(_vm._s(item.name))]), _c(\"div\", {\n      staticStyle: {\n        \"margin-top\": \"5px\",\n        \"font-size\": \"20px\",\n        color: \"#FF5000FF\"\n      }\n    }, [_vm._v(\"￥ \" + _vm._s(item.price) + \" / \" + _vm._s(item.unit))])]);\n  }), 1)], 1)])])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "width", "margin", "height", "padding", "display", "attrs", "src", "businessData", "avatar", "alt", "_v", "_s", "name", "require", "color", "phone", "email", "flex", "overflow", "description", "goodsData", "length", "_l", "item", "span", "border", "img", "on", "click", "$event", "navTo", "id", "price", "unit", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/src/views/front/Business.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"main-content\" }, [\n    _c(\n      \"div\",\n      {\n        staticStyle: {\n          width: \"60%\",\n          margin: \"30px auto\",\n          \"border-radius\": \"20px\",\n        },\n      },\n      [\n        _c(\n          \"div\",\n          {\n            staticStyle: {\n              height: \"100px\",\n              padding: \"0 10px\",\n              display: \"flex\",\n              \"align-items\": \"center\",\n              \"border-radius\": \"25px\",\n              \"background-color\": \"white\",\n            },\n          },\n          [\n            _c(\"img\", {\n              staticStyle: {\n                height: \"60px\",\n                width: \"60px\",\n                \"border-radius\": \"50%\",\n              },\n              attrs: { src: _vm.businessData.avatar, alt: \"\" },\n            }),\n            _c(\n              \"div\",\n              {\n                staticStyle: {\n                  width: \"220px\",\n                  margin: \"0 30px 0 15px\",\n                  \"font-size\": \"20px\",\n                  \"font-weight\": \"bold\",\n                },\n              },\n              [\n                _c(\n                  \"div\",\n                  { staticStyle: { height: \"30px\", \"line-height\": \"30px\" } },\n                  [_vm._v(_vm._s(_vm.businessData.name))]\n                ),\n                _c(\"img\", {\n                  staticStyle: { height: \"25px\", \"margin-top\": \"5px\" },\n                  attrs: { src: require(\"@/assets/imgs/icon.png\"), alt: \"\" },\n                }),\n              ]\n            ),\n            _c(\n              \"div\",\n              {\n                staticStyle: {\n                  width: \"150px\",\n                  height: \"100px\",\n                  padding: \"20px\",\n                },\n              },\n              [\n                _c(\n                  \"div\",\n                  {\n                    staticStyle: {\n                      \"font-size\": \"16px\",\n                      height: \"30px\",\n                      \"line-height\": \"30px\",\n                      color: \"#7F7F7FFF\",\n                    },\n                  },\n                  [_vm._v(\"店铺电话\")]\n                ),\n                _c(\n                  \"div\",\n                  {\n                    staticStyle: {\n                      \"font-size\": \"16px\",\n                      height: \"30px\",\n                      \"line-height\": \"30px\",\n                    },\n                  },\n                  [_vm._v(_vm._s(_vm.businessData.phone))]\n                ),\n              ]\n            ),\n            _c(\n              \"div\",\n              {\n                staticStyle: {\n                  width: \"150px\",\n                  height: \"100px\",\n                  padding: \"20px\",\n                },\n              },\n              [\n                _c(\n                  \"div\",\n                  {\n                    staticStyle: {\n                      \"font-size\": \"16px\",\n                      height: \"30px\",\n                      \"line-height\": \"30px\",\n                      color: \"#7F7F7FFF\",\n                    },\n                  },\n                  [_vm._v(\"店铺邮箱\")]\n                ),\n                _c(\n                  \"div\",\n                  {\n                    staticStyle: {\n                      \"font-size\": \"16px\",\n                      height: \"30px\",\n                      \"line-height\": \"30px\",\n                    },\n                  },\n                  [_vm._v(_vm._s(_vm.businessData.email))]\n                ),\n              ]\n            ),\n            _c(\n              \"div\",\n              { staticStyle: { flex: \"1\", height: \"100px\", padding: \"20px\" } },\n              [\n                _c(\n                  \"div\",\n                  {\n                    staticStyle: {\n                      height: \"60px\",\n                      \"line-height\": \"30px\",\n                      \"font-size\": \"16px\",\n                      color: \"#000000FF\",\n                      overflow: \"hidden\",\n                      \"text-overflow\": \"ellipsis\",\n                      display: \"-webkit-box\",\n                      \"-webkit-line-clamp\": \"2\",\n                      \"-webkit-box-orient\": \"vertical\",\n                    },\n                  },\n                  [\n                    _vm._v(\n                      \" 店铺介绍：\" + _vm._s(_vm.businessData.description) + \" \"\n                    ),\n                  ]\n                ),\n              ]\n            ),\n          ]\n        ),\n        _c(\n          \"div\",\n          {\n            staticStyle: {\n              \"border-radius\": \"20px\",\n              padding: \"0 20px\",\n              \"background-color\": \"white\",\n              \"margin-top\": \"20px\",\n            },\n          },\n          [\n            _c(\n              \"div\",\n              {\n                staticStyle: {\n                  \"font-size\": \"18px\",\n                  color: \"#000000FF\",\n                  \"line-height\": \"80px\",\n                  \"border-bottom\": \"#cccccc 1px solid\",\n                },\n              },\n              [_vm._v(\"本店所有商品（\" + _vm._s(_vm.goodsData.length) + \"件）\")]\n            ),\n            _c(\n              \"div\",\n              { staticStyle: { \"margin-top\": \"20px\" } },\n              [\n                _c(\n                  \"el-row\",\n                  _vm._l(_vm.goodsData, function (item) {\n                    return _c(\n                      \"el-col\",\n                      {\n                        staticStyle: { \"margin-bottom\": \"20px\" },\n                        attrs: { span: 5 },\n                      },\n                      [\n                        _c(\"img\", {\n                          staticStyle: {\n                            width: \"100%\",\n                            height: \"150px\",\n                            \"border-radius\": \"10px\",\n                            border: \"#cccccc 1px solid\",\n                          },\n                          attrs: { src: item.img, alt: \"\" },\n                          on: {\n                            click: function ($event) {\n                              return _vm.navTo(\"/front/detail?id=\" + item.id)\n                            },\n                          },\n                        }),\n                        _c(\n                          \"div\",\n                          {\n                            staticStyle: {\n                              \"margin-top\": \"10px\",\n                              \"font-weight\": \"500\",\n                              \"font-size\": \"16px\",\n                              width: \"160px\",\n                              color: \"#000000FF\",\n                              \"text-overflow\": \"ellipsis\",\n                              overflow: \"hidden\",\n                              \"white-space\": \"nowrap\",\n                            },\n                          },\n                          [_vm._v(_vm._s(item.name))]\n                        ),\n                        _c(\n                          \"div\",\n                          {\n                            staticStyle: {\n                              \"margin-top\": \"5px\",\n                              \"font-size\": \"20px\",\n                              color: \"#FF5000FF\",\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"￥ \" +\n                                _vm._s(item.price) +\n                                \" / \" +\n                                _vm._s(item.unit)\n                            ),\n                          ]\n                        ),\n                      ]\n                    )\n                  }),\n                  1\n                ),\n              ],\n              1\n            ),\n          ]\n        ),\n      ]\n    ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CAChDF,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACXC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,WAAW;MACnB,eAAe,EAAE;IACnB;EACF,CAAC,EACD,CACEL,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACXG,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,QAAQ;MACjBC,OAAO,EAAE,MAAM;MACf,aAAa,EAAE,QAAQ;MACvB,eAAe,EAAE,MAAM;MACvB,kBAAkB,EAAE;IACtB;EACF,CAAC,EACD,CACER,EAAE,CAAC,KAAK,EAAE;IACRG,WAAW,EAAE;MACXG,MAAM,EAAE,MAAM;MACdF,KAAK,EAAE,MAAM;MACb,eAAe,EAAE;IACnB,CAAC;IACDK,KAAK,EAAE;MAAEC,GAAG,EAAEX,GAAG,CAACY,YAAY,CAACC,MAAM;MAAEC,GAAG,EAAE;IAAG;EACjD,CAAC,CAAC,EACFb,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACXC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,eAAe;MACvB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CACEL,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;MAAEG,MAAM,EAAE,MAAM;MAAE,aAAa,EAAE;IAAO;EAAE,CAAC,EAC1D,CAACP,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACY,YAAY,CAACK,IAAI,CAAC,CAAC,CACxC,CAAC,EACDhB,EAAE,CAAC,KAAK,EAAE;IACRG,WAAW,EAAE;MAAEG,MAAM,EAAE,MAAM;MAAE,YAAY,EAAE;IAAM,CAAC;IACpDG,KAAK,EAAE;MAAEC,GAAG,EAAEO,OAAO,CAAC,wBAAwB,CAAC;MAAEJ,GAAG,EAAE;IAAG;EAC3D,CAAC,CAAC,CAEN,CAAC,EACDb,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACXC,KAAK,EAAE,OAAO;MACdE,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE;IACX;EACF,CAAC,EACD,CACEP,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACX,WAAW,EAAE,MAAM;MACnBG,MAAM,EAAE,MAAM;MACd,aAAa,EAAE,MAAM;MACrBY,KAAK,EAAE;IACT;EACF,CAAC,EACD,CAACnB,GAAG,CAACe,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDd,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACX,WAAW,EAAE,MAAM;MACnBG,MAAM,EAAE,MAAM;MACd,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CAACP,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACY,YAAY,CAACQ,KAAK,CAAC,CAAC,CACzC,CAAC,CAEL,CAAC,EACDnB,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACXC,KAAK,EAAE,OAAO;MACdE,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE;IACX;EACF,CAAC,EACD,CACEP,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACX,WAAW,EAAE,MAAM;MACnBG,MAAM,EAAE,MAAM;MACd,aAAa,EAAE,MAAM;MACrBY,KAAK,EAAE;IACT;EACF,CAAC,EACD,CAACnB,GAAG,CAACe,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDd,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACX,WAAW,EAAE,MAAM;MACnBG,MAAM,EAAE,MAAM;MACd,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CAACP,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACY,YAAY,CAACS,KAAK,CAAC,CAAC,CACzC,CAAC,CAEL,CAAC,EACDpB,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;MAAEkB,IAAI,EAAE,GAAG;MAAEf,MAAM,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAO;EAAE,CAAC,EAChE,CACEP,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACXG,MAAM,EAAE,MAAM;MACd,aAAa,EAAE,MAAM;MACrB,WAAW,EAAE,MAAM;MACnBY,KAAK,EAAE,WAAW;MAClBI,QAAQ,EAAE,QAAQ;MAClB,eAAe,EAAE,UAAU;MAC3Bd,OAAO,EAAE,aAAa;MACtB,oBAAoB,EAAE,GAAG;MACzB,oBAAoB,EAAE;IACxB;EACF,CAAC,EACD,CACET,GAAG,CAACe,EAAE,CACJ,QAAQ,GAAGf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACY,YAAY,CAACY,WAAW,CAAC,GAAG,GACpD,CAAC,CAEL,CAAC,CAEL,CAAC,CAEL,CAAC,EACDvB,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACX,eAAe,EAAE,MAAM;MACvBI,OAAO,EAAE,QAAQ;MACjB,kBAAkB,EAAE,OAAO;MAC3B,YAAY,EAAE;IAChB;EACF,CAAC,EACD,CACEP,EAAE,CACA,KAAK,EACL;IACEG,WAAW,EAAE;MACX,WAAW,EAAE,MAAM;MACnBe,KAAK,EAAE,WAAW;MAClB,aAAa,EAAE,MAAM;MACrB,eAAe,EAAE;IACnB;EACF,CAAC,EACD,CAACnB,GAAG,CAACe,EAAE,CAAC,SAAS,GAAGf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACyB,SAAS,CAACC,MAAM,CAAC,GAAG,IAAI,CAAC,CAC1D,CAAC,EACDzB,EAAE,CACA,KAAK,EACL;IAAEG,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO;EAAE,CAAC,EACzC,CACEH,EAAE,CACA,QAAQ,EACRD,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAACyB,SAAS,EAAE,UAAUG,IAAI,EAAE;IACpC,OAAO3B,EAAE,CACP,QAAQ,EACR;MACEG,WAAW,EAAE;QAAE,eAAe,EAAE;MAAO,CAAC;MACxCM,KAAK,EAAE;QAAEmB,IAAI,EAAE;MAAE;IACnB,CAAC,EACD,CACE5B,EAAE,CAAC,KAAK,EAAE;MACRG,WAAW,EAAE;QACXC,KAAK,EAAE,MAAM;QACbE,MAAM,EAAE,OAAO;QACf,eAAe,EAAE,MAAM;QACvBuB,MAAM,EAAE;MACV,CAAC;MACDpB,KAAK,EAAE;QAAEC,GAAG,EAAEiB,IAAI,CAACG,GAAG;QAAEjB,GAAG,EAAE;MAAG,CAAC;MACjCkB,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAOlC,GAAG,CAACmC,KAAK,CAAC,mBAAmB,GAAGP,IAAI,CAACQ,EAAE,CAAC;QACjD;MACF;IACF,CAAC,CAAC,EACFnC,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE;QACX,YAAY,EAAE,MAAM;QACpB,aAAa,EAAE,KAAK;QACpB,WAAW,EAAE,MAAM;QACnBC,KAAK,EAAE,OAAO;QACdc,KAAK,EAAE,WAAW;QAClB,eAAe,EAAE,UAAU;QAC3BI,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE;MACjB;IACF,CAAC,EACD,CAACvB,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAACY,IAAI,CAACX,IAAI,CAAC,CAAC,CAC5B,CAAC,EACDhB,EAAE,CACA,KAAK,EACL;MACEG,WAAW,EAAE;QACX,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,MAAM;QACnBe,KAAK,EAAE;MACT;IACF,CAAC,EACD,CACEnB,GAAG,CAACe,EAAE,CACJ,IAAI,GACFf,GAAG,CAACgB,EAAE,CAACY,IAAI,CAACS,KAAK,CAAC,GAClB,KAAK,GACLrC,GAAG,CAACgB,EAAE,CAACY,IAAI,CAACU,IAAI,CACpB,CAAC,CAEL,CAAC,CAEL,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC,CAEL,CAAC,CACF,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBxC,MAAM,CAACyC,aAAa,GAAG,IAAI;AAE3B,SAASzC,MAAM,EAAEwC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}