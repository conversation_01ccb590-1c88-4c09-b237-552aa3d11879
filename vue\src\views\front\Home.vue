<template>
    <div class="home-container">
        <!-- 搜索栏 -->
        <div class="search-section">
            <div class="search-container">
                <el-input
                    placeholder="搜索您想要的美食..."
                    v-model="name"
                    class="search-input"
                    clearable
                    size="large"
                    @keyup.enter.native="load(1)"
                    @input="handleSearchInput"
                >
                    <el-button
                        slot="append"
                        icon="el-icon-search"
                        @click="load(1)"
                        class="search-btn"
                    ></el-button>
                </el-input>
            </div>
        </div>

        <!-- 主要内容区 -->
        <div class="main-content">
            <!-- 左侧分类菜单 -->
            <div class="sidebar-container" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
                <!-- 菜单切换按钮 -->
                <div class="sidebar-toggle" @click="toggleSidebar">
                    <i :class="sidebarCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
                </div>
                
                <!-- 分类菜单 -->
                <div class="category-sidebar">
                    <div class="sidebar-header">
                        <i class="el-icon-menu"></i>
                        <span v-show="!sidebarCollapsed" class="sidebar-title">商品分类</span>
                    </div>
                    
                    <div v-if="categoriesLoading" class="category-loading">
                        <i class="el-icon-loading"></i>
                        <span v-show="!sidebarCollapsed">加载中...</span>
                    </div>
                    
                    <div v-else class="category-menu">
                        <!-- 全部分类 -->
                        <div 
                            class="category-menu-item"
                            :class="{ 'active': selectedCategoryId === null }"
                            @click="selectCategory(null)"
                        >
                            <div class="menu-item-icon">
                                <i class="el-icon-s-grid"></i>
                            </div>
                            <span v-show="!sidebarCollapsed" class="menu-item-text">全部商品</span>
                            <div v-show="selectedCategoryId === null && !sidebarCollapsed" class="menu-item-indicator"></div>
                        </div>
                        
                        <!-- 具体分类 -->
                        <div 
                            class="category-menu-item"
                            :class="{ 'active': selectedCategoryId === category.id }"
                            v-for="category in categories"
                            :key="category.id"
                            @click="selectCategory(category.id)"
                        >
                            <div class="menu-item-icon">
                                <el-image 
                                    v-if="category.icon" 
                                    :src="category.icon" 
                                    fit="cover"
                                    class="category-menu-image"
                                ></el-image>
                                <i v-else class="el-icon-dish"></i>
                            </div>
                            <span v-show="!sidebarCollapsed" class="menu-item-text">{{ category.name }}</span>
                            <div v-show="selectedCategoryId === category.id && !sidebarCollapsed" class="menu-item-indicator"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="content-area" :class="{ 'content-expanded': sidebarCollapsed }">
                <!-- 商品展示区 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">精选美食</h2>
                        <p class="section-subtitle">为您精心挑选的优质美食</p>
                    </div>
                    
                    <div v-if="goodsLoading" class="loading-state">
                        <i class="el-icon-loading"></i>
                        <p>加载中...</p>
                    </div>
                    
                    <div v-else-if="tableData.length === 0" class="empty-state">
                        <i class="el-icon-dish"></i>
                        <h3>暂无商品</h3>
                        <p v-if="selectedCategoryId">该分类下暂无商品，试试其他分类吧</p>
                        <p v-else-if="name">没有找到相关商品，试试其他关键词吧</p>
                        <p v-else>暂无商品信息，敬请期待</p>
                    </div>
                    
                    <div v-else class="goods-grid">
                        <div
                            class="goods-card"
                            v-for="item in tableData"
                            :key="item.id"
                            @click="showDetail(item)"
                        >
                            <div class="card-image-container">
                                <el-image
                                    :src="item.sfImage"
                                    fit="cover"
                                    class="card-image"
                                    lazy
                                    :placeholder="'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7liqDovb3kuK08L3RleHQ+Cjwvc3ZnPg=='"
                                >
                                    <div slot="error" class="image-error">
                                        <i class="el-icon-picture-outline"></i>
                                        <span>图片加载失败</span>
                                    </div>
                                </el-image>
                                <div class="card-overlay">
                                    <el-button
                                        type="primary"
                                        size="small"
                                        circle
                                        icon="el-icon-view"
                                        class="view-btn"
                                    ></el-button>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">{{ item.name }}</h3>
                                <div class="card-price">
                                    <span class="price-symbol">¥</span>
                                    <span class="price-number">{{ item.sfPrice }}</span>
                                </div>
                                <div class="card-actions">
                                    <el-button
                                        type="primary"
                                        size="small"
                                        @click.stop="showCartDialog(item)"
                                        class="cart-btn"
                                    >
                                        <i class="el-icon-shopping-cart-2"></i>
                                        加入购物车
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <div class="pagination-section">
                    <el-pagination
                        background
                        @current-change="handleCurrentChange"
                        :current-page="pageNum"
                        :page-size="pageSize"
                        layout="prev, pager, next"
                        :total="total"
                        :pager-count="5"
                        prev-text="上一页"
                        next-text="下一页"
                        class="custom-pagination"
                    >
                    </el-pagination>
                </div>
            </div>
        </div>

        <!-- 商品详情弹窗 -->
        <el-dialog
            :visible.sync="detailVisible"
            width="70%"
            top="5vh"
            custom-class="detail-dialog"
            :close-on-click-modal="false"
        >
            <div class="detail-container" v-if="currentGoods">
                <div class="detail-left">
                    <div class="detail-image-container">
                        <el-image
                            :src="currentGoods.sfImage"
                            fit="contain"
                            class="detail-image"
                        ></el-image>
                    </div>
                </div>
                <div class="detail-right">
                    <div class="detail-header">
                        <h2 class="detail-title">{{ currentGoods.name }}</h2>
                        <div class="detail-price">
                            <span class="price-symbol">¥</span>
                            <span class="price-number">{{ currentGoods.sfPrice }}</span>
                        </div>
                    </div>
                    
                    <div class="detail-info">
                        <div class="info-card">
                            <div class="info-item">
                                <i class="el-icon-goods info-icon"></i>
                                <div class="info-content">
                                    <span class="info-label">商品类型</span>
                                    <span class="info-value">{{ currentGoods.foodtyope }}</span>
                                </div>
                            </div>
                            <div class="info-item">
                                <i class="el-icon-box info-icon"></i>
                                <div class="info-content">
                                    <span class="info-label">库存状态</span>
                                    <span class="info-value">{{ currentGoods.amount }}件</span>
                                </div>
                            </div>
                            <div class="info-item">
                                <i class="el-icon-check info-icon"></i>
                                <div class="info-content">
                                    <span class="info-label">上架状态</span>
                                    <span class="info-value">{{ currentGoods.fstatus }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-description">
                        <h3 class="desc-title">商品描述</h3>
                        <p class="desc-content">{{ currentGoods.sfDescription }}</p>
                    </div>
                    
                    <div class="detail-actions">
                        <el-button
                            type="primary"
                            size="large"
                            @click="showCartDialog(currentGoods)"
                            class="action-btn cart-action"
                        >
                            <i class="el-icon-shopping-cart-2"></i>
                            加入购物车
                        </el-button>
                        <el-button
                            type="danger"
                            size="large"
                            @click="showBuyDialog(currentGoods)"
                            class="action-btn buy-action"
                        >
                            <i class="el-icon-lightning"></i>
                            立即购买
                        </el-button>
                    </div>
                </div>
            </div>
        </el-dialog>

        <!-- 购物车/购买备注弹窗 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="orderDialogVisible"
            width="40%"
            :close-on-click-modal="false"
            custom-class="order-dialog"
        >
            <div class="order-form">
                <el-form :model="orderForm" label-width="80px">
                    <el-form-item label="商品名称">
                        <el-input v-model="orderForm.goodsName" disabled class="form-input"></el-input>
                    </el-form-item>
                    <el-form-item label="商品价格">
                        <el-input v-model="orderForm.goodsPrice" disabled class="form-input"></el-input>
                    </el-form-item>
                    <el-form-item label="购买数量">
                        <el-input-number 
                            v-model="orderForm.quantity" 
                            :min="1" 
                            :max="99"
                            controls-position="right"
                            class="quantity-input">
                        </el-input-number>
                    </el-form-item>
                    <el-form-item label="订单备注" prop="sfRemark">
                        <el-input
                            type="textarea"
                            v-model="orderForm.sfRemark"
                            placeholder="请输入订单备注（可选）"
                            :rows="3"
                            maxlength="200"
                            show-word-limit>
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="orderDialogVisible = false" class="cancel-btn">取消</el-button>
                <el-button type="primary" @click="confirmOrder" class="confirm-btn">{{ dialogButtonText }}</el-button>
            </div>
        </el-dialog>

        <!-- 餐桌选择对话框（立即购买用） -->
        <el-dialog 
            title="选择餐桌" 
            :visible.sync="buyTableDialogVisible" 
            width="800px"
            :close-on-click-modal="false">
            <div class="table-selection-container">
                <div class="table-selection-header">
                    <div class="selection-info">
                        <p>立即购买：{{ orderForm.goodsName }}，数量：{{ orderForm.quantity }}，总金额：<span class="total-price">¥{{ (orderForm.goodsPrice * orderForm.quantity).toFixed(2) }}</span></p>
                        <p v-if="selectedBuyTable">已选择：{{ selectedBuyTable.tableNumber }}号桌 ({{ selectedBuyTable.seats }}人座，{{ selectedBuyTable.area }})</p>
                    </div>
                </div>
                
                <!-- 餐桌选择组件 -->
                <table-select 
                    v-model="selectedBuyTable" 
                    @table-selected="handleBuyTableSelected">
                </table-select>
            </div>
            
            <template slot="footer">
                <span class="dialog-footer">
                    <el-button @click="buyTableDialogVisible = false">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="confirmBuyOrder"
                        :disabled="!selectedBuyTable">
                        确认下单
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import TableSelect from '@/components/TableSelect.vue'

export default {
    name: "GoodsList",
    components: {
        TableSelect
    },
    data() {
        return {
            tableData: [],  // 商品数据
            pageNum: 1,     // 当前页码
            pageSize: 12,   // 每页12条
            total: 0,       // 总数
            name: null,     // 搜索关键词
            
            // 分类相关
            categories: [],           // 分类列表
            selectedCategoryId: null, // 选中的分类ID
            categoriesLoading: false, // 分类加载状态
            goodsLoading: false,      // 商品加载状态
            loadingRequest: null,     // 当前加载请求
            searchTimer: null,        // 搜索防抖定时器
            sidebarCollapsed: false,  // 侧边栏折叠状态
            
            detailVisible: false, // 详情弹窗显示
            currentGoods: null,   // 当前查看的商品
            user: JSON.parse(localStorage.getItem('xm-user') || '{}'), // 当前登录用户
            cartCount: 0,         // 购物车商品数量

            // 订单相关
            orderDialogVisible: false,
            dialogTitle: '',
            dialogButtonText: '',
            orderForm: {
                goodsId: null,
                goodsName: '',
                goodsPrice: 0,
                quantity: 1,
                sfRemark: '',
                actionType: '' // 'cart' or 'buy'
            },

            // 立即购买餐桌选择相关
            buyTableDialogVisible: false,
            selectedBuyTable: null,
            buySubmitting: false // 防重复提交
        }
    },
    created() {
        this.load(1)
        this.loadCategories()
        this.updateCartCount() // 初始化购物车数量
    },
    beforeDestroy() {
        // 清理定时器
        if (this.searchTimer) {
            clearTimeout(this.searchTimer)
        }
        
        // 取消未完成的请求
        if (this.loadingRequest) {
            this.loadingRequest.cancel && this.loadingRequest.cancel()
        }
    },
    methods: {
        // 格式化时间为年月日时分秒
        formatDateTime(date) {
            const d = new Date(date);
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            const hours = String(d.getHours()).padStart(2, '0');
            const minutes = String(d.getMinutes()).padStart(2, '0');
            const seconds = String(d.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },

        load(pageNum) {  // 加载商品数据
            if (pageNum) this.pageNum = pageNum
            
            this.goodsLoading = true
            this.$request.get('/foods/selectPage', {
                params: {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize,
                    name: this.name,
                    categoryId: this.selectedCategoryId,
                }
            }).then(res => {
                if (res.code === '200') {
                    this.tableData = res.data?.list || []
                    this.total = res.data?.total || 0
                } else {
                    this.$message.error(res.msg || '加载商品失败，请重试')
                }
            }).catch(err => {
                console.error('加载商品失败:', err)
                this.$message.error('网络异常，请检查网络连接后重试')
            }).finally(() => {
                this.goodsLoading = false
            })
        },

        showDetail(item) {  // 显示商品详情
            this.currentGoods = item
            this.detailVisible = true
        },

        // 显示购物车弹窗
        showCartDialog(goods) {
            this.orderForm = {
                goodsId: goods.id,
                goodsName: goods.name,
                goodsPrice: goods.sfPrice,
                quantity: 1,
                sfRemark: '',
                actionType: 'cart'
            }
            this.dialogTitle = '加入购物车'
            this.dialogButtonText = '确认加入'
            this.orderDialogVisible = true
        },

        // 显示购买弹窗
        showBuyDialog(goods) {
            this.orderForm = {
                goodsId: goods.id,
                goodsName: goods.name,
                goodsPrice: goods.sfPrice,
                quantity: 1,
                sfRemark: '',
                actionType: 'buy'
            }
            this.dialogTitle = '立即购买'
            this.dialogButtonText = '确认购买'
            this.orderDialogVisible = true
        },

        // 确认订单（购物车或购买）
        confirmOrder() {
            if (this.orderForm.actionType === 'cart') {
                this.addToCart()
            } else {
                this.handleBuy()
            }
        },

        // 加入购物车
        addToCart() {
            this.$request.post('/cart/add', {
                foodId: this.orderForm.goodsId,
                quantity: this.orderForm.quantity || 1
            }).then(res => {
                if (res.code === '200') {
                    this.$message.success(`成功加入 ${this.orderForm.quantity} 件商品到购物车！`)
                    this.orderDialogVisible = false
                    this.updateCartCount() // 更新购物车数量提示
                } else {
                    this.$message.error(res.msg || '操作失败')
                }
            }).catch(() => {
                this.$message.error('操作失败，请重试')
            })
        },

        // 立即购买（打开餐桌选择对话框）
        handleBuy() {
            // 关闭订单对话框，打开餐桌选择对话框
            this.orderDialogVisible = false
            this.selectedBuyTable = null
            this.buyTableDialogVisible = true
        },

        // 处理立即购买的餐桌选择
        handleBuyTableSelected(table) {
            this.selectedBuyTable = table
        },

        // 确认立即购买订单（选择餐桌后）
        confirmBuyOrder() {
            if (!this.selectedBuyTable) {
                this.$message.warning('请选择餐桌')
                return
            }

            // 防重复提交
            if (this.buySubmitting) {
                this.$message.warning('正在处理中，请勿重复提交')
                return
            }

            const totalPrice = (this.orderForm.goodsPrice * this.orderForm.quantity).toFixed(2)
            
            this.$confirm(`确认要在${this.selectedBuyTable.tableNumber}号桌下单吗？总金额：¥${totalPrice}`, '立即购买确认', {
                confirmButtonText: '确定下单',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.buySubmitting = true
                this.$request.post('/dingdan/add', {
                    sfUserName: this.user.name || '匿名用户',
                    sfUserId: this.user.id || 0,
                    sfProductIds: this.orderForm.goodsId.toString(),
                    status: '已支付', // 立即购买直接设为已支付状态
                    sfCartStatus: '', // 购物车状态为空
                    sfTotalPrice: totalPrice, // 商品价格放入订单价格字段
                    sfRemark: this.orderForm.sfRemark, // 添加备注
                    tableNumber: this.selectedBuyTable.tableNumber, // 添加餐桌号
                    sfCreateTime: this.formatDateTime(new Date()) // 格式化时间
                }).then(res => {
                    if (res.code === '200') {
                        this.$message.success(`订单创建成功！餐桌：${this.selectedBuyTable.tableNumber}号`)
                        this.detailVisible = false
                        this.buyTableDialogVisible = false
                        this.selectedBuyTable = null
                    } else {
                        this.$message.error(res.msg || '下单失败')
                    }
                }).catch(err => {
                    console.error('下单失败:', err)
                    if (err.response && err.response.data && err.response.data.msg) {
                        this.$message.error(err.response.data.msg)
                    } else {
                        this.$message.error('下单失败，请重试')
                    }
                }).finally(() => {
                    this.buySubmitting = false
                })
            }).catch(() => {
                this.$message.info('已取消下单')
            })
        },

        handleCurrentChange(pageNum) {  // 分页变化
            this.load(pageNum)
        },

        // 更新购物车数量
        updateCartCount() {
            this.$request.get('/cart/count').then(res => {
                if (res.code === '200') {
                    this.cartCount = res.data || 0
                }
            }).catch(() => {
                // 静默处理错误，不影响用户体验
                console.error('获取购物车数量失败')
            })
        },

        // 加载分类数据
        loadCategories() {
            this.categoriesLoading = true
            this.$request.get('/category/selectEnabled').then(res => {
                if (res.code === '200') {
                    this.categories = res.data || []
                } else {
                    console.error('加载分类失败:', res.msg)
                    this.$message.warning('分类加载失败，但不影响商品浏览')
                }
            }).catch(err => {
                console.error('加载分类失败:', err)
                this.$message.warning('分类加载失败，但不影响商品浏览')
            }).finally(() => {
                this.categoriesLoading = false
            })
        },

        // 选择分类
        selectCategory(categoryId) {
            if (this.selectedCategoryId === categoryId) {
                return // 避免重复选择
            }
            
            // 如果正在加载，取消之前的请求
            if (this.loadingRequest) {
                this.loadingRequest.cancel && this.loadingRequest.cancel()
            }
            
            this.selectedCategoryId = categoryId
            
            // 提供用户反馈
            const categoryName = categoryId === null ? '全部商品' : 
                this.categories.find(c => c.id === categoryId)?.name || '未知分类'
            
            // 使用 Toast 提示而不是 Message，避免干扰用户
            this.$message({
                message: `正在加载${categoryName}...`,
                type: 'info',
                duration: 1000
            })
            
            this.load(1) // 重新加载第一页数据
        },

        // 搜索输入防抖处理
        handleSearchInput() {
            // 清除之前的定时器
            if (this.searchTimer) {
                clearTimeout(this.searchTimer)
            }
            
            // 设置新的定时器，500ms后执行搜索
            this.searchTimer = setTimeout(() => {
                this.load(1)
            }, 500)
        },

        // 切换侧边栏
        toggleSidebar() {
            this.sidebarCollapsed = !this.sidebarCollapsed
        }
    }
}
</script>

<style scoped>
.home-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    min-height: 100vh;
}

/* 搜索区域 */
.search-section {
    padding: 40px 0;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
    position: relative;
}

.search-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 20px;
}

.search-input {
    width: 100%;
}

.search-input >>> .el-input__inner {
    height: 50px;
    border-radius: 25px;
    border: 2px solid #e5e7eb;
    padding-left: 20px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.search-input >>> .el-input__inner:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-btn {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
    border-radius: 0 25px 25px 0;
    padding: 0 20px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: #1e40af;
    border-color: #1e40af;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    min-height: calc(100vh - 140px);
}

/* 左侧分类菜单 */
.sidebar-container {
    width: 280px;
    background: white;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 5;
}

.sidebar-container.sidebar-collapsed {
    width: 80px;
}

.sidebar-toggle {
    position: absolute;
    top: 20px;
    right: -15px;
    width: 30px;
    height: 30px;
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
    z-index: 10;
}

.sidebar-toggle:hover {
    background: #1e40af;
    transform: scale(1.1);
}

.category-sidebar {
    height: 100%;
    overflow-y: auto;
    padding: 20px 0;
}

.sidebar-header {
    display: flex;
    align-items: center;
    padding: 0 20px 20px 20px;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 20px;
}

.sidebar-header i {
    font-size: 20px;
    color: #3b82f6;
    margin-right: 12px;
}

.sidebar-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    transition: opacity 0.3s ease;
}

.sidebar-collapsed .sidebar-title {
    opacity: 0;
}

/* 分类菜单 */
.category-menu {
    padding: 0 10px;
}

.category-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.category-menu-item:hover {
    background: #f1f5f9;
    transform: translateX(4px);
}

.category-menu-item.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.category-menu-item.active:hover {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
}

.menu-item-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.menu-item-icon i {
    font-size: 18px;
    color: #64748b;
    transition: color 0.3s ease;
}

.category-menu-item.active .menu-item-icon i {
    color: white;
}

.category-menu-image {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
}

.menu-item-text {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #475569;
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.category-menu-item.active .menu-item-text {
    color: white;
    font-weight: 600;
}

.menu-item-indicator {
    width: 4px;
    height: 20px;
    background: white;
    border-radius: 2px;
    margin-left: 8px;
    opacity: 0.8;
}

/* 分类加载状态 */
.category-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #64748b;
    flex-direction: column;
}

.sidebar-collapsed .category-loading {
    flex-direction: column;
}

.category-loading i {
    font-size: 24px;
    margin-bottom: 8px;
    color: #3b82f6;
    animation: rotating 2s linear infinite;
}

.sidebar-collapsed .category-loading i {
    margin-right: 0;
    margin-bottom: 8px;
}

.category-loading span {
    font-size: 12px;
    transition: opacity 0.3s ease;
}

/* 右侧内容区域 */
.content-area {
    flex: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-left: 20px;
}

.content-area.content-expanded {
    margin-left: 20px;
}

/* 内容区域 */
.content-section {
    padding: 40px 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-title {
    font-size: 32px;
    font-weight: 700;
    color: #1e40af;
    margin-bottom: 12px;
}

.section-subtitle {
    font-size: 16px;
    color: #64748b;
    margin: 0;
}

/* 商品加载状态 */
.loading-state {
    text-align: center;
    padding: 80px 20px;
    color: #64748b;
}

.loading-state i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #3b82f6;
    animation: rotating 2s linear infinite;
}

.loading-state p {
    font-size: 16px;
    margin: 0;
}

/* 旋转动画 */
@keyframes rotating {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 图片错误状态 */
.image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f5f5f5;
    color: #999;
}

.image-error i {
    font-size: 32px;
    margin-bottom: 8px;
}

.image-error span {
    font-size: 12px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #64748b;
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 16px;
    color: #3b82f6;
}

.empty-state h3 {
    font-size: 20px;
    margin-bottom: 8px;
    color: #475569;
}

.empty-state p {
    margin-bottom: 0;
    font-size: 16px;
}

/* 商品网格 */
.goods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.goods-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: 1px solid #f1f5f9;
}

.goods-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);
    border-color: #3b82f6;
}

.card-image-container {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.card-image {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
}

.goods-card:hover .card-image {
    transform: scale(1.05);
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.goods-card:hover .card-overlay {
    opacity: 1;
}

.view-btn {
    background: white;
    color: #3b82f6;
    border: none;
    width: 50px;
    height: 50px;
    font-size: 20px;
}

.card-content {
    padding: 20px;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 12px;
    height: 48px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.5;
}

.card-price {
    margin-bottom: 16px;
}

.price-symbol {
    color: #3b82f6;
    font-size: 16px;
    font-weight: 600;
}

.price-number {
    color: #3b82f6;
    font-size: 24px;
    font-weight: 700;
}

.card-actions {
    display: flex;
    justify-content: center;
}

.cart-btn {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
    border-radius: 20px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.cart-btn:hover {
    background: #1e40af;
    border-color: #1e40af;
    transform: translateY(-2px);
}

/* 分页区域 */
.pagination-section {
    display: flex;
    justify-content: center;
    padding: 40px 0;
}

.custom-pagination >>> .el-pager li {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin: 0 4px;
    transition: all 0.3s ease;
}

.custom-pagination >>> .el-pager li:hover {
    border-color: #3b82f6;
    color: #3b82f6;
}

.custom-pagination >>> .el-pager li.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

/* 详情弹窗 */
.detail-dialog >>> .el-dialog {
    border-radius: 16px;
    overflow: hidden;
}

.detail-dialog >>> .el-dialog__header {
    display: none;
}

.detail-dialog >>> .el-dialog__body {
    padding: 0;
}

.detail-container {
    display: flex;
    min-height: 500px;
}

.detail-left {
    flex: 1;
    background: #f8fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.detail-image-container {
    width: 100%;
    max-width: 400px;
}

.detail-image {
    width: 100%;
    height: 400px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.detail-right {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
}

.detail-header {
    margin-bottom: 30px;
}

.detail-title {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 16px;
    line-height: 1.3;
}

.detail-price {
    margin-bottom: 20px;
}

.detail-price .price-symbol {
    color: #3b82f6;
    font-size: 24px;
    font-weight: 600;
}

.detail-price .price-number {
    color: #3b82f6;
    font-size: 36px;
    font-weight: 700;
}

.detail-info {
    margin-bottom: 30px;
}

.info-card {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e2e8f0;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-icon {
    width: 40px;
    height: 40px;
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 16px;
}

.info-content {
    flex: 1;
}

.info-label {
    display: block;
    font-size: 14px;
    color: #64748b;
    margin-bottom: 4px;
}

.info-value {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
}

.detail-description {
    margin-bottom: 40px;
    flex: 1;
}

.desc-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 12px;
}

.desc-content {
    font-size: 15px;
    color: #475569;
    line-height: 1.7;
    margin: 0;
}

.detail-actions {
    display: flex;
    gap: 16px;
}

.action-btn {
    flex: 1;
    height: 50px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.cart-action {
    background: #3b82f6;
    border-color: #3b82f6;
}

.cart-action:hover {
    background: #1e40af;
    border-color: #1e40af;
    transform: translateY(-2px);
}

.buy-action {
    background: #ef4444;
    border-color: #ef4444;
}

.buy-action:hover {
    background: #dc2626;
    border-color: #dc2626;
    transform: translateY(-2px);
}

/* 订单弹窗 */
.order-dialog >>> .el-dialog {
    border-radius: 16px;
}

.order-dialog >>> .el-dialog__header {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.order-form {
    padding: 20px 0;
}

.form-input >>> .el-input__inner,
.form-textarea >>> .el-textarea__inner {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.form-input >>> .el-input__inner:focus,
.form-textarea >>> .el-textarea__inner:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.quantity-input >>> .el-input-number__input {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.quantity-input >>> .el-input-number__input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.cancel-btn {
    border-radius: 8px;
    padding: 10px 20px;
}

.confirm-btn {
    background: #3b82f6;
    border-color: #3b82f6;
    border-radius: 8px;
    padding: 10px 20px;
}

.confirm-btn:hover {
    background: #1e40af;
    border-color: #1e40af;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .sidebar-container {
        width: 240px;
    }
    
    .sidebar-container.sidebar-collapsed {
        width: 70px;
    }
    
    .goods-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar-container {
        width: 100%;
        height: auto;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .sidebar-container.sidebar-collapsed {
        width: 100%;
        height: 60px;
        overflow: hidden;
    }
    
    .category-sidebar {
        padding: 10px 0;
    }
    
    .sidebar-collapsed .category-menu {
        display: none;
    }
    
    .content-area {
        margin-left: 0;
    }
    
    .content-area.content-expanded {
        margin-left: 0;
    }
    
    .goods-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 16px;
    }
    
    .detail-container {
        flex-direction: column;
    }
    
    .detail-left {
        padding: 20px;
    }
    
    .detail-right {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .content-section {
        padding: 20px 16px;
    }
    
    .goods-grid {
        grid-template-columns: 1fr;
    }
    
    .search-container {
        padding: 0 16px;
    }
}
</style>