{"ast": null, "code": "/* eslint-disable no-undefined */\n\nvar throttle = require('./throttle');\n\n/**\n * Debounce execution of a function. Debouncing, unlike throttling,\n * guarantees that a function is only executed a single time, either at the\n * very beginning of a series of calls, or at the very end.\n *\n * @param  {Number}   delay         A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param  {Boolean}  [atBegin]     Optional, defaults to false. If atBegin is false or unspecified, callback will only be executed `delay` milliseconds\n *                                  after the last debounced-function call. If atBegin is true, callback will be executed only at the first debounced-function call.\n *                                  (After the throttled-function has not been called for `delay` milliseconds, the internal counter is reset).\n * @param  {Function} callback      A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                  to `callback` when the debounced-function is executed.\n *\n * @return {Function} A new, debounced function.\n */\nmodule.exports = function (delay, atBegin, callback) {\n  return callback === undefined ? throttle(delay, atBegin, false) : throttle(delay, callback, atBegin !== false);\n};", "map": {"version": 3, "names": ["throttle", "require", "module", "exports", "delay", "atBegin", "callback", "undefined"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/node_modules/throttle-debounce/debounce.js"], "sourcesContent": ["/* eslint-disable no-undefined */\n\nvar throttle = require('./throttle');\n\n/**\n * Debounce execution of a function. Debouncing, unlike throttling,\n * guarantees that a function is only executed a single time, either at the\n * very beginning of a series of calls, or at the very end.\n *\n * @param  {Number}   delay         A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param  {Boolean}  [atBegin]     Optional, defaults to false. If atBegin is false or unspecified, callback will only be executed `delay` milliseconds\n *                                  after the last debounced-function call. If atBegin is true, callback will be executed only at the first debounced-function call.\n *                                  (After the throttled-function has not been called for `delay` milliseconds, the internal counter is reset).\n * @param  {Function} callback      A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                  to `callback` when the debounced-function is executed.\n *\n * @return {Function} A new, debounced function.\n */\nmodule.exports = function ( delay, atBegin, callback ) {\n\treturn callback === undefined ? throttle(delay, atBegin, false) : throttle(delay, callback, atBegin !== false);\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAWC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAG;EACtD,OAAOA,QAAQ,KAAKC,SAAS,GAAGP,QAAQ,CAACI,KAAK,EAAEC,OAAO,EAAE,KAAK,CAAC,GAAGL,QAAQ,CAACI,KAAK,EAAEE,QAAQ,EAAED,OAAO,KAAK,KAAK,CAAC;AAC/G,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}